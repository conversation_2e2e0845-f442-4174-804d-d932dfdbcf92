package com.datatech.slgzt.model.bpmn;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 封装当前节点以及历史节点
 *
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/3/12
 */

@Data
@Accessors(chain = true)
public class CurrentBeforeTaskNodeVO {

    /**
     * 节点名称编码
     */
    private String activityNodeCode;

    /**
     * 节点id
     */
    private String activityNodeId;

    /**
     * 流程历史节点数据集
     */
    private List<CurrentBeforeTaskNodeVO> historyNodes = new ArrayList<>();

    public void addNodeElement(String activityNodeCode, String activityNodeId) {
        CurrentBeforeTaskNodeVO vo = new CurrentBeforeTaskNodeVO().setActivityNodeCode(activityNodeCode).setActivityNodeId(activityNodeId);
        historyNodes.add(vo);
    }
}

