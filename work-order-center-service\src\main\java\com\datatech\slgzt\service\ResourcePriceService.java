package com.datatech.slgzt.service;

import com.datatech.slgzt.model.nostander.*;

import java.math.BigDecimal;

public interface ResourcePriceService {
    /**
     * 计算云主机价格
     *
     * @param vmModel 云主机模型
     * @return 价格结果
     */
    BigDecimal calculateVmPrice(EcsModel vmModel);

    /**
     * 计算云硬盘价格
     *
     * @param evsList 云硬盘列表
     * @return 价格结果
     */

    BigDecimal calculateVolumePrice(EvsModel evsList);


    BigDecimal calculateObsPrice(ObsModel obsModel);

    BigDecimal calculateSlbPrice(SlbModel slbModel);

    BigDecimal calculateNatPrice(NatGatwayModel natGatwayModel);

    //vpn
    BigDecimal calculateVpnPrice(VpnModel vpnModel);
}