package com.datatech.slgzt.enums.bpmn;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import com.datatech.slgzt.enums.OrderLogStatusEnum;
import com.datatech.slgzt.utils.Precondition;

import java.util.Arrays;
import java.util.List;

/**
 * 工单流状态枚举
 */
@Getter
@AllArgsConstructor
public enum ActivitiStatusEnum {

    USER_TASK("user_task", "发起工单"),
    SCHEMA_ADMINISTRATOR("schema_administrator", "架构负责人审核"),
//    PROFESSIONAL_GROUP("professional_group", "主机专业组审核"),
    TENANT_TASK("tenant_task", "租户确认"),
    BUSINESS_DEPART_LEADER2("business_depart_leader2", "三级业务部门领导审核"),
    BUSINESS2_DEPART_LEADER("business2_depart_leader", "三级业务部门领导审核"),
    BUSINESS_DEPART_LEADER("business_depart_leader", "二级业务部门领导审核"),
    cloud_leader("cloud_leader", "三级云资源部领导审核"),
    cloud_leader_2("cloud_leader_2", "二级云资源部领导审核"),

    ALARM_SUPPRESSION("alarm_suppression", "屏蔽告警"),
    SHUTDOWN("shutdown", "云主机关机"),
    //-------------------------------非标流程--------------------------------
    RESPONSE_SCHEME_MANAGER("response_scheme_manager", "响应方案经理审批"),
    BRANCH_LEADER("branch_leader", "分公司领导审批"),
    PROVINCE_GOV_ADMIN("province_gov_admin", "省政企管理员审批"),
    PROVINCE_GOV_LEADER("province_gov_leader", "省政企领导审批"),
    CLOUD_RESOURCE_LEADER("cloud_resource_leader", "云资源部领导审批"),
    OFFLINE_OPEN_H("offline_open_h", "线下开通"),
    INFORMATION_ARCHIVE_H("information_archive_h", "信息归档"),
    NETWORK_PROVISIONING_H("network_provisioning_h", "网络开通"),
    RESOURCE_CREATION_H("resource_creation_h", "资源开通"),
    OFFLINE_OPEN_L("offline_open_l", "线下开通"),
    INFORMATION_ARCHIVE_L("information_archive_l", "信息归档"),
    NETWORK_PROVISIONING_L("network_provisioning_l", "网络开通"),
    RESOURCE_CREATION_L("resource_creation_l", "资源开通"),

    NETWORK_PROVISIONING("network_provisioning", "网络开通"),
    RESOURCE_CREATION("resource_creation", "资源开通"),
    ACCESS_TO_4A("access_to_4a", "入网交维"),
    RETREAT_DIMENSION("retreat_dimension", "交维清退"),
    RESOURCE_RECOVERY("resource_recovery", "资源回收"),
    RESOURCE_CHANGE("resource_change", "资源变更"),
    network_recovery("network_recovery", "网络回收"),
    ORDER_COMPLETED("order_completed", "工单已完结"),
    OPERATION_GROUP("operation_group", "资源回收"),
    ORDER_CANCEL("", "工单已撤销"),
    ORDER_REJECT("", "工单驳回"),
    AUTODIT_END("autodit end", "完成审批"),

    ;


    private final String node;
    private final String nodeRemark;

    public static String getNodeRemarkByNode(String node, String orderStatus) {
        if (StringUtils.isEmpty(node)) {
            if (RecoveryOrderNodeEnum.CLOSE.getCode().equals(orderStatus)) {
                return ActivitiStatusEnum.ORDER_CANCEL.nodeRemark;
            }
            if (RecoveryOrderNodeEnum.END.getCode().equals(orderStatus)) {
                return ActivitiStatusEnum.AUTODIT_END.nodeRemark;
            }
            if (RecoveryOrderNodeEnum.REJECT.getCode().equalsIgnoreCase(orderStatus)) {
                return ActivitiStatusEnum.ORDER_REJECT.nodeRemark;
            }
        }
        List<ActivitiStatusEnum> list = Arrays.asList(values());
        for (ActivitiStatusEnum activitiStatusEnum : list) {
            if (activitiStatusEnum.node.equals(node)) {
                return activitiStatusEnum.nodeRemark;
            }
        }

        return ActivitiStatusEnum.ORDER_CANCEL.nodeRemark;
    }
    
    public static ActivitiStatusEnum getNodeRemarkByNode2(String node, OrderLogStatusEnum orderStatus) {
        if (orderStatus == OrderLogStatusEnum.CLOSE) {
            return ActivitiStatusEnum.ORDER_CANCEL;
        }
        if (orderStatus == OrderLogStatusEnum.END) {
            return ActivitiStatusEnum.AUTODIT_END;
        }
        if (orderStatus == OrderLogStatusEnum.REJECT) {
            return ActivitiStatusEnum.ORDER_REJECT;
        }

        Precondition.checkArgument(StringUtils.isNotEmpty(node), "节点不能为空");
        for (ActivitiStatusEnum activitiStatusEnum : values()) {
            if (activitiStatusEnum.node.equals(node)) {
                return activitiStatusEnum;
            }
        }

        return null;
    }
}
