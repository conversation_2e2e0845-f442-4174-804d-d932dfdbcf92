package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.constant.DeteteConstant;
import com.cloud.marginal.enums.layout.TaskTypeEnum;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskDefService;
import com.cloud.marginal.mapper.layout.LayoutTaskDefMapper;
import com.cloud.marginal.model.dto.layout.LayoutTaskDto;
import com.cloud.marginal.model.entity.layout.FollowTaskDef;
import com.cloud.marginal.model.entity.layout.LayoutTask;
import com.cloud.marginal.model.entity.layout.LayoutTaskDef;
import com.cloud.marginal.model.entity.layout.LayoutTaskNode;
import com.cloud.marginal.model.vo.layout.LayoutTaskDefVo;
import com.cloud.marginal.utils.AssertUtil;
import com.cloud.marginal.utils.UuidUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 编排任务配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class LayoutTaskDefServiceImpl extends ServiceImpl<LayoutTaskDefMapper, LayoutTaskDef> implements LayoutTaskDefService {

    @Resource
    private  LayoutTaskDefMapper layoutTaskDefMapper;

    @Override
    public LayoutTaskNode generateTaskNodeByTemplateCodeAndTaskCode(String templateCode, String taskCode) {
        return this.baseMapper.generateTaskNodeByTemplateCodeAndTaskCode(templateCode, taskCode);
    }

    @Override
    public LayoutTask generateMainTaskByTemplateCode(String templateCode) {
        return this.baseMapper.generateMainTaskByTemplateCode(templateCode);
    }

    @Override
    public List<FollowTaskDef> getFollowTaskDefByTemplateCode(String templateCode) {
        return this.baseMapper.getFollowTaskDefByTemplateCode(templateCode);
    }

    @Override
    @Transactional
    public void create(LayoutTaskDef layoutTaskDef) {
        LayoutTaskDef code = getCode(layoutTaskDef.getTaskCode());
        if (code != null){
            throw new BusinessException("编排任务编号重复");
        }
        layoutTaskDef.setId(UuidUtil.generateId());
        layoutTaskDef.setTaskType("SUB");
        layoutTaskDefMapper.insert(layoutTaskDef);
    }

    @Override
    @Transactional
    public void update(LayoutTaskDef layoutTaskDef) {
        LayoutTaskDef code = getCode(layoutTaskDef.getTaskCode());
        if (code != null && !code.getId().equals(layoutTaskDef.getId())){
            throw new BusinessException("编排任务编号重复");
        }
        layoutTaskDefMapper.updateById(layoutTaskDef);
    }

    @Override
    public CecPage<LayoutTaskDefVo> getPage(LayoutTaskDto layoutTaskDto) {
        Page<LayoutTaskDef> page = new Page<>(layoutTaskDto.getPageNum(),layoutTaskDto.getPageSize());
        LambdaQueryWrapper<LayoutTaskDef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LayoutTaskDef::getStatus,DeteteConstant.EFFECTIVE);
        wrapper.like(StrUtil.isNotEmpty(layoutTaskDto.getTaskName()),LayoutTaskDef::getTaskName,layoutTaskDto.getTaskName());
        wrapper.notIn(LayoutTaskDef::getTaskCode, Arrays.asList("MASK_TASK","MASK_NOTICE"));
        wrapper.orderByDesc(LayoutTaskDef::getCreatedBy,LayoutTaskDef::getId);
        page = layoutTaskDefMapper.selectPage(page, wrapper);
        List<LayoutTaskDefVo> list = BeanUtil.copyToList(page.getRecords(), LayoutTaskDefVo.class);
        return new CecPage<>(list,layoutTaskDto.getPageNum(),layoutTaskDto.getPageSize(),page.getTotal());
    }

    @Override
    public List<LayoutTaskDefVo> getList(String name,String templateId) {
        List<LayoutTaskDef> layoutTaskDefs = layoutTaskDefMapper.getTask(name,templateId);
        List<LayoutTaskDefVo> list = BeanUtil.copyToList(layoutTaskDefs, LayoutTaskDefVo.class);
        return list;
    }


    @Override
    @Transactional
    public void remove(String id) {
        LayoutTaskDef task = layoutTaskDefMapper.selectById(id);
        AssertUtil.notNull(task,"编排任务不存在");
        if (task.getTaskType().equals(TaskTypeEnum.MASTER)){
            throw new BusinessException("主任务不能删除");
        }
        task.setStatus(DeteteConstant.INVALID.intValue());
        layoutTaskDefMapper.updateById(task);
    }

    /**
     * 通过code查询任务
     */
    @Override
    public LayoutTaskDef getCode(String code){
        LambdaQueryWrapper<LayoutTaskDef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LayoutTaskDef::getStatus,DeteteConstant.EFFECTIVE);
        wrapper.eq(LayoutTaskDef::getTaskCode,code);
        List<LayoutTaskDef> layoutTaskDefs = layoutTaskDefMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(layoutTaskDefs)){
            return layoutTaskDefs.get(0);
        }
        return null;
    }

    @Override
    public boolean isMasterTask(String taskId) {
        LayoutTaskDef layoutTaskDef = layoutTaskDefMapper.selectById(taskId);
        return layoutTaskDef !=null && TaskTypeEnum.MASTER.toString().equals(layoutTaskDef.getTaskType());
    }

    // 默认

    @Override
    public List<LayoutTaskNode> generateTaskNodeByDefault(String templateCode) {
        return this.baseMapper.generateTaskNodeByDefault(templateCode);
    }
}
