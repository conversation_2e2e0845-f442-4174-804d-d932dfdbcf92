package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.marginal.layoutcenter.service.layoutdb.TaskParamDefService;
import com.cloud.marginal.mapper.layout.TaskParamDefMapper;
import com.cloud.marginal.model.entity.layout.LayoutParamDef;
import com.cloud.marginal.model.entity.layout.TaskParamDef;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 编排模板与参数关系配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class TaskParamDefServiceImpl extends ServiceImpl<TaskParamDefMapper, TaskParamDef> implements TaskParamDefService {

    @Override
    public LayoutParamDef getLayoutParamDefByTemplateCode(String templateCode) {
        return this.baseMapper.getLayoutParamDefByTemplateCode(templateCode);
    }
}
