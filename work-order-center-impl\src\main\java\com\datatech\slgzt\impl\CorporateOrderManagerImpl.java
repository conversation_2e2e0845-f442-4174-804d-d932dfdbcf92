package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.CorporateOrderManagerConvert;
import com.datatech.slgzt.dao.CorporateOrderDAO;
import com.datatech.slgzt.dao.model.CorporateOrderDO;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.model.dto.CorporateOrderDTO;
import com.datatech.slgzt.model.query.CorporateOrderQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CorporateOrderManagerImpl implements CorporateOrderManager {

    @Resource
    private CorporateOrderDAO corporateOrderDAO;

    @Resource
    private CorporateOrderManagerConvert corporateOrderManagerConvert;

    @Override
    public List<CorporateOrderDTO> list(CorporateOrderQuery query) {
        List<CorporateOrderDO> list = corporateOrderDAO.list(query);
        return corporateOrderManagerConvert.dos2DTOs(list);
    }

    @Override
    public PageResult<CorporateOrderDTO> page(CorporateOrderQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<CorporateOrderDO> list = corporateOrderDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list),corporateOrderManagerConvert::do2dto);
    }

    @Override
    public String insert(CorporateOrderDTO dto) {
        CorporateOrderDO corporateOrderDO = corporateOrderManagerConvert.dto2do(dto);
        corporateOrderDAO.insert(corporateOrderDO);
        return corporateOrderDO.getId();
    }

    @Override
    public void update(CorporateOrderDTO dto) {
        CorporateOrderDO corporateOrderDO = corporateOrderManagerConvert.dto2do(dto);
        corporateOrderDAO.update(corporateOrderDO);
    }

    @Override
    public void delete(String id) {
        corporateOrderDAO.delete(id);
    }

    @Override
    public CorporateOrderDTO getById(String id) {
        CorporateOrderDO corporateOrderDO = corporateOrderDAO.getById(id);
        return corporateOrderManagerConvert.do2dto(corporateOrderDO);
    }

    @Override
    public CorporateOrderDTO getByOrderCode(String orderCode) {
        CorporateOrderDO corporateOrderDO = corporateOrderDAO.getByOrderCode(orderCode);
        return corporateOrderManagerConvert.do2dto(corporateOrderDO);
    }
} 