package com.datatech.slgzt.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.mapper.VirtualIpMapper;
import com.datatech.slgzt.manager.SlbCertificateManager;
import com.datatech.slgzt.manager.SlbListenerManager;
import com.datatech.slgzt.manager.SlbListenerServerGroupManager;
import com.datatech.slgzt.model.TaskStatusExt;
import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import com.datatech.slgzt.model.dto.SlbListenerDTO;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import com.datatech.slgzt.service.SlbListenerService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月14日 11:27:21
 */
@Slf4j
@Service
public class SlbListenerServiceImpl implements SlbListenerService {

    @Resource
    private SlbListenerManager slbListenerManager;

    @Resource
    private SlbListenerServerGroupManager slbListenerServerGroupManager;

    @Resource
    private VirtualIpMapper virtualIpMapper;


    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;


    /**
     * @param slbListenerDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSlbListener(SlbListenerDTO slbListenerDTO) {
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/slb/listener/create";
        SlbListenerDTO.SlbListenerProtocolModel slbListenerProtocolModel = slbListenerDTO.getSlbListenerProtocolModel();
        SlbListenerServerGroupDTO slbListenerServerGroup = slbListenerDTO.getSlbListenerServerGroup();
        SlbListenerDTO.SlbHealthCheckModel slbHealthCheckModel = slbListenerDTO.getSlbHealthCheckModel();
        HashMap<String, Object> params = new HashMap<>();
        params.put("globalId", IdUtil.fastSimpleUUID());
        params.put("slbId", slbListenerDTO.getSlbDeviceId());
        params.put("name", slbListenerDTO.getListenerName());
        params.put("port", slbListenerProtocolModel.getListenerPort());
        params.put("protocol", slbListenerProtocolModel.getProtocolType());
        params.put("lbAlgorithm", slbListenerProtocolModel.getLbAlgorithm());
        params.put("sessionPersistence", slbListenerProtocolModel.getSessionPersistence());
        params.put("cookieMethod", slbListenerProtocolModel.getCookieMethod());
        params.put("cookieName", slbListenerProtocolModel.getCookieName());
        params.put("serverCertificateId", slbListenerProtocolModel.getServerCertificateId());
        params.put("certificateId", slbListenerProtocolModel.getCertificateId());
        params.put("monitorProtocol", slbHealthCheckModel.getHealthCheckProtocolType());
        params.put("checkedInterval", slbHealthCheckModel.getHealthCheckIntervalTime());
        params.put("timeout", slbHealthCheckModel.getHealthCheckTimeoutTime());
        params.put("monitorUrl", slbHealthCheckModel.getHealthCheckUrl());
        params.put("receiveMessage", slbHealthCheckModel.getHealthCheckReturnValue());
        //-------------------分组信息-------------------
        HashMap<String, Object> serverGroupInfo = new HashMap<>();
        serverGroupInfo.put("globalId", IdUtil.fastSimpleUUID());
        serverGroupInfo.put("slbId", slbListenerDTO.getSlbDeviceId());
        serverGroupInfo.put("groupName", slbListenerServerGroup.getGroupName());
        serverGroupInfo.put("groupType", slbListenerServerGroup.getGroupType());
        serverGroupInfo.put("groupPriorityGroup", slbListenerServerGroup.getGroupPriorityGroup());
        List<Map<String, Object>> slbGroupVMList = new ArrayList<>();
        int num=0;
        for (SlbListenerServerGroupDTO.SlbListenerServerInfoModel slbListenerServerInfoModel : slbListenerServerGroup.getServerInfoModelList()) {
            //ip通过设备id去查询
            List<String> ips = virtualIpMapper.getBusIpListByDeviceId(slbListenerServerInfoModel.getDeviceId());
            Precondition.checkArgument(ips,"没有查询到相关云主机ip信息");
            HashMap<String, Object> slbGroupVM = new HashMap<>();
            slbGroupVM.put("globalId", IdUtil.fastSimpleUUID());
            slbGroupVM.put("slbId", slbListenerDTO.getSlbDeviceId());
            slbGroupVM.put("vmId", slbListenerServerInfoModel.getDeviceId());
            slbGroupVM.put("port", slbListenerServerInfoModel.getPort());
            slbGroupVM.put("privateIp", ips.get(0));
            if (ObjNullUtils.isNotNull(slbListenerServerGroup.getGroupPriorityGroup())) {
                if (num<slbListenerServerGroup.getGroupPriorityGroup()){
                    slbGroupVM.put("priorityGroup", "1");
                    num++;
                }else {
                    slbGroupVM.put("priorityGroup", "2");
                }
            }
            slbGroupVMList.add(slbGroupVM);
        }
        serverGroupInfo.put("slbGroupVMList", slbGroupVMList);
        //分组信息
        params.put("serverGroupInfo", serverGroupInfo);
        log.info("发送Slb监听创建请求: {}", JSON.toJSONString(params));
        //调用平台服务创建虚拟IP
        Mapper responseMapper = OkHttpsUtils.http()
                .sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("发送Slb监听创建请求结果: {}", responseMapper.toString());
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                "发送Slb监听创建请求失败: " + responseMapper.getString("message"));
        //调用成功后拿到返回的任务ID
        Mapper mapper = responseMapper.getMapper("entity");
        String taskId = mapper.getString("id");
        String status = mapper.getString("status");
        String resourceId = mapper.getString("resourceId");
        Precondition.checkArgument(!"ERROR".equals(status), "创建虚拟IP失败: " + responseMapper.getString("message"));
        TaskStatusExt taskStatusExt = new TaskStatusExt();
        taskStatusExt.setCreateStatus(status);
        taskStatusExt.setCreateTaskId(taskId);
        slbListenerDTO.setTaskStatusExt(taskStatusExt);
        slbListenerDTO.setDeviceId(resourceId);
        slbListenerDTO.setStatus("1");
        String listenerId = slbListenerManager.create(slbListenerDTO);
        //创建服务器组
        slbListenerServerGroup.setSlbListenerId(listenerId);
        slbListenerServerGroup.setTaskStatusExt(taskStatusExt);
        slbListenerServerGroup.setStatus("1");
        slbListenerServerGroupManager.create(slbListenerServerGroup);
    }



    @Override
    public void deleteSlbListener(String slbListenerId) {
        SlbListenerDTO slbListenerDTO = slbListenerManager.getById(slbListenerId);
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/slb/listener/delete";
        HashMap<String, Object> params = new HashMap<>();
        params.put("listenerId", slbListenerDTO.getDeviceId());
        params.put("slbId", slbListenerDTO.getSlbDeviceId());
        log.info("发送删除Slb监听请求: {}", JSON.toJSONString(params));
        //调用平台服务创建虚拟IP
        Mapper responseMapper = OkHttpsUtils.http()
                                            .sync(url)
                                            .bodyType(OkHttps.JSON)
                                            .setBodyPara(JSON.toJSONString(params))
                                            .post()
                                            .getBody()
                                            .toMapper();
        log.info("发送删除Slb监听请求结果: {}", responseMapper.toString());
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                "发送删除Slb监听请求失败: " + responseMapper.getString("message"));
        //调用成功后拿到返回的任务ID
        Mapper mapper = responseMapper.getMapper("entity");
        String taskId = mapper.getString("id");
        String status = mapper.getString("status");
        Precondition.checkArgument(!"ERROR".equals(status), "删除虚拟IP失败: " + responseMapper.getString("message"));
        TaskStatusExt taskStatusExt = new TaskStatusExt();
        taskStatusExt.setDeleteStatus(status);
        taskStatusExt.setDeleteTaskId(taskId);
        slbListenerDTO.setTaskStatusExt(taskStatusExt);
        slbListenerDTO.setStatus("1");
        //直接结束等待回调操作
        slbListenerManager.update(slbListenerDTO);
    }

    @Override
    public void updateSlbListener(SlbListenerDTO slbListenerDTO) {
        //查询原始更新目标
        SlbListenerDTO slbListenerDB = slbListenerManager.getById(slbListenerDTO.getId());
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/slb/listener/update";
        SlbListenerDTO.SlbListenerProtocolModel slbListenerProtocolModel = slbListenerDTO.getSlbListenerProtocolModel();
        SlbListenerDTO.SlbHealthCheckModel slbHealthCheckModel = slbListenerDTO.getSlbHealthCheckModel();
        HashMap<String, Object> params = new HashMap<>();
        params.put("listenerId",slbListenerDB.getDeviceId());
        params.put("name", slbListenerDTO.getListenerName());
        params.put("lbAlgorithm", slbListenerProtocolModel.getLbAlgorithm());
        params.put("sessionPersistence", slbListenerProtocolModel.getSessionPersistence());
        params.put("cookieMethod", slbListenerProtocolModel.getCookieMethod());
        params.put("cookieName", slbListenerProtocolModel.getCookieName());
        params.put("serverCertificateId", slbListenerProtocolModel.getServerCertificateId());
        params.put("certificateId", slbListenerProtocolModel.getCertificateId());
        params.put("monitorProtocol", slbHealthCheckModel.getHealthCheckProtocolType());
        params.put("checkedInterval", slbHealthCheckModel.getHealthCheckIntervalTime());
        params.put("timeout", slbHealthCheckModel.getHealthCheckTimeoutTime());
        params.put("monitorUrl", slbHealthCheckModel.getHealthCheckUrl());
        params.put("receiveMessage", slbHealthCheckModel.getHealthCheckReturnValue());
        log.info("发送Slb监听器更新请求: {}", JSON.toJSONString(params));
        //调用平台服务创建虚拟IP
        Mapper responseMapper = OkHttpsUtils.http()
                                            .sync(url)
                                            .bodyType(OkHttps.JSON)
                                            .setBodyPara(JSON.toJSONString(params))
                                            .post()
                                            .getBody()
                                            .toMapper();
        log.info("发送Slb监听器更新请求结果: {}", responseMapper.toString());
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                "发送Slb监听器更新请求失败: " + responseMapper.getString("message"));
        //调用成功后拿到返回的任务ID
        Mapper mapper = responseMapper.getMapper("entity");
        String taskId = mapper.getString("id");
        String status = mapper.getString("status");
        Precondition.checkArgument(!"ERROR".equals(status), "更新虚拟IP失败: " + responseMapper.getString("message"));
        TaskStatusExt taskStatusExt = new TaskStatusExt();
        taskStatusExt.setUpdateStatus(status);
        taskStatusExt.setUpdateTaskId(taskId);
        String updateSnapshot = JSON.toJSONString(slbListenerDTO);
        taskStatusExt.setUpdateSnapshot(updateSnapshot);
        slbListenerDB.setTaskStatusExt(taskStatusExt);
        slbListenerDB.setStatus("1");
        //直接结束等待回调操作
        slbListenerManager.update(slbListenerDB);
    }

}
