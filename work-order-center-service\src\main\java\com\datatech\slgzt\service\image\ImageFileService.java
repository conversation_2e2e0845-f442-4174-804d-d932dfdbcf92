package com.datatech.slgzt.service.image;

import com.datatech.slgzt.model.dto.ImageFileDTO;
import com.datatech.slgzt.model.query.ImageFileQuery;
import com.datatech.slgzt.utils.PageResult;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 11:23
 **/
public interface ImageFileService {

    Long insertImageFile(ImageFileDTO dto);

    PageResult<ImageFileDTO> page(ImageFileQuery query);

    void updateImageFile(ImageFileDTO dto);

    void deleteImageFile(Long id);
}
