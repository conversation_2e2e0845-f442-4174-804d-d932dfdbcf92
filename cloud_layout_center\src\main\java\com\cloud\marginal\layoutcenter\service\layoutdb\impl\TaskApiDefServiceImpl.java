package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.StatusEnum;
import com.cloud.marginal.layoutcenter.service.layoutdb.TaskApiDefService;
import com.cloud.marginal.mapper.layout.TaskApiDefMapper;
import com.cloud.marginal.model.entity.layout.LayoutApiExt;
import com.cloud.marginal.model.entity.layout.TaskApiDef;
import com.cloud.marginal.utils.AssertUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 编排任务与API关系配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class TaskApiDefServiceImpl extends ServiceImpl<TaskApiDefMapper, TaskApiDef> implements TaskApiDefService {

    @Resource
    private TaskApiDefMapper taskApiDefMapper;

    @Override
    public LayoutApiExt generateApi(String templateCode, String taskCode) {
        return this.baseMapper.generateApi(templateCode,taskCode);
    }
    @Override
    public TaskApiDef getSingle(String templateId, String taskId, String apiId) {
        LambdaQueryWrapper<TaskApiDef> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TaskApiDef::getTemplateId,templateId);
        wrapper.eq(TaskApiDef::getTaskId,taskId);
        wrapper.eq(TaskApiDef::getApiId,apiId);
        wrapper.eq(TaskApiDef::getStatus, StatusEnum.VALID.getCode());
        List<TaskApiDef> taskApis = taskApiDefMapper.selectList(wrapper);
        if(CollUtil.isNotEmpty(taskApis)){
            return taskApis.get(0);
        }
        return null;
    }

    @Override
    public void delete(String id) {
        LambdaUpdateWrapper<TaskApiDef> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(TaskApiDef::getStatus,StatusEnum.INVALID.getCode());
        wrapper.set(TaskApiDef::getUpdatedTime,new Date());
        wrapper.eq(TaskApiDef::getId,id);
        this.update(wrapper);
    }

    @Override
    public CecResult createTaskApiDef(TaskApiDef taskApiDef) {
        taskApiDef.setStatus(StatusEnum.VALID.getCode());

        Boolean result = this.save(taskApiDef);
        if (result) {
            return CecResult.success();
        }else{
            return CecResult.failure("create exception,please retry later");
        }

    }

    @Override
    public CecResult updateTaskApiDef(TaskApiDef taskApiDef) {
        TaskApiDef taskApiDefOld =  this.baseMapper.selectById(taskApiDef.getId());

        //todo 关联关系不能重复
        LambdaQueryWrapper<TaskApiDef> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TaskApiDef::getApiId, taskApiDef.getApiId());
        lambdaQueryWrapper.eq(TaskApiDef::getTaskId, taskApiDef.getTaskId());
        lambdaQueryWrapper.ne(TaskApiDef::getId, taskApiDefOld.getId());
        TaskApiDef taskApi =  this.baseMapper.selectOne(lambdaQueryWrapper);
        AssertUtil.isTrue(taskApi==null,"不能重复关联");

        BeanUtil.copyProperties(taskApiDef,taskApiDefOld);

        int result =  this.baseMapper.updateById(taskApiDefOld);
        if (result > 0) {
            return CecResult.success();
        }else{
            return CecResult.failure("update exception,please retry later");
        }
    }

    @Override
    public CecResult deleteTaskApiDef(String apiDefId) {
        TaskApiDef taskApiDefOld =  this.baseMapper.selectById(apiDefId);
        if (ObjectUtils.isEmpty(taskApiDefOld)){
            return CecResult.success("该关联关系已删除");
        }
        //物理删除
        int result = this.baseMapper.deleteById(apiDefId);

        if (result > 0) {
            return CecResult.success();
        }else{
            return CecResult.failure("delete exception,please retry later");
        }
    }

    @Override
    public CecPage<TaskApiDef> pageTaskApiDef(Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<TaskApiDef> lambdaQueryWrapper = Wrappers.lambdaQuery();


        Page<TaskApiDef> taskApis = this.baseMapper.selectPage(new Page(pageNum,pageSize),lambdaQueryWrapper);

        return new CecPage(taskApis.getRecords(), taskApis.getCurrent(), taskApis.getSize(), taskApis.getTotal());


    }
}
