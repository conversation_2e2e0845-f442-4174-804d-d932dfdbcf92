package com.datatech.slgzt.config;

import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngineConfiguration;
import org.activiti.engine.impl.history.HistoryLevel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Configuration
public class ActProcessEngineConfig {

    @Value("${spring.datasource.dynamic.datasource.cmp.username}")
    private String username;
    @Value("${spring.datasource.dynamic.datasource.cmp.password}")
    private String password;
    @Value("${spring.datasource.dynamic.datasource.cmp.driver-class-name}")
    private String driverClassName;
    @Value("${spring.datasource.dynamic.datasource.cmp.url}")
    private String url;

    @ConditionalOnMissingBean
    @Bean
    public ProcessEngine create() {
        return ProcessEngineConfiguration.createStandaloneInMemProcessEngineConfiguration()
                .setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE)
                .setJdbcUrl(url)
                .setJdbcDriver(driverClassName)
                .setJdbcUsername(username)
                .setJdbcPassword(password)
                .setAsyncExecutorActivate(false)
                .setHistoryLevel(HistoryLevel.AUDIT)
                .buildProcessEngine();
    }
}
