package com.datatech.slgzt.controller;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.SlbListenerServerGroupWebConvert;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.SlbListenerManager;
import com.datatech.slgzt.manager.SlbListenerServerGroupManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.SlbListenerDTO;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupPageReq;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupIdReq;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupListenerIdReq;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupCreateReq;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupUpdateReq;
import com.datatech.slgzt.model.vo.slb.SlbListenerServerGroupVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SLB监听器服务组控制器
 */
@RestController
@RequestMapping("/slbListenerServerGroup")
public class SlbListenerServerGroupController {

    @Resource
    private SlbListenerServerGroupManager slbListenerServerGroupManager;

    @Resource
    private SlbListenerManager slbListenerManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private SlbListenerServerGroupWebConvert slbListenerServerGroupWebConvert;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    @OperationLog(description = "分页查询SLB监听器服务组", operationType = "QUERY")
    public CommonResult<PageResult<SlbListenerServerGroupVO>> page(@RequestBody SlbListenerServerGroupPageReq req) {
        PageResult<SlbListenerServerGroupDTO> page = slbListenerServerGroupManager.page(slbListenerServerGroupWebConvert.convert(req));
        List<SlbListenerServerGroupDTO> records = page.getRecords();
        records.forEach(slbListenerServerGroupDTO -> {
            //设置SLB监听器的名称
            SlbListenerDTO slbListenerDTO = slbListenerManager.getById(slbListenerServerGroupDTO.getSlbListenerId());
            if (ObjNullUtils.isNotNull(slbListenerDTO)) {
                slbListenerServerGroupDTO.setSlbListenerName(slbListenerDTO.getListenerName());
            }
        });
        return CommonResult.success(PageWarppers.box(page, slbListenerServerGroupWebConvert::convert));
    }

    /**
     * 创建
     */
    @PostMapping("/create")
    @OperationLog(description = "创建SLB监听器服务组", operationType = "CREATE")
    public CommonResult<Void> create(@Validated @RequestBody SlbListenerServerGroupCreateReq req) {
        slbListenerServerGroupManager.create(slbListenerServerGroupWebConvert.convert(req));
        return CommonResult.success();
    }

    /**
     * 更新
     */
    @PostMapping("/update")
    @OperationLog(description = "更新SLB监听器服务组", operationType = "UPDATE")
    public CommonResult<Void> update(@Validated @RequestBody SlbListenerServerGroupUpdateReq req) {
        slbListenerServerGroupManager.update(slbListenerServerGroupWebConvert.convert(req));
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @OperationLog(description = "删除SLB监听器服务组", operationType = "DELETE")
    public CommonResult<Void> delete(@Validated @RequestBody SlbListenerServerGroupIdReq req) {
        slbListenerServerGroupManager.delete(req.getId());
        return CommonResult.success();
    }

    /**
     * 获取详情
     */
    @PostMapping("/detail")
    public CommonResult<SlbListenerServerGroupVO> getById(@Validated @RequestBody SlbListenerServerGroupIdReq req) {
        SlbListenerServerGroupDTO dto = slbListenerServerGroupManager.getById(req.getId());
        SlbListenerServerGroupVO vo = slbListenerServerGroupWebConvert.convert(dto);
        if (ObjNullUtils.isNotNull(vo)) {
            List<SlbListenerServerGroupVO.SlbListenerServerInfoVO> serverInfoModelList = vo.getServerInfoModelList();
            //如果不为空
            if (ObjNullUtils.isNotNull(serverInfoModelList)) {
                Iterator<SlbListenerServerGroupVO.SlbListenerServerInfoVO> iterator = serverInfoModelList.iterator();
                while (iterator.hasNext()) {
                    SlbListenerServerGroupVO.SlbListenerServerInfoVO serverInfoModel = iterator.next();
                    Long resourceDetailId = serverInfoModel.getResourceDetailId();
                    ResourceDetailDTO detailDTO = resourceDetailManager.getById(resourceDetailId);
                    if (ObjNullUtils.isNull(detailDTO)) {
                        iterator.remove();
                    } else {
                        slbListenerServerGroupWebConvert.fill(serverInfoModel, detailDTO);
                    }
                }
            }
        }
        return CommonResult.success(vo);
    }

//    /**
//     * 根据监听器ID查询列表
//     */
//    @PostMapping("/listByListenerId")
//    @OperationLog(description = "根据监听器ID查询服务组列表", operationType = "QUERY")
//    public CommonResult<List<SlbListenerServerGroupVO>> listByListenerId(@Validated @RequestBody SlbListenerServerGroupListenerIdReq req) {
//        List<SlbListenerServerGroupDTO> list = slbListenerServerGroupManager.listByListenerId(req.getListenerId());
//        return CommonResult.success(
//            list.stream()
//                .map(slbListenerServerGroupWebConvert::convert)
//                .collect(Collectors.toList())
//        );
//    }
} 