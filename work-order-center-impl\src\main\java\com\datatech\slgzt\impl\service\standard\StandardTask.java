package com.datatech.slgzt.impl.service.standard;

import com.datatech.slgzt.annotation.Lock;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.service.standard.StandardWorkOrderService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class StandardTask implements InitializingBean {

    @Resource
    private StandardWorkOrderService orderService;
    @Resource
    private StandardWorkOrderManager orderManager;
    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Autowired
    private List<StandardResOpenService> standardResOpenServiceList;

    private final Map<String, StandardResOpenService> standardResOpenServiceMap = Maps.newHashMap();

    // redis分布式锁
    @Scheduled(cron = "#{@scheduledTaskProperties.cloudPlatformCron}")
    @Lock(prefixKey = "woc", waitTime = 30000, key = "'cloudPlatformTask'")
    public void cloudPlatform() {
        log.info("cloudPlatform start");
        //
        int count = productManager.countExecutingCloudPlatform();
        if (count > 0) {
            log.info("云平台有任务正在执行，跳过");
            return;
        }
        StandardWorkOrderProductDTO productDTO = productManager.listNextCloudPlatform();
        if (productDTO == null) {
            log.info("当前没有需要执行的云平台任务");
            return;
        }

        StandardResOpenService standardResOpenService = standardResOpenServiceMap.get(productDTO.getProductType());

        StandardWorkOrderProductDTO openingUpdateDTO = new StandardWorkOrderProductDTO();
        openingUpdateDTO.setId(productDTO.getId());
        openingUpdateDTO.setCloudPlatformStatus(2);
        productManager.update(openingUpdateDTO);

        try {
            standardResOpenService.openStandardResource(productDTO);
        } catch (Exception e) {
            log.error("云平台任务执行失败, error message:{}", ExceptionUtils.getStackTrace(e));
            StandardWorkOrderProductDTO failUpdateDTO = new StandardWorkOrderProductDTO();
            failUpdateDTO.setId(productDTO.getId());
            failUpdateDTO.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
            failUpdateDTO.setMessage(e.getMessage());
            productManager.update(failUpdateDTO);
        }
        log.info("cloudPlatform end");
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (StandardResOpenService standardResOpenService : standardResOpenServiceList) {
            standardResOpenServiceMap.put(standardResOpenService.registerOpenService()
                    .getCode(), standardResOpenService);
        }
    }
}
