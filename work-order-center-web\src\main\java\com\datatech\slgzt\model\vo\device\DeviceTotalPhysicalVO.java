package com.datatech.slgzt.model.vo.device;

import com.datatech.slgzt.enums.DeviceModelTFEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 10:13
 * @description TODO
 */
@Data
@Accessors(chain = true)
public class DeviceTotalPhysicalVO implements Serializable {
    //显卡类型
    private String modelName;

    //总数
    private Integer totalCount;

    //已分配数量
    private Integer allocatedCount;

    //可用数量
    private Integer availableCount;

    //切片数 4
    private Integer spliceRatio =0;

    public Integer getSpliceCount() {
        //如果类型=T4默认给 4
        if ("T4".equals(modelName)) {
            return 4;
        }
        return spliceRatio;
    }

}