package com.datatech.slgzt.impl.service.corporate;

import cn.hutool.core.collection.ListUtil;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.DgRecoveryOrderProductManager;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;
import com.datatech.slgzt.service.corporate.DgRecoveryResourceService;
import com.datatech.slgzt.service.network.VpcMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对公vpc回收
 */
@Service
@Slf4j
public class DgRecoveryVpcServiceImpl implements DgRecoveryResourceService {

    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private VpcMessageService vpcMessageService;

    @Override
    public void recoveryResource(DgRecoveryOrderDTO dto, List<DgRecoveryOrderProductDTO> productDTOs) {
        for (DgRecoveryOrderProductDTO product : productDTOs) {
            if (ProductTypeEnum.VPC.getCode().equals(product.getProductType())) {
                String resourceDetailId = product.getResourceDetailId();
                vpcMessageService.vpcRecycle(resourceDetailId, 1L, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                productManager.updateStatusByIds(ListUtil.toList(product.getId()), RecoveryStatusEnum.RECOVERING.getType());
            }
        }
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.VPC;
    }
}
