package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月03日 18:27:05
 */
@Data
@Accessors(chain = true)
public class DgRecoveryOrderQuery {

    private Integer pageNum;

    private Integer pageSize;

    private String orderCode;

    private Boolean jobExecutionIdNull;

    private LocalDateTime createTimeStart;

    private LocalDateTime createTimeEnd;

    //创建人
    private String creator;

    /**
     * 回收类型。null-全部,0-默认，1-用户注销
     */
    private Integer recoveryType;

    private List<Long> tenantIds;
}
