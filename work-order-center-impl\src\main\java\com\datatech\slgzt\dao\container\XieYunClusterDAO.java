package com.datatech.slgzt.dao.container;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.container.XieYunClusterMapper;
import com.datatech.slgzt.dao.model.container.XieYunClusterDO;
import com.datatech.slgzt.model.query.container.XieYunClusterQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: liu<PERSON>ihan
 * @Date: 2025/4/14
 */

@Repository
public class XieYunClusterDAO {

    @Resource
    private XieYunClusterMapper mapper;

    public void insert(XieYunClusterDO clusterDO) {
        mapper.insert(clusterDO);
    }

    public XieYunClusterDO getById(String id) {
        return mapper.selectById(id);
    }

    public void update(XieYunClusterDO clusterDO) {
        mapper.updateById(clusterDO);
    }

    public List<XieYunClusterDO> list(XieYunClusterQuery query) {
        return mapper.selectList(Wrappers.<XieYunClusterDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getClusterName()), XieYunClusterDO::getClusterName, query.getClusterName())
        );
    }
}

