package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.CorporateOrderDO;
import com.datatech.slgzt.model.dto.CorporateOrderDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CorporateOrderManagerConvert {

    CorporateOrderDTO do2dto(CorporateOrderDO corporateOrder);

    CorporateOrderDO dto2do(CorporateOrderDTO corporateOrderDTO);

    List<CorporateOrderDTO> dos2DTOs(List<CorporateOrderDO> orderDOS);

    List<CorporateOrderDO> dtoList2DOs(List<CorporateOrderDTO> orderDTOS);
} 