package com.datatech.slgzt.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: 数据库用户VO
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Data
public class RdsUserVO {

    private Long id;

    /**
     * 数据库主键ID
     */
    private String rdsId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 唯一标识
     */
    private String gid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 状态（1有效 0删除）
     */
    private Integer enabled;
} 