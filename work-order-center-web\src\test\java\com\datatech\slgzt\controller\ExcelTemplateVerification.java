package com.datatech.slgzt.controller;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Excel模板验证程序
 * 用于验证数值字段设置是否正确
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ExcelTemplateVerification {

    public static void main(String[] args) {
        try {
            // 创建一个简单的Excel文件来验证数值类型设置
            createTestExcel();
            System.out.println("Excel验证文件创建成功！");
            
            // 验证GoodsController的方法逻辑
            verifyControllerLogic();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建测试Excel文件验证数值类型设置
     */
    private static void createTestExcel() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试");

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "物理机名称", "交维状态", "系统版本", "显卡类型", "显卡型号", "显卡数量",
            "CPU核心数", "内存(GB)", "硬盘(GB)", "IP地址", "申请时长", "业务系统",
            "所属云", "资源池", "开通时间", "到期时间", "申请人", "配置项编号"
        };

        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }

        // 创建数据行，模拟GoodsController的逻辑
        Row dataRow = sheet.createRow(1);

        // 文本字段
        dataRow.createCell(0).setCellValue("示例物理机名称");
        dataRow.createCell(1).setCellValue("已交维");
        dataRow.createCell(2).setCellValue("BCLinux for Euler 21.10");
        dataRow.createCell(3).setCellValue("NPU");
        dataRow.createCell(4).setCellValue("910B");

        // 数值字段 - 关键测试点
        Cell gpuNumCell = dataRow.createCell(5);
        gpuNumCell.setCellValue(8.0);
        System.out.println("显卡数量单元格类型: " + gpuNumCell.getCellType());

        Cell cpuCell = dataRow.createCell(6);
        cpuCell.setCellValue(192.0);
        System.out.println("CPU核心数单元格类型: " + cpuCell.getCellType());

        Cell memoryCell = dataRow.createCell(7);
        memoryCell.setCellValue(2048.0);
        System.out.println("内存单元格类型: " + memoryCell.getCellType());

        Cell diskCell = dataRow.createCell(8);
        diskCell.setCellValue(96960.0);
        System.out.println("硬盘单元格类型: " + diskCell.getCellType());

        // 其他文本字段
        dataRow.createCell(9).setCellValue("188.107.164.69");
        dataRow.createCell(10).setCellValue("两年");
        dataRow.createCell(11).setCellValue("网络运维AI+Paas平台");
        dataRow.createCell(12).setCellValue("平台云");
        dataRow.createCell(13).setCellValue("平台云-萧山02");
        dataRow.createCell(14).setCellValue("2024年6月21日");
        dataRow.createCell(15).setCellValue("2026年6月21日");
        dataRow.createCell(16).setCellValue("示例申请人");
        dataRow.createCell(17).setCellValue("唯一id");

        // 保存文件
        try (FileOutputStream fileOut = new FileOutputStream("test_template_verification.xlsx")) {
            workbook.write(fileOut);
        }

        workbook.close();
    }

    /**
     * 验证控制器逻辑的正确性
     */
    private static void verifyControllerLogic() {
        System.out.println("\n=== 验证GoodsController逻辑 ===");
        
        // 验证数值字段的索引是否正确
        String[] headers = {
            "物理机名称", "交维状态", "系统版本", "显卡类型", "显卡型号", "显卡数量",
            "CPU核心数", "内存(GB)", "硬盘(GB)", "IP地址", "申请时长", "业务系统",
            "所属云", "资源池", "开通时间", "到期时间", "申请人", "配置项编号"
        };

        System.out.println("表头验证:");
        for (int i = 0; i < headers.length; i++) {
            System.out.println("第" + i + "列: " + headers[i]);
        }

        System.out.println("\n数值字段索引验证:");
        System.out.println("第5列 (显卡数量): " + headers[5]);
        System.out.println("第6列 (CPU核心数): " + headers[6]);
        System.out.println("第7列 (内存(GB)): " + headers[7]);
        System.out.println("第8列 (硬盘(GB)): " + headers[8]);

        // 验证数值
        System.out.println("\n示例数值验证:");
        System.out.println("显卡数量: 8 -> " + (8.0));
        System.out.println("CPU核心数: 192 -> " + (192.0));
        System.out.println("内存(GB): 2048 -> " + (2048.0));
        System.out.println("硬盘(GB): 96960 -> " + (96960.0));

        System.out.println("\n验证完成！");
    }
}
