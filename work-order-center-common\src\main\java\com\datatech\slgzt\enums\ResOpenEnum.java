package com.datatech.slgzt.enums;

import lombok.Getter;

@Getter
public enum ResOpenEnum {
    //待开通
    WAIT_OPEN("wait_open", "待开通"),
    //开通中
    OPENING("opening", "开通中"),
    //开通成功
    OPEN_SUCCESS("open_success", "开通成功"),
    //开通失败
    OPEN_FAIL("open_fail", "开通失败"),
    OFFLINE_OPEN("offline_open", "线下开通"),


    ;

    private final String code;

    private final String message;

    ResOpenEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 适配任务中心结果
     * 1是成功
     * 0是失败
     */
    public static ResOpenEnum adaptTaskCenterResult(Integer result) {
        if (result.equals(1)) {
            return OPEN_SUCCESS;
        } else {
            return OPEN_FAIL;
        }
    }
}
