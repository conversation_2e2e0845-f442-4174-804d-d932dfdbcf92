package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.edge.CecVolumeOperationTypeEnum;
import com.cloud.marginal.enums.edge.TaskStatusEnum;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EcsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EvsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.mapper.layout.TasksRelMapper;
import com.cloud.marginal.model.dto.edge.CreateVolumeDTO;
import com.cloud.marginal.model.dto.edge.DeleteVolumeDTO;
import com.cloud.marginal.model.dto.edge.OperationVolumeDTO;
import com.cloud.marginal.model.dto.edge.VolumeResizeDTO;
import com.cloud.marginal.model.dto.layout.OperationData;
import com.cloud.marginal.model.dto.layout.Operations;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.entity.layout.LayoutTaskNode;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.model.vo.layout.RelTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 云硬盘管理接口适配
 */
@Component
@Slf4j
public class VolumeMgAdapter extends BaseNorthInterfaceAdapter {

    @Resource
    private TasksRelMapper tasksRelMapper;

    /**
     * 创建云硬盘
     */
    public TaskVO createVolume(String taskId, Integer taskSource) {
        log.info("createVolume start");
        CreateVolumeDTO createVolumeDTO = generateCreateVolumeDto(getLayoutParam(taskId));
        log.info("创建云硬盘--createVolume,params=" + JSON.toJSON(createVolumeDTO));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateVolume(),
                null,
                createVolumeDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create volume");
        return tasksVoResult.getEntity();
    }

    /**
     * 生成北向接口创建云硬盘的参数
     *
     * @param layoutParam 编排参数
     */
    private CreateVolumeDTO generateCreateVolumeDto(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam evsOrder = getEvsProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EVS_CREATE.getCode(), layoutParam.getgId());
        EvsParam evsParam = JSONObject.parseObject(evsOrder.getAttrs(), EvsParam.class);

        CreateVolumeDTO createVolumeDTO = new CreateVolumeDTO();
        createVolumeDTO.setOutInstanceId(evsParam.getOutInstanceId());
        createVolumeDTO.setRegionCode(layoutOrderParam.getRegionCode());
        createVolumeDTO.setTenantId(layoutOrderParam.getTenantId());
        createVolumeDTO.setName(evsParam.getVolumeName());
        createVolumeDTO.setVolumeSize(evsParam.getVolumeSize());
        createVolumeDTO.setVolumeType(evsParam.getVolumeType());
        createVolumeDTO.setOrderId(evsOrder.getProductOrderId());
        createVolumeDTO.setAzCode(evsParam.getAzCode());
        createVolumeDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        createVolumeDTO.setgId(evsParam.getgId());

        // 创建盘并挂载要云主机ID
        EcsParam ecsParam = getEcsParam(layoutOrderParam);
        if(ecsParam != null){
            createVolumeDTO.setVmId(ecsParam.getId());
            createVolumeDTO.setDescription("EDGE_SPECIAL_HANDLE");
        }
        //挂载云主机时，设置描述字段
        if (!StringUtils.isEmpty(evsParam.getVmId())) {
            createVolumeDTO.setVmId(evsParam.getVmId());
            createVolumeDTO.setDescription("EDGE_SPECIAL_HANDLE");
        }
        return createVolumeDTO;
    }

    /**
     * 挂载云硬盘到指定虚拟机
     */
    public TaskVO attachVolume(String taskId, Integer taskSource) {
        log.info("attachVolume start");
        // 获取关联的云硬盘任务的gid
        String gId = null;
        List<RelTaskVO> relTaskVos = tasksRelMapper.selectRelTaskByTaskId(taskId);
        for (RelTaskVO relTaskVO : relTaskVos) {
            if (ProductOrderTypeEnum.EVS_CREATE.toString().equals(relTaskVO.getTaskCode())) {
                gId = relTaskVO.getgId();
            }
        }

        OperationVolumeDTO volumeAttachParam = generateAttachVolumeDto(getLayoutParam(taskId), CecVolumeOperationTypeEnum.MOUNT.getCode(), gId);
        log.info("挂载云硬盘到指定虚拟机attachVolume start,params=" + JSON.toJSON(volumeAttachParam));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getAttachVolume(),
                null,
                volumeAttachParam,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "attach volume");
        return tasksVoResult.getEntity();
    }

    /**
     * 生成北向接口挂盘接口的参数
     *
     * @param layoutParam 编排参数
     */
    private OperationVolumeDTO generateAttachVolumeDto(LayoutParam layoutParam, String operationType, String gId) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);

        List<ProductOrderParam> productOrders = layoutOrderParam.getProductOrders();
        Boolean isEcs = false;
        Boolean isEvs = false;
        for (ProductOrderParam productOrder : productOrders) {
            if (productOrder.getProductOrderType().equals(ProductOrderTypeEnum.ECS_CREATE.toString())) {
                isEcs = true;
            }
            if (productOrder.getProductOrderType().equals(ProductOrderTypeEnum.EVS_CREATE.toString())) {
                isEvs = true;
            }
        }
        EvsParam evsParam = null;
        EcsParam ecsParam = null;
        ecsParam = getEcsParam(layoutOrderParam, isEcs, ecsParam);
        evsParam = getEvsParam(layoutOrderParam, isEvs, evsParam, gId);
        //封装挂载硬盘数据
        OperationVolumeDTO operationVolumeDTO = new OperationVolumeDTO();
        if (null != ecsParam) {
            operationVolumeDTO.setVmId(ecsParam.getId());
            //云主机创建挂载已有云硬盘
            operationVolumeDTO.setVolumeId(layoutParam.getResourceId());
        }
        if (null != evsParam) {
            if (null == operationVolumeDTO.getVmId()) {
                if (StringUtils.isEmpty(evsParam.getVmId()) && CollectionUtil.isNotEmpty(layoutOrderParam.getProductOrders())) {
                    ProductOrderParam productOrderParam = layoutOrderParam.getProductOrders().get(0);
                    if (CollectionUtil.isNotEmpty(productOrderParam.getOperations())) {
                        String optarionJson = productOrderParam.getOperations().get(0);
                        Operations operation = JSON.parseObject(optarionJson,Operations.class);
                        if (operation != null && CollectionUtil.isNotEmpty(operation.getOperationDataList())) {
                            OperationData operationData = operation.getOperationDataList().get(0);
                            operationVolumeDTO.setVmId(operationData != null ? operationData.getOperationId() : "");
                        }
                    }
                } else {
                    operationVolumeDTO.setVmId(evsParam.getVmId());
                }
            }
            operationVolumeDTO.setVolumeId(StringUtils.isEmpty(evsParam.getId()) ? evsParam.getEvsId() : evsParam.getId());
            operationVolumeDTO.setSize(evsParam.getSize());
        }
        operationVolumeDTO.setTenantId(layoutOrderParam.getTenantId());
        operationVolumeDTO.setVolumeOperationType(operationType);
        return operationVolumeDTO;
    }


    /**
     * 卸载云硬盘
     */
    public TaskVO unMountVolume(String taskId, Integer taskSource) {
        log.info("unMountVolume start");
        // 获取关联的云硬盘的gid
        LayoutTaskNode layoutTaskNode = layoutTaskNodeMapper.selectById(taskId);

        OperationVolumeDTO volumeAttachParam = generateUnMountVolumeDto(getLayoutParam(taskId), CecVolumeOperationTypeEnum.UNMOUNT.getCode(), layoutTaskNode.getgId());
        log.info("云主机卸载云硬盘，Volume start,params=：{}", JSON.toJSON(volumeAttachParam));
        //如果云主机id为空，则说明未绑定云主机，任务直接成功
        if (Objects.isNull(volumeAttachParam.getVmId())) {
            TaskVO taskVO = new TaskVO();
            taskVO.setStatus(TaskStatusEnum.SUCCESS.getCode());
            return taskVO;
        }
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getAttachVolume(),
                null,
                volumeAttachParam,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "unMountVolume volume");
        return tasksVoResult.getEntity();
    }

    /**
     * 生成北向接口挂盘接口的参数
     *
     * @param layoutParam 编排参数
     */
    private OperationVolumeDTO generateUnMountVolumeDto(LayoutParam layoutParam, String operationType, String gid) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);

        List<ProductOrderParam> productOrders = layoutOrderParam.getProductOrders();
        Boolean isEcs = false;
        Boolean isEvs = false;
        for (ProductOrderParam productOrder : productOrders) {
            if (productOrder.getProductOrderType().equals(ProductOrderTypeEnum.ECS_DELETE.toString())) {
                isEcs = true;
            }
            if (productOrder.getProductOrderType().equals(ProductOrderTypeEnum.EVS_DELETE.toString())) {
                isEvs = true;
            }
        }
        EvsParam evsParam = null;
        EcsParam ecsParam = null;
        ecsParam = getEcsUnmoutParam(layoutOrderParam, isEcs, ecsParam);
        evsParam = getEvsUnmoutParam(layoutOrderParam, isEvs, evsParam, gid);
        //封装挂载硬盘数据
        OperationVolumeDTO operationVolumeDTO = new OperationVolumeDTO();
        if (null != ecsParam) {
            operationVolumeDTO.setVmId(ecsParam.getResourceId());
        }
        if (null != evsParam) {
            operationVolumeDTO.setVmId(evsParam.getVmId());
            operationVolumeDTO.setVolumeId(evsParam.getResourceId());
        }
        operationVolumeDTO.setTenantId(layoutOrderParam.getTenantId());
        operationVolumeDTO.setVolumeOperationType(operationType);
        return operationVolumeDTO;
    }

    /**
     * 生成北向接口卸载接口的参数
     *
     * @param layoutParam 编排参数
     */
    private OperationVolumeDTO generateUnMountVolumeDtoV2(LayoutParam layoutParam, String operationType, String gid) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        List<ProductOrderParam> productOrders = layoutOrderParam.getProductOrders();
        ProductOrderParam productOrder = getProductOrder(productOrders, ProductOrderTypeEnum.EVS_DELETE.getCode());
        EvsParam evsParam = JSONObject.parseObject(productOrder.getAttrs(), EvsParam.class);
        OperationVolumeDTO operationVolumeDTO = new OperationVolumeDTO();
        operationVolumeDTO.setVmId(evsParam.getVmId());
        operationVolumeDTO.setVolumeId(evsParam.getEvsId());
        operationVolumeDTO.setTenantId(layoutOrderParam.getTenantId());
        operationVolumeDTO.setVolumeOperationType(operationType);
        return operationVolumeDTO;
    }

    private EvsParam getEvsParam(LayoutOrderParam layoutOrderParam, Boolean isEvs, EvsParam evsParam, String gId) {
        if (isEvs) {
            String evsCreateOrEvsMountCode = ProductOrderTypeEnum.EVS_CREATE.getCode() + ProductOrderTypeEnum.EVS_MOUNT.getCode();
            ProductOrderParam evsOrder = getEvsProductOrder(layoutOrderParam.getProductOrders(), evsCreateOrEvsMountCode, gId);
            evsParam = JSONObject.parseObject(evsOrder.getAttrs(), EvsParam.class);
        }
        return evsParam;
    }

    private EvsParam getEvsUnmoutParam(LayoutOrderParam layoutOrderParam, Boolean isEvs, EvsParam evsParam, String gId) {
        if (isEvs) {
            String evsCreateOrEvsMountCode = ProductOrderTypeEnum.EVS_DELETE.getCode() + ProductOrderTypeEnum.EVS_MOUNT.getCode();
            ProductOrderParam evsOrder = getEvsProductOrder(layoutOrderParam.getProductOrders(), evsCreateOrEvsMountCode, gId);
            evsParam = JSONObject.parseObject(evsOrder.getAttrs(), EvsParam.class);
        }
        return evsParam;
    }

    private EcsParam getEcsParam(LayoutOrderParam layoutOrderParam, Boolean isEcs, EcsParam ecsParam) {
        if (isEcs) {
            String evsMountOrEcsCreateCode = ProductOrderTypeEnum.ECS_CREATE.getCode();
            ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), evsMountOrEcsCreateCode);
            ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);
        }
        return ecsParam;
    }

    private EcsParam getEcsUnmoutParam(LayoutOrderParam layoutOrderParam, Boolean isEcs, EcsParam ecsParam) {
        if (isEcs) {
            String evsMountOrEcsCreateCode = ProductOrderTypeEnum.ECS_DELETE.getCode();
            ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), evsMountOrEcsCreateCode);
            ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);
        }
        return ecsParam;
    }

    private EcsParam getEcsParam(LayoutOrderParam layoutOrderParam) {
        try {
            String evsMountOrEcsCreateCode = ProductOrderTypeEnum.ECS_CREATE.getCode();
            ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), evsMountOrEcsCreateCode);
            return JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);
        }catch (Exception e){
            // 云硬盘创建时可以没有云主机创建,只打印日志
            log.warn("获取ecs参数失败:"+e);
            return null;
        }

    }

    /**
     * 变更云硬盘大小
     */
    public TaskVO modifyVolume(String taskId, Integer taskSource) {
        log.info("modifyVolume start");
        LayoutTaskNode layoutTaskNode = layoutTaskNodeMapper.selectById(taskId);
        VolumeResizeDTO volumeResizeDTO = generateModifyVolumeDto(getLayoutParam(taskId), layoutTaskNode.getgId());
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getModifyVolume(),
                null,
                volumeResizeDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult, "modify volume");
        return tasksVOResult.getEntity();
    }

    private VolumeResizeDTO generateModifyVolumeDto(LayoutParam layoutParam, String gid) {
        VolumeResizeDTO volumeResizeDTO = new VolumeResizeDTO();
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam evsOrder = getEvsProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EVS_MODIFY.getCode(), gid);
        EvsParam evsParam = JSONObject.parseObject(evsOrder.getAttrs(), EvsParam.class);

        volumeResizeDTO.setTenantId(layoutOrderParam.getTenantId());
        volumeResizeDTO.setVolumeSize(evsParam.getVolumeSize());
        volumeResizeDTO.setVolumeId(evsParam.getResourceId());
        return volumeResizeDTO;
    }


    /**
     * 删除云硬盘
     */
    public TaskVO deleteVolume(String taskId, Integer taskSource) {
        log.info("deleteVolume start");
        // 获取关联的云硬盘的gid
        LayoutTaskNode layoutTaskNode = layoutTaskNodeMapper.selectById(taskId);

        DeleteVolumeDTO deleteVolumeDTO = generateDeleteVolumeDto(getLayoutParam(taskId), layoutTaskNode.getgId());
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteVolume(),
                null,
                deleteVolumeDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult, "delete volume");
        return tasksVOResult.getEntity();
    }

    private DeleteVolumeDTO generateDeleteVolumeDto(LayoutParam layoutParam, String gid) {

        DeleteVolumeDTO deleteVolumeDTO = new DeleteVolumeDTO();
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam evsOrder = getEvsProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EVS_DELETE.getCode(), gid);
        EvsParam evsParam = JSONObject.parseObject(evsOrder.getAttrs(), EvsParam.class);

        deleteVolumeDTO.setTenantId(layoutOrderParam.getTenantId());
        deleteVolumeDTO.setVolumeId(evsParam.getResourceId());
        return deleteVolumeDTO;
    }

    /**
     * 云硬盘创建独有
     * 在产品订单集合中取出指定类型的订单
     *
     * @param productOrderParams    产品订单集合
     * @param productOrderTypeEnums 指定产品订单类型
     */
    private ProductOrderParam getEvsProductOrder(List<ProductOrderParam> productOrderParams, String productOrderTypeEnums, String gId) {
        for (ProductOrderParam productOrderParam : productOrderParams) {
            ProductOrderTypeEnum productOrderTypeEnum = ProductOrderTypeEnum.valueOf(productOrderParam.getProductOrderType());
            if (productOrderTypeEnums.contains(productOrderTypeEnum.getCode())) {
                if (StringUtils.isEmpty(gId)) {
                    return productOrderParam;
                }

                //EvsParam evsParam = JSONObject.parseObject(productOrderParam.getAttrs(), EvsParam.class);
                if (gId.equals(productOrderParam.getProductOrderId())) {
                    return productOrderParam;
                }
            }
        }
        throw new BusinessException(productOrderTypeEnums + " order lost.Provide at least one");
    }
}
