package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.ImageFileMapper;
import com.datatech.slgzt.dao.model.ImageFileDO;
import com.datatech.slgzt.model.query.ImageFileQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 11:13
 **/
@Repository
public class ImageFileDAO {

    @Resource
    private ImageFileMapper mapper;

    public void insert(ImageFileDO imageFileDO) {
        mapper.insert(imageFileDO);
    }

    public ImageFileDO getById(Long id) {
        return mapper.selectById(id);
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }

    public void updateById(ImageFileDO imageFileDO) {
        mapper.updateById(imageFileDO);
    }

    public List<ImageFileDO> list(ImageFileQuery query) {
        LambdaQueryWrapper<ImageFileDO> queryWrapper = Wrappers.<ImageFileDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getId()), ImageFileDO::getId, query.getId())
                .like(ObjNullUtils.isNotNull(query.getImageName()), ImageFileDO::getImageName, query.getImageName())
                .like(ObjNullUtils.isNotNull(query.getOsName()), ImageFileDO::getOsName, query.getOsName())
                .like(ObjNullUtils.isNotNull(query.getOsVersion()), ImageFileDO::getOsVersion, query.getOsVersion())
                .eq(ObjNullUtils.isNotNull(query.getFormat()), ImageFileDO::getFormat, query.getFormat())
                .ge(ObjNullUtils.isNotNull(query.getUploadTimeStart()), ImageFileDO::getUploadTime, query.getUploadTimeStart())
                .le(ObjNullUtils.isNotNull(query.getUploadTimeEnd()), ImageFileDO::getUploadTime, query.getUploadTimeEnd())
                .orderByDesc(ImageFileDO::getUploadTime);
        return mapper.selectList(queryWrapper);
    }

    public void updateByMd5(String md5, String preSignedObjectUrl, Boolean uploadCompleted) {
        LambdaUpdateWrapper<ImageFileDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImageFileDO::getMd5, md5);
        if (preSignedObjectUrl != null) {
            updateWrapper.set(ImageFileDO::getDownloadUrl, preSignedObjectUrl);
        }
        if (uploadCompleted != null) {
            updateWrapper.set(ImageFileDO::getUploadCompleted, uploadCompleted);
        }
        mapper.update(null, updateWrapper);
    }
}
