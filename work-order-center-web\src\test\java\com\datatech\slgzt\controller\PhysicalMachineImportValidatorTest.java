package com.datatech.slgzt.controller;

import com.datatech.slgzt.model.vo.resource.PhysicalMachineImportVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 物理机导入数据验证工具类测试
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@DisplayName("物理机导入数据验证工具类测试")
class PhysicalMachineImportValidatorTest {

    private List<String> existingDeviceIds;
    private List<String> businessSystemNames;
    private List<String> resourcePoolNames;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        existingDeviceIds = new ArrayList<>(Arrays.asList("existing-device-001", "existing-device-002"));
        businessSystemNames = new ArrayList<>(Arrays.asList("网络运维AI+Paas平台", "云资源管理平台", "数据中心管理系统"));
        resourcePoolNames = new ArrayList<>(Arrays.asList("平台云-萧山02", "创新云-杭州01", "测试云-北京01"));
    }

    @Test
    @DisplayName("测试有效数据验证 - 无错误")
    void testValidDataValidation() {
        PhysicalMachineImportVO validData = createValidPhysicalMachineData();

        List<PhysicalMachineImportValidator.ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                validData, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.isEmpty(), "有效数据不应该有验证错误");
    }

    @Test
    @DisplayName("测试必填字段验证")
    void testRequiredFieldsValidation() {
        PhysicalMachineImportVO invalidData = new PhysicalMachineImportVO();
        // 所有字段都为空

        List<PhysicalMachineImportValidator.ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidData, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertFalse(errors.isEmpty(), "空数据应该有验证错误");
        
        // 验证必填字段错误
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("物理机名称")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("显卡类型")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("显卡型号")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("数量")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("资源池")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("开通时间")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("到期时间")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("申请人")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("配置项编号")));
    }

    @Test
    @DisplayName("测试显卡类型验证")
    void testGpuCardTypeValidation() {
        PhysicalMachineImportVO invalidGpuType = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidGpuType).setGpuCardType("INVALID_GPU");

        List<PhysicalMachineImportValidator.ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidGpuType, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("显卡类型") && e.getErrorMessage().contains("只能填写NPU或GPU")));
    }

    @Test
    @DisplayName("测试数量验证")
    void testGpuNumValidation() {
        // 测试非数字
        PhysicalMachineImportVO invalidNum1 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidNum1).setGpuNum("abc");

        List<PhysicalMachineImportValidator.ValidationError> errors1 = PhysicalMachineImportValidator.validateRow(
                invalidNum1, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors1.stream().anyMatch(e -> 
                e.getFieldName().equals("数量") && e.getErrorMessage().contains("必须为正整数")));

        // 测试负数
        PhysicalMachineImportVO invalidNum2 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidNum2).setGpuNum("-5");

        List<PhysicalMachineImportValidator.ValidationError> errors2 = PhysicalMachineImportValidator.validateRow(
                invalidNum2, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors2.stream().anyMatch(e -> 
                e.getFieldName().equals("数量") && e.getErrorMessage().contains("必须为正整数")));

        // 测试零
        PhysicalMachineImportVO invalidNum3 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidNum3).setGpuNum("0");

        List<PhysicalMachineImportValidator.ValidationError> errors3 = PhysicalMachineImportValidator.validateRow(
                invalidNum3, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors3.stream().anyMatch(e -> 
                e.getFieldName().equals("数量") && e.getErrorMessage().contains("必须为正整数")));
    }

    @Test
    @DisplayName("测试IP地址格式验证")
    void testIpAddressValidation() {
        // 测试无效IPv4
        PhysicalMachineImportVO invalidIp1 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidIp1).setIp("999.999.999.999");

        List<PhysicalMachineImportValidator.ValidationError> errors1 = PhysicalMachineImportValidator.validateRow(
                invalidIp1, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors1.stream().anyMatch(e -> 
                e.getFieldName().equals("IP地址") && e.getErrorMessage().contains("格式不正确")));

        // 测试有效IPv4
        PhysicalMachineImportVO validIp = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) validIp).setIp("*************");

        List<PhysicalMachineImportValidator.ValidationError> errors2 = PhysicalMachineImportValidator.validateRow(
                validIp, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertFalse(errors2.stream().anyMatch(e -> e.getFieldName().equals("IP地址")));
    }

    @Test
    @DisplayName("测试日期格式验证")
    void testDateFormatValidation() {
        // 测试无效日期格式
        PhysicalMachineImportVO invalidDate = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidDate).setResourceApplyTime("2024-06-21");

        List<PhysicalMachineImportValidator.ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidDate, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("开通时间") && e.getErrorMessage().contains("格式不正确")));
    }

    @Test
    @DisplayName("测试设备ID唯一性验证")
    void testDeviceIdUniquenessValidation() {
        PhysicalMachineImportVO duplicateDevice = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) duplicateDevice).setDeviceId("existing-device-001");

        List<PhysicalMachineImportValidator.ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                duplicateDevice, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("配置项编号") && e.getErrorMessage().contains("已存在")));
    }

    @Test
    @DisplayName("测试业务系统存在性验证")
    void testBusinessSystemExistsValidation() {
        PhysicalMachineImportVO invalidBusiness = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidBusiness).setBusinessSysName("不存在的业务系统");

        List<PhysicalMachineImportValidator.ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidBusiness, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("业务系统") && e.getErrorMessage().contains("不存在")));
    }

    @Test
    @DisplayName("测试资源池存在性验证")
    void testResourcePoolExistsValidation() {
        PhysicalMachineImportVO invalidPool = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidPool).setResourcePoolName("不存在的资源池");

        List<PhysicalMachineImportValidator.ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidPool, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("资源池") && e.getErrorMessage().contains("不存在")));
    }

    @Test
    @DisplayName("测试日期解析工具方法")
    void testParseDateMethod() {
        // 测试有效日期
        LocalDateTime validDate = PhysicalMachineImportValidator.parseDate("2024年6月21日");
        assertNotNull(validDate);
        assertEquals(2024, validDate.getYear());
        assertEquals(6, validDate.getMonthValue());
        assertEquals(21, validDate.getDayOfMonth());

        // 测试无效日期
        LocalDateTime invalidDate = PhysicalMachineImportValidator.parseDate("invalid-date");
        assertNull(invalidDate);

        // 测试空字符串
        LocalDateTime emptyDate = PhysicalMachineImportValidator.parseDate("");
        assertNull(emptyDate);

        // 测试null
        LocalDateTime nullDate = PhysicalMachineImportValidator.parseDate(null);
        assertNull(nullDate);
    }

    @Test
    @DisplayName("测试硬盘字段验证")
    void testDataDiskValidation() {
        // 测试有效的纯数值
        PhysicalMachineImportVO validDisk1 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) validDisk1).setDataDisk("96960");

        List<PhysicalMachineImportValidator.ValidationError> errors1 = PhysicalMachineImportValidator.validateRow(
                validDisk1, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertFalse(errors1.stream().anyMatch(e -> e.getFieldName().equals("硬盘(GB)")));

        // 测试有效的小数
        PhysicalMachineImportVO validDisk2 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) validDisk2).setDataDisk("1024.5");

        List<PhysicalMachineImportValidator.ValidationError> errors2 = PhysicalMachineImportValidator.validateRow(
                validDisk2, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertFalse(errors2.stream().anyMatch(e -> e.getFieldName().equals("硬盘(GB)")));

        // 测试包含GB单位的错误情况
        PhysicalMachineImportVO invalidDisk1 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidDisk1).setDataDisk("96960GB");

        List<PhysicalMachineImportValidator.ValidationError> errors3 = PhysicalMachineImportValidator.validateRow(
                invalidDisk1, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors3.stream().anyMatch(e ->
                e.getFieldName().equals("硬盘(GB)") && e.getErrorMessage().contains("不要包含单位")));

        // 测试包含TB单位的错误情况
        PhysicalMachineImportVO invalidDisk2 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidDisk2).setDataDisk("1TB");

        List<PhysicalMachineImportValidator.ValidationError> errors4 = PhysicalMachineImportValidator.validateRow(
                invalidDisk2, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors4.stream().anyMatch(e ->
                e.getFieldName().equals("硬盘(GB)") && e.getErrorMessage().contains("不要包含单位")));

        // 测试非数字格式
        PhysicalMachineImportVO invalidDisk3 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidDisk3).setDataDisk("abc");

        List<PhysicalMachineImportValidator.ValidationError> errors5 = PhysicalMachineImportValidator.validateRow(
                invalidDisk3, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors5.stream().anyMatch(e ->
                e.getFieldName().equals("硬盘(GB)") && e.getErrorMessage().contains("格式不正确")));

        // 测试负数
        PhysicalMachineImportVO invalidDisk4 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidDisk4).setDataDisk("-1000");

        List<PhysicalMachineImportValidator.ValidationError> errors6 = PhysicalMachineImportValidator.validateRow(
                invalidDisk4, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors6.stream().anyMatch(e ->
                e.getFieldName().equals("硬盘(GB)") && e.getErrorMessage().contains("必须为正数")));

        // 测试零
        PhysicalMachineImportVO invalidDisk5 = createValidPhysicalMachineData();
        ((PhysicalMachineImportVO) invalidDisk5).setDataDisk("0");

        List<PhysicalMachineImportValidator.ValidationError> errors7 = PhysicalMachineImportValidator.validateRow(
                invalidDisk5, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors7.stream().anyMatch(e ->
                e.getFieldName().equals("硬盘(GB)") && e.getErrorMessage().contains("必须为正数")));
    }

    @Test
    @DisplayName("测试硬盘格式化工具方法")
    void testFormatDataDiskMethod() {
        // 测试正常数值格式化
        String formatted1 = PhysicalMachineImportValidator.formatDataDisk("96960");
        assertEquals("96960GB", formatted1);

        // 测试小数格式化
        String formatted2 = PhysicalMachineImportValidator.formatDataDisk("1024.5");
        assertEquals("1024.5GB", formatted2);

        // 测试已包含单位的情况
        String formatted3 = PhysicalMachineImportValidator.formatDataDisk("96960GB");
        assertEquals("96960GB", formatted3);

        String formatted4 = PhysicalMachineImportValidator.formatDataDisk("1TB");
        assertEquals("1TB", formatted4);

        // 测试空值
        String formatted5 = PhysicalMachineImportValidator.formatDataDisk("");
        assertNull(formatted5);

        String formatted6 = PhysicalMachineImportValidator.formatDataDisk(null);
        assertNull(formatted6);

        // 测试无效数值（应该返回原值）
        String formatted7 = PhysicalMachineImportValidator.formatDataDisk("abc");
        assertEquals("abc", formatted7);
    }

    @Test
    @DisplayName("测试CPU和内存组合工具方法")
    void testCombineSpecMethod() {
        // 测试正常组合
        String spec1 = PhysicalMachineImportValidator.combineSpec("192", "2048");
        assertEquals("192|2048", spec1);

        // 测试部分为空
        String spec2 = PhysicalMachineImportValidator.combineSpec("192", "");
        assertEquals("192|", spec2);

        String spec3 = PhysicalMachineImportValidator.combineSpec("", "2048");
        assertEquals("|2048", spec3);

        // 测试全部为空
        String spec4 = PhysicalMachineImportValidator.combineSpec("", "");
        assertNull(spec4);

        // 测试null
        String spec5 = PhysicalMachineImportValidator.combineSpec(null, null);
        assertNull(spec5);
    }

    /**
     * 创建有效的物理机数据用于测试
     */
    private PhysicalMachineImportVO createValidPhysicalMachineData() {
        PhysicalMachineImportVO data = new PhysicalMachineImportVO();
        data.setDeviceName("TEST-PM-001");
        data.setHandoverStatus("已交维");
        data.setOsVersion("CentOS 7.6");
        data.setGpuCardType("NPU");
        data.setGpuType("910B");
        data.setGpuNum("8");
        data.setCpu("192");
        data.setMemory("2048");
        data.setDataDisk("96960");
        data.setIp("*************");
        data.setApplyTime("两年");
        data.setBusinessSysName("网络运维AI+Paas平台");
        data.setCloudPlatform("平台云");
        data.setResourcePoolName("平台云-萧山02");
        data.setResourceApplyTime("2024年6月21日");
        data.setExpireTime("2026年6月21日");
        data.setApplyUserName("测试用户");
        data.setDeviceId("test-device-001");
        return data;
    }
}
