package com.datatech.slgzt.impl.service.recovery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.layout.ResRecoveryReqModel;
import com.datatech.slgzt.model.recovery.RecoveryNasModel;
import com.datatech.slgzt.service.recovery.RecoveryResourceService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: vpn回收
 * @author: LK
 * @create: 2025-06-10 17:10
 **/
@Slf4j
@Service
public class RecoveryNasServiceImpl implements RecoveryResourceService {

    @Resource
    private RecoveryWorkOrderProductManager manager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void recoveryResource(RecoveryWorkOrderDTO dto, List<RecoveryWorkOrderProductDTO> recoveryWorkOrderProducts) {
        for (RecoveryWorkOrderProductDTO product : recoveryWorkOrderProducts) {
            ResRecoveryReqModel resRecoveryReqModel = new ResRecoveryReqModel();
            //参数封装
            List<ResRecoveryReqModel.ProductOrder> reqProductList = Lists.newArrayList();
            if (ProductTypeEnum.NAS.getCode().equals(product.getProductType())) {
                RecoveryNasModel recoveryNasModel = JSONObject.parseObject(product.getPropertySnapshot(), RecoveryNasModel.class);
                //基础参数封装
                baseParamInit(recoveryNasModel, resRecoveryReqModel, product, dto);
                //nas参数填充
                ResRecoveryReqModel.ProductOrder nasProductOrder = new ResRecoveryReqModel.ProductOrder();
                nasProductOrder.setProductOrderId(recoveryNasModel.getProductOrderId().toString());
                nasProductOrder.setProductOrderType("NAS_DELETE");
                nasProductOrder.setProductType(ProductTypeEnum.NAS.getCode());
                nasProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                ResRecoveryReqModel.Attrs nasAttrs = new ResRecoveryReqModel.Attrs();
                nasAttrs.setResourceId(recoveryNasModel.getResourceId());
                nasProductOrder.setAttrs(nasAttrs);
                reqProductList.add(nasProductOrder);
            }
            //调用任务中心回收资源
            resRecoveryReqModel.setProductOrders(reqProductList);
            log.info("资源回收，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={},param={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl, JSON.toJSONString(resRecoveryReqModel));
            Mapper dataMapper= OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(resRecoveryReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "资源回收失败，callLayoutOrder--编排中心初始化返回结果失败");
            log.info("资源回收，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
        }
        //把对应的产品都改成回收中状态
        List<Long> ids = recoveryWorkOrderProducts.stream().map(RecoveryWorkOrderProductDTO::getId).collect(Collectors.toList());
        manager.updateStatusByIds(ids, String.valueOf(RecoveryStatusEnum.RECOVERING.getType()));
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.NAS;
    }

    private void baseParamInit(RecoveryNasModel recoveryVpnModel,
                               ResRecoveryReqModel resRecoveryReqModel,
                               RecoveryWorkOrderProductDTO product,
                               RecoveryWorkOrderDTO dto) {
        //设置计费号
        resRecoveryReqModel.setAccount(recoveryVpnModel.getBillId());
        //设置业务code;
        resRecoveryReqModel.setSourceExtType(OrderTypeEnum.RECOVERY.getCode());
        //设置业务code
        resRecoveryReqModel.setBusinessCode("NAS_DELETE");
        //设置业务系统code
        resRecoveryReqModel.setBusinessSystemCode(dto.getBusinessSystemCode());
        //设置客户id
        resRecoveryReqModel.setCustomId(recoveryVpnModel.getCustomNo());
        //设置区域编码
        resRecoveryReqModel.setRegionCode(recoveryVpnModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resRecoveryReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resRecoveryReqModel.setTenantId(dto.getTenantId());
        //设置userId
        resRecoveryReqModel.setUserId(dto.getCreatedBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resRecoveryReqModel.setTaskSource(4);
    }
}
