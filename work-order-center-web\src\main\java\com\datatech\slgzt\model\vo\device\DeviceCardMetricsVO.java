package com.datatech.slgzt.model.vo.device;

import lombok.Data;

import java.io.Serializable;

/**
 * @Desc 显卡指标转换器
 * <AUTHOR>
 * @DATA 2025-06-12
 */
@Data
public class DeviceCardMetricsVO implements Serializable {

    /**
     * GPU/NPU卡序列号/uuid
     */
    private String deviceId;

    /**
     * 算力利用率
     */
    private Double gpuUtilPercent;

    /**
     * 显存利用率
     */
    private Double memUtilpercent;

    /**
     * 显存大小 显存大小（GB）
     */
    private Integer memoryUsage;

    /**
     * 算力能耗top
     */
    private Double devPowerUsage;

    /**
     * gpu温度
     */
    private Double devGpuTemp;


    private String  gpuTime;

    private String  deviceType;


}
