package com.datatech.slgzt.model.query;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库用户查询对象
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Data
public class RdsUserQuery {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID列表
     */
    private List<Long> idList;

    /**
     * 数据库主键ID
     */
    private String rdsId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 唯一标识
     */
    private String gid;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态（1有效 0删除）
     */
    private Integer enabled;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;
} 