package com.datatech.slgzt.impl.service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum;
import com.datatech.slgzt.manager.ChangeWorkOrderManager;
import com.datatech.slgzt.manager.RecoveryWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.model.bpmn.BaseWorkOrder;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.query.ChangeWorkOrderQuery;
import com.datatech.slgzt.model.query.RecoveryWorkOrderQuery;
import com.datatech.slgzt.model.query.StandardWorkOrderQuery;
import com.datatech.slgzt.model.sms.SmsSendModel;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.producer.SmsProducer;

import lombok.extern.slf4j.Slf4j;

/**
 * 标准/变更/回收工单 租户确认 自动化服务实现类
 * 主要功能：
 * 1. 定时检查处于租户确认环节的工单
 * 2. 对于超过两天未确认的工单，发送短信提醒用户和资源组
 *
 * <AUTHOR>
 * @date 2025年05月19日 10:00:00
 */
@Service
@Slf4j
public class TenantConfirmWorkOrderServiceImpl {

    /**
     * Redis键前缀，用于存储工单短信发送记录
     */
    private static final String TENANT_CONFIRM_SMS_KEY_PREFIX = "tenant_confirm_work_order:sms:";

    /**
     * 短信提醒间隔时间（天）
     */
    private static final int SMS_REMINDER_INTERVAL_DAYS = 2;

    /**
     * redis短信提醒间隔时间（小时）
     */
    private static final int SMS_REMINDER_REDIS_INTERVAL_HOURS = 40;

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;
    @Resource
    private ChangeWorkOrderManager changeWorkOrderManager;
    @Resource
    private RecoveryWorkOrderManager recoveryWorkOrderManager;

    @Resource
    private UserService userService;

    @Resource
    private SmsProducer smsProducer;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 定时任务，每天凌晨1点执行
     * 检查处于租户确认环节的工单，对于超过两天未确认的工单，发送短信提醒
     */
    // @Scheduled(cron = "0 0 1 * * ?")
    @Scheduled(cron = "0 0 9 * * ?")
    public void checkTenantConfirmWorkOrders() {
        log.info("开始执行租户确认工单检查任务");

        checkStandardWorkOrder();
        checkChangeWorkOrder();
        checkRecoveryWorkOrder();

        log.info("租户确认工单检查任务执行完成");
    }

    private void checkStandardWorkOrder() {
        log.info("开始执行标准工单检查任务");
        try {
            // 查询所有处于租户确认环节的工单
            StandardWorkOrderQuery query = new StandardWorkOrderQuery();
            query.setCurrentNodeCode(ActivitiStatusEnum.TENANT_TASK.getNode());
            List<StandardWorkOrderDTO> workOrders = standardWorkOrderManager.list(query);

            if (CollectionUtils.isEmpty(workOrders)) {
                log.info("没有处于租户确认环节的工单");
                return;
            }

            log.info("找到{}个处于租户确认环节的工单", workOrders.size());

            // 遍历工单，检查是否需要发送提醒短信
            for (StandardWorkOrderDTO workOrder : workOrders) {
                processWorkOrder(workOrder);
            }
        } catch (Exception e) {
            log.error("租户确认工单检查任务执行异常", e);
        }
        log.info("标准工单检查任务执行完成");
    }

    private void checkChangeWorkOrder() {
        log.info("开始执行变更工单检查任务");
        try {
            // 查询所有处于租户确认环节的工单
            ChangeWorkOrderQuery query = new ChangeWorkOrderQuery();
            query.setCurrentNodeCode(ActivitiStatusEnum.TENANT_TASK.getNode());
            List<ChangeWorkOrderDTO> workOrders = changeWorkOrderManager.list(query);

            if (CollectionUtils.isEmpty(workOrders)) {
                log.info("没有处于租户确认环节的工单");
                return;
            }

            log.info("找到{}个处于租户确认环节的工单", workOrders.size());

            // 遍历工单，检查是否需要发送提醒短信
            for (ChangeWorkOrderDTO workOrder : workOrders) {
                processWorkOrder(workOrder);
            }
        } catch (Exception e) {
            log.error("租户确认工单检查任务执行异常", e);
        }
        log.info("变更工单检查任务执行完成");
    }

    private void checkRecoveryWorkOrder() {
        log.info("开始执行回收工单检查任务");
        try {
            // 查询所有处于租户确认环节的工单
            RecoveryWorkOrderQuery query = new RecoveryWorkOrderQuery();
            query.setCurrentNodeCode(ActivitiStatusEnum.TENANT_TASK.getNode());
            List<RecoveryWorkOrderDTO> workOrders = recoveryWorkOrderManager.list(query);

            if (CollectionUtils.isEmpty(workOrders)) {
                log.info("没有处于租户确认环节的工单");
                return;
            }

            log.info("找到{}个处于租户确认环节的工单", workOrders.size());

            // 遍历工单，检查是否需要发送提醒短信
            for (RecoveryWorkOrderDTO workOrder : workOrders) {
                processWorkOrder(workOrder);
            }
        } catch (Exception e) {
            log.error("租户确认工单检查任务执行异常", e);
        }
        log.info("回收工单检查任务执行完成");
    }

    /**
     * 处理单个工单，检查是否需要发送提醒短信
     *
     * @param workOrder 工单信息
     */
    private void processWorkOrder(BaseWorkOrder workOrder) {
        String workOrderId = workOrder.getId();
        LocalDateTime currentNodeStartTime = workOrder.getCurrentNodeStartTime();

        if (currentNodeStartTime == null) {
            log.warn("工单[{}]的当前节点开始时间为空", workOrderId);
            return;
        }

        // 检查是否已经发送过短信
        String redisKey = TENANT_CONFIRM_SMS_KEY_PREFIX + workOrderId;
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        String lastSentTimeStr = bucket.get();

        // 判断是否需要发送短信
        boolean needSendSms = false;
        if (lastSentTimeStr == null) {
            // 计算工单在当前节点的停留时间（天）
            long daysInCurrentNode = ChronoUnit.DAYS.between(currentNodeStartTime, LocalDateTime.now());
            // 从未发送过短信，且工单在当前节点停留超过2天
            needSendSms = daysInCurrentNode >= SMS_REMINDER_INTERVAL_DAYS;
        } else {
            // 已发送过短信，检查上次发送时间
            LocalDateTime lastSentTime = LocalDateTime.parse(lastSentTimeStr);
            long hoursSinceLastSent = ChronoUnit.HOURS.between(lastSentTime, LocalDateTime.now());
            // 距离上次发送已超过40小时
            needSendSms = hoursSinceLastSent >= SMS_REMINDER_REDIS_INTERVAL_HOURS;
        }

        if (needSendSms) {
            sendReminderSms(workOrder);
            // 更新Redis记录
            bucket.set(LocalDateTime.now().toString(), 7, TimeUnit.DAYS);
            log.info("工单[{}]发送租户确认提醒短信成功，更新Redis记录", workOrderId);
        } else {
            log.debug("工单[{}]不需要发送租户确认提醒短信", workOrderId);
        }
    }

    /**
     * 发送租户确认提醒短信
     *
     * @param workOrder 工单信息
     */
    private void sendReminderSms(BaseWorkOrder workOrder) {
        try {
            Long creatorId = workOrder.getCreatedBy();
            if (creatorId == null) {
                log.warn("工单[{}]的创建人ID为空", workOrder.getId());
            }
            // 发送短信给工单创建人
            SmsSendModel smsSendModel = new SmsSendModel();
            smsSendModel.setUserId(creatorId);
            smsSendModel.setOrderType("tenant_confirm_reminder");
            smsSendModel.setOrderCode(workOrder.getOrderCode());
            smsSendModel.setOrderTitle(workOrder.getOrderTitle());
            smsSendModel.setCurrentNode(ActivitiStatusEnum.TENANT_TASK.getNode());
            smsSendModel.setCurrentNodeName(ActivitiStatusEnum.TENANT_TASK.getNodeRemark());
            if(workOrder instanceof StandardWorkOrderDTO){
                smsSendModel.setOrderTypeCn(OrderTypeEnum.SUBSCRIBE.getMessage());
            }else if(workOrder instanceof ChangeWorkOrderDTO){
                smsSendModel.setOrderTypeCn(OrderTypeEnum.CHANGE.getMessage());
            }else if(workOrder instanceof RecoveryWorkOrderDTO){
                smsSendModel.setOrderTypeCn(OrderTypeEnum.RECOVERY.getMessage());
            }
            smsProducer.sendMessage(workOrder.getId(), smsSendModel);

            // 发送短信给资源组
            smsSendModel.setOrderType("tenant_confirm_reminder_resource");
            smsSendModel.setUserId(null);
            smsSendModel.setRoles(Collections.singletonList(AuthorityCodeEnum.OPERATION_GROUP.code()));
            smsProducer.sendMessage(workOrder.getId(), smsSendModel);

            log.info("工单[{}]发送租户确认提醒短信成功", workOrder.getId());
        } catch (Exception e) {
            log.error("工单[{}]发送租户确认提醒短信失败", workOrder.getId(), e);
        }
    }
}
