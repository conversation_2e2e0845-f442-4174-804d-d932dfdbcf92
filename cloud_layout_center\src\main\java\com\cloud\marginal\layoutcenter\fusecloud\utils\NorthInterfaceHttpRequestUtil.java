package com.cloud.marginal.layoutcenter.fusecloud.utils;

import com.alibaba.fastjson.TypeReference;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.utils.AbstractHttpRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;

import org.springframework.stereotype.Component;


import java.util.Map;
import java.util.function.Consumer;

/**
 * 北向http请求工具
 */
@Component
@Slf4j
public class NorthInterfaceHttpRequestUtil extends AbstractHttpRequestUtils {

    @Value("${http.addresses.resourcecenter}")
    private String resourceCenterAddress;

    /**
     * 通用post请求（请求体为json数据）
     * @param url 请求地址
     * @param headerConsumer 自定义请求头
     * @param jsonData json数据
     * @param resultClass 请求结果返回类型
     * @param pathParam 路径参数
     * @param <T>
     * @return
     */
    public <T> T postJson(String url,
                             Consumer<Map<String, String>> headerConsumer,
                             Object jsonData,
                             TypeReference<T> resultClass,
                             Map<String, Object> pathParam) {
        return this.postJson(url, headerConsumer, jsonData, resultClass, pathParam,
                requestException -> {
                    this.startRequestException(requestException);
                }, (resultCode, result) -> {
                    this.requestResponseCodeIsNo2xx(resultCode, result);
                });
    }



    /**
     * 通用postForm数据请求
     * @param url 请求地址
     * @param headerConsumer 自定义请求头
     * @param formData 请求表单数据
     * @param resultClass 请求结果返回类型
     * @param pathParam 路径参数
     * @param <T>
     * @return
     */
    public <T> T postForm(String url,
                             Consumer<Map<String, String>> headerConsumer,
                             Map<String, Object> formData,
                             TypeReference<T> resultClass,
                             Map<String, Object> pathParam) {
        return this.postForm(url,headerConsumer,formData,resultClass,pathParam,
                requestException -> {
                    this.startRequestException(requestException);
                }, (resultCode, result) -> {
                    this.requestResponseCodeIsNo2xx(resultCode, result);
                });
    }

    /**
     * 通用get请求
     * @param url 请求地址
     * @param headerConsumer 自定义请求头
     * @param resultClass 请求结果返回类型
     * @param pathParam 路径参数
     * @param <T>
     * @return
     */
    public <T> T get(String url,
                        Consumer<Map<String, String>> headerConsumer,
                        TypeReference<T> resultClass,
                        Map<String, Object> pathParam) {
        return this.get(url, headerConsumer , resultClass, pathParam,
                requestException -> {
                    this.startRequestException(requestException);
                }, (resultCode, result) -> {
                    this.requestResponseCodeIsNo2xx(resultCode, result);
                });
    }

    /**
     * 通用delete请求
     * @param url 请求地址
     * @param headerConsumer 自定义请求头
     * @param formData 请求表单数据
     * @param resultClass 请求结果返回类型
     * @param pathParam 路径参数
     * @param <T>
     * @return
     */
    public <T> T delete(String url,
                           Consumer<Map<String, String>> headerConsumer,
                           Map<String, Object> formData,
                           TypeReference<T> resultClass,
                           Map<String, Object> pathParam) {
        return this.delete(url, headerConsumer, formData, resultClass, pathParam,
                requestException -> {
                    this.startRequestException(requestException);
                }, (resultCode, result) -> {
                    this.requestResponseCodeIsNo2xx(resultCode, result);
                });
    }

    /**
     * 通用delete请求（参数体为json数据）
     * @param url 请求地址
     * @param headerConsumer 自定义请求头
     * @param jsonData json数据
     * @param resultClass 请求结果返回类型
     * @param pathParam 路径参数
     * @param <T>
     * @return
     */
    public <T> T deleteJson(String url,
                               Consumer<Map<String, String>> headerConsumer,
                               Object jsonData,
                               TypeReference<T> resultClass,
                               Map<String, Object> pathParam) {
        return this.deleteJson(url, headerConsumer, jsonData ,resultClass, pathParam,
                requestException -> {
                    this.startRequestException(requestException);
                }, (resultCode, result) -> {
                    this.requestResponseCodeIsNo2xx(resultCode, result);
                });
    }

    @Override
    protected void appendCommonHeader(HttpHeaders httpHeaders) {

    }

    @Override
    protected String getHostAddress() {
        return resourceCenterAddress;
    }

    /**
     * 发起http请求响应码不是2xx（xx是其他数字），的处理
     * @param resultCode 响应码
     * @param result 结果字符串
     */
    private void requestResponseCodeIsNo2xx(Integer resultCode, String result) {
        log.error(String.format("接口调用失败。请求响应码：%d。返回结果字符串：%s",resultCode,result));
        throw new BusinessException(String.format("接口调用失败，响应码：%s,返回结果：%s",resultCode,result));
    }

    /**
     * 发起请求出现异常
     * @param requestException
     */
    private void startRequestException(Exception requestException) {
        log.error("接口调用异常", requestException);
        BusinessException businessException = new BusinessException(requestException.getMessage());
        businessException.setStackTrace(requestException.getStackTrace());
        throw businessException;
    }
}
