package com.datatech.slgzt.model.vo.image;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 16:41
 **/
@Data
public class ImageFileVO {

    private Long id;

    private String imageName;

    private String osName;

    private String osVersion;

    private String size;

    private String format;

    private LocalDateTime uploadTime;

    private Boolean uploadCompleted;

    private int progress;

    private String md5;

    private String downloadUrl;

    private int totalParts;
}
