package com.datatech.slgzt.dao.model.container;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@TableName("XIE_YUN_ORG")
public class XieYunOrgDO {

    @TableField("ID")
    private String id;

    @TableField("XIE_YUN_ORG_ID")
    private String xieYunOrgId;

    /**
     * 组织编码
     */
    @TableField("CODE")
    private String code;

    /**
     * 组织名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 用户id
     */
    @TableField("XIE_YUN_USER_ID")
    private String xieYunUserId;

    /**
     * 谐云仓库id，修改时插入
     */
    @TableField("XIE_YUN_REPO_ID")
    private String xieYunRepoId;

    /**
     * 谐云制品id，修改时插入
     */
    @TableField("XIE_YUN_REGISTRY_ID")
    private String xieYunRegistryId;

    /**
     * 集群名称
     */
    @TableField("CLUSTER_NAME")
    private String clusterName;

    /**
     * 节点名称
     */
    @TableField("NODE_POOL_NAME")
    private String nodePoolName;

    /**
     * cpu值
     */
    @TableField("CPU_VALUE")
    private String cpuValue;

    /**
     * 内存值
     */
    @TableField("MEMORY_VALUE")
    private String memoryValue;

    /**
     * 资源数据
     */
    @TableField("XIE_YUN_ORG_QUOTA_OPM_LIST")
    private String xieYunOrgQuotaOpmList;

    /**
     * 描述
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;

    /**
     * 1：删除,0：正常
     */
    @TableField("DELETED")
    private Boolean deleted;

}

