package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.RdsWhiteDTO;
import com.datatech.slgzt.model.query.RdsWhiteQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单Manager接口
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
public interface RdsWhiteManager {

    /**
     * 分页查询数据库白名单
     */
    PageResult<RdsWhiteDTO> page(RdsWhiteQuery query);

    /**
     * 查询数据库白名单列表
     */
    List<RdsWhiteDTO> list(RdsWhiteQuery query);

    /**
     * 新增数据库白名单
     */
    void add(RdsWhiteDTO rdsWhiteDTO);

    /**
     * 更新数据库白名单
     */
    void update(RdsWhiteDTO rdsWhiteDTO);

    /**
     * 删除数据库白名单
     */
    void delete(Long id);

    /**
     * 根据ID查询数据库白名单
     */
    RdsWhiteDTO getById(Long id);
} 