package com.datatech.slgzt.impl.cloudPort;


import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.cloudPort.IWocCloudPortManager;
import com.datatech.slgzt.convert.CloudPortManagerConvert;
import com.datatech.slgzt.dao.WocCloudPortDAO;
import com.datatech.slgzt.dao.model.SlbCertificateDO;
import com.datatech.slgzt.dao.model.cloudPort.WocCloudPortDO;
import com.datatech.slgzt.enums.BusinessExceptionEnum;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.manager.VpcOrderManager;
import com.datatech.slgzt.model.TaskStatusExt;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.dto.cloudPort.CloudPortDTO;
import com.datatech.slgzt.model.query.WocCloudPortQuery;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-31
 */
@Slf4j
@Service
public class WocCloudPortServiceImpl implements IWocCloudPortManager {



    @Resource
    private WocCloudPortDAO wocCloudPortDAO;

    @Resource
    private CloudPortManagerConvert cloudPortManagerConvert;

    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;
    @Resource
    private VpcOrderManager vpcOrderManager;


    @Resource
    private PlatformService platformService;

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;


    private static final String CLOULD_PORT_INIT_STATUS = "1";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(CloudPortDTO cloudPortDTO) {
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/cloudPort/create";

        VpcOrderExtDTO vpcOrderExtDTO = vpcOrderManager.getById(cloudPortDTO.getVpcId());
        String  billNo = queryBillId(vpcOrderExtDTO);
        String businessSysName = vpcOrderExtDTO.getBusinessSysName();
        HashMap<String, Object> params = new HashMap<>();
        params.put("regionCode", cloudPortDTO.getRegionCode());
        params.put("billId", billNo);
        //可用区编码
        params.put("azCode", cloudPortDTO.getAzCode());
        params.put("vpcId", cloudPortDTO.getVpcId());
        params.put("vlanId", cloudPortDTO.getVlanId());
        //云区域id
        params.put("peerIp", cloudPortDTO.getPeerIp());
        params.put("srcIp", cloudPortDTO.getSrcIp());
        params.put("peerPassword", cloudPortDTO.getPeerPassword());
        params.put("name", cloudPortDTO.getCloudPortName());
        params.put("peerAsNumber", StringUtils.isNotBlank(cloudPortDTO.getPeerAsNumber())?cloudPortDTO.getPeerAsNumber():"");
        params.put("remoteCidr", StringUtils.isNotBlank(cloudPortDTO.getRemoteCidr())?cloudPortDTO.getRemoteCidr():"");
        log.info("发送云端口创建参数: {}",params);
        Mapper responseMapper = OkHttpsUtils.http()
                .sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("发送云端口创建请求结果: {}", responseMapper.toString());
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                "发送云端口创建请求失败: " + responseMapper.getString("message"));
        //调用成功后拿到返回的任务ID
        Mapper mapper = responseMapper.getMapper("entity");
        String taskId = mapper.getString("id");
        String status = mapper.getString("status");
        String resourceId = mapper.getString("resourceId");

        String instanceId = mapper.getString("instanceId");
        Precondition.checkArgument(!"ERROR".equals(status), "创建云端口失败: " + responseMapper.getString("message"));
        TaskStatusExt taskStatusExt = new TaskStatusExt();
        taskStatusExt.setCreateStatus(status);
        taskStatusExt.setCreateTaskId(taskId);
        cloudPortDTO.setStatus("1");
        WocCloudPortDO wocCloudPortDO = cloudPortManagerConvert.dto2do(cloudPortDTO);
        wocCloudPortDO.setTaskStatusExt(JSON.toJSONString(taskStatusExt));
        wocCloudPortDO.setStatus(CLOULD_PORT_INIT_STATUS);
        wocCloudPortDO.setCreateTime(LocalDateTime.now());
        wocCloudPortDO.setBusinessSystemName(businessSysName);
        wocCloudPortDO.setBusinessSystemId(String.valueOf(vpcOrderExtDTO.getBusinessSysId()));
        wocCloudPortDO.setTenantId(String.valueOf(queryTenantId(vpcOrderExtDTO)));
        wocCloudPortDO.setPortId(instanceId);
        wocCloudPortDO.setTaskStatusExt(JSON.toJSONString(taskStatusExt));
        wocCloudPortDO.setResourceId(resourceId);

        wocCloudPortDO.setId(taskId);
        wocCloudPortDAO.insert(wocCloudPortDO);
    }


    /**
     * 调用远程云端口
     * @param cloudPortDTO
     */
    public Mapper remoteHttpCreate(CloudPortDTO cloudPortDTO) {
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/cloudPort/create";
        VpcOrderExtDTO vpcOrderExtDTO = vpcOrderManager.getById(cloudPortDTO.getVpcId());
        String  billNo = queryBillId(vpcOrderExtDTO);
        cloudPortDTO.setBusinessSystemName(vpcOrderExtDTO.getBusinessSysName());
        cloudPortDTO.setBusinessSystemId(String.valueOf(vpcOrderExtDTO.getBusinessSysId()));
        HashMap<String, Object> params = new HashMap<>();
        params.put("regionCode", cloudPortDTO.getRegionCode());
        params.put("billId", billNo);
        //可用区编码
        params.put("azCode", cloudPortDTO.getAzCode());
        params.put("vpcId", cloudPortDTO.getVpcId());
        params.put("vlanId", cloudPortDTO.getVlanId());
        //云区域id
        params.put("peerIp", cloudPortDTO.getPeerIp());
        params.put("srcIp", cloudPortDTO.getSrcIp());
        params.put("peerPassword", cloudPortDTO.getPeerPassword());
        params.put("name", cloudPortDTO.getCloudPortName());
        params.put("peerAsNumber", "");
        params.put("remoteCidr", "");
        log.info("发送云端口创建参数: {}",params);
        Mapper responseMapper = OkHttpsUtils.http()
                .sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("远程发送云端口创建请求结果: {}", responseMapper.toString());
        return responseMapper;
    }

    /**
     * 租户ID
     * @param vpcOrderExtDTO
     * @return
     */
    private Long queryTenantId(VpcOrderExtDTO vpcOrderExtDTO) {
        Long businessSystemId = vpcOrderExtDTO.getBusinessSysId();
        CmpAppDTO cmpAppDTO = businessService.getById(Long.valueOf(businessSystemId));
        return cmpAppDTO.getTenantId();
    }

    /**
     * 获取计费号
     * @param vpcOrderExtDTO
     * @return
     */
    private String queryBillId(VpcOrderExtDTO vpcOrderExtDTO){
        //根据vpcID 查询业务
        Long businessSystemId = vpcOrderExtDTO.getBusinessSysId();
        CmpAppDTO cmpAppDTO = businessService.getById(Long.valueOf(businessSystemId));
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        return tenantDTO.getBillId();
    }


    /**
     * 查询云端口详情
     * @param id
     * @return
     */
    @Override
    public CloudPortDTO queryWocCloudPortDetail(String id){
        return cloudPortManagerConvert.do2dto(wocCloudPortDAO.getById(id));
    }

    @Override
    public List<CloudPortDTO> queryList(WocCloudPortQuery wocCloudPortQuery) {
        List<WocCloudPortDO> wocCloudPortDOS = wocCloudPortDAO.queryListBySysBusyIds(StreamUtils.mapArray(wocCloudPortQuery.getBusinessSystemIds(),String::valueOf));
        return StreamUtils.mapArray(wocCloudPortDOS, cloudPortManagerConvert::do2dto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String id) {
        WocCloudPortDO wocCloudPortDO = wocCloudPortDAO.getById(id);
        Precondition.checkArgument(wocCloudPortDO != null, "云端口不存在");
        if(StringUtils.isAllBlank(wocCloudPortDO.getResourceId())){
            Precondition.checkArgument(wocCloudPortDO != null, "云端口不存在任务不存在");
        }
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/cloudPort/delete";
        HashMap<String,Object> params = new HashMap();
        params.put("cloudPortId",wocCloudPortDO.getResourceId());
        Mapper responseMapper = OkHttpsUtils.http()
                .sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("发送删除云端口请求结果: {}", responseMapper);
//        //处理远程云端口本地情况
        Precondition.checkArgument(responseMapper !=null,"发送删除云端口请求返回结果为空");
        String successStr = responseMapper.getString("success");
        String code = responseMapper.getString("code");
       //删除成功或者云端口不存在删除本地数据库中数据
        if(String.valueOf(BusinessExceptionEnum.SUCCESS.getCode()).equalsIgnoreCase(code)
                || responseMapper.getString("message").contains("云端口不存在")){
            wocCloudPortDO.setStatus("0");
            wocCloudPortDAO.update(wocCloudPortDO);
        }else{
            Precondition.checkArgument("1".equals(successStr) || "true".equals(successStr),
                    "删除云端口请求失败"+responseMapper.getString("message"));
        }
    }

    @Override
    public CloudPortDTO selectOnde(String id) {
        return cloudPortManagerConvert.do2dto(wocCloudPortDAO.getById(id));
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CloudPortDTO dto) {
        WocCloudPortDO wocCloudPortDO = cloudPortManagerConvert.dto2do(dto);
        wocCloudPortDAO.update(wocCloudPortDO);
    }

    @Override
    public PageResult<CloudPortDTO> page(WocCloudPortQuery query) {

        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<WocCloudPortDO> list = wocCloudPortDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), cloudPortManagerConvert::do2dto);
    }

}
