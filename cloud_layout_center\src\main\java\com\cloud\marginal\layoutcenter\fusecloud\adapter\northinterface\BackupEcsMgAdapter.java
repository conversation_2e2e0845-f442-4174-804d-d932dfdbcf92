package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.BackupParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.resource.api.backup.dto.CreateBackupRcDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * ecs备份适配管理
 */
@Component
@Slf4j
public class BackupEcsMgAdapter extends BaseNorthInterfaceAdapter {
    /**
     * 开通ecs
     */
    public TaskVO createBackupEcs(String taskId, Integer taskSource){
        log.info("createBackupEcs start");
        CreateBackupRcDto ecsDTO = generateCreateEcsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateBackupEcs(),
                null,
                ecsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("createBackupEcs url is:{}",northInterfaceAddress.getCreateBackupEcs());
        log.info("createBackupEcs params is:{}",JSONObject.toJSON(ecsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"create backup ecs");
        return tasksVOResult.getEntity();
    }





    private CreateBackupRcDto generateCreateEcsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.BACKUP_ECS_CREATE.getCode());
        BackupParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), BackupParam.class);
        CreateBackupRcDto ecsDTO = new CreateBackupRcDto();
        BeanUtils.copyProperties(ecsParam,ecsDTO);
        ecsDTO.setOrderId(layoutOrderParam.getSubOrderId());
        ecsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        ecsDTO.setBillId(layoutOrderParam.getAccount());
        ecsDTO.setGroupId(layoutOrderParam.getCustomId());
        ecsDTO.setgId(ecsParam.getGId());
        ecsDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());

        CreateBackupRcDto.Job job = new CreateBackupRcDto.Job();
        BeanUtils.copyProperties(ecsParam.getJob(),job);
        List<Integer> daysOfWeek = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ecsParam.getJob().getDaysOfWeek())){
            ecsParam.getJob().getDaysOfWeek().forEach(item->{
                daysOfWeek.add(item.getDay());
            });
            Integer[] integers = ArrayUtil.toArray(daysOfWeek,Integer.class);
            job.setDaysOfWeek(integers);
        }
        ecsDTO.setJob(job);

        if(ObjectUtil.isNotEmpty(ecsParam.getExtendparam())){
            CreateBackupRcDto.Extendparam extendparam = new CreateBackupRcDto.Extendparam();
            BeanUtils.copyProperties(ecsParam.getExtendparam(),extendparam);
            ecsDTO.setExtendparam(extendparam);
        }

        if(ObjectUtil.isNotEmpty(ecsParam.getExtendparam())) {
            CreateBackupRcDto.Retention retention = new CreateBackupRcDto.Retention();
            BeanUtils.copyProperties(ecsParam.getRetention(),retention);
            ecsDTO.setRetention(retention);
        }

        return ecsDTO;
    }


}
