package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.DcnNetworkSegmentDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DcnNetworkSegmentMapper extends BaseMapper<DcnNetworkSegmentDO> {

    @Select("SELECT NETWORK_CIDR FROM WOC_DCN_NETWORK_SEGMENT WHERE REGION_ID = #{region}")
    List<String> getByRegionId(@Param("region") String regionId);
}
