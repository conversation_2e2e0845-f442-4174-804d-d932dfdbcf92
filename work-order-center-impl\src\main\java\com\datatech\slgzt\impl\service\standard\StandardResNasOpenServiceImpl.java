package com.datatech.slgzt.impl.service.standard;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.NasModel;
import com.datatech.slgzt.model.nostander.VpnModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: vpn开通
 * @author: LK
 * @create: 2025-06-10 10:15
 **/
@Service
@Slf4j
public class StandardResNasOpenServiceImpl implements StandardResOpenService {

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void openStandardResource(StandardWorkOrderProductDTO productDTO) {
        //获取工单
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(productDTO.getWorkOrderId());
        NasModel nasModel = JSON.parseObject(productDTO.getPropertySnapshot(), NasModel.class);
        Long tenantId = platformService.getOrCreateTenantId(orderDTO.getBillId(), nasModel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        ResOpenReqModel resOpenReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        resOpenReqModel.setAccount(orderDTO.getBillId());
        //设置业务code;
        resOpenReqModel.setSourceExtType(OrderTypeEnum.SUBSCRIBE.getCode());
        //设置业务code
        resOpenReqModel.setBusinessCode("NAS_CREATE");
        //设置业务系统code
        resOpenReqModel.setBusinessSystemCode(orderDTO.getBusiSystemId().toString());
        //设置客户id
        resOpenReqModel.setCustomId(orderDTO.getCustomNo());
        //设置区域编码
        resOpenReqModel.setRegionCode(nasModel.getRegionCode());
        //设置的是主产品的gid 这里适配任务中心回调
        resOpenReqModel.setSubOrderId(productDTO.getSubOrderId());
        resOpenReqModel.setGid(productDTO.getGid());
        //设置租户id
        resOpenReqModel.setTenantId(tenantId);
        //设置userId
        resOpenReqModel.setUserId(orderDTO.getCreatedBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resOpenReqModel.setTaskSource(4);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList= Lists.newArrayList();
        //------------设置vpn的参数-----------------------------------------------------
        List<ResOpenReqModel.ProductOrder> natProduct = StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.NAS).assembleParam(new ResOpenOpm()
                .setAccount(orderDTO.getBillId())
                .setSubOrderId(productDTO.getSubOrderId().toString())
                .setGId(productDTO.getGid())
                .setNasModelList(Lists.newArrayList(nasModel)));
        reqProductList.addAll(natProduct);

        resOpenReqModel.setProductOrders(reqProductList);
        //------------------产品参数设置结束-------------------------------------------------------
        //把对应的产品都改成开通中状态
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        log.info("资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={},request param={}", JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl, JSON.toJSONString(resOpenReqModel));
        Mapper dataMapper= OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(resOpenReqModel))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败");
        log.info("资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.NAS;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
}
