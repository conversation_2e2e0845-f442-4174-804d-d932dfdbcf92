package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.DagProductManagerConvert;
import com.datatech.slgzt.dao.DagProductDAO;
import com.datatech.slgzt.dao.model.DagProductDO;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.query.DagProductQuery;
import com.datatech.slgzt.utils.StreamUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * DAG产品Manager实现类
 */
@Service
public class DagProductManagerImpl implements DagProductManager {

    @Resource
    private DagProductDAO dao;

    @Resource
    private DagProductManagerConvert convert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(DagProductDTO dto) {
        DagProductDO dagProductDO = convert.dto2do(dto);
        dao.insert(dagProductDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DagProductDTO dto) {
        DagProductDO dagProductDO = convert.dto2do(dto);
        dao.updateById(dagProductDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        dao.deleteById(id);
    }

    @Override
    public DagProductDTO getById(String id) {
        return convert.do2dto(dao.getById(id));
    }


    @Override
    public List<DagProductDTO> list(DagProductQuery query) {
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    @Override
    public DagProductDTO getBySubOrderId(Long subOrderId) {
        return convert.do2dto(dao.getBySubOrderId(subOrderId));
    }

    @Override
    public void updateStatusById(Long id, String status) {
        DagProductDTO dagProductDTO = new DagProductDTO();
        dagProductDTO.setId(id);
        dagProductDTO.setOpenStatus(status);
        dao.updateById(convert.dto2do(dagProductDTO));
    }

    @Override
    public void updateStatusByParentId(Long id, String status) {
        DagProductDTO dagProductDTO = new DagProductDTO();
        dagProductDTO.setParentProductId(id);
        dagProductDTO.setOpenStatus(status);
        dao.updateByParentId(convert.dto2do(dagProductDTO));
    }
} 