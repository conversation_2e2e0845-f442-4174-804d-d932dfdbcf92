package com.datatech.slgzt.model.vo.container;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 容器配额VO
 * <AUTHOR>
 * @description 容器配额视图对象
 * @date 2025年05月27日
 */
@Data
@Accessors(chain = true)
public class ContainerQuotaVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 子订单ID
     */
    private String subOrderId;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 配额名称
     */
    private String cqName;

    /**
     * 容器配额-核心数
     */
    @JsonProperty("vCpus")
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G
     */
    private Integer ram;

    /**
     * GPU算力
     */
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB
     */
    private Integer gpuVirtualMemory;

    /**
     * 物理GPU卡(个)
     */
    @JsonProperty("gpuVirtualCore")
    private Integer gpuCore;

    /**
     * 虚拟GPU卡(个)
     */
    @JsonProperty("gpuCore")
    private Integer gpuVirtualCore;

    /**
     * 4A账号
     */
    private String a4Account;

    /**
     * 4A账号绑定的手机
     */
    private String a4Phone;

    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 开通数量
     */
    private Integer openNum;

    /**
     * 云类型编码
     */
    private String catalogueDomainCode;

    /**
     * 云类型名称
     */
    private String catalogueDomainName;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;

    /**
     * 资源池ID
     */
    private Long regionId;

    /**
     * 资源池编码
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 状态
     */
    private String status;

    /**
     * 原始名称
     */
    private String originName;

    /**
     * 申请用户ID
     */
    private Long applyUserId;

    /**
     * 申请用户名称
     */
    private String applyUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
}
