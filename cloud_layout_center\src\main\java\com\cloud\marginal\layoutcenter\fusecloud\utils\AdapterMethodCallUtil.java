package com.cloud.marginal.layoutcenter.fusecloud.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 北向接口调用工具
 */
@Component
public class AdapterMethodCallUtil implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<String, Class> classCache = new ConcurrentHashMap<>(64);

    /**
     * 接口调用
     * @param fullTypeName 全类名
     * @param methodName 方法名称
     * @param params 参数列表
     * @return 调用返回结果
     * @throws ClassNotFoundException
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public Map adapterMethodCall(String fullTypeName, String methodName, Object... params) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<?> aClass = this.getClazz(fullTypeName);
        Object bean = applicationContext.getBean(aClass);
        Object callResult = null;
        if (params == null || params.length == 0) {
            Method method = aClass.getMethod(methodName);
            callResult =  method.invoke(bean);
        }else{
            Class[] methodParamTypeList = this.getMethodParamTypeArr(params);
            Method method = aClass.getMethod(methodName, methodParamTypeList);
            callResult =  method.invoke(bean,params);
        }
        return callResult == null ? null : (Map)callResult;
    }

    /**
     * 获取方法参数类型数组
     * @param params 方法参数数组
     * @return
     */
    private Class[] getMethodParamTypeArr(Object... params) {
        Class[] methodParamTypeArr = new Class[params.length];
        for (int i = 0; i < params.length; i++) {
            methodParamTypeArr[i] = params[i].getClass();
        }
        return methodParamTypeArr;
    }

    /**
     * 根据全类名获取class
     *
     * @param fullTypeName 全类名
     * @return
     * @throws ClassNotFoundException
     */
    private Class<?> getClazz(String fullTypeName) throws ClassNotFoundException {
        Class<?> aClass = classCache.get(fullTypeName);
        if (aClass == null) {
            synchronized (fullTypeName.intern()) {
                if (aClass == null) {
                    aClass = Class.forName(fullTypeName);
                    classCache.put(fullTypeName, aClass);
                }
            }
        }
        return aClass;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
