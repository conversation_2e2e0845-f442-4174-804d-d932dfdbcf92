package com.datatech.slgzt.controller;

import com.datatech.slgzt.convert.ExportTaskWebConvert;
import com.datatech.slgzt.manager.ExportTaskManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.req.export.ExportTaskPageReq;
import com.datatech.slgzt.model.req.export.ExportTaskSaveReq;
import com.datatech.slgzt.model.req.slb.SlbCertificateIdReq;
import com.datatech.slgzt.model.vo.export.ExportTaskVO;
import com.datatech.slgzt.service.ExportTaskService;
import com.datatech.slgzt.service.file.FileService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import lombok.SneakyThrows;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出任务控制器
 */
@RestController
@RequestMapping("/exportTask")
public class ExportTaskController implements InitializingBean {

    @Resource
    private ExportTaskManager exportTaskManager;

    @Resource
    private List<ExportTaskService> exportTaskService;

    private final Map<String,ExportTaskService> exportTaskServiceMap =new HashMap<>();

    @Resource
    private FileService fileService;

    @Resource
    private ExportTaskWebConvert exportTaskWebConvert;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    public CommonResult<PageResult<ExportTaskVO>> page(@RequestBody ExportTaskPageReq req) {
        PageResult<ExportTaskDTO> page = exportTaskManager.page(exportTaskWebConvert.convert(req));
        return CommonResult.success(PageWarppers.box(page, exportTaskWebConvert::convert));
    }

    /**
     * 生成资源池报表
     */
    @PostMapping("/create")
    public CommonResult<Void> create(@RequestBody ExportTaskSaveReq req) {
        exportTaskServiceMap.get("REGION").export(exportTaskWebConvert.convert(req));
        return CommonResult.success(null);
    }

    /**
     * 生成证书报表
     */
    @PostMapping("/tenantReportCreate")
    public CommonResult<Void> tenantReportCreate(@RequestBody ExportTaskSaveReq req) {
        exportTaskServiceMap.get("TENANT").export(exportTaskWebConvert.convert(req));
        return CommonResult.success(null);
    }

    /**
     * 生成GPU报表
     */
    @PostMapping("/gpuReportCreate")
    public CommonResult<Void> gpuReportCreate(@RequestBody ExportTaskSaveReq req) {
        exportTaskServiceMap.get("GPU_REPORT").export(exportTaskWebConvert.convert(req));
        return CommonResult.success(null);
    }




    /**
     * 获取导出任务详情
     */
    @PostMapping("/detail")
    public CommonResult<ExportTaskVO> detail(@RequestBody SlbCertificateIdReq req) {
        ExportTaskDTO dto = exportTaskManager.getById(req.getId());
        return CommonResult.success(exportTaskWebConvert.convert(dto));
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    public CommonResult<Void> remove(@RequestBody SlbCertificateIdReq req) {
        req.getIds().forEach(i -> {
            Precondition.checkArgument(i, "导出任务id不能为空");
            exportTaskManager.deleteById(i);
        });
        return CommonResult.success(null);
    }

    /**
     * 下载
     */
    @SneakyThrows
    @GetMapping("/download")
    public CommonResult<Void> download(String id, HttpServletResponse response) {
        Precondition.checkArgument(id, "导出任务id不能为空");
        ExportTaskDTO dto = exportTaskManager.getById(id);
        InputStream inputStream = fileService.downloadFile(dto.getFilePath());
        outPutStream(dto.getReportName(), response, inputStream);
        return CommonResult.success(null);
    }

    private void outPutStream(String fileName, HttpServletResponse response, InputStream inputStream) throws IOException {
        byte[] bytes = fileName.getBytes(StandardCharsets.UTF_8);
        //3.2再将字节数组以 ISO-8859-1转换字符串
        fileName = new String(bytes, StandardCharsets.ISO_8859_1);
        //4.响应的内容应该是以附件的形式响应给浏览器(设置响应头)
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName+".xlsx");
        //5.响应文件给浏览器
        IOUtils.copy(inputStream, response.getOutputStream());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        exportTaskService.forEach(i->{
            exportTaskServiceMap.put(i.getReportType(),i);
        });
    }
}