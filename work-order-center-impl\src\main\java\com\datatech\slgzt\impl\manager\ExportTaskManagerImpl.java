package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.ExportTaskManagerConvert;
import com.datatech.slgzt.dao.ExportTaskDAO;
import com.datatech.slgzt.dao.model.report.ExportTaskDO;
import com.datatech.slgzt.manager.ExportTaskManager;
import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.query.ExportTaskQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 导出任务Manager实现
 */
@Slf4j
@Service
public class ExportTaskManagerImpl implements ExportTaskManager {
    
    @Resource
    private ExportTaskDAO exportTaskDAO;
    
    @Resource
    private ExportTaskManagerConvert exportTaskManagerConvert;
    
    @Override
    public String createTask(ExportTaskDTO dto) {
        dto.setCreateTime(LocalDateTime.now());
        ExportTaskDO taskDO = exportTaskManagerConvert.dto2do(dto);
        exportTaskDAO.insert(taskDO);
        return taskDO.getId();
    }
    
    @Override
    public void updateTask(ExportTaskDTO dto) {
        ExportTaskDO taskDO = exportTaskManagerConvert.dto2do(dto);
        exportTaskDAO.update(taskDO);
    }
    
    @Override
    public ExportTaskDTO getById(String id) {
        ExportTaskDO taskDO = exportTaskDAO.getById(id);
        return exportTaskManagerConvert.do2dto(taskDO);
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteById(String id) {
        exportTaskDAO.deleteById(id);
    }

    @Override
    public PageResult<ExportTaskDTO> page(ExportTaskQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ExportTaskDO> list = exportTaskDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), exportTaskManagerConvert::do2dto);
    }
} 