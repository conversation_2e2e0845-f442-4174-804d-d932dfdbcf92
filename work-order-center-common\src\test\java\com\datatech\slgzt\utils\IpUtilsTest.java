package com.datatech.slgzt.utils;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * IpUtils 工具类单元测试
 *
 * <AUTHOR>
 */
@DisplayName("IP工具类测试")
public class IpUtilsTest {

    // ==================== IPv4 测试 ====================

    @Test
    @DisplayName("IPv4地址检测")
    public void testIsIPv4() {
        // 有效的IPv4地址
        assertThat(IpUtils.isIPv4("***********")).isTrue();
        assertThat(IpUtils.isIPv4("0.0.0.0")).isTrue();
        assertThat(IpUtils.isIPv4("***************")).isTrue();
        assertThat(IpUtils.isIPv4("********")).isTrue();

        // 无效的IPv4地址
        assertThat(IpUtils.isIPv4("256.1.1.1")).isFalse();
        assertThat(IpUtils.isIPv4("192.168.1")).isFalse();
        assertThat(IpUtils.isIPv4("***********.1")).isFalse();
        assertThat(IpUtils.isIPv4("2001:db8::1")).isFalse();
        assertThat(IpUtils.isIPv4(null)).isFalse();
        assertThat(IpUtils.isIPv4("")).isFalse();
    }

    @Test
    @DisplayName("IPv4 CIDR检测")
    public void testIsIPv4Cidr() {
        // 有效的IPv4 CIDR
        assertThat(IpUtils.isIPv4Cidr("***********/24")).isTrue();
        assertThat(IpUtils.isIPv4Cidr("10.0.0.0/8")).isTrue();
        assertThat(IpUtils.isIPv4Cidr("**********/12")).isTrue();
        assertThat(IpUtils.isIPv4Cidr("***********/32")).isTrue();

        // 无效的IPv4 CIDR
        assertThat(IpUtils.isIPv4Cidr("***********/33")).isFalse();
        assertThat(IpUtils.isIPv4Cidr("***********")).isFalse();
        assertThat(IpUtils.isIPv4Cidr("2001:db8::/32")).isFalse();
        assertThat(IpUtils.isIPv4Cidr(null)).isFalse();
    }

    @Test
    @DisplayName("IPv4地址转换")
    public void testIpv4Conversion() {
        // 测试IPv4转long
        assertThat(IpUtils.ipv4ToLong("***********")).isEqualTo(3232235777L);
        assertThat(IpUtils.ipv4ToLong("0.0.0.0")).isEqualTo(0L);
        assertThat(IpUtils.ipv4ToLong("***************")).isEqualTo(4294967295L);

        // 测试long转IPv4
        assertThat(IpUtils.longToIpv4(3232235777L)).isEqualTo("***********");
        assertThat(IpUtils.longToIpv4(0L)).isEqualTo("0.0.0.0");
        assertThat(IpUtils.longToIpv4(4294967295L)).isEqualTo("***************");

        // 测试往返转换
        String originalIp = "********00";
        long longValue = IpUtils.ipv4ToLong(originalIp);
        String convertedIp = IpUtils.longToIpv4(longValue);
        assertThat(convertedIp).isEqualTo(originalIp);
    }

    @Test
    @DisplayName("IPv4网络地址计算")
    public void testIpv4NetworkCalculation() {
        // 测试网络地址计算
        assertThat(IpUtils.getNetworkAddress("***********00/24")).isEqualTo("***********");
        assertThat(IpUtils.getNetworkAddress("*********/8")).isEqualTo("10.0.0.0");
        assertThat(IpUtils.getNetworkAddress("***********/16")).isEqualTo("**********");

        // 测试广播地址计算
        assertThat(IpUtils.getBroadcastAddress("***********/24")).isEqualTo("*************");
        assertThat(IpUtils.getBroadcastAddress("10.0.0.0/8")).isEqualTo("**************");
        assertThat(IpUtils.getBroadcastAddress("**********/16")).isEqualTo("**************");
    }

    @Test
    @DisplayName("IPv4地址范围检测")
    public void testIpv4InCidr() {
        // 在范围内的情况
        assertThat(IpUtils.isIpInCidr("***********00", "***********/24")).isTrue();
        assertThat(IpUtils.isIpInCidr("********", "10.0.0.0/8")).isTrue();
        assertThat(IpUtils.isIpInCidr("***********", "**********/16")).isTrue();

        // 不在范围内的情况
        assertThat(IpUtils.isIpInCidr("***********", "***********/24")).isFalse();
        assertThat(IpUtils.isIpInCidr("********", "10.0.0.0/8")).isFalse();

        // 边界情况
        assertThat(IpUtils.isIpInCidr("***********", "***********/24")).isTrue(); // 网络地址
        assertThat(IpUtils.isIpInCidr("*************", "***********/24")).isTrue(); // 广播地址
    }

    @Test
    @DisplayName("IPv4可用IP生成")
    public void testGenerateAvailableIpv4s() {
        // 测试基本生成
        List<String> ips = IpUtils.generateAvailableIpv4s("***********/30", null, null, 2);
        assertThat(ips).hasSize(2);
        assertThat(ips).containsExactly("***********", "***********");

        // 测试带过滤的生成
        List<String> filterIps = Arrays.asList("***********");
        List<String> filteredIps = IpUtils.generateAvailableIpv4s("***********/30", null, filterIps, 1);
        assertThat(filteredIps).hasSize(1);
        assertThat(filteredIps).containsExactly("***********");

        // 测试带已使用IP的生成（使用更大的网段）
        List<String> usedIps = Arrays.asList("***********", "***********");
        List<String> availableIps = IpUtils.generateAvailableIpv4s("***********/29", usedIps, null, 1);
        assertThat(availableIps).hasSize(1);
        assertThat(availableIps).containsExactly("***********");
    }

    // ==================== IPv6 测试 ====================

    @ParameterizedTest
    @DisplayName("IPv6地址检测 - 有效地址")
    @ValueSource(strings = {
            "2001:db8::1",
            "::1",
            "2001:db8:85a3::8a2e:370:7334",
            "::",
            "2001:db8:85a3:0:0:8a2e:370:7334"
    })
    public void testValidIPv6Addresses(String ipv6) {
        assertThat(IpUtils.isIPv6(ipv6)).isTrue();
    }

    @ParameterizedTest
    @DisplayName("IPv6地址检测 - 无效地址")
    @ValueSource(strings = {
            "***********",
            "invalid",
            "2001:db8::1::2",
            "2001:db8:85a3::8a2e::370:7334",
            "gggg::1"
    })
    public void testInvalidIPv6Addresses(String ipv6) {
        assertThat(IpUtils.isIPv6(ipv6)).isFalse();
    }

    @Test
    @DisplayName("IPv6地址检测 - null和空字符串")
    public void testIPv6NullAndEmpty() {
        assertThat(IpUtils.isIPv6(null)).isFalse();
        assertThat(IpUtils.isIPv6("")).isFalse();
    }

    @Test
    @DisplayName("IPv6 CIDR检测")
    public void testIsIPv6Cidr() {
        // 有效的IPv6 CIDR
        assertThat(IpUtils.isIPv6Cidr("2001:db8::/32")).isTrue();
        assertThat(IpUtils.isIPv6Cidr("::1/128")).isTrue();
        assertThat(IpUtils.isIPv6Cidr("2001:db8:85a3::/48")).isTrue();
        assertThat(IpUtils.isIPv6Cidr("::/0")).isTrue();

        // 无效的IPv6 CIDR
        assertThat(IpUtils.isIPv6Cidr("***********/24")).isFalse();
        assertThat(IpUtils.isIPv6Cidr("2001:db8::/129")).isFalse(); // 前缀长度超出范围
        assertThat(IpUtils.isIPv6Cidr("invalid/32")).isFalse();
        assertThat(IpUtils.isIPv6Cidr("2001:db8::")).isFalse(); // 缺少前缀长度
        assertThat(IpUtils.isIPv6Cidr(null)).isFalse();
    }

    @Test
    @DisplayName("IPv6地址压缩")
    public void testCompressIpv6Address() {
        // 测试基本压缩
        assertThat(IpUtils.compressIpv6Address("2001:0db8:0000:0000:0000:0000:0000:0001"))
                .isEqualTo("2001:db8::1");

        assertThat(IpUtils.compressIpv6Address("2001:0db8:0000:0000:0000:8a2e:0370:7334"))
                .isEqualTo("2001:db8::8a2e:370:7334");

        assertThat(IpUtils.compressIpv6Address("0000:0000:0000:0000:0000:0000:0000:0001"))
                .isEqualTo("::1");

        assertThat(IpUtils.compressIpv6Address("0000:0000:0000:0000:0000:0000:0000:0000"))
                .isEqualTo("::");

        // 测试去除前导0但不压缩的情况
        assertThat(IpUtils.compressIpv6Address("2001:0db8:0001:0002:0003:0004:0005:0006"))
                .isEqualTo("2001:db8:1:2:3:4:5:6");

        // 测试已经压缩的地址
        assertThat(IpUtils.compressIpv6Address("2001:db8::1")).isEqualTo("2001:db8::1");

        //
        assertThat(IpUtils.compressIpv6Address("2409:8029:5cd0:5600:0:0:0:1")).isEqualTo("2409:8029:5cd0:5600::1");

        // 测试边界情况
        assertThat(IpUtils.compressIpv6Address(null)).isNull();
        assertThat(IpUtils.compressIpv6Address("")).isEmpty();
    }

    @Test
    @DisplayName("IPv6地址转换")
    public void testIpv6Conversion() {
        // 测试 ::1
        BigInteger bigInt1 = IpUtils.ipv6ToBigInteger("::1");
        assertThat(bigInt1).isEqualTo(BigInteger.ONE);
        assertThat(IpUtils.bigIntegerToIpv6(bigInt1)).isEqualTo("::1");

        // 测试 2001:db8::1
        String ipv6 = "2001:db8::1";
        BigInteger bigInt2 = IpUtils.ipv6ToBigInteger(ipv6);
        String converted = IpUtils.bigIntegerToIpv6(bigInt2);
        assertThat(converted).isEqualTo("2001:db8::1");

        // 测试往返转换
        String originalIpv6 = "2001:db8:85a3::8a2e:370:7334";
        BigInteger bigIntValue = IpUtils.ipv6ToBigInteger(originalIpv6);
        String convertedIpv6 = IpUtils.bigIntegerToIpv6(bigIntValue);
        // 验证转换后的地址在语义上是相同的
        assertThat(IpUtils.ipv6ToBigInteger(convertedIpv6)).isEqualTo(bigIntValue);
    }

    @Test
    @DisplayName("IPv6地址转换异常情况")
    public void testIpv6ConversionExceptions() {
        // 测试无效IPv6地址
        assertThatThrownBy(() -> IpUtils.ipv6ToBigInteger("***********"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6地址格式");

        assertThatThrownBy(() -> IpUtils.ipv6ToBigInteger("invalid"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6地址格式");

        // 测试无效BigInteger
        assertThatThrownBy(() -> IpUtils.bigIntegerToIpv6(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6数值");

        assertThatThrownBy(() -> IpUtils.bigIntegerToIpv6(BigInteger.valueOf(-1)))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6数值");
    }

    @Test
    @DisplayName("IPv6 CIDR解析")
    public void testParseIpv6Cidr() {
        // 测试正常解析
        String[] parts = IpUtils.parseIpv6Cidr("2001:db8::/32");
        assertThat(parts).hasSize(2);
        assertThat(parts[0]).isEqualTo("2001:db8::");
        assertThat(parts[1]).isEqualTo("32");

        // 测试其他格式
        String[] parts2 = IpUtils.parseIpv6Cidr("::1/128");
        assertThat(parts2).containsExactly("::1", "128");

        // 测试异常情况
        assertThatThrownBy(() -> IpUtils.parseIpv6Cidr("***********/24"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6 CIDR格式");
    }

    @Test
    @DisplayName("IPv6网络地址计算")
    public void testIpv6NetworkCalculation() {
        // 测试网络地址计算
        assertThat(IpUtils.getIpv6NetworkAddress("2001:db8:85a3::8a2e:370:7334/48"))
                .isEqualTo("2001:db8:85a3::");

        assertThat(IpUtils.getIpv6NetworkAddress("2001:db8::1/64"))
                .isEqualTo("2001:db8::");

        assertThat(IpUtils.getIpv6NetworkAddress("2001:db8::/32"))
                .isEqualTo("2001:db8::");

        // 测试最大地址计算
        assertThat(IpUtils.getIpv6MaxAddress("2001:db8::/32"))
                .isEqualTo("2001:db8:ffff:ffff:ffff:ffff:ffff:ffff");

        // 测试 /128 的情况（只有一个地址）
        assertThat(IpUtils.getIpv6NetworkAddress("::1/128")).isEqualTo("::1");
        assertThat(IpUtils.getIpv6MaxAddress("::1/128")).isEqualTo("::1");
    }

    @Test
    @DisplayName("IPv6地址范围检测")
    public void testIpv6InCidr() {
        // 在范围内的情况
        assertThat(IpUtils.isIpv6InCidr("2001:db8::1", "2001:db8::/32")).isTrue();
        assertThat(IpUtils.isIpv6InCidr("2001:db8:85a3::1", "2001:db8::/16")).isTrue();
        assertThat(IpUtils.isIpv6InCidr("::1", "::1/128")).isTrue();

        // 不在范围内的情况
        assertThat(IpUtils.isIpv6InCidr("2002:db8::1", "2001:db8::/32")).isFalse();
        assertThat(IpUtils.isIpv6InCidr("2001:db9::1", "2001:db8::/32")).isFalse();

        // 测试异常情况
        assertThatThrownBy(() -> IpUtils.isIpv6InCidr("***********", "2001:db8::/32"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6地址格式");

        assertThatThrownBy(() -> IpUtils.isIpv6InCidr("2001:db8::1", "***********/24"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6 CIDR格式");
    }

    @Test
    @DisplayName("IPv6可用IP生成")
    public void testGenerateAvailableIpv6s() {
        // 测试小网段生成
        List<String> ips = IpUtils.generateAvailableIpv6s("2001:db8::/126", null, null, 2);
        assertThat(ips).hasSize(2); // /126 只有4个地址，去掉网络地址，剩下3个，但只要求2个，实际能生成2个

        // 验证生成的IP都在网段内
        for (String ip : ips) {
            assertThat(IpUtils.isIpv6InCidr(ip, "2001:db8::/126")).isTrue();
        }

        // 测试带过滤的情况
        List<String> filterIps = Arrays.asList("2001:db8::1");
        List<String> filteredIps = IpUtils.generateAvailableIpv6s("2001:db8::/126", null, filterIps, 2);
        assertThat(filteredIps).doesNotContain("2001:db8::1");

        // 测试空结果
        List<String> emptyResult = IpUtils.generateAvailableIpv6s("2001:db8::/126", null, null, 0);
        assertThat(emptyResult).isEmpty();

        // 测试异常情况
        assertThatThrownBy(() -> IpUtils.generateAvailableIpv6s("***********/24", null, null, 1))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的IPv6 CIDR格式");
    }

    // ==================== 通用方法测试 ====================

    @ParameterizedTest
    @DisplayName("IP版本检测")
    @CsvSource({
            "***********, 4",
            "2001:db8::1, 6",
            "::1, 6",
            "********, 4",
            "invalid, 0",
            "'', 0"
    })
    public void testGetIpVersion(String ip, int expectedVersion) {
        assertThat(IpUtils.getIpVersion(ip)).isEqualTo(expectedVersion);
    }

    @Test
    @DisplayName("IP版本检测 - null值")
    public void testGetIpVersionNull() {
        assertThat(IpUtils.getIpVersion(null)).isEqualTo(0);
    }

    @ParameterizedTest
    @DisplayName("CIDR版本检测")
    @CsvSource({
            "***********/24, 4",
            "2001:db8::/32, 6",
            "::1/128, 6",
            "10.0.0.0/8, 4",
            "invalid/24, 0",
            "'', 0"
    })
    public void testGetCidrVersion(String cidr, int expectedVersion) {
        assertThat(IpUtils.getCidrVersion(cidr)).isEqualTo(expectedVersion);
    }

    @Test
    @DisplayName("CIDR版本检测 - null值")
    public void testGetCidrVersionNull() {
        assertThat(IpUtils.getCidrVersion(null)).isEqualTo(0);
    }

    @Test
    @DisplayName("自动检测IP在CIDR范围内")
    public void testIsIpInCidrAuto() {
        // IPv4测试
        assertThat(IpUtils.isIpInCidrAuto("***********00", "***********/24")).isTrue();
        assertThat(IpUtils.isIpInCidrAuto("***********", "***********/24")).isFalse();

        // IPv6测试
        assertThat(IpUtils.isIpInCidrAuto("2001:db8::1", "2001:db8::/32")).isTrue();
        assertThat(IpUtils.isIpInCidrAuto("2002:db8::1", "2001:db8::/32")).isFalse();

        // 测试版本不匹配的异常
        assertThatThrownBy(() -> IpUtils.isIpInCidrAuto("***********", "2001:db8::/32"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("IP版本与CIDR版本不匹配");

        assertThatThrownBy(() -> IpUtils.isIpInCidrAuto("2001:db8::1", "***********/24"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("IP版本与CIDR版本不匹配");
    }

    @Test
    @DisplayName("自动检测生成可用IP")
    public void testGenerateAvailableIpsAuto() {
        // IPv4测试
        List<String> ipv4Result = IpUtils.generateAvailableIpsAuto("***********/30", null, null, 2);
        assertThat(ipv4Result).hasSize(2);
        assertThat(ipv4Result).allMatch(ip -> IpUtils.getIpVersion(ip) == 4);

        // IPv6测试
        List<String> ipv6Result = IpUtils.generateAvailableIpsAuto("2001:db8::/126", null, null, 2);
        assertThat(ipv6Result).hasSize(2);
        assertThat(ipv6Result).allMatch(ip -> IpUtils.getIpVersion(ip) == 6);

        // 测试无效CIDR
        assertThatThrownBy(() -> IpUtils.generateAvailableIpsAuto("invalid/24", null, null, 2))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的CIDR格式");
    }

    @Test
    @DisplayName("自动检测获取网络地址")
    public void testGetNetworkAddressAuto() {
        // IPv4测试
        assertThat(IpUtils.getNetworkAddressAuto("***********00/24")).isEqualTo("***********");

        // IPv6测试
        assertThat(IpUtils.getNetworkAddressAuto("2001:db8::1/32")).isEqualTo("2001:db8::");

        // 测试无效CIDR
        assertThatThrownBy(() -> IpUtils.getNetworkAddressAuto("invalid/24"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的CIDR格式");
    }

    @Test
    @DisplayName("更新后的generateAvailableIps方法（自动检测）")
    public void testGenerateAvailableIpsUpdated() {
        // 测试IPv4自动检测
        List<String> ipv4Result = IpUtils.generateAvailableIps("***********/30", null, null, 2);
        assertThat(ipv4Result).hasSize(2);
        assertThat(ipv4Result).containsExactly("***********", "***********");

        // 测试IPv6自动检测
        List<String> ipv6Result = IpUtils.generateAvailableIps("2001:db8::/126", null, null, 2);
        assertThat(ipv6Result).hasSize(2);
        // 验证生成的都是IPv6地址
        assertThat(ipv6Result).allMatch(ip -> IpUtils.getIpVersion(ip) == 6);
        // 验证都在网段内
        assertThat(ipv6Result).allMatch(ip -> IpUtils.isIpv6InCidr(ip, "2001:db8::/126"));

        // 测试无效CIDR
        assertThatThrownBy(() -> IpUtils.generateAvailableIps("invalid/24", null, null, 2))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的CIDR格式");
    }

    // ==================== 边界情况和异常测试 ====================

    @Test
    @DisplayName("IPv4异常情况测试")
    public void testIpv4Exceptions() {
        // 测试无效IPv4地址转换
        assertThatThrownBy(() -> IpUtils.ipv4ToLong("256.1.1.1"))
                .isInstanceOf(IllegalArgumentException.class);

        assertThatThrownBy(() -> IpUtils.ipv4ToLong(null))
                .isInstanceOf(IllegalArgumentException.class);

        // 测试无效CIDR
        assertThatThrownBy(() -> IpUtils.getNetworkAddress("***********/33"))
                .isInstanceOf(IllegalArgumentException.class);

        assertThatThrownBy(() -> IpUtils.isIpInCidr("***********", "invalid"))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("边界值测试")
    public void testBoundaryValues() {
        // IPv4边界值
        assertThat(IpUtils.ipv4ToLong("0.0.0.0")).isEqualTo(0L);
        assertThat(IpUtils.ipv4ToLong("***************")).isEqualTo(4294967295L);
        assertThat(IpUtils.longToIpv4(0L)).isEqualTo("0.0.0.0");
        assertThat(IpUtils.longToIpv4(4294967295L)).isEqualTo("***************");

        // IPv6边界值
        assertThat(IpUtils.ipv6ToBigInteger("::")).isEqualTo(BigInteger.ZERO);
        assertThat(IpUtils.bigIntegerToIpv6(BigInteger.ZERO)).isEqualTo("::");

        // CIDR边界值
        assertThat(IpUtils.isIPv4Cidr("***********/0")).isTrue();
        assertThat(IpUtils.isIPv4Cidr("***********/32")).isTrue();
        assertThat(IpUtils.isIPv6Cidr("2001:db8::/0")).isTrue();
        assertThat(IpUtils.isIPv6Cidr("2001:db8::/128")).isTrue();
    }

    @Test
    @DisplayName("性能测试 - 大量IP生成")
    public void testPerformance() {
        // 测试IPv4大量IP生成（但不要太多，避免测试时间过长）
        long startTime = System.currentTimeMillis();
        List<String> ipv4Ips = IpUtils.generateAvailableIps("10.0.0.0/24", null, null, 100);
        long ipv4Time = System.currentTimeMillis() - startTime;

        assertThat(ipv4Ips).hasSize(100);
        assertThat(ipv4Time).isLessThan(1000); // 应该在1秒内完成

        // 测试IPv6生成（小网段）
        startTime = System.currentTimeMillis();
        List<String> ipv6Ips = IpUtils.generateAvailableIps("2001:db8::/120", null, null, 50);
        long ipv6Time = System.currentTimeMillis() - startTime;

        assertThat(ipv6Ips).hasSize(50);
        assertThat(ipv6Time).isLessThan(2000); // IPv6可能稍慢，但应该在2秒内完成
    }

    @Test
    @DisplayName("压缩IPv6地址的特殊情况")
    public void testCompressIpv6SpecialCases() {
        // 测试多个连续0段的情况，应该压缩最长的
        assertThat(IpUtils.compressIpv6Address("2001:0000:0000:0000:0000:0000:0000:0001"))
                .isEqualTo("2001::1");

        // 测试开头的连续0
        assertThat(IpUtils.compressIpv6Address("0000:0000:0000:0001:0002:0003:0004:0005"))
                .isEqualTo("::1:2:3:4:5");

        // 测试结尾的连续0
        assertThat(IpUtils.compressIpv6Address("2001:0db8:0001:0002:0000:0000:0000:0000"))
                .isEqualTo("2001:db8:1:2::");

        // 测试中间的连续0
        assertThat(IpUtils.compressIpv6Address("2001:0db8:0000:0000:0000:0000:0001:0002"))
                .isEqualTo("2001:db8::1:2");

        // 测试只有一个0的情况（不应该压缩）
        assertThat(IpUtils.compressIpv6Address("2001:0db8:0000:0001:0002:0003:0004:0005"))
                .isEqualTo("2001:db8:0:1:2:3:4:5");
    }

    @Test
    @DisplayName("验证IPv6最大地址是否为可用IP")
    void testIPv6MaxAddressAvailability() {
        // 测试小网段的最大地址可用性
        String cidr = "2001:db8::/126"; // 只有4个地址的小网段
        String maxAddress = IpUtils.getIpv6MaxAddress(cidr);
        String networkAddress = IpUtils.getIpv6NetworkAddress(cidr);

        System.out.println("=== IPv6最大地址可用性测试 ===");
        System.out.println(String.format("网段: %s", cidr));
        System.out.println(String.format("网络地址: %s", networkAddress));
        System.out.println(String.format("最大地址: %s", maxAddress));

        // 验证最大地址在网段内
        assertThat(IpUtils.isIpv6InCidr(maxAddress, cidr))
                .as("IPv6最大地址 %s 应该在网段 %s 内", maxAddress, cidr)
                .isTrue();

        // 生成可用IP，应该包含最大地址
        List<String> availableIps = IpUtils.generateAvailableIpv6s(cidr, Lists.newArrayList(), Lists.newArrayList(), 10);

        System.out.println(String.format("生成的可用IP: %s", availableIps));

        // 验证最大地址被包含在可用IP中
        assertThat(availableIps)
                .as("IPv6最大地址 %s 应该在可用IP列表中", maxAddress)
                .contains(maxAddress);

        // 验证网络地址不在可用IP中（网络地址通常不分配给主机）
        assertThat(availableIps)
                .as("IPv6网络地址 %s 不应该在可用IP列表中", networkAddress)
                .doesNotContain(networkAddress);
    }

    @Test
    @DisplayName("对比IPv4和IPv6的地址边界处理")
    void testIPv4vsIPv6BoundaryHandling() {
        System.out.println("\n=== IPv4 vs IPv6 边界处理对比 ===");

        // IPv4测试 - 广播地址不应该可用
        String ipv4Cidr = "***********/30"; // 只有4个地址
        String ipv4Broadcast = IpUtils.getBroadcastAddress(ipv4Cidr);
        String ipv4Network = IpUtils.getNetworkAddress(ipv4Cidr);

        List<String> ipv4Available = IpUtils.generateAvailableIpv4s(ipv4Cidr, Lists.newArrayList(), Lists.newArrayList(), 10);

        System.out.println(String.format("IPv4网段: %s", ipv4Cidr));
        System.out.println(String.format("IPv4网络地址: %s", ipv4Network));
        System.out.println(String.format("IPv4广播地址: %s", ipv4Broadcast));
        System.out.println(String.format("IPv4可用IP: %s", ipv4Available));

        assertThat(ipv4Available)
                .as("IPv4广播地址 %s 不应该在可用IP列表中", ipv4Broadcast)
                .doesNotContain(ipv4Broadcast);
        assertThat(ipv4Available)
                .as("IPv4网络地址 %s 不应该在可用IP列表中", ipv4Network)
                .doesNotContain(ipv4Network);

        // IPv6测试 - 最大地址应该可用
        String ipv6Cidr = "2001:db8::/126"; // 只有4个地址
        String ipv6Max = IpUtils.getIpv6MaxAddress(ipv6Cidr);
        String ipv6Network = IpUtils.getIpv6NetworkAddress(ipv6Cidr);

        List<String> ipv6Available = IpUtils.generateAvailableIpv6s(ipv6Cidr, Lists.newArrayList(), Lists.newArrayList(), 10);

        System.out.println(String.format("IPv6网段: %s", ipv6Cidr));
        System.out.println(String.format("IPv6网络地址: %s", ipv6Network));
        System.out.println(String.format("IPv6最大地址: %s", ipv6Max));
        System.out.println(String.format("IPv6可用IP: %s", ipv6Available));

        assertThat(ipv6Available)
                .as("IPv6最大地址 %s 应该在可用IP列表中", ipv6Max)
                .contains(ipv6Max);
        assertThat(ipv6Available)
                .as("IPv6网络地址 %s 不应该在可用IP列表中", ipv6Network)
                .doesNotContain(ipv6Network);

        // 验证关键差异
        System.out.println("\n=== 关键差异总结 ===");
        System.out.println("IPv4: 广播地址不可用 ❌");
        System.out.println("IPv6: 最大地址可用 ✅");
    }

    @Test
    @DisplayName("测试不同大小IPv6网段的最大地址可用性")
    void testDifferentIPv6SubnetSizes() {
        System.out.println("\n=== 不同大小IPv6网段测试 ===");

        // 测试不同大小的网段
        String[] testCidrs = {
                "2001:db8::/127", // 2个地址
                "2001:db8::/126", // 4个地址
                "2001:db8::/125", // 8个地址
                "2001:db8::/124"  // 16个地址
        };

        for (String cidr : testCidrs) {
            String maxAddress = IpUtils.getIpv6MaxAddress(cidr);
            String networkAddress = IpUtils.getIpv6NetworkAddress(cidr);

            List<String> availableIps = IpUtils.generateAvailableIpv6s(cidr, Lists.newArrayList(), Lists.newArrayList(), 20);

            System.out.println(String.format("网段: %s", cidr));
            System.out.println(String.format("  网络地址: %s", networkAddress));
            System.out.println(String.format("  最大地址: %s", maxAddress));
            System.out.println(String.format("  可用IP数量: %d", availableIps.size()));
            System.out.println(String.format("  包含最大地址: %s", availableIps.contains(maxAddress) ? "✅" : "❌"));

            // 验证最大地址总是可用的
            assertThat(availableIps)
                    .as("网段 %s 的最大地址 %s 应该可用", cidr, maxAddress)
                    .contains(maxAddress);
        }
    }
}
