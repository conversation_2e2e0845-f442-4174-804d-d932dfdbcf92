package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.SlbListenerDO;
import com.datatech.slgzt.model.TaskStatusExt;
import com.datatech.slgzt.model.dto.SlbListenerDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * SLB监听器转换器
 */
@Mapper(componentModel = "spring")
public interface SlbListenerManagerConvert {

    /**
     * DTO转DO
     */
    @Mapping(target = "slbListenerProtocolModel", source = "slbListenerProtocolModel", qualifiedByName = "modelToString")
    @Mapping(target = "slbHealthCheckModel", source = "slbHealthCheckModel", qualifiedByName = "modelToString")
    @Mapping(target = "taskStatusExt", source = "taskStatusExt", qualifiedByName = "taskStatusExt")
    SlbListenerDO dto2do(SlbListenerDTO dto);
    
    /**
     * DO转DTO
     */
    @Mapping(target = "slbListenerProtocolModel", source = "slbListenerProtocolModel", qualifiedByName = "stringToProtocolModel")
    @Mapping(target = "slbHealthCheckModel", source = "slbHealthCheckModel", qualifiedByName = "stringToHealthModel")
    @Mapping(target = "taskStatusExt", source = "taskStatusExt", qualifiedByName = "taskStatusExt")
    SlbListenerDTO do2dto(SlbListenerDO entity);

    /**
     * 将Model对象转换为JSON字符串
     */
    @Named("modelToString")
    default String modelToString(Object model) {
        if (model == null) {
            return null;
        }
        return JSON.toJSONString(model);
    }

    /**
     * 将JSON字符串转换为ProtocolModel对象
     */
    @Named("stringToProtocolModel")
    default SlbListenerDTO.SlbListenerProtocolModel stringToProtocolModel(String json) {
        if (json == null) {
            return null;
        }
        return JSON.parseObject(json, SlbListenerDTO.SlbListenerProtocolModel.class);
    }

    /**
     * 将JSON字符串转换为HealthModel对象
     */
    @Named("stringToHealthModel")
    default SlbListenerDTO.SlbHealthCheckModel stringToHealthModel(String json) {
        if (json == null) {
            return null;
        }
        return JSON.parseObject(json, SlbListenerDTO.SlbHealthCheckModel.class);
    }

    /**
     * 将JSON字符串转换为TaskStatusExt对象
     */
    @Named("taskStatusExt")
    default String taskStatusExt(TaskStatusExt taskStatusExt) {
        if (taskStatusExt == null) {
            return null;
        }
        return JSON.toJSONString(taskStatusExt);
    }
    /**
     * 将JSON字符串转换为TaskStatusExt对象
     */
    @Named("taskStatusExt")
    default TaskStatusExt taskStatusExt(String json) {
        if (json == null) {
            return new TaskStatusExt();
        }
        return JSON.parseObject(json, TaskStatusExt.class);
    }
} 