package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.NetworkParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.dto.edge.NetWorkDTO;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 网络适配管理
 */
@Component
@Slf4j
public class NetworkMgAdapter extends BaseNorthInterfaceAdapter{

    /**
     * 创建网络
     */
    public TaskVO createNetwork(String taskId, Integer taskSource){
        log.info("createNetwork start");
        NetWorkDTO netWorkDTO = generateCreateNetWorkDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateNetwork(),
                null,
                netWorkDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult,"create network");
        return tasksVOResult.getEntity();
    }

    private NetWorkDTO generateCreateNetWorkDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam vpcOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NETWORK_CREATE.getCode());
        NetworkParam networkParam = JSONObject.parseObject(vpcOrder.getAttrs(), NetworkParam.class);
        NetWorkDTO netWorkDTO = new NetWorkDTO();
        BeanUtils.copyProperties(networkParam,netWorkDTO);
        netWorkDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        return netWorkDTO;
    }
}
