package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.cloudPort.WocCloudPortDO;
import com.datatech.slgzt.model.dto.cloudPort.CloudPortDTO;
import com.datatech.slgzt.model.nostander.CloudPortModel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 云端口转换器
 */
@Mapper(componentModel = "spring")

public interface CloudPortManagerConvert {

    CloudPortDTO do2dto(WocCloudPortDO dto);

    WocCloudPortDO  dto2do(CloudPortDTO dto);

    @Mapping(target = "platformCode", source = "domainCode")
    @Mapping(target = "platformName", source = "domainName")
    CloudPortDTO  model2dto(CloudPortModel dto);
}
