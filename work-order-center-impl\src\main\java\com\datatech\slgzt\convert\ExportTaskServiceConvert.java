package com.datatech.slgzt.convert;

import cn.hutool.core.util.NumberUtil;
import com.datatech.slgzt.dao.model.report.ComResUsageDO;
import com.datatech.slgzt.model.report.RegionReportExcelDTO;
import com.datatech.slgzt.utils.DateUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Mapper(componentModel = "spring")
public interface ExportTaskServiceConvert {

    default RegionReportExcelDTO convert(ComResUsageDO comResUsageDO) {
        RegionReportExcelDTO reportExcelDTO = new RegionReportExcelDTO();
        reportExcelDTO.setRegionId(comResUsageDO.getRegionId());
        reportExcelDTO.setRegionCode(comResUsageDO.getRegionCode());
        reportExcelDTO.setRegionName(comResUsageDO.getRegionName());
        reportExcelDTO.setPlatform(comResUsageDO.getPlatformCode());
        reportExcelDTO.setDataTime(DateUtils.toString(comResUsageDO.getCreatedTime()));
        //虚拟资源池名称
        reportExcelDTO.setVirtualRegionName(comResUsageDO.getRegionName());
        //计算服务器节点数
        reportExcelDTO.setComputeNodeNum(comResUsageDO.getHostNum());
        //虚拟机数
        reportExcelDTO.setVmNum(comResUsageDO.getVmNum());
        //vCpu总量
        reportExcelDTO.setVcpuTotal(comResUsageDO.getVcpuTotal());
        //vCpu可用量
        reportExcelDTO.setVcpuAvailable(comResUsageDO.getVcpuAvi());
        //内存总量
        reportExcelDTO.setMemoryTotal(NumberUtil.mul(comResUsageDO.getMemoryTotal(), 1024L));
        //内存可用量
        reportExcelDTO.setMemoryAvailable(NumberUtil.mul(comResUsageDO.getMemoryAvi(), 1024L));
        //存储总量
        reportExcelDTO.setStorageTotal(NumberUtil.mul(comResUsageDO.getStorageTotal(), 1024L));
        //VCpu已分配 拿使用量
        reportExcelDTO.setVcpuAllocated(comResUsageDO.getVcpuUsed());
        //内存已分配
        reportExcelDTO.setMemoryAllocated(NumberUtil.mul(comResUsageDO.getMemoryUsed(), 1024L));
        //存储已分配
        reportExcelDTO.setStorageAllocated(NumberUtil.mul(comResUsageDO.getStorageUsed(), 1024L));
        //VCpu剩余
        reportExcelDTO.setVcpuRemaining(comResUsageDO.getVcpuAvi());
        //内存剩余
        reportExcelDTO.setMemoryRemaining(NumberUtil.mul(comResUsageDO.getMemoryAvi(), 1024L));
        //存储剩余
        reportExcelDTO.setStorageRemaining(NumberUtil.mul(comResUsageDO.getStorageAvi(), 1024L));
        
        //vCpu分配率 = 已分配 / 总量 * 100%
        reportExcelDTO.setVcpuAllocationRate(calculatePercentage(comResUsageDO.getVcpuUsed(), comResUsageDO.getVcpuTotal()));
        
        //内存分配率 = 已分配 / 总量 * 100%
        reportExcelDTO.setMemoryAllocationRate(calculatePercentage(
            NumberUtil.mul(comResUsageDO.getMemoryUsed(), 1024L),
            NumberUtil.mul(comResUsageDO.getMemoryTotal(), 1024L)
        ));
        
        //存储分配率 = 已分配 / 总量 * 100%
        reportExcelDTO.setStorageAllocationRate(calculatePercentage(
            NumberUtil.mul(comResUsageDO.getStorageUsed(), 1024L),
            NumberUtil.mul(comResUsageDO.getStorageTotal(), 1024L)
        ));
        
        //vCPU 使用率均值 = 已使用 / 总量
        reportExcelDTO.setVcpuUsageAvg(null);//下个流程中获取
        //内存使用率均值 = 已使用 / 总量
        reportExcelDTO.setMemoryUsageAvg(null);//下个流程中获取
        //存储使用率均值 = 已使用 / 总量
        reportExcelDTO.setStorageUsageAvg(null);//下个流程中获取
        //vCPU 使用率峰值
        reportExcelDTO.setVcpuUsagePeak(null);//下个流程中获取
        //内存使用率峰值
        reportExcelDTO.setMemoryUsagePeak(null);//下个流程中获取
        //存储使用率峰值
        reportExcelDTO.setStorageUsagePeak(null);//下个流程中获取
        //创建时间
        reportExcelDTO.setCreateTime(comResUsageDO.getCreatedTime());
        return reportExcelDTO;
    }

    /**
     * 计算百分比，处理除数为0的情况，并转换为整数百分比保留两位小数
     * @param numerator 分子
     * @param denominator 分母
     * @return 百分比，如果分母为0则返回0
     */
    default BigDecimal calculatePercentage(Number numerator, Number denominator) {
        if (denominator == null || denominator.doubleValue() == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal num = new BigDecimal(numerator.toString());
        BigDecimal den = new BigDecimal(denominator.toString());
        
        return num.multiply(new BigDecimal("100"))
                 .divide(den, 2, RoundingMode.HALF_UP);
    }


}
