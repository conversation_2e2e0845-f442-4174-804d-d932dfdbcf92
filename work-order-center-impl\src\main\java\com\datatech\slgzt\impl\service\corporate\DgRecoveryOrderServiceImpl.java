package com.datatech.slgzt.impl.service.corporate;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.consumer.BatchRestartConsumer;
import com.datatech.slgzt.enums.DisDimensionStatusEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.opm.DgRecoveryOrderCreateOpm;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.recovery.*;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.service.corporate.DgRecoveryOrderService;
import com.datatech.slgzt.utils.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.NoSuchJobException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 21:52:30
 */
@Service
@Slf4j
public class DgRecoveryOrderServiceImpl implements DgRecoveryOrderService {

    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private SlbListenerManager slbListenerManager;

    @Resource
    private DgRecoveryOrderManager dgRecoveryOrderManager;

    @Resource
    private RegionManager regionManager;

    @Resource
    private JobLauncher jobLauncher;

    @Resource
    private JobRegistry jobRegistry;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;

    /**
     * createRecoveryWorkOrder
     *
     * @param opm
     */
    @Override
    public String createRecoveryWorkOrder(DgRecoveryOrderCreateOpm opm) {
        DgRecoveryOrderDTO submitDTO = new DgRecoveryOrderDTO();
        submitDTO.setCreatorId(opm.getCreateUserId());
        submitDTO.setCreator(opm.getCreatedUserName());
        submitDTO.setCreateTime(LocalDateTime.now());
        submitDTO.setRecoveryType(opm.getRecoveryType());
        submitDTO.setBillId(opm.getBillId());
        submitDTO.setTenantId(opm.getTenantId());
        submitDTO.setTenantName(opm.getTenantName());
        //如果传入id不是空的 先对所有关联的产品全部解绑,然后清空关联的产品
        submitDTO.setOrderCode("DGHS" + System.currentTimeMillis());
        String workOrderId = dgRecoveryOrderManager.createWorkOrder(submitDTO);
        //--------------------创建工单并对接流程服务和记录日志----------

        //--------------------开始查询对应的产品创建对应的product-------
        //--------------------ecs----------------------------------
        fillCreateEcs(opm, workOrderId);
        //--------------------gcs----------------------------------
        fillCreateGcs(opm, workOrderId);
        //--------------------mysql----------------------------------
        fillCreateMysql(opm, workOrderId);
        //--------------------evs----------------------------------
        fillCreateEvs(opm, workOrderId);
        //--------------------eip----------------------------------
        fillCreateEip(opm, workOrderId);
        //--------------------obs----------------------------------
        fillCreateObs(opm, workOrderId);
        //--------------------nat----------------------------------
        fillCreateNat(opm, workOrderId);
        //--------------------slb----------------------------------
        fillCreateSlb(opm, workOrderId);
        //--------------------backup------------------------------
        fillCreateBackup(opm, workOrderId);
        //--------------------vpn------------------------------
        fillCreateVpn(opm, workOrderId);
        //--------------------vpc----------------------------------
        fillCreateVpc(opm, workOrderId);
        //批量更新产品 更变退订状态
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        if (ObjNullUtils.isNotNull(recoveryDeviceIdList)) {
            resourceDetailManager.updateDisOrderStatusByDeviceIds(recoveryDeviceIdList, "1");
        }
        return workOrderId;
    }

    private void fillCreateVpc(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getVpcIdList())) {
            return;
        }
        List<VpcOrderResult> vpcOrderResultList = vpcOrderManager.listByIdList(opm.getVpcIdList());
        //创建VPC存储对象
        vpcOrderResultList.forEach(vpcOrderResult -> {
            //先锁定资源
            vpcOrderManager.updateRecoveryStatusByIds(Collections.singletonList(vpcOrderResult.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryVpcModel vpcModel = new RecoveryVpcModel();
            vpcModel.setVpcId(vpcOrderResult.getId());
            vpcModel.setVpcName(vpcOrderResult.getVpcName());
            vpcModel.setRegionName(vpcOrderResult.getPoolName());
            vpcModel.setRegionCode(vpcOrderResult.getRegionCode());
            vpcModel.setDomainName(vpcOrderResult.getDomainName());
            vpcModel.setCatalogueDomainCode(vpcOrderResult.getCatalogueDomainCode());
            vpcModel.setCatalogueDomainName(vpcOrderResult.getCatalogueDomainName());
            vpcModel.setBillId(vpcOrderResult.getBillId());
            vpcModel.setTenantName(vpcOrderResult.getTenantName());
            vpcModel.setCidr(vpcOrderResult.getCidr());
            vpcModel.setSubnetNum(vpcOrderResult.getSubnetNum());
            vpcModel.setOrderCode(vpcOrderResult.getOrderCode());
            vpcModel.setUserName(vpcOrderResult.getUserName());
            long productOrderId = IdUtil.getSnowflake().nextId();
            vpcModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.VPC.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(vpcModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(vpcOrderResult.getId());
            productManager.insert(product);
        });

    }


    private void fillCreateSlb(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getSlbIdList())) {
            return;
        }
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getSlbIdList())
                .setType(ProductTypeEnum.SLB.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        List<Long> syncRecoveryIdList = opm.getSyncRecoveryIdList();
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoverySlbModel slbModel = new RecoverySlbModel();
            slbModel.setSlbId(dto.getDeviceId());
            slbModel.setSpec(dto.getSpec());
            slbModel.setSlbName(dto.getDeviceName());
            fillCommonParam(slbModel, dto, opm);
            if (ObjNullUtils.isNotNull(dto.getEip()) && ObjNullUtils.isNotNull(syncRecoveryIdList) && syncRecoveryIdList.contains(dto.getId())) {
                if (ObjNullUtils.isNotNull(dto.getEip())) {
                    List<ResourceDetailDTO> eipDTOList = resourceDetailManager.listByDeviceIds(Collections.singletonList(dto.getEipId()));
                    eipDTOList = eipDTOList.stream()
                            .filter(i -> !"1".equals(i.getDisOrderStatus()))
                            .collect(Collectors.toList());
                    ResourceDetailDTO eipDTO = StreamUtils.findAny(eipDTOList);
                    if (ObjNullUtils.isNotNull(eipDTO)) {
                        resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                        RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                        recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        recoveryEipModel.setEipId(dto.getEipId());
                        recoveryEipModel.setEip(dto.getEip());
                        recoveryEipModel.setBandwidth(dto.getBandWidth());
                        slbModel.setEipModel(recoveryEipModel);
                        recoveryDeviceIdList.add(eipDTO.getDeviceId());
                    }

                }
            }
            long productOrderId = IdUtil.getSnowflake().nextId();
            slbModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setSyncRecovery(ObjNullUtils.isNotNull(opm.getSyncRecoveryIdList()) && opm.getSyncRecoveryIdList().contains(dto.getId()));
            product.setProductType(ProductTypeEnum.SLB.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(slbModel));
            product.setParentProductId(0L);
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setResourceDetailId(dto.getId().toString());
            productManager.insert(product);
            //插入eip产品
            if (ObjNullUtils.isNotNull(slbModel.getEipModel())) {
                createEipProduct(Collections.singletonList(slbModel.getEipModel()), product.getId(), workOrderId);
            }
        });
    }


    private void fillCreateNat(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getNatIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getNatIdList())
                .setType(ProductTypeEnum.NAT.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        List<Long> syncRecoveryIdList = opm.getSyncRecoveryIdList();
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryNatModel natModel = new RecoveryNatModel();
            natModel.setNatId(dto.getDeviceId());
            natModel.setNatName(dto.getDeviceName());
            natModel.setVpcName(dto.getVpcName());
            natModel.setSpec(dto.getSpec());
            //eip部分------------------------
            String eipId = dto.getEipId();
            if (ObjNullUtils.isNotNull(syncRecoveryIdList) && syncRecoveryIdList.contains(dto.getId())) {
                if (ObjNullUtils.isNotNull(eipId)) {
                    List<ResourceDetailDTO> eipDTOList = resourceDetailManager.listByDeviceIds(Collections.singletonList(dto.getEipId()));
                    eipDTOList = eipDTOList.stream()
                            .filter(i -> !"1".equals(i.getDisOrderStatus()))
                            .collect(Collectors.toList());
                    ResourceDetailDTO eipDTO = StreamUtils.findAny(eipDTOList);
                    if (ObjNullUtils.isNotNull(eipDTO)) {
                        resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                        RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                        recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        recoveryEipModel.setBandwidth(dto.getBandWidth());
                        recoveryEipModel.setEip(dto.getEip());
                        recoveryEipModel.setEipId(dto.getEipId());
                        natModel.setEipModel(recoveryEipModel);
                        recoveryDeviceIdList.add(eipDTO.getDeviceId());
                    }

                }
            }
            fillCommonParam(natModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            natModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setSyncRecovery(ObjNullUtils.isNotNull(syncRecoveryIdList) && syncRecoveryIdList.contains(dto.getId()));
            product.setProductType(ProductTypeEnum.NAT.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(natModel));
            product.setParentProductId(0L);
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setResourceDetailId(dto.getId().toString());
            productManager.insert(product);
            //插入eip产品
            if (ObjNullUtils.isNotNull(natModel.getEipModel())) {
                createEipProduct(Collections.singletonList(natModel.getEipModel()), product.getId(), workOrderId);
            }
        });

    }


    private void fillCreateObs(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getObsIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getObsIdList())
                .setType(ProductTypeEnum.OBS.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryObsModel obsModel = new RecoveryObsModel();
            obsModel.setObsId(dto.getDeviceId());
            obsModel.setSpec(dto.getSpec());
            obsModel.setObsName(dto.getDeviceName());
            fillCommonParam(obsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            obsModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.OBS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(obsModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }

    private void fillCreateEvs(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getEvsIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEvsIdList())
                .setType(ProductTypeEnum.EVS.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
            recoveryEvsModel.setDataDiskId(dto.getDeviceId());
            recoveryEvsModel.setDataDiskName(dto.getDeviceName());
            recoveryEvsModel.setDataDisk(dto.getDataDisk());
            recoveryEvsModel.setVmId(dto.getVmId());
            recoveryEvsModel.setVmName(dto.getEcsName());
            recoveryEvsModel.setMountVm("是".equals(dto.getMountOrNot()));
            fillCommonParam(recoveryEvsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEvsModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(recoveryEvsModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }

    private void fillCreateEip(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getEipIdList())) {
            return;
        }
        //先拉回eip列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEipIdList())
                .setType(ProductTypeEnum.EIP.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
            recoveryEipModel.setBandwidth(dto.getBandWidth());
            recoveryEipModel.setEip(dto.getEip());
            recoveryEipModel.setEipId(dto.getDeviceId());
            recoveryEipModel.setEipName(dto.getDeviceName());
            recoveryEipModel.setVmId(dto.getVmId());
            recoveryEipModel.setVmName(dto.getEcsName());
            recoveryEipModel.setRelatedDeviceId(dto.getRelatedDeviceId());
            recoveryEipModel.setRelatedDeviceType(dto.getRelatedDeviceType());
            recoveryEipModel.setRelatedDeviceName(dto.getRelatedDeviceName());
            fillCommonParam(recoveryEipModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEipModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(recoveryEipModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }


    private void fillCreateMysql(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getRdsMysqlIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getRdsMysqlIdList())
                .setType(ProductTypeEnum.RDS_MYSQL.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryMysqlV2Model recoveryMysqlV2Model = new RecoveryMysqlV2Model();
            recoveryMysqlV2Model.setDeviceId(dto.getDeviceId());
            recoveryMysqlV2Model.setMysqlName(dto.getDeviceName());
            recoveryMysqlV2Model.setOsVersion(dto.getOsVersion());
            recoveryMysqlV2Model.setSpec(dto.getSpec());
            recoveryMysqlV2Model.setSysDisk(dto.getSysDisk());
            recoveryMysqlV2Model.setIp(dto.getIp());
            recoveryMysqlV2Model.setMountOrNot(dto.getMountOrNot());
            recoveryMysqlV2Model.setDeviceId(dto.getDeviceId());
            fillCommonParam(recoveryMysqlV2Model, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryMysqlV2Model.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.RDS_MYSQL.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(recoveryMysqlV2Model));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }

    private void fillCreateGcs(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getGcsIdList())) {
            return;
        }
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        List<Long> syncRecoveryIdList = opm.getSyncRecoveryIdList();
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getGcsIdList())
                .setType(ProductTypeEnum.GCS.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryEcsModel recoveryEcsModel = new RecoveryEcsModel();
            recoveryEcsModel.setVmId(dto.getDeviceId());
            recoveryEcsModel.setVmName(dto.getDeviceName());
            recoveryEcsModel.setOsVersion(dto.getOsVersion());
            recoveryEcsModel.setSpec(dto.getSpec());
            recoveryEcsModel.setSysDisk(dto.getSysDisk());
            recoveryEcsModel.setCmdbId(dto.getConfigId());
            recoveryEcsModel.setManageIp(dto.getManageIp());
            recoveryEcsModel.setIp(dto.getIp());
            recoveryEcsModel.setResourceId(dto.getResourceId());
            if (ObjNullUtils.isNotNull(syncRecoveryIdList) && syncRecoveryIdList.contains(dto.getId())) {
                List<RecoveryEvsModel> volumeModelList = Lists.newArrayList();
                //evs部分
                String volumeId = dto.getVolumeId();
                String dataDisk = dto.getDataDisk();
                if (ObjNullUtils.isNotNull(volumeId) && ObjNullUtils.isNotNull(dataDisk)) {
                    //逗号拆分volumeId 和 dataDisk 变成list
                    List<String> volumeIds = Arrays.asList(volumeId.split(","));
                    List<String> dataDisks = Arrays.asList(dataDisk.split(","));
                    List<ResourceDetailDTO> volumeDTOList = resourceDetailManager.listByDeviceIds(volumeIds);
                    volumeDTOList = volumeDTOList.stream()
                            .filter(i -> !"1".equals(i.getDisOrderStatus()))
                            .collect(Collectors.toList());
                    //确保两个列表长度一致
                    Precondition.checkArgument(volumeIds.size() == dataDisks.size(), "数据盘ID和数据盘大小数量不一致，请检查数据");
                    if (!volumeDTOList.isEmpty()) {
                        recoveryDeviceIdList.addAll(volumeIds);
                        resourceDetailManager.updateRecoveryTypeByDeviceIds(volumeIds, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                    }
                    for (ResourceDetailDTO resourceDetailDTO : volumeDTOList) {
                        RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
                        recoveryEvsModel.setDataDiskId(resourceDetailDTO.getDeviceId());
                        recoveryEvsModel.setDataDisk(resourceDetailDTO.getDataDisk());
                        recoveryEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        volumeModelList.add(recoveryEvsModel);
                    }
                }
                recoveryEcsModel.setEvsModelList(volumeModelList);
                //eip部分 eip不存在多个的情况
                if (ObjNullUtils.isNotNull(dto.getEip())) {
                    List<ResourceDetailDTO> eipDTOList = resourceDetailManager.listByDeviceIds(Collections.singletonList(dto.getEipId()));
                    eipDTOList = eipDTOList.stream()
                            .filter(i -> !"1".equals(i.getDisOrderStatus()))
                            .collect(Collectors.toList());
                    ResourceDetailDTO eipDTO = StreamUtils.findAny(eipDTOList);
                    if (ObjNullUtils.isNotNull(eipDTO)) {
                        resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(eipDTO.getDeviceId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                        recoveryDeviceIdList.add(eipDTO.getDeviceId());
                        RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                        recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        recoveryEipModel.setEipId(eipDTO.getDeviceId());
                        recoveryEipModel.setEip(eipDTO.getEip());
                        recoveryEipModel.setBandwidth(eipDTO.getBandWidth());
                        recoveryEcsModel.setEipModel(recoveryEipModel);
                    }

                }
            }
            fillCommonParam(recoveryEcsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEcsModel.setProductOrderId(productOrderId);
            DgRecoveryOrderProductDTO mainProduct = createEcsProduct(recoveryEcsModel, productOrderId, ProductTypeEnum.GCS.getCode(), workOrderId, syncRecoveryIdList);
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEvsModelList())) {
                createEvsProduct(recoveryEcsModel.getEvsModelList(), mainProduct.getId(), workOrderId);
            }
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEipModel())) {
                createEipProduct(Collections.singletonList(recoveryEcsModel.getEipModel()), mainProduct.getId(), workOrderId);
            }
        });
    }

    private void fillCreateEcs(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getEcsIdList())) {
            return;
        }
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        List<Long> syncRecoveryIdList = opm.getSyncRecoveryIdList();
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEcsIdList())
                .setType(ProductTypeEnum.ECS.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryEcsModel recoveryEcsModel = new RecoveryEcsModel();
            recoveryEcsModel.setVmId(dto.getDeviceId());
            recoveryEcsModel.setVmName(dto.getDeviceName());
            recoveryEcsModel.setOsVersion(dto.getOsVersion());
            recoveryEcsModel.setSpec(dto.getSpec());
            recoveryEcsModel.setSysDisk(dto.getSysDisk());
            recoveryEcsModel.setCmdbId(dto.getConfigId());
            recoveryEcsModel.setManageIp(dto.getManageIp());
            recoveryEcsModel.setIp(dto.getIp());
            recoveryEcsModel.setResourceId(dto.getResourceId());
            if (ObjNullUtils.isNotNull(syncRecoveryIdList) && syncRecoveryIdList.contains(dto.getId())) {
                List<RecoveryEvsModel> volumeModelList = Lists.newArrayList();
                //evs部分
                String volumeId = dto.getVolumeId();
                String dataDisk = dto.getDataDisk();
                if (ObjNullUtils.isNotNull(volumeId) && ObjNullUtils.isNotNull(dataDisk)) {
                    //逗号拆分volumeId 和 dataDisk 变成list
                    List<String> volumeIds = Arrays.asList(volumeId.split(","));
                    List<String> dataDisks = Arrays.asList(dataDisk.split(","));
                    List<ResourceDetailDTO> volumeDTOList = resourceDetailManager.listByDeviceIds(volumeIds);
                    volumeDTOList = volumeDTOList.stream()
                            .filter(i -> !"1".equals(i.getDisOrderStatus()))
                            .collect(Collectors.toList());
                    //确保两个列表长度一致
                    Precondition.checkArgument(volumeIds.size() == dataDisks.size(), "数据盘ID和数据盘大小数量不一致，请检查数据");
                    if (!volumeDTOList.isEmpty()) {
                        recoveryDeviceIdList.addAll(volumeIds);
                        resourceDetailManager.updateRecoveryTypeByDeviceIds(volumeIds, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                    }
                    for (ResourceDetailDTO resourceDetailDTO : volumeDTOList) {
                        RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
                        recoveryEvsModel.setDataDiskId(resourceDetailDTO.getDeviceId());
                        recoveryEvsModel.setDataDisk(resourceDetailDTO.getDataDisk());
                        recoveryEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        volumeModelList.add(recoveryEvsModel);
                    }
                }
                recoveryEcsModel.setEvsModelList(volumeModelList);
                //eip部分 eip不存在多个的情况
                if (ObjNullUtils.isNotNull(dto.getEip())) {
                    List<ResourceDetailDTO> eipDTOList = resourceDetailManager.listByDeviceIds(Collections.singletonList(dto.getEipId()));
                    eipDTOList = eipDTOList.stream()
                            .filter(i -> !"1".equals(i.getDisOrderStatus()))
                            .collect(Collectors.toList());
                    ResourceDetailDTO eipDTO = StreamUtils.findAny(eipDTOList);
                    if (ObjNullUtils.isNotNull(eipDTO)) {
                        resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(eipDTO.getDeviceId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                        recoveryDeviceIdList.add(eipDTO.getDeviceId());
                        RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                        recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        recoveryEipModel.setEipId(eipDTO.getDeviceId());
                        recoveryEipModel.setEip(eipDTO.getEip());
                        recoveryEipModel.setBandwidth(eipDTO.getBandWidth());
                        recoveryEcsModel.setEipModel(recoveryEipModel);
                    }

                }
            }
            fillCommonParam(recoveryEcsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEcsModel.setProductOrderId(productOrderId);
            DgRecoveryOrderProductDTO mainProduct = createEcsProduct(recoveryEcsModel, productOrderId, ProductTypeEnum.ECS.getCode(), workOrderId, syncRecoveryIdList);
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEvsModelList())) {
                createEvsProduct(recoveryEcsModel.getEvsModelList(), mainProduct.getId(), workOrderId);
            }
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEipModel())) {
                createEipProduct(Collections.singletonList(recoveryEcsModel.getEipModel()), mainProduct.getId(), workOrderId);
            }
        });
    }

    private void fillCreateBackup(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getBackupIdList())) {
            return;
        }
        //先拉回backup列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getBackupIdList())
                .setType(ProductTypeEnum.BACKUP.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryBackupModel recoveryBackupModel = new RecoveryBackupModel();
            recoveryBackupModel.setResourceId(dto.getDeviceId());
            recoveryBackupModel.setBackupType(dto.getBackupType());
            recoveryBackupModel.setJobName(dto.getDeviceName());
            recoveryBackupModel.setFrequency(dto.getFrequency());
            recoveryBackupModel.setDaysOfWeek(dto.getDaysOfWeek());
            if (StringUtils.isNotBlank(dto.getVmId())) {
                recoveryBackupModel.setObjectIdList(ListUtil.toList(dto.getVmId().split(",")));
            }
            fillCommonParam(recoveryBackupModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryBackupModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.BACKUP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(recoveryBackupModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }

    private void fillCreateVpn(DgRecoveryOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getVpnIdList())) {
            return;
        }
        //先拉回vpn列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getVpnIdList())
                .setType(ProductTypeEnum.VPN.getCode()));
        List<String> recoveryDeviceIdList = opm.getRecoveryDeviceIdList();
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            recoveryDeviceIdList.add(dto.getDeviceId());
            RecoveryVpnModel recoveryVpnModel = new RecoveryVpnModel();
            recoveryVpnModel.setResourceId(dto.getDeviceId());
            recoveryVpnModel.setName(dto.getDeviceName());
            recoveryVpnModel.setMaxConnection(dto.getSpec());
            recoveryVpnModel.setBandwidth(dto.getBandWidth());
            recoveryVpnModel.setVpcName(dto.getVpcName());
            recoveryVpnModel.setSubnetName(dto.getSubnetName());
            fillCommonParam(recoveryVpnModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryVpnModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.VPN.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(recoveryVpnModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }


    private void createEvsProduct(List<RecoveryEvsModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;

        models.stream().map(model -> {
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            ResourceDetailDTO detailEvs = resourceDetailManager.getByDeviceId(model.getDataDiskId());
            product.setResourceDetailId(detailEvs.getId().toString());
            return product;
        }).forEach(productManager::insert);
    }

    private void createEipProduct(List<RecoveryEipModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;
        models.stream().map(model -> {
            DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            ResourceDetailDTO detailEip = resourceDetailManager.getByDeviceId(model.getEipId());
            product.setResourceDetailId(detailEip.getId().toString());
            return product;
        }).forEach(productManager::insert);
    }


    private DgRecoveryOrderProductDTO createEcsProduct(RecoveryEcsModel model, Long mainId, String productType, String workOrderId, List<Long> syncRecoveryIdList) {
        DgRecoveryOrderProductDTO product = new DgRecoveryOrderProductDTO();
        product.setId(mainId);
        product.setProductType(productType);
        product.setSyncRecovery(ObjNullUtils.isNotNull(syncRecoveryIdList) && syncRecoveryIdList.contains(model.getResourceDetailId()));
        product.setWorkOrderId(workOrderId);
        product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
        product.setGid(UuidUtil.getGid(productType));
        product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
        product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        product.setResourceDetailId(model.getResourceDetailId().toString());
        product.setCmdbId(model.getCmdbId());
        return product;
    }


    private void fillCommonParam(BaseReconveryProductModel baseModel, ResourceDetailDTO resourceDetailDTO, DgRecoveryOrderCreateOpm opm) {
        RegionDTO regionDTO = new RegionDTO();
        if (ObjNullUtils.isNotNull(resourceDetailDTO.getResourcePoolId())) {
            regionDTO = regionManager.getById(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        regionManager.getById(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        baseModel.setResourceDetailId(resourceDetailDTO.getId());
        baseModel.setProductType(resourceDetailDTO.getType());
        baseModel.setAzName(resourceDetailDTO.getAzName());
        baseModel.setAzCode(resourceDetailDTO.getAzCode());
        baseModel.setAzId(resourceDetailDTO.getAzId());
        baseModel.setRegionId(regionDTO.getId().toString());
        baseModel.setRegionCode(regionDTO.getCode());
        baseModel.setRegionName(regionDTO.getName());
        baseModel.setExpireTime(resourceDetailDTO.getExpireTime());
        baseModel.setApplyTime(resourceDetailDTO.getApplyTime());
        baseModel.setTenantId(resourceDetailDTO.getTenantId());
        baseModel.setTenantName(resourceDetailDTO.getTenantName());
        baseModel.setBusinessSystemName(resourceDetailDTO.getBusinessSysName());
        baseModel.setBusinessSystemId(resourceDetailDTO.getBusinessSysId());
        baseModel.setDomainCode(resourceDetailDTO.getDomainCode());
        baseModel.setDomainName(resourceDetailDTO.getDomainName());
        baseModel.setBillId(resourceDetailDTO.getBillId());
        baseModel.setCreateTime(resourceDetailDTO.getCreateTime());
        baseModel.setBillType(resourceDetailDTO.getBillType());
        baseModel.setChargeType(resourceDetailDTO.getChargeType());
        baseModel.setDeviceId(resourceDetailDTO.getDeviceId());
        baseModel.setCreateOrderId(resourceDetailDTO.getOrderId());
        baseModel.setCreateOrderCode(resourceDetailDTO.getOrderCode());
        baseModel.setApplyUserName(resourceDetailDTO.getApplyUserName());
        baseModel.setCustomNo("");
    }


    @Override
    public PageResult<DgRecoveryOrderDTO> page(DgRecoveryOrderQuery query, Long currentUserId) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult<>();
        }

        return dgRecoveryOrderManager.page(query);
    }

    /**
     * fillCheckCreate
     */
    @Override
    public void fillCheckCreate(DgRecoveryOrderCreateOpm opm) {
        //-----------------------------------------------校验ecs部分------------------------------------------------------------------
        //如果ecsIdList不为空，校验ecsList
        if (ObjNullUtils.isNotNull(opm.getEcsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEcsIdList())
                    .setType(ProductTypeEnum.ECS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定不包含要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)
                        && ObjNullUtils.isNotNull(opm.getSyncRecoveryIdList())
                        && opm.getSyncRecoveryIdList().contains(resourceDetailDTO.getId())) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO,
                            "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                    if (ObjNullUtils.isNotNull(opm.getEipIdList())) {
                        Precondition.checkArgument(!opm.getEipIdList().contains(eipResourceDetailDTO.getId()),
                                "提交的弹性公网产品已经存在于云主机的回收列表中,无需重复回收");
                    }
                }
                //校验下云主机是否有绑定的evs 如果有一定要在提交的evsIdList中
                String evsId = resourceDetailDTO.getVolumeId();
                if (ObjNullUtils.isNotNull(evsId)
                        && ObjNullUtils.isNotNull(opm.getSyncRecoveryIdList())
                        && opm.getSyncRecoveryIdList().contains(resourceDetailDTO.getId())) {
                    //evsId可能存在多个的逗号隔开
                    for (String evsIdItem : evsId.split(",")) {
                        //查询Evs的resourceDetailDTO
                        ResourceDetailDTO evsResourceDetailDTO = resourceDetailManager.getByDeviceId(evsIdItem);
                        Precondition.checkArgument(evsResourceDetailDTO,
                                "找不到需要关联回收的evs数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                        if (ObjNullUtils.isNotNull(opm.getEvsIdList())) {
                            Precondition.checkArgument(!opm.getEvsIdList().contains(evsResourceDetailDTO.getId()),
                                    "提交的云硬盘产品已经存在于云主机的回收列表中,无需重复回收");
                        }
                    }
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getEcsIdList()
                    .size(), "ECS列表数据有误,请检查");
        }
        //-----------------------------------------------校验gcs部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getGcsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getGcsIdList())
                    .setType(ProductTypeEnum.GCS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)
                        && ObjNullUtils.isNotNull(opm.getSyncRecoveryIdList())
                        && opm.getSyncRecoveryIdList().contains(resourceDetailDTO.getId())) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO,
                            "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                    if (ObjNullUtils.isNotNull(opm.getEipIdList())) {
                        Precondition.checkArgument(!opm.getEipIdList().contains(eipResourceDetailDTO.getId()),
                                "提交的弹性公网产品已经存在于CPU云主机的回收列表中,无需重复回收");
                    }
                }
                //校验下云主机是否有绑定的evs 如果有一定要在提交的evsIdList中
                String evsId = resourceDetailDTO.getVolumeId();
                if (ObjNullUtils.isNotNull(evsId)
                        && ObjNullUtils.isNotNull(opm.getSyncRecoveryIdList())
                        && opm.getSyncRecoveryIdList().contains(resourceDetailDTO.getId())) {
                    //evsId可能存在多个的逗号隔开
                    for (String evsIdItem : evsId.split(",")) {
                        //查询Evs的resourceDetailDTO
                        ResourceDetailDTO evsResourceDetailDTO = resourceDetailManager.getByDeviceId(evsIdItem);
                        Precondition.checkArgument(evsResourceDetailDTO,
                                "找不到需要关联回收的evs数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                        if (ObjNullUtils.isNotNull(opm.getEvsIdList())) {
                            Precondition.checkArgument(!opm.getEvsIdList().contains(evsResourceDetailDTO.getId()),
                                    "提交的云硬盘产品已经存在于CPU云主机的回收列表中,无需重复回收");
                        }
                    }
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getGcsIdList()
                    .size(), "GCS列表数据有误,请检查");

        }
        //-----------------------------------------------校验mysql部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getRdsMysqlIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getRdsMysqlIdList())
                    .setType(ProductTypeEnum.RDS_MYSQL.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getRdsMysqlIdList()
                    .size(), "Mysql列表数据有误,请检查");

        }
        //-----------------------------------------------校验redis部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getRedisIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getRedisIdList())
                    .setType(ProductTypeEnum.REDIS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO,
                            "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                    if (ObjNullUtils.isNotNull(opm.getEipIdList())) {
                        Precondition.checkArgument(!opm.getEipIdList().contains(eipResourceDetailDTO.getId()),
                                "提交的弹性公网产品已经存在于Redis的回收列表中,无需重复回收");
                    }
                }
                //校验下云主机是否有绑定的evs 如果有一定要在提交的evsIdList中
                String evsId = resourceDetailDTO.getVolumeId();
                if (ObjNullUtils.isNotNull(evsId)) {
                    //evsId可能存在多个的逗号隔开
                    for (String evsIdItem : evsId.split(",")) {
                        //查询Evs的resourceDetailDTO
                        ResourceDetailDTO evsResourceDetailDTO = resourceDetailManager.getByDeviceId(evsIdItem);
                        Precondition.checkArgument(evsResourceDetailDTO,
                                "找不到需要关联回收的evs数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                        if (ObjNullUtils.isNotNull(opm.getEvsIdList())) {
                            Precondition.checkArgument(!opm.getEvsIdList().contains(evsResourceDetailDTO.getId()),
                                    "提交的云硬盘产品已经存在于Redis的回收列表中,无需重复回收");
                        }
                    }
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getRedisIdList()
                    .size(), "Redis列表数据有误,请检查");

        }
        //-----------------------------------------------校验eip部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getEipIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEipIdList())
                    .setType(ProductTypeEnum.EIP.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getEipIdList()
                    .size(), "EIP列表数据有误,请检查");
        }
        //-----------------------------------------------校验evs部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getEvsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEvsIdList())
                    .setType(ProductTypeEnum.EVS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getEvsIdList()
                    .size(), "EVS列表数据有误,请检查");
        }
        //-----------------------------------------------校验nat部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getNatIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getNatIdList())
                    .setType(ProductTypeEnum.NAT.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)
                        && ObjNullUtils.isNotNull(opm.getSyncRecoveryIdList())
                        && opm.getSyncRecoveryIdList().contains(resourceDetailDTO.getId())) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO,
                            "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                    if (ObjNullUtils.isNotNull(opm.getEipIdList())) {
                        Precondition.checkArgument(!opm.getEipIdList().contains(eipResourceDetailDTO.getId()),
                                "提交的EIP产品已经存在于nat网关的回收列表中,无需重复回收");
                    }
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getNatIdList()
                    .size(), "NAT列表数据有误,请检查");
        }
        //-----------------------------------------------校验obs部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getObsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getObsIdList())
                    .setType(ProductTypeEnum.OBS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getObsIdList()
                    .size(), "OBS列表数据有误,请检查");
        }
        //-----------------------------------------------校验slb部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getSlbIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getSlbIdList())
                    .setType(ProductTypeEnum.SLB.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)
                        && ObjNullUtils.isNotNull(opm.getSyncRecoveryIdList())
                        && opm.getSyncRecoveryIdList().contains(resourceDetailDTO.getId())) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO,
                            "找不到需要关联回收的弹性公网数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());

                    if (ObjNullUtils.isNotNull(opm.getEipIdList())) {
                        Precondition.checkArgument(!opm.getEipIdList().contains(eipResourceDetailDTO.getId()),
                                "提交的弹性公网产品已经存在于负载均衡的回收列表中,无需重复回收");
                    }
                }
                //如果SLB下有监听不能回收
                List<SlbListenerDTO> slbListenerDTOS = slbListenerManager.listBySlbIds(opm.getSlbIdList());
                Precondition.checkArgument(CollectionUtil.isEmpty(slbListenerDTOS), "当前SLB下存在监听，无法进行回收");
            });
        }
        //-----------------------------------------------校验backup部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getBackupIdList())) {
            //先拉回backup列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getBackupIdList())
                    .setType(ProductTypeEnum.BACKUP.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getBackupIdList()
                    .size(), "backup列表数据有误,请检查");
        }

        //-----------------------------------------------校验vpn部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getVpnIdList())) {
            //先拉回vpn列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getVpnIdList())
                    .setType(ProductTypeEnum.VPN.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                fillCheckTenantId(opm, resourceDetailDTO);
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getVpnIdList()
                    .size(), "vpn列表数据有误,请检查");
        }
    }

    @Override
    public void recovery(String orderId) {
        // spring batch 开启 dgDef
        DgRecoveryOrderDTO dgRecoveryOrderDTO = dgRecoveryOrderManager.getById(orderId);
        JobParameters jobParameters = new JobParametersBuilder()
                .addString("orderId", orderId)
                .addString("billId", dgRecoveryOrderDTO.getBillId())
                .addLong("time", System.currentTimeMillis())
                .toJobParameters();
        //第一次同步执行必然会失败的 主要是为了返回id
        Long jobId = null;
        try {
            jobId = jobLauncher.run(jobRegistry.getJob("dgProductRecoveryJob"), jobParameters)
                    .getId();
        } catch (JobExecutionAlreadyRunningException | JobInstanceAlreadyCompleteException | JobRestartException |
                 JobParametersInvalidException | NoSuchJobException e) {
            throw new RuntimeException(e);
        }
        //存入jobId
        dgRecoveryOrderDTO.setJobExecutionId(jobId);
        dgRecoveryOrderManager.update(dgRecoveryOrderDTO);
        //重启流程
        kafkaTemplate.send(BatchRestartConsumer.CORPORATE_BATCH_RESTART, orderId, KafkaMessage.of(new BatchRestartModel()
                .setJobExecutionId(jobId)
                .setRestartOnly(true)));

    }

    @Override
    public void restart(String id) {
        DgRecoveryOrderDTO corporateOrderDTO = dgRecoveryOrderManager.getById(id);
        //重启流程
        kafkaTemplate.send(BatchRestartConsumer.CORPORATE_BATCH_RESTART, id, KafkaMessage.of(new BatchRestartModel()
                .setJobExecutionId(corporateOrderDTO.getJobExecutionId())
                .setRestartOnly(true)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(List<Long> productIds) {
        for (Long productId : productIds) {
            // 1.product表根据id查询相应的产品
            DgRecoveryOrderProductDTO productDTO = productManager.getById(productId);
            Precondition.checkArgument(productDTO, "找不到对应产品");
            DgRecoveryOrderDTO orderDTO = dgRecoveryOrderManager.getById(productDTO.getWorkOrderId());
            // 作业已执行，就不能取消退订了，提示用户已经过了7天
            Precondition.checkArgument(orderDTO.getJobExecutionId() == null, "资源退订已过七天，不能取消退订");
            // 更新主产品
            cancel(productId, Long.valueOf(productDTO.getResourceDetailId()));
            // 更新子产品
            productManager.listChildren(productId).forEach(dto -> {
                cancel(dto.getId(), Long.valueOf(dto.getResourceDetailId()));
            });
        }
    }

    private void cancel(Long productId, Long resourceDetailID) {
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(resourceDetailID);
        resourceDetailDTO.setRecoveryStatus(RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
        resourceDetailDTO.setDisOrderStatus("0");
        resourceDetailManager.updateById(resourceDetailDTO);

        DgRecoveryOrderProductDTO updateDTO = new DgRecoveryOrderProductDTO();
        updateDTO.setId(productId);
        updateDTO.setRecoveryStatus(RecoveryStatusEnum.CANCELED.getType());
        productManager.update(updateDTO);
    }

    private static void fillCheckTenantId(DgRecoveryOrderCreateOpm opm, ResourceDetailDTO resourceDetailDTO) {
        if (opm.getTenantId() == null) {
            opm.setTenantId(resourceDetailDTO.getTenantId());
            opm.setTenantName(resourceDetailDTO.getTenantName());
        } else {
            Precondition.checkArgument(opm.getTenantId().equals(resourceDetailDTO.getTenantId()),
                    "每个退订订单中的资源需同属一个租户");
        }
    }
}
