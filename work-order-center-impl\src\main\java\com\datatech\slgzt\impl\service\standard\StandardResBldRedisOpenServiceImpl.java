package com.datatech.slgzt.impl.service.standard;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.BldRedisModel;
import com.datatech.slgzt.model.sms.SmsSendModel;
import com.datatech.slgzt.service.producer.SmsProducer;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.utils.*;
import com.ejlchina.okhttps.HttpResult;
import com.ejlchina.okhttps.OkHttps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @program: workordercenterproject
 * @description: 宝兰德redis开通
 * @author: LK
 * @create: 2025-06-23 14:34
 **/
@Service
@Slf4j
public class StandardResBldRedisOpenServiceImpl implements StandardResOpenService {

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SmsProducer smsProducer;

    @Value("${bldRedis.url}")
    private String url;

    @Value("${bldRedis.clientKey}")
    private String clientKey;

    @Value("${bldRedis.username}")
    private String username;

    @Value("${bldRedis.password}")
    private String password;

    @Value("${bldRedis.tenantId}")
    private String tenantId;

    @Value("${bldRedis.tenantName}")
    private String tenantName;

    @Value("${bldRedis.passPhone}")
    private String passPhone;


    @SneakyThrows
    @Override
    public void openStandardResource(StandardWorkOrderProductDTO productDTO) {
        Long productId = productDTO.getId();
        BldRedisModel bldRedisModel = JSON.parseObject(productDTO.getPropertySnapshot(), BldRedisModel.class);
        // 1. 获取token
        String token = getToken(productId);
        Precondition.checkArgument(StringUtils.isNotBlank(token), "获取token异常");
        // 2. 创建用户
        String userId = createUser(productId, bldRedisModel, token);
        Precondition.checkArgument(StringUtils.isNotBlank(userId), "创建用户异常");
        // 3. 获取sid
        String sid = getSid();
        Precondition.checkArgument(StringUtils.isNotBlank(sid), "获取sid异常");
        // 4. 创建业务系统
        String businessSysId = creatBusinessSys(productId, bldRedisModel, sid);
        Precondition.checkArgument(StringUtils.isNotBlank(businessSysId), "创建业务系统异常");
        // 5. 获取角色
        JSONObject role = getRole(productId, sid);
        Precondition.checkArgument(Objects.nonNull(role), "获取角色异常");
        // 6. 用户添加到租户下
        boolean tag = addUserToTenant(productId, sid, userId, bldRedisModel.getUsername());
        Precondition.checkArgument(tag, "用户添加到租户异常");
        // 7. 租户授权
        boolean b = tenantGrant(productId, sid, userId, role);
        Precondition.checkArgument(b, "租户授权异常");
        // 8. 创建实例
        boolean flag = createInstance(productId, bldRedisModel, sid, businessSysId);
        Precondition.checkArgument(flag, "创建实例异常");
        // 9. 数据入库（到这里，资源已经开通）
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
        //产品入库操作
        saveResource(productDTO.getWorkOrderId(), bldRedisModel);
        //最后把业务系统和用户存在缓存中，用来校验是否存在重复数据
        RBucket<String> bucket = redissonClient.getBucket(bldRedisModel.getBusinessSysName() + ":" + bldRedisModel.getUsername());
        bucket.set(bldRedisModel.getBusinessSysName() + "-" + bldRedisModel.getUsername());
        //将用户名、密码、实例密码发送短信给租户和pass组
        sendSms(productDTO);
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.BLD_REDIS;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }

    /**
     * 获取token
     */
    @SneakyThrows
    private String getToken(long productId) {
        String accessToken = null;
        String query = String.format("grant_type=password&username=%s&password=%s",
                URLEncoder.encode(username,"UTF-8"),
                URLEncoder.encode(password, "UTF-8"));
        log.info("获取token，请求url：{}", url + "/usermanager/oauth/token?" + query);
        HttpResult post = OkHttps.sync(url + "/usermanager/oauth/token?" + query)
                .addHeader("Authorization", "Basic " + clientKey)
                .post();
        String jsonResponse = post.getBody().toString();
        log.info("获取token，响应数据：{}", jsonResponse);
        if (200 != post.getStatus()) {
            updateWorkOrder(productId, "获取token失败：" + jsonResponse);
        } else {
            JSONObject json = JSON.parseObject(jsonResponse);
            accessToken = json.getString("access_token");
        }
        return accessToken;

    }

    /**
     * 创建用户
     */
    private String createUser(Long productId, BldRedisModel bldRedisModel, String accessToken) {
        // 该产品如果有对应的userId，直接返回（解决重复创建的问题）
        String redisKey = "productId:" + productId;
        RMap<String, String> workOrderMap = redissonClient.getMap(redisKey);
        String userId = workOrderMap.get("userId");
        if (StringUtils.isNotBlank(userId)) {
            return userId;
        }
        Map<String, Object> data = new HashMap<>();
        data.put("username", bldRedisModel.getUsername());
        data.put("password", invertEncrypt(bldRedisModel.getPassword()));
        data.put("enable", true);
        data.put("description", "算力工作台用户");
        log.info("创建用户，请求url：{}， 参数：{}", url + "/usermanager/user", JSON.toJSONString(data));
        HttpResult post = OkHttps.sync(url + "/usermanager/user")
                .bodyType(OkHttps.JSON)
                .addHeader("Authorization", "Bearer " + accessToken)
                .setBodyPara(data)
                .post();
        // 先提取响应体内容（只能调用一次）
        String responseBody = post.getBody().toString();
        log.info("创建用户，响应数据：{}", responseBody);
        // 检查状态码
        Precondition.checkArgument(post.getStatus() == 200, responseBody);
        if (200 != post.getStatus()) {
            updateWorkOrder(productId, "创建用户异常：" + responseBody);
        } else {
            // 直接解析为 JSONObject
            JSONObject jsonObject = JSON.parseObject(responseBody);
            userId = jsonObject.getString("id");
            // 创建完成，放到缓存中
            workOrderMap.put("userId", userId);
        }
        return userId;
    }

    /**
     * 获取sid
     */
    private String getSid() {
        String encodePassword = Base64.getEncoder().encodeToString(password.getBytes(StandardCharsets.UTF_8));
        Map<String, Object> data = new HashMap<>();
        data.put("username", username);
        data.put("password", encodePassword);
        // 1. 准备参数
        String requestUrl = url + "/apis/v1/auth/direct-login";

        // 2. 创建带Cookie管理的OkHttpClient
        log.info("获取sid，请求url：{}， 参数：{}", requestUrl, JSON.toJSONString(data));
        OkHttpClient client = new OkHttpClient.Builder()
                .cookieJar(new MyCookieJar())
                .build();

        // 3. 构建请求
        Request request = new Request.Builder()
                .url(requestUrl)
                .post(RequestBody.create(MediaType.get("application/json"), JSON.toJSONString(data)))
                .header("Content-Type", "application/json")
                .build();

        // 4. 发送请求并处理响应
        String sid = null;
        try (Response response = client.newCall(request).execute()) {
            //  获取特定Cookie（sid）
            sid = extractSidFromCookies(client, url);
            log.info("获取sid: " + sid);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return sid;
    }

    /**
     * 创建业务系统
     */
    private String creatBusinessSys(Long productId, BldRedisModel bldRedisModel,String sid) {
        String gid = UuidUtil.getGid("bldRedis");
        // 业务系统创建完成放入缓存 -- 全局维度
        RBucket<String> bucket = redissonClient.getBucket(bldRedisModel.getBusinessSysName());
        String s = bucket.get();
        if (StringUtils.isNotBlank(s)) {
            log.info("该业务系统已存在，直接返回");
            return s;
        }
        // 该产品如果有对应的businessSysId，直接返回（解决重复创建的问题）-- 工单维度
        String redisKey = "productId:" + productId;
        RMap<String, String> workOrderMap = redissonClient.getMap(redisKey);
        String businessSysId = workOrderMap.get("businessSysId");
        if (StringUtils.isNotBlank(businessSysId)) {
            return businessSysId;
        }
        Map<String, Object> data = new HashMap<>();
        data.put("id", gid);
        data.put("name", bldRedisModel.getBusinessSysName());
        data.put("description", "算力工作台业务系统");
        data.put("tenantId", tenantId);
        data.put("tenantName", tenantName);
        log.info("创建业务系统，请求url：{}， 参数：{}", url + "/apis/v1/tenants/" + tenantId + "/business-systems?sid=" + sid, JSON.toJSONString(data));
        HttpResult post = OkHttps.sync(url + "/apis/v1/tenants/" + tenantId + "/business-systems?sid=" + sid)
                .bodyType(OkHttps.JSON)
                .setBodyPara(data)
                .post();
        // 先提取响应体内容（只能调用一次）
        String responseBody = post.getBody().toString();
        log.info("创建业务系统，响应数据：{}", responseBody);
        if (200 != post.getStatus()) {
            updateWorkOrder(productId, "创建业务系统异常：" + responseBody);
        } else {
            // 直接解析为 JSONObject
            JSONObject jsonObject = JSON.parseObject(responseBody);
            businessSysId = jsonObject.getString("id");
            // 存到缓存里
            workOrderMap.put("businessSysId", businessSysId);
            bucket.set(businessSysId);
        }
        return businessSysId;
    }

    /**
     * 获取角色
     */
    private JSONObject getRole(Long productId, String sid) {
        JSONObject role = null;
        String query = String.format("/apis/v1/tenants/%s/roles?sid=%s",
                tenantId, sid);
        log.info("获取角色，请求url：{}", url + query);
        HttpResult result = OkHttps.sync(url + query)
                .bodyType(OkHttps.JSON)
                .get();

        // 只读取一次 body
        String responseBody = result.getBody().toString();
        log.info("获取角色，响应数据：{}", responseBody);
        if (200 != result.getStatus()) {
            updateWorkOrder(productId, "获取角色异常：" + responseBody);
        } else {
            // 使用 FastJSON 解析整个响应
            JSONObject jsonObject = JSON.parseObject(responseBody);
            // 获取 items 数组
            JSONArray items = jsonObject.getJSONArray("items");
            if (items != null && !items.isEmpty()) {
                // 遍历查找 name == "自动化开通" 的角色
                for (int i = 0; i < items.size(); i++) {
                    role = items.getJSONObject(i);
                    if (role != null && "自动化开通".equals(role.getString("name"))) {
                        return role;
                    }
                }
            }
        }
        return role;
    }

    /**
     * 用户分配
     */
    private boolean addUserToTenant(Long productId, String sid, String userId, String username) {
        boolean flag = false;
        // 1. 准备参数
        String requestUrl = String.format(url + "/apis/v1/tenants/%s/users?sid=%s", tenantId, sid);
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("id", userId);
        data.put("name", username);
        log.info("创建实例，请求url：{}， 参数：{}", requestUrl, JSON.toJSONString(data));
        HttpResult post = OkHttps.sync(requestUrl)
                .bodyType("JSON")
                .setBodyPara(data)
                .post();
        // 只读取一次 body
        String responseBody = post.getBody().toString();
        log.info("用户分配，响应数据：{}", responseBody);
        if (200 != post.getStatus()) {
            updateWorkOrder(productId, "用户分配异常：" + responseBody);
        } else {
            flag = true;
        }
        return flag;
    }

    /**
     * 租户授权
     */
    private boolean tenantGrant(Long productId, String sid, String userId, JSONObject role) {
        boolean flag = false;
        // 1. 准备参数
        String requestUrl = String.format(
                url + "/apis/v1/tenants/%s/users/%s/rolebindings?sid=%s",
                tenantId, userId, sid
        );
        // 2. 构造json
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("user", userId);
        data.put("roleId", role.getString("id"));
        data.put("roleName", role.getString("name"));
        data.put("tenant", "00000000-0000-0000-0000-000000000000");
        data.put("scopes", JSON.parseObject(role.getString("scopes")));
        log.info("创建实例，请求url：{}， 参数：{}", requestUrl, JSON.toJSONString(data));
        // 3. 发送请求
        HttpResult post = OkHttps.sync(requestUrl)
                .bodyType("JSON")
                .setBodyPara(data)
                .post();
        // 先提取响应体内容（只能调用一次）
        String responseBody = post.getBody().toString();
        log.info("租户授权，响应数据：{}", responseBody);
        if (200 != post.getStatus()) {
            updateWorkOrder(productId, "租户授权异常：" + responseBody);
        } else {
            flag = true;
        }
        return flag;
    }

    /**
     * 开通实例
     */
    private boolean createInstance(Long productId, BldRedisModel bldRedisModel, String sid, String businessSysId) {
        boolean flag = false;
        // 1. 构建URL
        String requestUrl = String.format(
                url + "/apis/v1/tenants/%s/business-systems/%s/middlewares/%s/middleware-instances?sid=%s",
                tenantId, businessSysId, "bes-cacheserver", sid
        );
        // 2. 构造json
        Map<String, Object> data = createInstanceMap(bldRedisModel, businessSysId);
        // 3. 发送请求
        log.info("创建实例，请求url：{}， 参数：{}", requestUrl, JSON.toJSONString(data));
        HttpResult post = OkHttps.sync(requestUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(data)
                .post();
        // 先提取响应体内容（只能调用一次）
        String responseBody = post.getBody().toString();
        log.info("创建实例，响应数据：{}", responseBody);
        if (200 != post.getStatus()) {
            updateWorkOrder(productId, "开通实例异常：" + responseBody);
        } else {
            flag = true;
        }
        return flag;
    }

    private Map<String, Object> createInstanceMap(BldRedisModel bldRedisModel, String businessSysId) {
        Map<String, Object> jsonMap = new LinkedHashMap<>();

        // dependences
        List<Map<String, String>> dependences = new ArrayList<>();
        Map<String, String> jdkDependence = new HashMap<>();
        jdkDependence.put("family", "JDK");
        jdkDependence.put("library", "openjdk-8-x64");
        dependences.add(jdkDependence);
        jsonMap.put("dependences", dependences);

        // businessLabels
        jsonMap.put("businessLabels", new HashMap<>());

        // basic fields
        jsonMap.put("businessSystem", businessSysId);
        jsonMap.put("tenantId", tenantId);
        jsonMap.put("middleware", "bes-cacheserve");
        jsonMap.put("version", "3.2.0");
        jsonMap.put("deployType", "non-container");
        jsonMap.put("serviceClass", "cluster-media");
        jsonMap.put("patches", new ArrayList<>());
        jsonMap.put("servicePlan", "16cbc82c-d1d7-4993-9771-45d5bf165f09");
        jsonMap.put("configTemplate", "f00852c3-c0ee-4ef8-b6d6-cbdfeb083451");
        jsonMap.put("center", "wgzx");
        jsonMap.put("cluster", bldRedisModel.getCpuArchitecture() + "-pool");
        jsonMap.put("name", bldRedisModel.getName());

        // params
        Map<String, Object> params = new HashMap<>();

        // params.base
        Map<String, Object> baseParams = new HashMap<>();
        baseParams.put("installAsUser", "root");
        baseParams.put("logMonitor", false);
        baseParams.put("nodeHome", "/opt/bes/cacheserver");
        baseParams.put("runAsUser", "bcs");
        baseParams.put("shareNum", 3);
        baseParams.put("slaveNum", 1);
        baseParams.put("subPath", true);
        baseParams.put("tz", "SYS.TIMEZONE");
        params.put("base", baseParams);

        // params.instance
        Map<String, String> instanceParams = new HashMap<>();
        instanceParams.put("rootPassword", bldRedisModel.getInstancePassword());
        params.put("instance", instanceParams);

        // params.limits
        Map<String, Integer> limits = new HashMap<>();
        limits.put("cpu", 0);
        limits.put("memory", 0);
        params.put("limits", limits);

        // params.requests
        Map<String, Integer> requests = new HashMap<>();
        requests.put("cpu", 0);
        requests.put("memory", 0);
        params.put("requests", requests);

        jsonMap.put("params", params);
        jsonMap.put("namespace", tenantName);

        // deployTopology
        Map<String, Object> deployTopology = new HashMap<>();
        Map<String, Object> nodes = new HashMap<>();

        // data list
        List<Map<String, Object>> dataList = new ArrayList<>();

        // node 0
        Map<String, Object> node0 = new HashMap<>();
        node0.put("seq", 0);
        List<Map<String, Object>> master0 = createNodeList(Arrays.asList(bldRedisModel.getIp()));
        List<Map<String, Object>> slave0 = createNodeList(Arrays.asList(bldRedisModel.getIp()));
        node0.put("master", master0);
        node0.put("slave", slave0);
        dataList.add(node0);

        // node 1
        Map<String, Object> node1 = new HashMap<>();
        node1.put("seq", 1);
        List<Map<String, Object>> master1 = createNodeList(Arrays.asList(bldRedisModel.getIp()));
        List<Map<String, Object>> slave1 = createNodeList(Arrays.asList(bldRedisModel.getIp()));
        node1.put("master", master1);
        node1.put("slave", slave1);
        dataList.add(node1);

        // node 2
        Map<String, Object> node2 = new HashMap<>();
        node2.put("seq", 2);
        List<Map<String, Object>> master2 = createNodeList(Arrays.asList(bldRedisModel.getIp()));
        List<Map<String, Object>> slave2 = createNodeList(Arrays.asList(bldRedisModel.getIp()));
        node2.put("master", master2);
        node2.put("slave", slave2);
        dataList.add(node2);

        nodes.put("data", dataList);
        deployTopology.put("nodes", nodes);
        jsonMap.put("deployTopology", deployTopology);
        return jsonMap;
    }

    private static List<Map<String, Object>> createNodeList(List<String> ips) {
        List<Map<String, Object>> nodeList = new ArrayList<>();
        for (String ip : ips) {
            Map<String, Object> nodeEntry = new HashMap<>();
            nodeEntry.put("seq", 0);

            Map<String, Object> extParams = new HashMap<>();
            Map<String, Object> ports = new HashMap<>();
            Map<String, Object> global = new HashMap<>();
            Map<String, Object> tls = new HashMap<>();
            tls.put("tlsport", null);
            global.put("tls", tls);
            ports.put("global", global);

            Map<String, Object> listener = new HashMap<>();
            listener.put("metricport", null);
            listener.put("port", null);
            ports.put("listener", listener);
            extParams.put("ports", ports);
            extParams.put("properties", new ArrayList<>());

            nodeEntry.put("extParams", extParams);
            nodeEntry.put("node", ip);
            nodeList.add(nodeEntry);
        }
        return nodeList;
    }

    private static String extractSidFromCookies(OkHttpClient client, String url) {
        HttpUrl httpUrl = HttpUrl.parse(url);
        List<Cookie> cookies = ((MyCookieJar) client.cookieJar()).loadForRequest(httpUrl);

        for (Cookie cookie : cookies) {
            if ("sid".equalsIgnoreCase(cookie.name())) {
                return cookie.value();
            }
        }
        return null;
    }

    static class MyCookieJar implements CookieJar {
        private final List<Cookie> cookies = new ArrayList<>();

        @Override
        public void saveFromResponse(HttpUrl url, List<Cookie> cookies) {
            this.cookies.addAll(cookies);
        }

        @Override
        public List<Cookie> loadForRequest(HttpUrl url) {
            return cookies;
        }
    }

    /**
     * 加解密
     */
    public String invertEncrypt(String str) {
        StringBuilder encrypted = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            encrypted.append((char) (~c & 0xFF));
        }
        return encrypted.toString();
    }

    private void updateWorkOrder(Long id, String errorMessage) {
        StandardWorkOrderProductDTO updateDto = new StandardWorkOrderProductDTO();
        updateDto.setId(id);
        updateDto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
        updateDto.setMessage(errorMessage);
        productManager.update(updateDto);
        productManager.updateStatusByParentId(id, ResOpenEnum.OPEN_FAIL.getCode());
    }

    private void saveResource(String workOrderId, BldRedisModel bldRedisModel) {
        StandardWorkOrderDTO standardWorkOrderDTO = standardWorkOrderManager.getById(workOrderId);
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setType(ProductTypeEnum.BLD_REDIS.getCode());
        resourceDetailDTO.setOrderId(standardWorkOrderDTO.getId());
        resourceDetailDTO.setOrderCode(standardWorkOrderDTO.getOrderCode());
        resourceDetailDTO.setBillId(standardWorkOrderDTO.getBillId());
        resourceDetailDTO.setDeviceId(IdUtil.simpleUUID());
        resourceDetailDTO.setDeviceName(bldRedisModel.getName());
        resourceDetailDTO.setTenantId(standardWorkOrderDTO.getTenantId());
        resourceDetailDTO.setTenantName(standardWorkOrderDTO.getTenantName());
        resourceDetailDTO.setBusinessSysId(standardWorkOrderDTO.getBusiSystemId());
        resourceDetailDTO.setBusinessSysName(standardWorkOrderDTO.getBusinessSystemName());
        resourceDetailDTO.setDomainCode(standardWorkOrderDTO.getDomainCode());
        resourceDetailDTO.setDomainName(standardWorkOrderDTO.getDomainName());
        resourceDetailDTO.setCloudPlatform(standardWorkOrderDTO.getDomainName());
        resourceDetailDTO.setApplyUserName(standardWorkOrderDTO.getCreatedUserName());
        resourceDetailDTO.setResourcePoolId(String.valueOf(bldRedisModel.getRegionId()));
        resourceDetailDTO.setResourcePoolCode(bldRedisModel.getRegionCode());
        resourceDetailDTO.setResourcePoolName(bldRedisModel.getRegionName());
        //实例ip
        resourceDetailDTO.setIp(bldRedisModel.getIp());
        //cpu架构存到spec
        resourceDetailDTO.setSpec(bldRedisModel.getCpuArchitecture());
        //自己的业务系统名称存到frequency
        resourceDetailDTO.setFrequency(bldRedisModel.getBusinessSysName());
        //用户名存到capacity
        resourceDetailDTO.setCapacity(bldRedisModel.getUsername());
        //密码存到storeType
        resourceDetailDTO.setStoreType(bldRedisModel.getPassword());
        resourceDetailDTO.setCreateTime(LocalDateTime.now());
        resourceDetailDTO.setResourceApplyTime(standardWorkOrderDTO.getCreateTime());
        resourceDetailDTO.setApplyTime(bldRedisModel.getApplyTime());
        resourceDetailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), bldRedisModel.getApplyTime()));
        resourceDetailDTO.setEffectiveTime(LocalDateTime.now());
        resourceDetailManager.batchSaveResourceDetail(ListUtil.toList(resourceDetailDTO));
    }

    private void sendSms(StandardWorkOrderProductDTO productDTO) {
        StandardWorkOrderDTO workOrder = standardWorkOrderManager.getById(productDTO.getWorkOrderId());
        BldRedisModel bldRedisModel = JSON.parseObject(productDTO.getPropertySnapshot(), BldRedisModel.class);
        SmsSendModel smsSendModel = new SmsSendModel();
        smsSendModel.setUserId(workOrder.getCreateUserId());
        smsSendModel.setOrderType("bld_redis_open_success");
        smsSendModel.setOrderCode(workOrder.getOrderCode());
        smsSendModel.setOrderTitle(workOrder.getOrderTitle());
        smsSendModel.setTenantId(workOrder.getTenantId());
        smsSendModel.setPhone(passPhone);
        smsSendModel.setOrderTypeCn(OrderTypeEnum.SUBSCRIBE.getMessage());
        smsSendModel.setUsername(bldRedisModel.getUsername());
        smsSendModel.setPassword(bldRedisModel.getPassword());
        smsSendModel.setInstancePassword(bldRedisModel.getInstancePassword());
        smsProducer.sendMessage(workOrder.getId(), smsSendModel);
    }
}
