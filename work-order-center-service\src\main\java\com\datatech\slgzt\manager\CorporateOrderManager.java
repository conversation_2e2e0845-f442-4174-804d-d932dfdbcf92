package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.CorporateOrderDTO;
import com.datatech.slgzt.model.query.CorporateOrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface CorporateOrderManager {

    List<CorporateOrderDTO> list(CorporateOrderQuery query);

    PageResult<CorporateOrderDTO> page(CorporateOrderQuery query);

    String insert(CorporateOrderDTO dto);

    void update(CorporateOrderDTO dto);

    void delete(String id);

    CorporateOrderDTO getById(String id);

    CorporateOrderDTO getByOrderCode(String orderCode);
} 