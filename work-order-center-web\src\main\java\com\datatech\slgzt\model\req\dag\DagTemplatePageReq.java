package com.datatech.slgzt.model.req.dag;

import lombok.Data;

import javax.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * DAG模板分页查询请求
 */
@Data
public class DagTemplatePageReq {
    
    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;
    
    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 创建人ID
     */
    private String creatorId;

    private String creator;

    //createTimeEnd&createTimeStart
    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;


    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;
} 