package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.xieyun.XieyunProjectQuotaOpm;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@Accessors(chain = true)
public class XieYunProjectDTO {

    private String id;

    /**
     * 谐云项目id
     */
    private String xieYunProjectId;

    /**
     * 谐云组织id
     */
    private String xieYunOrgId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 节点名称
     */
    private String nodePoolName;

    /**
     * 资源数据
     */
    private List<XieyunProjectQuotaOpm> xieyunProjectQuotaOpmList;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * cpu值
     */
    private String cpuValue;

    /**
     * 内存值
     */
    private String memoryValue;
}

