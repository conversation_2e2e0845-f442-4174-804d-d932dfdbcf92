package com.datatech.slgzt.impl.device;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.DeviceGpuInfoConvert;
import com.datatech.slgzt.enums.DeviceMetricEnum;
import com.datatech.slgzt.enums.DeviceMetricSourceEnum;
import com.datatech.slgzt.enums.DeviceModelTFEnum;
import com.datatech.slgzt.model.BaseDevicePhysicalInfoModel;
import com.datatech.slgzt.model.BaseMetricInfoModel;
import com.datatech.slgzt.model.BaseVdevice;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.service.device.DeviceGupInfoService;
import com.datatech.slgzt.service.device.DeviceGupMetricsService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.ejlchina.data.Mapper;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DeviceQdGpuMetricsServiceImpl implements DeviceGupMetricsService {

    @Value("${http.gpuDeviceMetricUrl}")
    private String gpuDeviceMetricUrl;
    @Value("${http.gpuDeviceResourceUrl}")
    private String vgpuDeviceMetricUrl;

    @Value("${http.gpuDeviceResourceUrl}")
    private String gpuDeviceResourceUrl;

    @Resource
    private DeviceGpuInfoConvert deviceGpuInfoConvert;
    @Resource
    private DeviceGupInfoService deviceGupInfoService;


    /**
     * 统计物理显卡指标数据
     * @return
     */
    @Override
    public void syncRemoteDeviceDataInfo(){
//        log.info("开始同步GPU物理显卡数据");
//        List<DeviceGpuInfoDTO> deviceGpuInfoList = queryResourceGpuDevices();
//        Map<String, List<DeviceGpuInfoDTO>> gpuPhysicalMap = new HashMap<>();
//        gpuPhysicalMap.put(DeviceMetricSourceEnum.PHYSICAL_SOURCE.getCode(),deviceGpuInfoList);
    }






    /**
     * 计算物理显卡指标
     */
    @Override
    public List<DeviceCardMetricsDTO> calculateGpuDeviceMetric(){
        String url  = gpuDeviceMetricUrl + "/api/v1/query";
        //获取所有的物理卡信息
        List<DeviceGpuInfoDTO> localDeviceInfoList = deviceGupInfoService.queryLocalDeviceInfos(new DeviceInfoQuery().setSourceType(DeviceMetricSourceEnum.QD_PHYSICAL_SOURCE.getCode()));
        //过滤掉设备id为空的数据
        localDeviceInfoList = localDeviceInfoList.stream()
                                                 .filter(item -> StringUtils.isNotBlank(item.getDeviceId()))
                                                 .collect(Collectors.toList());
        //算力利用率
        Map<String, BaseMetricInfoModel> gupMetricUsagePercent = queryRemoteDeviceMetric(url,DeviceMetricEnum.DCGM_FI_DEV_GPU_UTIL);
        //显存利用率
        Map<String, BaseMetricInfoModel> memoryMetricMemoryUsagePercent = queryRemoteDeviceMetric(url,DeviceMetricEnum.DCGM_FI_DEV_MEM_COPY_UTIL);
        //算力能耗top
        Map<String, BaseMetricInfoModel> baseMetricPowerUsagePercent = queryRemoteDeviceMetric(url,DeviceMetricEnum.DCGM_FI_DEV_POWER_USAGE);
        //获取GPU的温度
        Map<String, BaseMetricInfoModel> baseTemperatureMetricInfo = queryRemoteDeviceMetric(url, DeviceMetricEnum.DCGM_FI_DEV_GPU_TEMP);

        String baseurl = vgpuDeviceMetricUrl + "/v2/vdevices";
        List<BaseVdevice> baseVdevices = queryVdeviceList(baseurl);
        //获取物理卡数量
        Map<String, List<BaseVdevice>> devicePhysicallMap = baseVdevices.stream().collect(Collectors.groupingBy(BaseVdevice::getDeviceId));
        List<DeviceCardMetricsDTO> deviceCardMetrics = new ArrayList<>();
        //创建一个监控指标
        localDeviceInfoList.forEach(deviceGupnfoDTO -> {
            //设备id存在以逗号分隔的情况,处理下循环
            List<String> deviceIdList= Splitter.on(",").splitToList(deviceGupnfoDTO.getDeviceId());
            for (String deiviceId : deviceIdList) {
                DeviceCardMetricsDTO deviceMetricsDTO = new DeviceCardMetricsDTO();
                deviceMetricsDTO.setDeviceId(deiviceId);
                //当前时间记录到小时
                deviceMetricsDTO.setGpuTime(DateUtils.currentHalf("yyyyMMddHH"));
                deviceMetricsDTO.setBusinessSystemName(deviceGupnfoDTO.getBusinessSystemName());
                //算力利用率
                BaseMetricInfoModel gupUsagePercent = gupMetricUsagePercent.get(deiviceId);

                //显存利用率
                BaseMetricInfoModel memoryUsagePercent = memoryMetricMemoryUsagePercent.get(deiviceId);
                BaseMetricInfoModel powerUsagePercent = baseMetricPowerUsagePercent.get(deiviceId);
                BaseMetricInfoModel temperatureMetric = baseTemperatureMetricInfo.get(deiviceId);
                //算力利用率
                if(temperatureMetric !=null && StringUtils.isNotBlank(temperatureMetric.getValue())){
                    deviceMetricsDTO.setDevGpuTemp(Double.valueOf(temperatureMetric.getValue()));
                }
                deviceMetricsDTO.setModelName(DeviceModelTFEnum.getByModelName(deviceGupnfoDTO.getModelName()).getModelName());
                //显存利用率
                if(gupUsagePercent !=null && StringUtils.isNotBlank(gupUsagePercent.getValue())){
                    deviceMetricsDTO.setGpuUtilPercent(Double.valueOf(gupUsagePercent.getValue()));
                }
                if(powerUsagePercent !=null && StringUtils.isNotBlank(powerUsagePercent.getValue())){
                    deviceMetricsDTO.setDevPowerUsage(Double.valueOf(powerUsagePercent.getValue()));
                }
                deviceMetricsDTO.setMetricSource(DeviceMetricSourceEnum.QD_PHYSICAL_SOURCE.getCode());

                //显存利用率
                if(memoryUsagePercent !=null && StringUtils.isNotBlank(memoryUsagePercent.getValue())){
                    deviceMetricsDTO.setMemUtilpercent(Double.valueOf(memoryUsagePercent.getValue()));
                    //显存大小（GB）
                    BigDecimal bigDecimal = new BigDecimal(memoryUsagePercent.getValue());
                    // 总内存为 15360 MB，换算成 GB：15360 / 1024 = 15
                    BigDecimal totalMemory = new BigDecimal(deviceGupnfoDTO.getMemory());
                    // 计算：usedMemory = totalMemory * (usagePercent / 100)
                    BigDecimal hundred = new BigDecimal("100");
                    BigDecimal usedMemory = totalMemory.multiply(bigDecimal.divide(hundred, 2, BigDecimal.ROUND_HALF_UP));
                    deviceMetricsDTO.setMemoryUsage(usedMemory);
                }

                Optional.ofNullable(devicePhysicallMap.get(deiviceId)).map(List::size)
                        .ifPresent(deviceMetricsDTO::setAllocationCount);
                //区域编码
                deviceMetricsDTO.setAreaCode(deviceGupnfoDTO.getAreaCode());
                deviceMetricsDTO.setDeviceType(deviceGupnfoDTO.getDeviceType());
                deviceCardMetrics.add(deviceMetricsDTO);
            }

        });
        return deviceCardMetrics;
    }

    /**
     * 获取物理显卡信息
     * @return
     */
    public List<DeviceGpuInfoDTO> queryResourceGpuDevices(){
        String url = gpuDeviceResourceUrl+ "/v2/devices";
        //获取所有的设备数据
        Mapper responseMapper = OkHttpsUtils.http().sync(url)
                .get()
                .getBody()
                .toMapper();
        String code = responseMapper.getString("code");
        List<DeviceGpuInfoDTO> deviceGpuInfoList = new ArrayList<>();
        Integer sucessCode = CommonResult.success().getCode();
        if(sucessCode.equals(Integer.valueOf(code))){
            List<JSONObject> devices = Optional.ofNullable(responseMapper.getString("data"))
                    .map(JSONObject::parseObject)
                    .map(json -> json.getString("items"))
                    .map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(JSONObject.class))
                    .orElse(Collections.emptyList());

            devices.forEach(device ->{
                JSONObject deviceMeta = JSONObject.parseObject(device.getString("DeviceMeta"));
                log.info("设备数据：{}",deviceMeta);
                BaseDevicePhysicalInfoModel deviceBaseModel = JSONObject.parseObject(device.getString("DeviceMeta"), BaseDevicePhysicalInfoModel.class);
                DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGpuInfoConvert.baseInfo2Dto(deviceBaseModel);
                //设备分配状态字段
                deviceGpuInfoDTO.setInUsed(device.getString("InUsed"));
                int memory = Optional.ofNullable(device.getString("Memory")).map(Integer::valueOf)
                        .map(m ->m / 1024).orElse(0);
                deviceGpuInfoDTO.setMemory(memory);
                deviceGpuInfoList.add(deviceGpuInfoDTO);
            });
        }
        return deviceGpuInfoList;
    }
}
