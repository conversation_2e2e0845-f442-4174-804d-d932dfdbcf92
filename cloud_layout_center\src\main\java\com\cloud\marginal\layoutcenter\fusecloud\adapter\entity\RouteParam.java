package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

import java.util.List;

@Data
public class RouteParam {

    private Long tenantId;

    private String regionCode;

    private String routerName;

    private String description;

    private String externalNetworkId;

    private List<ExternalFixedIp> externalFixedIP;

    private List<RouteInfo> routerInfo;

    @Data
    public static  class  ExternalFixedIp{
        /**
         * 外部IP地址
         */
        private String externalIpAddress;

        /**
         * 外部子网标识
         */
        private String externalSubnetId;
    }

    @Data
    public static  class  RouteInfo{
        /**
         * 目的IP地址,路由目的地址CIDR
         * 同一张路由表内的不同路由条目的目标网段不能相同
         */
        private String destination;
        /**
         * 下一跳IP地址
         */
        private String nextHop;
        /**
         * Instance（默认值）：ECS实例。
         * HaVip：高可用虚拟IP。
         * RouterInterface：路由器接口。
         * NetworkInterface：弹性网卡。
         * VpnGateway：VPN网关。
         * IPv6Gateway：IPv6网关。
         * NatGateway：NAT网关。
         * Attachment：转发路由器。
         * VpcPeer：VPC对等连接
         */
        private String type;
        /**
         * 网络类型:
         * IPV4
         * IPV6
         */
        private String ipVersion;

        /**
         * 是否使能BFD检测，默认使能 O
         */
        private Boolean bfd;

        /**
         * 启用BFD时必选
         */
        private List<SourceIp> sourceIPs;

        @Data
        public static class SourceIp {

            private String sourceIP;
        }
    }
    /**
     * 计费号
     */
    private String billId;
}
