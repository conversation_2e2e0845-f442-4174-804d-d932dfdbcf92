package com.datatech.slgzt.model.bpmn;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Map;


/**
 * 启动流程实例vo
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RunProcessVo {

    /**
     * 流程定义key
     */
    @NotNull(message = "流程定义key不能为空")
    private String processKey;

    /**
     * 操作订单id
     */
    @NotNull(message = "操作订单id不能为空")
    private String orderId;

    /**
     * 操作人id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 架构管理员key:schema
     * 业务部门领导key:business
     * 云中心领导key:cloud
     * 运营key:operation
     * 租户key:tenant
     * 运营组/专业组(执行人员)key:cross
     */
    private Map<String, Object> assignee;


}
