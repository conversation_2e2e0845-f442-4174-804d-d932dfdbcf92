package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import com.datatech.slgzt.model.BaseDevicePhysicalInfoModel;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface DeviceGpuInfoConvert {




    /**
     * 显卡基本信息转换成dto
     * @param baseDevicePhysicalInfoModel
     * @return
     */
    DeviceGpuInfoDTO baseInfo2Dto(BaseDevicePhysicalInfoModel baseDevicePhysicalInfoModel);

    DeviceGpuInfoDTO do2Dto(DeviceGpuInfoDO deviceGpuInfoDO);

    /**
     * DTO 转换成DO
     * @param deviceGpuInfoDTO
     * @return
     */
    DeviceGpuInfoDO dto2do(DeviceGpuInfoDTO deviceGpuInfoDTO);


    DeviceGpuInfoDTO query2dto(DeviceInfoQuery deviceInfoQuery);


    DeviceInfoQuery dto2query(DeviceGpuInfoDTO deviceGpuInfoDTO);
}
