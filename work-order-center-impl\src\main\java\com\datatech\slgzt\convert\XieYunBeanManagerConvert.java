package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.container.*;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.xieyun.XieyunNamespaceCreateOpm;
import com.datatech.slgzt.model.xieyun.XieyunOrgQuotaOpm;
import com.datatech.slgzt.model.xieyun.XieyunProjectQuotaOpm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * @Author: suxin
 * @Date: 2025/4/14
 * @Description:
 */

@Mapper(componentModel = "spring")
public interface XieYunBeanManagerConvert {

    XieYunUserDTO userDO2DTO(XieYunUserDO userDO);

    XieYunUserDO userDTO2DO(XieYunUserDTO userDTO);

    List<XieYunUserDTO> userDOS2DTOS(List<XieYunUserDO> userDOS);

    @Mapping(target = "xieYunOrgQuotaOpmList", source = "xieYunOrgQuotaOpmList", qualifiedByName = "xieYunOrgQuotaOpmList")
    XieYunOrgDTO orgDO2DTO(XieYunOrgDO orgDO);

    @Mapping(target = "xieYunOrgQuotaOpmList", source = "xieYunOrgQuotaOpmList", qualifiedByName = "xieYunOrgQuotaOpmList")
    XieYunOrgDO orgDTO2DO(XieYunOrgDTO orgDTO);

    XieYunProjectDTO projectDO2DTO(XieYunProjectDO projectDO);

    XieYunProjectDO projectDTO2DO(XieYunProjectDTO projectDTO);

    XieYunNamespaceDTO namespaceDO2DTO(XieYunNamespaceDO namespaceDO);

    XieYunNamespaceDO namespaceDTO2DO(XieYunNamespaceDTO namespaceDTO);

    XieYunRepositoryDTO repositoryDO2DTO(XieYunRepositoryDO repositoryDO);

    XieYunRepositoryDO repositoryDTO2DO(XieYunRepositoryDTO repositoryDTO);

    XieYunClusterDTO clusterDO2DTO(XieYunClusterDO clusterDO);

    XieYunClusterDO clusterDTO2DO(XieYunClusterDTO clusterDTO);

    @Named("xieYunOrgQuotaOpmList")
    default String xieYunOrgQuotaOpmList(List<XieyunOrgQuotaOpm> orgQuotaOpmList) {
        return JSON.toJSONString(orgQuotaOpmList);
    }

    @Named("xieYunOrgQuotaOpmList")
    default List<XieyunOrgQuotaOpm> xieYunOrgQuotaOpmList(String xieYunOrgQuotaOpmList) {
        return JSON.parseArray(xieYunOrgQuotaOpmList, XieyunOrgQuotaOpm.class);
    }

    @Named("xieYunProjectQuotaOpmList")
    default String xieYunProjectQuotaOpmList(List<XieyunProjectQuotaOpm> projectQuotaOpmList) {
        return JSON.toJSONString(projectQuotaOpmList);
    }

    @Named("xieYunProjectQuotaOpmList")
    default List<XieyunProjectQuotaOpm> xieYunProjectQuotaOpmList(String projectQuotaOpmList) {
        return JSON.parseArray(projectQuotaOpmList, XieyunProjectQuotaOpm.class);
    }

    @Named("xieYunNamespaceCreateOpmList")
    default String xieYunNamespaceCreateOpmList(List<XieyunNamespaceCreateOpm> xieYunNamespaceCreateOpmList) {
        return JSON.toJSONString(xieYunNamespaceCreateOpmList);
    }

    @Named("xieYunNamespaceCreateOpmList")
    default List<XieyunNamespaceCreateOpm> xieYunNamespaceCreateOpmList(String xieYunNamespaceCreateOpmList) {
        return JSON.parseArray(xieYunNamespaceCreateOpmList, XieyunNamespaceCreateOpm.class);
    }

}
