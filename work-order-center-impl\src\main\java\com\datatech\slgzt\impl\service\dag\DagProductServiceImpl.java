package com.datatech.slgzt.impl.service.dag;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dag.DagProductService;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.opm.DagProductOpm;
import com.datatech.slgzt.model.tem.TemGlobalParamModel;
import lombok.SneakyThrows;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.JobOperator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 16:41:46
 */
@Service
public class DagProductServiceImpl implements DagProductService {


    @Resource
    private JobLauncher jobLauncher;

    @Resource
    private JobRegistry jobRegistry;

    @Resource
    private JobOperator jobOperator;

    //job历史执行器
    @Resource
    private JobExplorer jobExplorer;


    @SneakyThrows
    @Override
    public String createDagProduct(DagProductOpm dagProductOpm) {
        // 这里应该是从数据库根据workOrderId查询参数
        // 以下是示例参数
        JobParameters jobParameters = new JobParametersBuilder()
                // .addString("workOrderId", workOrderId)
                .addString("configId", dagProductOpm.getConfigId())
                .addLong("time", System.currentTimeMillis())
                .toJobParameters();
        return jobLauncher.run(jobRegistry.getJob("dagProductCreateJob"), jobParameters).getId().toString();
    }



}
