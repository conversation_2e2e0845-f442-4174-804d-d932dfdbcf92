package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.druid.sql.visitor.functions.Lcase;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.edge.TaskStatusEnum;
import com.cloud.marginal.enums.layout.OrderTypeEnum;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.enums.layout.StateEnum;
import com.cloud.marginal.layoutcenter.base.BaseService;
import com.cloud.marginal.layoutcenter.factory.LaoutServiceFactory;
import com.cloud.marginal.layoutcenter.fusecloud.utils.OrderCenterHttpRequestUtil;
import com.cloud.marginal.mapper.layout.ResourceDetailMapper;
import com.cloud.marginal.mapper.layout.TasksRelMapper;
import com.cloud.marginal.model.entity.layout.LayoutTask;
import com.cloud.marginal.model.entity.layout.LayoutTaskNode;
import com.cloud.marginal.model.vo.edge.ResourceDetailVO;
import com.cloud.marginal.model.vo.edge.SecurityGroupRuleVO;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import com.cloud.marginal.model.vo.layout.RelTaskVO;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单中心状态通知管理适配
 */
@Component
@Slf4j
public class OrderNoticeMgAdapter extends BaseNorthInterfaceAdapter implements BaseService {

    @Resource
    private OrderCenterHttpRequestUtil orderCenterHttpRequestUtil;

    @Resource
    private TasksRelMapper tasksRelMapper;

    @Resource
    private ResourceDetailMapper resourceDetailMapper;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    private static final String ORDER_NOTICE_URL = "v1/eom/order/callbackUpdateOrdType";

    /**
     * 工单中心回调接口
     */
    private static final String WORK_ORDER_NOTICE_URL = "v1/woc/portalcenter/nonStanderWorkOrderResOpen/layoutTaskNotify";
    private static final String WORK_ORDER_NOTICE_SUBSCRIBE_URL = "v1/woc/portalcenter/standardWorkOrderResOpen/layoutTaskNotify";
    /**
     * 外部开通资源回调接口
     */
    private static final String WORK_ORDER_EXTERNAL_SUBSCRIBE_URL = "v1/woc/portalcenter/vm/layoutTaskNotify";

    /**
     * 子订单
     */
    private static final int SUB_ORDER = 1;

    /**
     * 产品订单
     */
    private static final int PRODUCT_ORDER = 2;

    /**
     * 成功处理结果
     */
    private static final int HANDLE_RESULT_SUCCESS = 1;

    /**
     * 失败处理结果
     */
    private static final int HANDLE_RESULT_FAIL = 0;

    /**
     * 订单状态通知返回成功结果码
     */
    private static final String CEC_RESULT_SUCCESS = "200";

    /**
     * 回调工单中心topic
     */
    private static final String WORK_ORDER_LAYOUT_CALLBACK_TOPIC = "work_order_layout_callback_topic";

    /**
     * 资源信息详情topic
     */
    private final static String WORK_ORDER_TOPIC = "prod_oac_resource_detail_topic";

    /**
     * 资源操作topic
     */
    private final static String RESOURCE_OPERATE_TOPIC = "prod_resource_operate_topic";

    @Resource
    private LaoutServiceFactory laoutServiceFactory;

    @Override
    public TaskVO handler(LayoutTaskVO layoutTaskVO) {
        String id = layoutTaskVO.getId();
        Integer taskSource = layoutTaskVO.getTaskSource();
        switch (layoutTaskVO.getTaskCode()) {
            case "CREATE_NOTICE":
                return this.orderStatusNotice(id, taskSource);
            case "DELETE_NOTICE":
                return this.deleteOrderStatusNotice(id, taskSource);
            case "MODIFY_NOTICE":
                return this.modifyOrderStatusNotice(id, taskSource);
            case "MASK_NOTICE":
                return this.maskOrderStatusNotice(id, taskSource);
            default:
                throw new BusinessException("产品类型错误");
        }
    }

    /**
     * 订购订单状态通知
     *
     * @param taskId 主任务id或者产品通知任务id
     */
    public TaskVO orderStatusNotice(String taskId, Integer taskResource) {
        log.info("orderStatusNotice start taskId:{}, taskResource:{}",taskId,taskResource);
        return orderTaskCallbackVO(taskId, OrderTypeEnum.SUBSCRIBE.getCode(), taskResource, getCallbackUrl(taskResource));




    }

    /**
     * 退订订单状态通知
     *
     * @param taskId 主任务id或者产品通知任务id
     */
    public TaskVO deleteOrderStatusNotice(String taskId, Integer taskResource) {
        log.info("deleteOrderStatusNotice start");
        return orderTaskCallbackVO(taskId, OrderTypeEnum.UNSUBSCRIBE.getCode(), taskResource, getCallbackUrl(taskResource));

    }

    /**
     * 变更订单状态通知
     *
     * @param taskId 主任务id或者产品通知任务id
     */
    public TaskVO modifyOrderStatusNotice(String taskId, Integer taskResource) {
        log.info("modifyOrderStatusNotice start");
        return orderTaskCallbackVO(taskId, OrderTypeEnum.MODIFY.getCode(), taskResource, getCallbackUrl(taskResource));

    }

    /**
     * 主任务订单状态通知
     *
     * @param taskId 主任务id或者产品通知任务id
     */
    public TaskVO maskOrderStatusNotice(String taskId, Integer taskResource) {
        log.info("maskOrderStatusNotice start");
        List<RelTaskVO> relTaskVos = tasksRelMapper.selectRelTaskByTaskId(taskId);
        String orderType = null;
        for (RelTaskVO relTaskVo : relTaskVos) {
            if (relTaskVo.getTaskCode().contains("CREATE")) {
                orderType = OrderTypeEnum.SUBSCRIBE.getCode();
                break;
            }
            if (relTaskVo.getTaskCode().contains("DELETE")) {
                orderType = OrderTypeEnum.UNSUBSCRIBE.getCode();
                break;
            }
            if (relTaskVo.getTaskCode().contains("MODIFY")) {
                orderType = OrderTypeEnum.MODIFY.getCode();
                break;
            }
        }
        if (StringUtils.isEmpty(orderType)) {
            throw new BusinessException("主任务通知的订单类型未知");
        }

        return orderTaskCallbackVO(taskId, orderType, taskResource, getCallbackUrl(taskResource));
    }

    /**
     * 获取调用地址
     */
    private String getCallbackUrl(Integer taskResource) {
        String url ="";
        switch(taskResource) {
            case 1:
                url = ORDER_NOTICE_URL;
                break;
            case 2:
                url = WORK_ORDER_NOTICE_URL;
                break;
            case 3:
                url = WORK_ORDER_NOTICE_URL;
                break;
            case 4:
                url = WORK_ORDER_NOTICE_SUBSCRIBE_URL;
                break;
            case 5:
                url = WORK_ORDER_EXTERNAL_SUBSCRIBE_URL;
                break;
            default:
                break;
        }
        return url;
    }

    private TaskVO orderTaskCallbackVO(String taskId, String type, Integer taskSource, String url) {
        OrderStatusNoticeDto orderStatusNoticeDto = new OrderStatusNoticeDto();
        LayoutTaskNode productNoticeTask = layoutTaskNodeMapper.selectById(taskId);
        List<RelTaskVO> relTaskVos = tasksRelMapper.selectRelTaskByTaskId(taskId);
        boolean relTaskIsAllSuccess = relTaskIsAllSuccess(relTaskVos);
        if ("MASK_NOTICE".equals(productNoticeTask.getTaskCode())) {
            LayoutTask layoutTask = layoutTaskMapper.selectById(productNoticeTask.getMasterTaskId());
            String errorMessage = layoutTaskNodeMapper.getErrorMessageByMasterTaskId(productNoticeTask.getMasterTaskId());
            orderStatusNoticeDto.setOrderId(layoutTask.getSubOrderId());
            orderStatusNoticeDto.setOrderType(SUB_ORDER);
            orderStatusNoticeDto.setHandleResult(HANDLE_RESULT_FAIL);
            orderStatusNoticeDto.setMessage(errorMessage);
            orderStatusNoticeDto.setType(type);
        } else {
            orderStatusNoticeDto.setOrderId(relTaskVos.get(0).getOrderId());
            orderStatusNoticeDto.setOrderType(PRODUCT_ORDER);
            orderStatusNoticeDto.setHandleResult(HANDLE_RESULT_FAIL);
            orderStatusNoticeDto.setMessage(relTaskVos.get(0).getMessage());
            orderStatusNoticeDto.setResourceId(relTaskVos.get(0).getResourceId());
            orderStatusNoticeDto.setInstanceId(relTaskVos.get(0).getInstanceId());
            orderStatusNoticeDto.setType(type);

        }
        if (relTaskIsAllSuccess) {
            orderStatusNoticeDto.setHandleResult(HANDLE_RESULT_SUCCESS);
        }
        CecResult<String> cecResultCecResult = orderCenterHttpRequestUtil.postJson(taskSource, url, orderStatusNoticeDto, new TypeReference<CecResult<String>>() {
        });
        TaskVO taskVO = new TaskVO();
        LayoutTask mainTask = new LayoutTask();
        mainTask.setId(productNoticeTask.getMasterTaskId());
        mainTask.setUpdatedTime(new Date());
        if (cecResultCecResult.getCode().equals(CEC_RESULT_SUCCESS)) {
            taskVO.setStatus(TaskStatusEnum.SUCCESS.getCode());
            if ("MASK_NOTICE".equals(productNoticeTask.getTaskCode())) {
                if (relTaskIsAllSuccess) {
                    log.info("主任务通知回调结果：{}", cecResultCecResult);
                    // 修改主任务状态为成功
                    mainTask.setState(StateEnum.SUCCESS);
                    mainTask.setEndTime(new Date());
                    // 到这步说明任务已经执行完毕，封装资源信息发送到kafka
                    // 判断任务类型
                    QueryWrapper<LayoutTaskNode> wrapper = new QueryWrapper<>();
                    wrapper.eq("MASTER_TASK_ID", productNoticeTask.getMasterTaskId());
                    wrapper.eq("STATUS", 1);
                    List<LayoutTaskNode> layoutTaskNodes = layoutTaskNodeMapper.selectList(wrapper);

                    Map<String, List<LayoutTaskNode>> layoutTaskNodeMap = layoutTaskNodes.stream().collect(Collectors.groupingBy(LayoutTaskNode::getTaskCode));

                    //独立开通EVS
                    log.info("开始判断任务类型=======》");
                    if (!layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.ECS_CREATE.getCode())
                            && !layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.ECS_MODIFY.getCode())) {
                        if (layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.EVS_CREATE.getCode())) {
                            packageEvsResource(layoutTaskNodeMap.get(ProductOrderTypeEnum.EVS_CREATE.getCode()).get(0));
                        }
                        if (layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.EVS_MODIFY.getCode())) {
                            packageEvsResource(layoutTaskNodeMap.get(ProductOrderTypeEnum.EVS_MODIFY.getCode()).get(0));
                        }
                    }

                    //独立开通EIP
                    if (!layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.ECS_CREATE.getCode())
                            && !layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.ECS_MODIFY.getCode())
                            && !layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.SLB_CREATE.getCode())
                            && !layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.SLB_MODIFY_FLAVOR.getCode())
                            && !layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.NAT_CREATE.getCode())
                            && !layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.NAT_BIND_EIP.getCode())
                            && !layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.NAT_UNBIND_EIP.getCode())) {
                        if (layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.EIP_CREATE.getCode())) {
                            packageEipResource(layoutTaskNodeMap.get(ProductOrderTypeEnum.EIP_CREATE.getCode()).get(0));
                        }
                        if (layoutTaskNodeMap.containsKey(ProductOrderTypeEnum.EIP_MODIFY.getCode())) {
                            packageEipResource(layoutTaskNodeMap.get(ProductOrderTypeEnum.EIP_MODIFY.getCode()).get(0));

                        }
                    }

                    // 云主机创建，资源封装
                    for (LayoutTaskNode layoutTaskNode : layoutTaskNodes) {
                        log.info("任务类型为：{}", layoutTaskNode.getTaskCode());
                        if (ProductOrderTypeEnum.ECS_CREATE.getCode().equals(layoutTaskNode.getTaskCode())
                                || ProductOrderTypeEnum.ECS_MODIFY.getCode().equals(layoutTaskNode.getTaskCode())) {
                            packageEcsResource(layoutTaskNode);
                        }

                        if (ProductOrderTypeEnum.OBS_CREATE.getCode().equals(layoutTaskNode.getTaskCode())) {
                            packageObsResource(layoutTaskNode);
                        }

                        if (ProductOrderTypeEnum.NAT_CREATE.getCode().equals(layoutTaskNode.getTaskCode())) {
                            packageNatResource(layoutTaskNode);
                        }

                        if (ProductOrderTypeEnum.SLB_CREATE.getCode().equals(layoutTaskNode.getTaskCode())
                                || ProductOrderTypeEnum.SLB_MODIFY_FLAVOR.getCode().equals(layoutTaskNode.getTaskCode())) {
                            packageSlbResource(layoutTaskNode);
                        }
                    }
                } else {
                    // 修改主任务状态为失败
                    mainTask.setState(StateEnum.ERROR);
                }
                layoutTaskMapper.updateById(mainTask);
            }
        } else {
            taskVO.setStatus(TaskStatusEnum.ERROR.getCode());
            taskVO.setMessage(cecResultCecResult.getMessage());
            if ("MASK_NOTICE".equals(productNoticeTask.getTaskCode())) {
                // 修改主任务状态为失败
                mainTask.setState(StateEnum.ERROR);
                layoutTaskMapper.updateById(mainTask);
            }
        }
        productNoticeTask.setNoticeState(relTaskIsAllSuccess ? StateEnum.SUCCESS.getName() : StateEnum.ERROR.getName());
        layoutTaskNodeMapper.updateById(productNoticeTask);
        return taskVO;
    }


    /**
     * 判断所有关联任务是否都成功了
     *
     * @param relTaskVos 关联任务集合
     */
    private boolean relTaskIsAllSuccess(List<RelTaskVO> relTaskVos) {
        //排除掉通知类型 通知类型就是taskCode
        boolean result = true;
        if (relTaskVos != null) {
            for (RelTaskVO relTaskVO : relTaskVos) {
                if (!relTaskVO.getState().equals(StateEnum.SUCCESS.toString()) ||
                        StateEnum.ERROR.toString().equals(relTaskVO.getRelNoticeState())) {
                    result = false;
                }
            }
        }
        return result;
    }

    @Data
    private static class OrderStatusNoticeDto {

        /**
         * 订购类型  订购 变更 退订
         */
        private String type;

        /**
         * 订单类型 1：子订单，2：产品订单
         */
        private Integer orderType;

        /**
         * 订单id
         */
        private String orderId;

        /**
         * 云管资源中心id
         */
        private String resourceId;

        /**
         * 底层实例id
         */
        private String instanceId;

        /**
         * 处理结果 0：失败，1：成功
         */
        private Integer handleResult;

        /**
         * 返回消息
         */
        private String message;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        laoutServiceFactory.register("NOTICE", this);
    }

    /**
     * 封装云主机资源详情
     * @param layoutTaskNode
     */
    @SneakyThrows
    private void packageEcsResource(LayoutTaskNode layoutTaskNode) {
        log.info("开始封装云主机的信息");
        //判断任务是否全部成功，成功再进行以下处理，否则关联不到ip和数据盘
        //子任务订单id，与任务中心主任务表关联，查出商品单id
        String goodsOrderId = layoutTaskMapper.selectGoodsOrderIdBySubOrderId(layoutTaskNode.getOrderId());
        log.info("商品单id为：{}", goodsOrderId);
        List<String> cloudTaskIds = layoutTaskMapper.selectCloudTaskIdsBySubOrderId(goodsOrderId);
        log.info("资源信息任务id集合：{}", cloudTaskIds);
        if (CollectionUtil.isNotEmpty(cloudTaskIds)) {
            Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(cloudTaskIds);
            //等于0则代表任务全部成功，查询相关数据发送kafka
            log.info("未完成的任务数：=={}", notSuccessNum);
            if (notSuccessNum == 0) {
                //资源表MC_VM_T的id
                Thread.sleep(10000);
                String resourceId = layoutTaskNode.getResourceId();
                //资源详情
                ResourceDetailVO resourceDetailVO = resourceDetailMapper.selectResourceDetailOfEcs(resourceId);
                //查询下主任务
                LayoutTask layoutTask = layoutTaskMapper.selectById(layoutTaskNode.getMasterTaskId());
                resourceDetailVO.setSourceExtType(layoutTask.getSourceExtType());
                resourceDetailVO.setGoodsOrderId(goodsOrderId);
                resourceDetailVO.setType(ProductOrderTypeEnum.ECS.getCode().toLowerCase());
                kafkaTemplate.send(WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
            }
        }
    }

    /**
     * 封装EVS云硬盘资源详情
     * @param taskVO
     */
    private void packageEvsResource(LayoutTaskNode layoutTaskNode) {
        String orderId = layoutTaskNode.getOrderId();
        //资源表MC_VOLUME_T的id
        String resourceId = layoutTaskNode.getResourceId();
        //子任务订单id，与任务中心主任务表关联，查出商品单id
        String goodsOrderId = layoutTaskMapper.selectGoodsOrderIdBySubOrderId(orderId);
        if (StringUtils.isEmpty(goodsOrderId)) {
            return;
        }
        log.info("独立开通EVS,orderId={}, resourceId={}, goodsOrderId={}", orderId, resourceId, goodsOrderId);
        List<String> cloudTaskIds = layoutTaskMapper.selectCloudTaskIdsBySubOrderId(goodsOrderId);
        if (CollectionUtil.isNotEmpty(cloudTaskIds)) {
            Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(cloudTaskIds);
            //等于0则代表任务全部成功，查询相关数据发送kafka
            log.info("未完成的任务数：=={}", notSuccessNum);
            //判断任务是否全部成功，成功再进行以下处理
            if (notSuccessNum == 0) {
                //资源详情
                ResourceDetailVO resourceDetailVO = resourceDetailMapper.selectResourceDetailOfEvs(resourceId);
                if(resourceDetailVO != null) {
                    //查询下主任务
                    LayoutTask layoutTask = layoutTaskMapper.selectById(layoutTaskNode.getMasterTaskId());
                    resourceDetailVO.setSourceExtType(layoutTask.getSourceExtType());
                    resourceDetailVO.setGoodsOrderId(goodsOrderId);
                    resourceDetailVO.setType(ProductOrderTypeEnum.EVS.getCode().toLowerCase());
                    kafkaTemplate.send(WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                    log.info("kafka发送evs消息,topic={},resourceDetailVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                }
            }
        }
    }

    /**
     * 封装EIP云硬盘资源详情
     * @param layoutTaskNode
     */
    private void packageEipResource(LayoutTaskNode layoutTaskNode) {
        String orderId = layoutTaskNode.getOrderId();
        //资源表MC_VOLUME_T的id
        String resourceId = layoutTaskNode.getResourceId();
        //子任务订单id，与任务中心主任务表关联，查出商品单id
        String goodsOrderId = layoutTaskMapper.selectGoodsOrderIdBySubOrderId(orderId);
        if (StringUtils.isEmpty(goodsOrderId)) {
            return;
        }
        log.info("独立开通EIP,orderId={}, resourceId={}, goodsOrderId={}", orderId, resourceId, goodsOrderId);
        List<String> cloudTaskIds = layoutTaskMapper.selectCloudTaskIdsBySubOrderId(goodsOrderId);
        if (CollectionUtil.isNotEmpty(cloudTaskIds)) {
            Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(cloudTaskIds);
            //等于0则代表任务全部成功，查询相关数据发送kafka
            log.info("未完成的任务数：=={}", notSuccessNum);
            //判断任务是否全部成功，成功再进行以下处理
            if (notSuccessNum == 0) {
                //资源详情
                ResourceDetailVO resourceDetailVO = resourceDetailMapper.selectResourceDetailOfEip(resourceId);
                if(resourceDetailVO != null) {
                    //查询下主任务
                    LayoutTask layoutTask = layoutTaskMapper.selectById(layoutTaskNode.getMasterTaskId());
                    resourceDetailVO.setSourceExtType(layoutTask.getSourceExtType());
                    resourceDetailVO.setGoodsOrderId(goodsOrderId);
                    resourceDetailVO.setType(ProductOrderTypeEnum.EIP.getCode().toLowerCase());
                    kafkaTemplate.send(WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                    log.info("kafka发送eip消息,topic={},resourceDetailVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                }
            }
        }
    }

    /**
     * 封装OBS对象存储桶创建-资源详情
     * @param taskVO
     */
    @SneakyThrows
    private void packageObsResource(LayoutTaskNode layoutTaskNode) {
        log.info("处理OBS,packageObsResource,topic={},taskVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(layoutTaskNode));
        //查询商品单id
        String goodsOrderId = layoutTaskMapper.selectGoodsOrderIdBySubOrderId(layoutTaskNode.getOrderId());
        if(StringUtils.isEmpty(goodsOrderId)) {
            return;
        }
        List<String> cloudTaskIds = layoutTaskMapper.selectCloudTaskIdsBySubOrderId(goodsOrderId);
        if (CollectionUtil.isNotEmpty(cloudTaskIds)) {
            Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(cloudTaskIds);
            //等于0则代表任务全部成功，查询相关数据发送kafka
            log.info("未完成的任务数：=={}", notSuccessNum);
            if(notSuccessNum == 0) {
                Thread.sleep(10000);
                //资源表MC_OBS_T的id
                String resourceId = layoutTaskNode.getResourceId();
                //资源详情
                List<ResourceDetailVO> detailVOs = resourceDetailMapper.selectResourceDetailOfObs(resourceId);
                log.info("该obs下有{}条桶记录", detailVOs.size());
                //首次开通obs并创建桶，发送kafka
                ResourceDetailVO resourceDetailVO = new ResourceDetailVO();
                ResourceDetailVO detailVO = detailVOs.get(0);
                if(CollectionUtil.isNotEmpty(detailVOs) && detailVOs.size() ==1) {
                    resourceDetailVO = detailVO;
                    resourceDetailVO.setGoodsOrderId(goodsOrderId);
                    resourceDetailVO.setType(ProductOrderTypeEnum.OBS.getCode().toLowerCase());
                }
//                else {
//                    //不是首次开通，仅同步四个字段
//                    resourceDetailVO.setGoodsOrderId(goodsOrderId);
//                    resourceDetailVO.setType(ProductOrderTypeEnum.OBS.getCode().toLowerCase());
//                    resourceDetailVO.setAccessKey(detailVO.getAccessKey());
//                    resourceDetailVO.setSecretKey(detailVO.getSecretKey());
//                    resourceDetailVO.setPublicAddress(detailVO.getPublicAddress());
//                    resourceDetailVO.setInternalAddress(detailVO.getInternalAddress());
//                }
                //查询下主任务
                LayoutTask layoutTask = layoutTaskMapper.selectById(layoutTaskNode.getMasterTaskId());
                resourceDetailVO.setSourceExtType(layoutTask.getSourceExtType());
                kafkaTemplate.send(WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                log.info("kafka发送obs消息,topic={},resourceDetailVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
            }
        }
    }

    /**
     * 封装NAT网关资源详情
     * @param taskVO
     */
    private void packageNatResource(LayoutTaskNode layoutTaskNode) {
        log.info("处理NAT,packageNatResource,topic={},taskVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(layoutTaskNode));

        //查询商品单id
        String goodsOrderId = layoutTaskMapper.selectGoodsOrderIdBySubOrderId(layoutTaskNode.getOrderId());
        if(StringUtils.isEmpty(goodsOrderId)) {
            return;
        }

        List<String> cloudTaskIds = layoutTaskMapper.selectCloudTaskIdsBySubOrderId(goodsOrderId);
        if (CollectionUtil.isNotEmpty(cloudTaskIds)) {
            Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(cloudTaskIds);
            //等于0则代表任务全部成功，查询相关数据发送kafka
            log.info("未完成的任务数：=={}", notSuccessNum);
            if (notSuccessNum == 0) {
                //资源表MC_NAT_T的id
                String resourceId = layoutTaskNode.getResourceId();
                //资源详情
                ResourceDetailVO resourceDetailVO = resourceDetailMapper.selectResourceDetailOfNat(resourceId);
                if(resourceDetailVO != null) {
                    //查询下主任务
                    LayoutTask layoutTask = layoutTaskMapper.selectById(layoutTaskNode.getMasterTaskId());
                    resourceDetailVO.setSourceExtType(layoutTask.getSourceExtType());
                    resourceDetailVO.setGoodsOrderId(goodsOrderId);
                    resourceDetailVO.setType(ProductOrderTypeEnum.NAT.getCode().toLowerCase());
                    kafkaTemplate.send(WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                    log.info("kafka发送nat消息,topic={},resourceDetailVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                }
            }
        }
    }

    /**
     * 封装SLB负载均衡资源详情
     * @param taskVO
     */
    private void packageSlbResource(LayoutTaskNode layoutTaskNode) {
        log.info("处理SLB,topic={},taskVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(layoutTaskNode));

        //查询商品单id
        String goodsOrderId = layoutTaskMapper.selectGoodsOrderIdBySubOrderId(layoutTaskNode.getOrderId());
        if(StringUtils.isEmpty(goodsOrderId)) {
            return;
        }

        List<String> cloudTaskIds = layoutTaskMapper.selectCloudTaskIdsBySubOrderId(goodsOrderId);
        if (CollectionUtil.isNotEmpty(cloudTaskIds)) {
            Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(cloudTaskIds);
            //等于0则代表任务全部成功，查询相关数据发送kafka
            log.info("未完成的任务数：=={}", notSuccessNum);
            //判断任务是否全部成功，成功再进行以下处理
            if (notSuccessNum == 0) {
                //资源表MC_SLB_T的id
                String resourceId = layoutTaskNode.getResourceId();
                //资源详情
                ResourceDetailVO resourceDetailVO = resourceDetailMapper.selectResourceDetailOfSlb(resourceId);
                if(resourceDetailVO != null) {
                    //查询下主任务
                    LayoutTask layoutTask = layoutTaskMapper.selectById(layoutTaskNode.getMasterTaskId());
                    resourceDetailVO.setSourceExtType(layoutTask.getSourceExtType());
                    resourceDetailVO.setGoodsOrderId(goodsOrderId);
                    resourceDetailVO.setType(ProductOrderTypeEnum.SLB.getCode().toLowerCase());
                    kafkaTemplate.send(WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                    log.info("kafka发送slb消息,topic={},resourceDetailVO={}", WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                }
            }
        }
    }

    /**
     * 封装安全组规则资源详情
     */
    public String packageSecurityRuleResource(String resourceId) {
        SecurityGroupRuleVO securityGroupRuleVO = resourceDetailMapper.selectSecurityRuleById(resourceId);
        return JSONObject.toJSONString(securityGroupRuleVO);
    }

}
