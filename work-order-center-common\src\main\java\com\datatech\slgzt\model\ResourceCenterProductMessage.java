package com.datatech.slgzt.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 */
@Data
@NoArgsConstructor
public class ResourceCenterProductMessage{

    /**
     * 任务id
     */
    private String id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 状态： 成功：SUCCESS 失败: ERROR 执行中: EXECUTION
     */
    private String status;
    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 绑定的资源id
     */
    private String attachResourceId;

    /**
     * 底层实例id
     */
    private String instanceId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 任务失败时的消息
     */
    private String message;


    /**
     * 资源类型
     */
    private String resourceType;

    private String operationType;

    /**
     * ip列表
     */
    private List<IpInfo> ipList;

    @Data
    public static class IpInfo {
        /**
         * ip类型
         */
        private String type;
        /**
         * ip地址
         */
        private String ipAddress;
    }

}