package com.datatech.slgzt.enums;

import lombok.Getter;

@Getter
public enum ResRecoveryEnum {
    //待回收
    WAIT_RECOVERY("wait_recovery", "待回收"),
    //回收中
    RECOVERYING("recoverying", "回收中"),
    //回收成功
    RECOVERY_SUCCESS("recovery_success", "回收成功"),
    //回收失败
    RECOVERY_FAIL("recovery_fail", "回收失败"),



    ;

    private final String code;

    private final String message;

    ResRecoveryEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


}
