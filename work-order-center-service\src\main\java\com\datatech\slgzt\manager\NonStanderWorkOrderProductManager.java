package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.model.resourcce.ResourceShowInfoDTO;

import java.util.List;

public interface NonStanderWorkOrderProductManager {

    List<NonStanderWorkOrderProductDTO> list(NonStanderWorkOrderProductQuery query);


    /**
     * update
     */
    void update(NonStanderWorkOrderProductDTO dto);


    NonStanderWorkOrderProductDTO getById(Long id);

    NonStanderWorkOrderProductDTO getByGid(String gid);

    NonStanderWorkOrderProductDTO getBySubOrderId(Long subOrderId);

    void deleteByWorkOrderId(String id);


    /**
     * insert
     */
    void insert(NonStanderWorkOrderProductDTO dto);

    void updateStatusById(Long id, String status);


    void updateStatusByParentId(Long id, String status);



    ResourceShowInfoDTO selectResourceOverview(NonStanderWorkOrderProductQuery productQuery);
}
