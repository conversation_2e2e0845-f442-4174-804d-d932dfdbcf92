package com.datatech.slgzt.impl.service.standard;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.config.FiProperties;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.KafkaModel;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: workordercenterproject
 * @description: kafka开通（创建topic）
 * @author: LK
 * @create: 2025-06-23 14:34
 **/
@Service
@Slf4j
public class StandardResKafkaOpenServiceImpl implements StandardResOpenService {

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private FiProperties fiProperties;


    @SneakyThrows
    @Override
    public void openStandardResource(StandardWorkOrderProductDTO productDTO) {
        KafkaModel kafkaModel = JSON.parseObject(productDTO.getPropertySnapshot(), KafkaModel.class);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("topic", kafkaModel.getName());
        paramMap.put("numPartitions", kafkaModel.getPartition());
        paramMap.put("replactionFactor", kafkaModel.getReplication());
        paramMap.put("retentionMs", kafkaModel.getRetainTime() * 24 * 60 * 60 * 1000);
        setDefaultValues(paramMap);
        // 更新产品状态为开通中
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        Mapper dataMapper = OkHttpsUtils.httpIgnoreHttpsCertificate(120)
                .sync(fiProperties.getRegionCode2KafkaCreateUrlMap().get(kafkaModel.getRegionCode()))
                .bodyType(OkHttps.JSON)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization",
                        "Basic " + Base64.getEncoder().encodeToString((fiProperties.getUsername() + ":" + fiProperties.getPassword()).getBytes()))
                .setBodyPara(JSON.toJSONString(paramMap))
                .post()
                .getBody()
                .toMapper();
        String code = dataMapper.getString("code");
        if ("-1".equals(code)) {
            log.error("资源开通失败：{}", dataMapper.getString("errorInfo"));
            StandardWorkOrderProductDTO updateDto = new StandardWorkOrderProductDTO();
            updateDto.setId(productDTO.getId());
            updateDto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
            updateDto.setMessage(dataMapper.getString("errorInfo"));
            productManager.update(updateDto);
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_FAIL.getCode());
        } else if ("0".equals(code)) {
            productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
            //产品入库操作
            saveResource(productDTO.getWorkOrderId(), kafkaModel);
        }
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.KAFKA;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
    
    private void setDefaultValues(Map<String, Object> paramMap) {
        paramMap.put("retentionBytes", "-1");
        paramMap.put("maxMessageBytes", "100001200");
        paramMap.put("segmentIndexBytes", "10485760");
        paramMap.put("segmentBytes", "1073741824");
        paramMap.put("minCleanableDirtyRatio", "0.5");
        paramMap.put("minInsyncReplicas", "1");
        paramMap.put("deleteRetentionMs", "86400000");
        paramMap.put("preallocate", false);
        paramMap.put("cleanupPolicy", "delete");
        paramMap.put("fileDeleteDelayMs", "60000");
        paramMap.put("segmentJitterMs", "0");
        paramMap.put("indexIntervalBytes", "4096");
        paramMap.put("compressionType", "producer");
        paramMap.put("segmentMs", "604800000");
        paramMap.put("uncleanLeaderElectionEnable", true);
    }

    private void saveResource(String workOrderId, KafkaModel kafkaModel) {
        StandardWorkOrderDTO standardWorkOrderDTO = standardWorkOrderManager.getById(workOrderId);
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setType(ProductTypeEnum.KAFKA.getCode());
        resourceDetailDTO.setOrderId(standardWorkOrderDTO.getId());
        resourceDetailDTO.setOrderCode(standardWorkOrderDTO.getOrderCode());
        resourceDetailDTO.setBillId(standardWorkOrderDTO.getBillId());
        resourceDetailDTO.setDeviceId(IdUtil.simpleUUID());
        resourceDetailDTO.setDeviceName(kafkaModel.getName());
        resourceDetailDTO.setTenantId(standardWorkOrderDTO.getTenantId());
        resourceDetailDTO.setTenantName(standardWorkOrderDTO.getTenantName());
        resourceDetailDTO.setBusinessSysId(standardWorkOrderDTO.getBusiSystemId());
        resourceDetailDTO.setBusinessSysName(standardWorkOrderDTO.getBusinessSystemName());
        resourceDetailDTO.setDomainCode(standardWorkOrderDTO.getDomainCode());
        resourceDetailDTO.setDomainName(standardWorkOrderDTO.getDomainName());
        resourceDetailDTO.setCloudPlatform(standardWorkOrderDTO.getDomainName());
        resourceDetailDTO.setApplyUserName(standardWorkOrderDTO.getCreatedUserName());
        resourceDetailDTO.setResourcePoolId(String.valueOf(kafkaModel.getRegionId()));
        resourceDetailDTO.setResourcePoolCode(kafkaModel.getRegionCode());
        resourceDetailDTO.setResourcePoolName(kafkaModel.getRegionName());
        //数据流量存到spec字段里
        resourceDetailDTO.setSpec(kafkaModel.getDataFlow() + "MB/天");
        //副本存到设备状态字段里
        resourceDetailDTO.setDeviceStatus(String.valueOf(kafkaModel.getReplication()));
        //保留时间存到频率字段里
        resourceDetailDTO.setFrequency(kafkaModel.getRetainTime() + "天");
        //数据存储总量存到容量字段里
        resourceDetailDTO.setCapacity(kafkaModel.getDataStorageTotal() + "GB");
        resourceDetailDTO.setCreateTime(LocalDateTime.now());
        resourceDetailDTO.setResourceApplyTime(standardWorkOrderDTO.getCreateTime());
        resourceDetailDTO.setApplyTime(kafkaModel.getApplyTime());
        resourceDetailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), kafkaModel.getApplyTime()));
        resourceDetailDTO.setEffectiveTime(LocalDateTime.now());
        resourceDetailManager.batchSaveResourceDetail(ListUtil.toList(resourceDetailDTO));
    }
}
