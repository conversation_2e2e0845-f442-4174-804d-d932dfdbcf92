package com.datatech.slgzt.dao.model.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/24
 */

@Data
@TableName("WOC_DG_RECOVERY_ORDER_PRODUCT")
public class DgRecoveryOrderProductDO {

    //产品id
    @TableField("ID")
    private Long id;

    //工单Id
    @TableField("WORK_ORDER_ID")
    private String workOrderId;

    /**
     * 产品类型
     * esc
     * gsc
     * eip 等
     */
    @TableField("PRODUCT_TYPE")
    private String productType;


    //属性快照
    @TableField("PROPERTY_SNAPSHOT")
    private String propertySnapshot;

    //父类产品id 可以为空
    @TableField("PARENT_PRODUCT_ID")
    private Long parentProductId;

    /**
     * gid
     */
    @TableField("GID")
    private String gid;

    /**
     * gid
     */
    @TableField("SUB_ORDER_ID")
    private Long subOrderId;

    //回收状态
    @TableField("RECOVERY_STATUS")
    private Integer recoveryStatus;

    //消息
    @TableField("MESSAGE")
    private String message;

    //退维状态
    @TableField("HCM_STATUS")
    private String hcmStatus;

    @TableLogic(value = "1", delval = "0")
    @TableField("ENABLED")
    private Boolean enabled;

    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    @TableField("EXT")
    private String ext;

    //资源详情id
    @TableField("RESOURCE_DETAIL_ID")
    private String resourceDetailId;


    /**
     * cmdbId
     */
    @TableField("CMDB_ID")
    private String cmdbId;


    /**
     * TENANT_CONFIRM
     */
    @TableField("TENANT_CONFIRM")
    private Boolean tenantConfirm;

    /**
     * 是否需要同步回收
     */
    @TableField("SYNC_RECOVERY")
    private Boolean syncRecovery;
}

