package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维护类型枚举
 */
@Getter
@AllArgsConstructor
public enum MaintenanceTypeEnum {
    
    UPGRADE("升级"),
    BACKUP("备份"),
    RESTORE("恢复");
    
    private final String desc;
    
    public static MaintenanceTypeEnum getByName(String name) {
        for (MaintenanceTypeEnum type : values()) {
            if (type.name().equals(name)) {
                return type;
            }
        }
        return null;
    }
}