package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.CustomCountDTO;
import com.datatech.slgzt.model.dto.CustomDTO;
import com.datatech.slgzt.model.dto.CustomTenantDTO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import com.datatech.slgzt.model.query.CustomQuery;
import com.datatech.slgzt.model.req.custom.CustomPageReq;
import com.datatech.slgzt.model.vo.managerView.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-02 14:58
 **/
@Mapper(componentModel = "spring")
public interface ManagerViewConvert {

    CustomCountVO convert(CustomCountDTO customCountDTO);

    List<CustomTenantVO> convertCustomTenants(List<CustomTenantDTO> customTenantDTOS);

    @Mapping(target = "cpuUsePercent", expression = "java(dto.getCkCpuUtil().setScale(2, java.math.RoundingMode.DOWN))")
    @Mapping(target = "memUsePercent", expression = "java(dto.getCkMemUtil().setScale(2, java.math.RoundingMode.DOWN))")
    CustomCPUVO convert(VMResourcePerformanceDTO dto);

    @Mapping(target = "deviceType", source = "resourceDetailType")
    @Mapping(target = "topPercent", expression = "java(dto.getTopPercent().setScale(2, java.math.RoundingMode.DOWN))")
    ResourceVO convertResource(VMResourcePerformanceDTO dto);

    CustomVO convert(CustomDTO dto);

    List<CustomCPUVO> convert(List<VMResourcePerformanceDTO> vmResourcePerformanceDTOS);

    List<ResourceVO> convertResources(List<VMResourcePerformanceDTO> vmResourcePerformanceDTOS);

    CustomQuery convertReq(CustomPageReq req);
}
