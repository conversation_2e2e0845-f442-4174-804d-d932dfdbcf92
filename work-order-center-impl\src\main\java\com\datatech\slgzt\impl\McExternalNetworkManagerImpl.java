package com.datatech.slgzt.impl;

import com.datatech.slgzt.dao.McExternalNetworkDAO;
import com.datatech.slgzt.dao.model.McExternalNetworkDO;
import com.datatech.slgzt.manager.McExternalNetworkManager;
import com.datatech.slgzt.model.dto.McExternalNetworkDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月06日 15:43:31
 */
@Service
public class McExternalNetworkManagerImpl implements McExternalNetworkManager {

    @Resource
    private McExternalNetworkDAO mcExternalNetworkDAO;

    @Override
    public List<McExternalNetworkDTO> listAll(Long regionId) {
        List<McExternalNetworkDO> list = mcExternalNetworkDAO.list(regionId);
        return list.stream()
                   .map(mcExternalNetworkDO -> new McExternalNetworkDTO()
                           .setId(mcExternalNetworkDO.getId())
                           .setName(mcExternalNetworkDO.getName())
                   )
                   .collect(Collectors.toList());
    }
}
