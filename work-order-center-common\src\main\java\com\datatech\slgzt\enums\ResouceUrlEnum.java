package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: suxin
 * @Date: 2024/11/19
 * @Description: vpc请求url
 */

@Getter
@AllArgsConstructor
public enum ResouceUrlEnum {

    RESOURCE_BASE_URL("v1/cloud/resourcecenter"),
    VPC_LIST_QUERY_URL("/vpc/vpcListPage"),
    VPC_NAME_VERIFY_URL("/vpc/vpcNameVerify"),
    VPC_CIDR_VERIFY_URL("/vpc/vpcCidrVerify"),
    VPC_LIST("/vpc/vpcList"),
    VPC_SUBNET_CIDR_VERIFY_URL("/vpc/vpcSubnetCidrVerify"),
    VPC_CREATE_VPC_AND_SUB_URL("/vpc/createVpcAndSub"),
    TENANT_CREATE_URL("/tenant/create"),
    TENANT_ALONE_URL("/tenant/alone"),
    NAT_RULES_CREATE("/snat/create"),
    NAT_RULES_DELETE("/snat/delete"),
    NETWORK_CREATE("/network/create"),
    SUBNET_CREATE("/subnet/create"),
    SUBNET_DELETE("/subnet/delete"),
    NETWORK_DELETE("/network/delete"),
    VPC_DELETE("/vpc/delete"),
    SECURITY_CREATE("/securitygroup/create"),
    ECS_BIND_SG("/ecs/sg/bind"),
    ECS_UNBIND_SG("/ecs/sg/unbind"),
    CREATE_FLAVOR("/flavor/createNonSt"),
    ;

    private final String url;



}
