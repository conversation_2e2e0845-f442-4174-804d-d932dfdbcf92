package com.cloud.marginal.layoutcenter.utils;

import com.cloud.marginal.model.entity.edge.Task;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentLinkedQueue;

//@Component
public class QueueUtils {

    /**
     * 并发入队出队线程安全，遍历不安全
     */
    private static final ConcurrentLinkedQueue<LayoutTaskVO> queue = new ConcurrentLinkedQueue<>();

    /**
     * 插入数据到队尾
     */
    public static boolean addTail(LayoutTaskVO task) {
        int size = size();
        // 队列达到100后,后续任务会丢弃,待xxl_job的定时任务重新触发执行,防止队列过大导致节点故障
        return  size <= 100 && queue.offer(task);
    }

    /**
     * 获取队头数据并删除,如果为空，则返回null
     */
    public static LayoutTaskVO getHead(){
        return queue.poll();
    }

    /**
     * 线程休眠时间,队列中的数据越多，休眠越短
     * 最大休眠10s,最小休眠100毫秒,最小不能为0秒,否则线程池处理不过来
     */
    public static int getSleepMillisecond(){
        // 1个任务,休眠10s
        // 2个任务,休眠5s
        // 3个任务,休眠3.3s
        // 4个任务,修眠 2.5s
        // 5个任务,休眠2s
        // ......
        // 10个任务,休眠1s
        // ......
        // 无论队列中多少任务,同一个任务从队头取出执行后,如果被再次放入队尾,至少需10s后才能再次被执行
        return 10000 / (size() == 0 ? 1 : size());
    }

    /**
     * 获取队列大小,并发情况下,不完全准确
     */
    private static int size(){
        return queue.size();
    }
}
