package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

import java.util.List;

@Data
public class SubnetParam {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 云区域编码
     */
    private String regionCode;

    /**
     * 计费号
     */
    private String billId;

    /**
     * 集团客户编号
     */
    private String groupId;

    /**
     * 子网名称
     */
    private String name;

    /**
     * 子网的CIDR地址
     */
    private String cidr;

    /**
     * 子网的CIDR地址
     */
    private String ipv6Cidr;

    /**
     * IP协议版本号（4或6）
     */
    private Long ipVersion;

    /**
     * 子ip池
     */
    private List<AllocationPools> allocationPools;

    /**
     * 网关ip
     */
    private String gatewayIp;

    /**
     * 是否创建IPv6子网
     * False（默认）：不创建
     * true：创建
     */
    private Boolean ipv6Enable = false;

    /**
     * 描述
     */
    private String description;

    /**
     * 子网是否开启dhcp功能
     * False（默认）不开启 true开启
     */
    private Boolean dhcpEnable = false;

    /**
     * 子网dns服务器地址1
     */
    private String primaryDns;

    /**
     * 子网dns服务器地址2
     */
    private String secondaryDns;

    /**
     * vpcID
     */
    private String vpcId;

    /**
     * 网络ID
     */
    private String networkId;

    /**
     * 可用区编码
     */
    private String azCode;

    /**
     * 可选取值：
     * SLAAC
     * DHCPv6 stateless
     * DHCPv6 stateful
     * 当网络类型为Ipv6但未指定时，虚层使用默认值
     */
    private String ipv6AddressMode;

    /**
     * 可选取值：
     * SLAAC
     * DHCPv6 stateless
     * DHCPv6 stateful
     * 当网络类型为Ipv6但未指定时，虚层使用默认值
     */
    private String ipv6RaMode;

    /**
     * 来源系统
     */
    private String systemSource;

    /**
     * 操作编号
     */
    private String optUuid;

    @Data
    public static class AllocationPools {

        private String start;

        private String end;
    }
}
