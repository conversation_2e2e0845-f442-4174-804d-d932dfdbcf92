package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.report.ExportTaskDO;
import com.datatech.slgzt.model.dto.ExportTaskDTO;
import org.mapstruct.Mapper;

/**
 * 导出任务转换器
 */
@Mapper(componentModel = "spring")
public interface ExportTaskManagerConvert {

    /**
     * DTO转DO
     */
    ExportTaskDO dto2do(ExportTaskDTO dto);
    
    /**
     * DO转DTO
     */
    ExportTaskDTO do2dto(ExportTaskDO entity);
} 