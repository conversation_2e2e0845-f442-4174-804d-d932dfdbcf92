package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EcsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.NetcardParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.dto.edge.CreateNetcardRcDto;
import com.cloud.marginal.model.dto.edge.UnbindNetCardDTO;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.utils.UuidUtil;
import com.cloud.resource.api.netcard.dto.BindNetcardRcDto;
import com.cloud.resource.api.netcard.dto.DeleteNetcardRcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 虚拟网卡接口适配
 */
@Component
@Slf4j
public class NetCardMgAdapter extends BaseNorthInterfaceAdapter {

    //网络多平面，需要创建多个网卡，且网卡间创建和绑定要按照顺序执行，故写了多个创建网卡的方法
    /**
     * 创建虚拟网卡绑定云主机1
     */
    public TaskVO createNetCard1(String taskId, Integer taskSource) {
        log.info("createNetCard1 start");
        CreateNetcardRcDto createNetcardRcDto = generateCreateNetCardRcDtoV2(getLayoutParam(taskId), ProductOrderTypeEnum.NETCARD_CREATE_1.getCode());
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateAndBinNetCard(),
                null,
                createNetcardRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create1 netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 创建虚拟网卡2
     */
    public TaskVO createNetCard2(String taskId, Integer taskSource) {
        log.info("createNetCard2 start");
        CreateNetcardRcDto createNetcardRcDto = generateCreateNetCardRcDtoV2(getLayoutParam(taskId), ProductOrderTypeEnum.NETCARD_CREATE_2.getCode());
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateAndBinNetCard(),
                null,
                createNetcardRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create2 netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 创建虚拟网卡3
     */
    public TaskVO createNetCard3(String taskId, Integer taskSource) {
        log.info("createNetCard3 start");
        CreateNetcardRcDto createNetcardRcDto = generateCreateNetCardRcDtoV2(getLayoutParam(taskId), ProductOrderTypeEnum.NETCARD_CREATE_3.getCode());
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateAndBinNetCard(),
                null,
                createNetcardRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create3 netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 创建虚拟网卡4
     */
    public TaskVO createNetCard4(String taskId, Integer taskSource) {
        log.info("createNetCard4 start");
        CreateNetcardRcDto createNetcardRcDto = generateCreateNetCardRcDtoV2(getLayoutParam(taskId), ProductOrderTypeEnum.NETCARD_CREATE_4.getCode());
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateAndBinNetCard(),
                null,
                createNetcardRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create4 netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 创建虚拟网卡5
     */
    public TaskVO createNetCard5(String taskId, Integer taskSource) {
        log.info("createNetCard5 start");
        CreateNetcardRcDto createNetcardRcDto = generateCreateNetCardRcDtoV2(getLayoutParam(taskId), ProductOrderTypeEnum.NETCARD_CREATE_5.getCode());
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateAndBinNetCard(),
                null,
                createNetcardRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create5 netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 虚拟网卡解绑
     */
    public TaskVO unbindNetCard(String taskId, Integer taskSource) {
        log.info("unBindNetCard start");
        UnbindNetCardDTO unbindNetCardDTO = generateUnbindNetCard(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getUnbindNetworkCard(),
                null,
                unbindNetCardDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "unbind netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 删除虚拟网卡
     */
    public TaskVO deleteNetCard(String taskId, Integer taskSource) {
        log.info("deleteNetCard start");
        DeleteNetcardRcDto deleteNetcardRcDto = generateDeleteNetcardRcDto(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteNetcard(),
                null,
                deleteNetcardRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "delete netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 虚拟网卡绑定云主机
     */
    public TaskVO bindNetCard(String taskId, Integer taskSource) {
        log.info("bindNetCard1 start");
        BindNetcardRcDto dto = generateBindNetCardDto(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getBindNetworkCard(),
                null,
                dto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "bindNetCard1 netCard");
        return tasksVoResult.getEntity();
    }

    /**
     * 生成北向接口删除虚拟网卡的参数
     *
     * @param layoutParam 编排参数
     */
    private DeleteNetcardRcDto generateDeleteNetcardRcDto(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam productOrderParam = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NETCARD_DELETE.getCode());
        NetcardParam netcardParam = JSONObject.parseObject(productOrderParam.getAttrs(), NetcardParam.class);
        // 参数设置
        DeleteNetcardRcDto deleteNetcardRcDto = new DeleteNetcardRcDto();
        deleteNetcardRcDto.setDescription(netcardParam.getDescription());
        deleteNetcardRcDto.setRegionCode(netcardParam.getRegionCode());
        deleteNetcardRcDto.setInstanceId(netcardParam.getInstanceId());
        deleteNetcardRcDto.setTenantId(netcardParam.getTenantId());
        return deleteNetcardRcDto;
    }

    /**
     * 生成北向接口创建虚拟网卡的参数
     *
     * @param layoutParam 编排参数
     */
    private CreateNetcardRcDto generateCreateNetcardRcDto(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam productOrderParam = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NETCARD_CREATE.getCode());
        NetcardParam netcardParam = JSONObject.parseObject(productOrderParam.getAttrs(), NetcardParam.class);
        // 参数设置
        CreateNetcardRcDto createNetcardRcDto = new CreateNetcardRcDto();
        createNetcardRcDto.setName(netcardParam.getName());
        createNetcardRcDto.setDescription(netcardParam.getDescription());
        createNetcardRcDto.setgId(netcardParam.getGId());
        createNetcardRcDto.setTenantId(netcardParam.getTenantId());
        createNetcardRcDto.setRegionCode(netcardParam.getRegionCode());
        createNetcardRcDto.setSubnetId(netcardParam.getSubnetId());
        createNetcardRcDto.setOrderId(productOrderParam.getProductOrderId());
        return createNetcardRcDto;
    }

    /**
     * 生成北向接口创建虚拟网卡的参数
     *
     * @param layoutParam 编排参数
     */
    private CreateNetcardRcDto generateCreateNetCardRcDtoV2(LayoutParam layoutParam, String productOrderType) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam productOrderParam = getProductOrder(layoutOrderParam.getProductOrders(), productOrderType);
        NetcardParam netcardParam = JSONObject.parseObject(productOrderParam.getAttrs(), NetcardParam.class);
        // 参数设置
        CreateNetcardRcDto createNetcardRcDto = new CreateNetcardRcDto();
        String ecsCreateCode = ProductOrderTypeEnum.ECS_CREATE.getCode();
        ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ecsCreateCode);
        EcsParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);
        createNetcardRcDto.setName(ecsParam.getVmName() + "-port-" + UuidUtil.generateId());
        createNetcardRcDto.setDescription(netcardParam.getDescription());
        createNetcardRcDto.setgId(netcardParam.getGId());
        createNetcardRcDto.setTenantId(layoutOrderParam.getTenantId());
        createNetcardRcDto.setRegionCode(layoutOrderParam.getRegionCode());
        createNetcardRcDto.setAzCode(ecsParam.getAzCode());
        createNetcardRcDto.setSubnetId(netcardParam.getSubnetId());
        createNetcardRcDto.setIp(netcardParam.getIp());
        createNetcardRcDto.setOrderId(productOrderParam.getProductOrderId());
        createNetcardRcDto.setServiceId(ecsParam.getId());
        return createNetcardRcDto;
    }

    private BindNetcardRcDto generateBindNetCardDto(LayoutParam layoutParam) {
        BindNetcardRcDto dto = new BindNetcardRcDto();
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        String ecsCreateCode = ProductOrderTypeEnum.ECS_CREATE.getCode();
        ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ecsCreateCode);
        EcsParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);
        dto.setRegionCode(layoutOrderParam.getRegionCode());
        dto.setBillId(layoutOrderParam.getAccount());
        dto.setTenantId(layoutOrderParam.getTenantId());
        dto.setGroupId(layoutOrderParam.getCustomId());
        dto.setServerId(ecsParam.getId());
        dto.setInstanceId(layoutParam.getResourceId());
        return dto;
    }

    private UnbindNetCardDTO generateUnbindNetCard(LayoutParam layoutParam) {
        UnbindNetCardDTO dto = new UnbindNetCardDTO();
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        String netCardDeleteCode = ProductOrderTypeEnum.NETCARD_DELETE.getCode();
        ProductOrderParam netCardOrder = getProductOrder(layoutOrderParam.getProductOrders(), netCardDeleteCode);
        NetcardParam netcardParam = JSONObject.parseObject(netCardOrder.getAttrs(), NetcardParam.class);
        dto.setTenantId(layoutOrderParam.getTenantId());
        dto.setOrderId(UuidUtil.generateId());
        dto.setRegionCode(layoutOrderParam.getRegionCode());
        dto.setAzCode(netcardParam.getAzCode());
        dto.setInstanceId(netcardParam.getInstanceId());
        dto.setServerId(netcardParam.getVmId());
        return dto;
    }
}
