package com.datatech.slgzt.model.req.device;

import lombok.Data;

/**
 * @Desc 请求domain
 * <AUTHOR>
 * @DATA 2025-06-12
 */
@Data
public class DeviceDomainPageReq {

    private int pageSize = 10;

    private int pageNum = 1;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 云类型编码
     */
    private String catalogueDomainCode;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 资源池id
     * 当工单是线下开通的时候，资源池id是空的
     */
    private Long regionId;

    /**
     * 资源池Code
     */
    private String regionCode;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 显卡型号
     */
    private String modelName;
}
