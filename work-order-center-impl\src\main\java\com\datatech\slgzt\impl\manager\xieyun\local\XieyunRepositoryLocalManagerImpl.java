package com.datatech.slgzt.impl.manager.xieyun.local;

import com.datatech.slgzt.convert.XieYunBeanManagerConvert;
import com.datatech.slgzt.dao.container.XieYunRepositoryDAO;
import com.datatech.slgzt.dao.model.container.XieYunRepositoryDO;
import com.datatech.slgzt.manager.xieyun.local.XieyunRepositoryLocalManager;
import com.datatech.slgzt.model.dto.XieYunRepositoryDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 谐云本地仓库处理
 *
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/4/15
 */

@Service
public class XieyunRepositoryLocalManagerImpl implements XieyunRepositoryLocalManager {

    @Resource
    private XieYunRepositoryDAO repositoryDAO;

    @Resource
    private XieYunBeanManagerConvert convert;

    @Override
    public void insert(XieYunRepositoryDTO repositoryDTO) {
        XieYunRepositoryDO repositoryDO = convert.repositoryDTO2DO(repositoryDTO);
        repositoryDO.setCreatedTime(LocalDateTime.now());
        repositoryDAO.insert(repositoryDO);
    }

    @Override
    public void updateByRepoId(XieYunRepositoryDTO repositoryDTO) {
        XieYunRepositoryDO repositoryDO = convert.repositoryDTO2DO(repositoryDTO);
        repositoryDO.setUpdatedTime(LocalDateTime.now());
        repositoryDAO.updateByRepoId(repositoryDO);
    }
}

