package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 容器资源配额模型
 * <AUTHOR>
 * @description 容器资源配额配合开通模型
 * @date 2025年05月27日
 */
@Data
public class CQModel extends BaseProductModel {

    /**
     * 配额名称（必填）
     */
    private String cqName;

    /**
     * 容器配额-核心数（必填）
     */
    @JsonProperty("vCpus")
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G（必填）
     * 类似4核8G中的8G
     */
    private Integer ram;

    /**
     * GPU算力(算力)（非必填）
     */
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB（非必填）
     */
    private Integer gpuVirtualMemory;

    /**
     * 物理GPU卡(个)（非必填）
     */
    // 暂时反过来，测试
    @JsonProperty("gpuVirtualCore")
    private Integer gpuCore;

    /**
     * 虚拟GPU卡(个)（非必填）
     */
    // 暂时反过来，测试
    @JsonProperty("gpuCore")
    private Integer gpuVirtualCore;

    /**
     * 4A账号(必填)
     */
    private String a4Account;

    /**
     * 4A账号绑定的手机(必填)
     */
    private String a4Phone;

    /**
     * 申请时长（必填）
     */
    private String applyTime;

    /**
     * 开通数量
     */
    private Integer openNum;

    /**
     * 云类型
     */
    private String catalogueDomainCode;

    /**
     * 云类型名称
     */
    private String catalogueDomainName;

    /**
     * 云平台id
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 申请用户ID
     */
    private Long applyUserId;

    /**
     * 申请用户名称
     */
    private String applyUserName;

    /**
     * 部门名称
     */
    private String departmentName;

}
