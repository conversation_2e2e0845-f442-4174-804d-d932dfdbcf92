<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:yaoqiang="http://bpmn.sourceforge.net" expressionLanguage="http://www.w3.org/1999/XPath" id="m1632821341533" name="" targetNamespace="http://www.activiti.org/test" typeLanguage="http://www.w3.org/2001/XMLSchema">
    <process id="resource-recovery-process" isClosed="false" isExecutable="true" name="resource-recovery-process" processType="None">
        <extensionElements>
            <yaoqiang:description/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
        </extensionElements>
        <exclusiveGateway gatewayDirection="Diverging" id="_3" name="entrance gateway"/>
        <sequenceFlow id="_4" sourceRef="_2" targetRef="_3">
            <documentation id="_4_D_1"><![CDATA[4]]></documentation>
        </sequenceFlow>
        <startEvent id="_2" isInterrupting="true" name="Start Event" parallelMultiple="false"/>
        <userTask activiti:assignee="${tenant}" activiti:exclusive="true" completionQuantity="1" id="_5" implementation="##unspecified" isForCompensation="false" name="user_task" startQuantity="1"/>
        <sequenceFlow id="_7" name="isTenant" sourceRef="_3" targetRef="_5">
            <documentation id="_7_D_1"><![CDATA[7]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <userTask activiti:assignee="${schema}" activiti:exclusive="true" completionQuantity="1" id="_9" implementation="##unspecified" isForCompensation="false" name="schema_administrator" startQuantity="1"/>
        <sequenceFlow id="_10" name="pass" sourceRef="_5" targetRef="_9">
            <documentation id="_10_D_1"><![CDATA[10]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <userTask activiti:assignee="${retreat_dimension}" activiti:exclusive="true" completionQuantity="1" id="_11" implementation="##unspecified" isForCompensation="false" name="retreat_dimension" startQuantity="1"/>
        <sequenceFlow id="_12" name="isRetreatDimensionRecovery=1" sourceRef="_29" targetRef="_11">
            <documentation id="_12_D_1"><![CDATA[12]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_41" name="isRetreatDimensionRecovery=0" sourceRef="_29" targetRef="_6">
            <documentation id="_41_D_1"><![CDATA[41]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_47" name="recoveryType=networkOrVpc" sourceRef="_29" targetRef="_13">
            <documentation id="_47_D_1"><![CDATA[47]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_20" name="no" sourceRef="_29" targetRef="_5">
            <documentation id="_20_D_1"><![CDATA[20]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_30" sourceRef="_9" targetRef="_29">
            <documentation id="_30_D_1"><![CDATA[30]]></documentation>
        </sequenceFlow>
        <sequenceFlow id="_39" name="schemaAdministratorClose" sourceRef="_29" targetRef="_18">
            <documentation id="_39_D_1"><![CDATA[39]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_40" name="userTaskClose" sourceRef="_5" targetRef="_18">
            <documentation id="_40_D_1"><![CDATA[40]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <exclusiveGateway gatewayDirection="Diverging" id="_29" name="schema gateway"/>
        <userTask activiti:assignee="${system}" activiti:exclusive="true" completionQuantity="1" id="_36" implementation="##unspecified" isForCompensation="false" name="system" startQuantity="1"/>
        <sequenceFlow id="_37" name="isSystem" sourceRef="_3" targetRef="_36">
            <documentation id="_37_D_1"><![CDATA[37]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_38" name="pass" sourceRef="_36" targetRef="_5">
            <documentation id="_38_D_1"><![CDATA[38]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_8" sourceRef="_13" targetRef="_18"/>
        <endEvent id="_18" name="End Event"/>
        <sequenceFlow id="_16" name="noHaveConfirmNetwork=0" sourceRef="_14" targetRef="_18">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_17" name="noHaveConfirmNetwork=1" sourceRef="_14" targetRef="_13">
            <documentation id="_17_D_1"><![CDATA[17]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <exclusiveGateway gatewayDirection="Diverging" id="_14" name="Exclusive Gateway"/>
        <userTask activiti:assignee="${resource_recovery}" activiti:exclusive="true" completionQuantity="1" id="_6" isForCompensation="false" name="resource_recovery" startQuantity="1"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_43" name="network recovery tenant Gateway"/>
        <userTask activiti:assignee="${network_recovery_tenant}" activiti:exclusive="true" completionQuantity="1" id="_42" isForCompensation="false" name="tenant_task" startQuantity="1"/>
        <sequenceFlow id="_45" name="noHaveNetwork=0" sourceRef="_43" targetRef="_18">
            <documentation id="_45_D_1"><![CDATA[45]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_46" name="noHaveNetwork=1" sourceRef="_43" targetRef="_42">
            <documentation id="_46_D_1"><![CDATA[46]]></documentation>
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
        </sequenceFlow>
        <userTask activiti:assignee="${network_recovery}" activiti:exclusive="true" completionQuantity="1" id="_13" implementation="##unspecified" isForCompensation="false" name="network_recovery" startQuantity="1"/>
        <sequenceFlow id="_19" name="d-pass" sourceRef="_11" targetRef="_6">
            <documentation id="_19_D_1"><![CDATA[19]]></documentation>
        </sequenceFlow>
        <sequenceFlow id="_21" name="r-pass" sourceRef="_42" targetRef="_14">
            <documentation id="_21_D_1"><![CDATA[21]]></documentation>
        </sequenceFlow>
        <sequenceFlow id="_44" name="r-pass" sourceRef="_6" targetRef="_43">
            <documentation id="_44_D_1"><![CDATA[44]]></documentation>
        </sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram documentation="background=#3C3F41;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0" id="Diagram-_1" name="Untitled Diagram">
        <bpmndi:BPMNPlane bpmnElement="resource-recovery-process">
            <bpmndi:BPMNShape bpmnElement="_3" id="Shape-_3" isMarkerVisible="true">
                <omgdc:Bounds height="32.0" width="32.0" x="474.58530217802763" y="126.56191581705315"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_2" id="Shape-_2">
                <omgdc:Bounds height="32.0" width="32.0" x="478.58530217802763" y="53.89242781767783"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_5" id="Shape-_5">
                <omgdc:Bounds height="55.0" width="85.0" x="336.57750672436947" y="176.08752854368703"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_9" id="Shape-_9">
                <omgdc:Bounds height="55.0" width="85.0" x="594.5775067243694" y="290.2320723608963"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_11" id="Shape-_11">
                <omgdc:Bounds height="55.0" width="85.0" x="184.72138199711094" y="475.36948926891546"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_13" id="Shape-_13">
                <omgdc:Bounds height="55.0" width="85.0" x="89.79331963348164" y="634.8017836316077"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_29" id="Shape-_29" isMarkerVisible="true">
                <omgdc:Bounds height="32.0" width="32.0" x="504.92819746264433" y="360.11704526350434"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_36" id="Shape-_36">
                <omgdc:Bounds height="55.0" width="85.0" x="598.2607078880407" y="178.22351470763232"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_18" id="Shape-_18">
                <omgdc:Bounds height="32.0" width="32.0" x="444.76548054118837" y="704.867262903256"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_14" id="Shape-_14" isMarkerVisible="true">
                <omgdc:Bounds height="32.0" width="32.0" x="225.27272727272725" y="590.3181818181818"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_6" id="Shape-_6">
                <omgdc:Bounds height="55.0" width="85.0" x="680.4312500583874" y="429.62252709808865"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_42" id="Shape-_42">
                <omgdc:Bounds height="55.0" width="85.0" x="655.4312500583874" y="579.6225270980887"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_43" id="Shape-_43" isMarkerVisible="true">
                <omgdc:Bounds height="32.0" width="32.0" x="355.27272727272725" y="515.3181818181818"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="_12" id="BPMNEdge__12" sourceElement="_29" targetElement="_11">
                <omgdi:waypoint x="505.11704526350434" y="376.11704526350434"/>
                <omgdi:waypoint x="270.0" y="502.86948926891546"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="30.0" x="363.93" y="440.05"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_37" id="BPMNEdge__37" sourceElement="_3" targetElement="_36">
                <omgdi:waypoint x="507.0" y="143.0"/>
                <omgdi:waypoint x="645.0" y="143.0"/>
                <omgdi:waypoint x="645.0" y="178.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="50.0" x="571.0" y="133.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_17" id="BPMNEdge__17" sourceElement="_14" targetElement="_13">
                <omgdi:waypoint x="243.0" y="592.0"/>
                <omgdi:waypoint x="283.0" y="592.0"/>
                <omgdi:waypoint x="175.0" y="662.3017836316077"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="67.0" x="273.82" y="553.4"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_39" id="BPMNEdge__39" sourceElement="_29" targetElement="_18">
                <omgdi:waypoint x="505.11704526350434" y="376.11704526350434"/>
                <omgdi:waypoint x="476.9994493924994" y="720.867262903256"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_16" id="BPMNEdge__16" sourceElement="_14" targetElement="_18">
                <omgdi:waypoint x="256.68181818181824" y="606.3181818181818"/>
                <omgdi:waypoint x="445.00055060750066" y="720.867262903256"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="67.0" x="341.27" y="614.97"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_38" id="BPMNEdge__38" sourceElement="_36" targetElement="_5">
                <omgdi:waypoint x="609.0" y="223.71428571428572"/>
                <omgdi:waypoint x="609.0" y="219.0"/>
                <omgdi:waypoint x="422.0" y="219.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="30.0" x="495.0" y="195.81"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_19" id="BPMNEdge__19" sourceElement="_11" targetElement="_6">
                <omgdi:waypoint x="270.0" y="502.86948926891546"/>
                <omgdi:waypoint x="680.0" y="457.12252709808865"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="40.0" x="438.58" y="483.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_40" id="BPMNEdge__40" sourceElement="_5" targetElement="_18">
                <omgdi:waypoint x="422.0" y="203.58752854368703"/>
                <omgdi:waypoint x="445.00055060750066" y="720.867262903256"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_20" id="BPMNEdge__20" sourceElement="_29" targetElement="_5">
                <omgdi:waypoint x="505.11704526350434" y="376.11704526350434"/>
                <omgdi:waypoint x="283.0" y="308.0"/>
                <omgdi:waypoint x="337.0" y="203.58752854368703"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="18.0" x="274.0" y="309.49"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_41" id="BPMNEdge__41" sourceElement="_29" targetElement="_6">
                <omgdi:waypoint x="536.8829547364957" y="376.11704526350434"/>
                <omgdi:waypoint x="680.0" y="457.12252709808865"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_44" id="BPMNEdge__44" sourceElement="_6" targetElement="_43">
                <omgdi:waypoint x="680.0" y="457.12252709808865"/>
                <omgdi:waypoint x="386.68181818181824" y="531.3181818181818"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_21" id="BPMNEdge__21" sourceElement="_42" targetElement="_14">
                <omgdi:waypoint x="697.9312500583874" y="580.0"/>
                <omgdi:waypoint x="550.0" y="564.0"/>
                <omgdi:waypoint x="241.27272727272725" y="590.2727272727273"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="38.0" x="475.45" y="554.01"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_46" id="BPMNEdge__46" sourceElement="_43" targetElement="_42">
                <omgdi:waypoint x="386.68181818181824" y="531.3181818181818"/>
                <omgdi:waypoint x="655.0" y="607.1225270980887"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_45" id="BPMNEdge__45" sourceElement="_43" targetElement="_18">
                <omgdi:waypoint x="386.68181818181824" y="531.3181818181818"/>
                <omgdi:waypoint x="445.00055060750066" y="720.867262903256"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_47" id="BPMNEdge__47" sourceElement="_29" targetElement="_13">
                <omgdi:waypoint x="505.11704526350434" y="376.11704526350434"/>
                <omgdi:waypoint x="175.0" y="662.3017836316077"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_4" id="BPMNEdge__4" sourceElement="_2" targetElement="_3">
                <omgdi:waypoint x="492.58530217802763" y="85.8167390579905"/>
                <omgdi:waypoint x="492.58530217802763" y="128.58530217802763"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="491.59" y="97.29"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_7" id="BPMNEdge__7" sourceElement="_3" targetElement="_5">
                <omgdi:waypoint x="475.4380841829468" y="142.56191581705315"/>
                <omgdi:waypoint x="379.0" y="201.0"/>
                <omgdi:waypoint x="379.0" y="176.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="47.0" x="389.5" y="137.64"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_8" id="BPMNEdge__8" sourceElement="_13" targetElement="_18">
                <omgdi:waypoint x="175.0" y="688.0"/>
                <omgdi:waypoint x="275.0" y="688.0"/>
                <omgdi:waypoint x="445.00055060750066" y="720.867262903256"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="291.4" y="678.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_30" id="BPMNEdge__30" sourceElement="_9" targetElement="_29">
                <omgdi:waypoint x="595.0" y="317.7320723608963"/>
                <omgdi:waypoint x="536.8829547364957" y="376.11704526350434"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="375.93" y="358.62"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_10" id="BPMNEdge__10" sourceElement="_5" targetElement="_9">
                <omgdi:waypoint x="422.0" y="203.58752854368703"/>
                <omgdi:waypoint x="595.0" y="317.7320723608963"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="30.0" x="362.58" y="250.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>
