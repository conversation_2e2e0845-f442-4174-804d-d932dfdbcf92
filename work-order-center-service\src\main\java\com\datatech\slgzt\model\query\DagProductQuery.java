package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

/**
 * DAG产品查询参数
 */
@Data
@Accessors(chain = true)
public class DagProductQuery {

    private String productType;

    private String status;

    private List<String> statusList;

    private String orderId;

    private Long parentId;

    private Collection<Long> subOrderIds;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
} 