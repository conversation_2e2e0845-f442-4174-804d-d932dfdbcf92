package com.datatech.slgzt.impl.service.xieyun;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 协云Job执行ID功能测试
 * 
 * <AUTHOR>
 * @description 测试Job执行ID的保存和更新功能
 * @date 2025年 04月10日 17:00:00
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class XieyunJobExecutionIdTest {

    @Mock
    private StandardWorkOrderProductManager productManager;
    
    @Mock
    private JobExecution jobExecution;
    
    private XieyunEnvironmentCreateJobListener jobListener;
    
    @BeforeEach
    void setUp() {
        jobListener = new XieyunEnvironmentCreateJobListener();
        // 使用反射设置私有字段
        setField(jobListener, "productManager", productManager);
    }
    
    @Test
    void testBeforeJob_CQProduct_ShouldUpdateJobExecutionId() {
        // 准备测试数据
        Long jobExecutionId = 12345L;
        Long subOrderId = 67890L;
        
        // Mock JobExecution
        when(jobExecution.getId()).thenReturn(jobExecutionId);
        
        // Mock JobParameters
        JobParameters jobParameters = new JobParametersBuilder()
                .addLong("subOrderId", subOrderId)
                .toJobParameters();
        when(jobExecution.getJobParameters()).thenReturn(jobParameters);
        
        // Mock产品信息（CQ产品）
        StandardWorkOrderProductDTO productDTO = createCQProduct(subOrderId);
        when(productManager.getBySubOrderId(subOrderId)).thenReturn(productDTO);
        
        // 执行测试
        jobListener.beforeJob(jobExecution);
        
        // 验证结果
        verify(productManager, times(1)).getBySubOrderId(subOrderId);
        verify(productManager, times(1)).updateJobExecutionIdBySubOrderId(subOrderId, jobExecutionId);
        
        log.info("测试通过：CQ产品成功更新Job执行ID");
    }
    
    @Test
    void testBeforeJob_NonCQProduct_ShouldNotUpdateJobExecutionId() {
        // 准备测试数据
        Long jobExecutionId = 12345L;
        Long subOrderId = 67890L;
        
        // Mock JobExecution
        when(jobExecution.getId()).thenReturn(jobExecutionId);
        
        // Mock JobParameters
        JobParameters jobParameters = new JobParametersBuilder()
                .addLong("subOrderId", subOrderId)
                .toJobParameters();
        when(jobExecution.getJobParameters()).thenReturn(jobParameters);
        
        // Mock产品信息（非CQ产品）
        StandardWorkOrderProductDTO productDTO = createECSProduct(subOrderId);
        when(productManager.getBySubOrderId(subOrderId)).thenReturn(productDTO);
        
        // 执行测试
        jobListener.beforeJob(jobExecution);
        
        // 验证结果
        verify(productManager, times(1)).getBySubOrderId(subOrderId);
        verify(productManager, never()).updateJobExecutionIdBySubOrderId(any(), any());
        
        log.info("测试通过：非CQ产品不会更新Job执行ID");
    }
    
    @Test
    void testBeforeJob_ProductNotFound_ShouldNotUpdateJobExecutionId() {
        // 准备测试数据
        Long jobExecutionId = 12345L;
        Long subOrderId = 67890L;
        
        // Mock JobExecution
        when(jobExecution.getId()).thenReturn(jobExecutionId);
        
        // Mock JobParameters
        JobParameters jobParameters = new JobParametersBuilder()
                .addLong("subOrderId", subOrderId)
                .toJobParameters();
        when(jobExecution.getJobParameters()).thenReturn(jobParameters);
        
        // Mock产品不存在
        when(productManager.getBySubOrderId(subOrderId)).thenReturn(null);
        
        // 执行测试
        jobListener.beforeJob(jobExecution);
        
        // 验证结果
        verify(productManager, times(1)).getBySubOrderId(subOrderId);
        verify(productManager, never()).updateJobExecutionIdBySubOrderId(any(), any());
        
        log.info("测试通过：产品不存在时不会更新Job执行ID");
    }
    
    @Test
    void testBeforeJob_ExceptionHandling_ShouldNotThrowException() {
        // 准备测试数据
        Long jobExecutionId = 12345L;
        Long subOrderId = 67890L;
        
        // Mock JobExecution
        when(jobExecution.getId()).thenReturn(jobExecutionId);
        
        // Mock JobParameters
        JobParameters jobParameters = new JobParametersBuilder()
                .addLong("subOrderId", subOrderId)
                .toJobParameters();
        when(jobExecution.getJobParameters()).thenReturn(jobParameters);
        
        // Mock异常
        when(productManager.getBySubOrderId(subOrderId)).thenThrow(new RuntimeException("数据库连接失败"));
        
        // 执行测试，不应该抛出异常
        assertDoesNotThrow(() -> jobListener.beforeJob(jobExecution));
        
        log.info("测试通过：异常情况下不会影响Job执行");
    }
    
    /**
     * 创建CQ产品测试数据
     */
    private StandardWorkOrderProductDTO createCQProduct(Long subOrderId) {
        StandardWorkOrderProductDTO productDTO = new StandardWorkOrderProductDTO();
        productDTO.setId(1001L);
        productDTO.setSubOrderId(subOrderId);
        productDTO.setProductType(ProductTypeEnum.CQ.getCode());
        productDTO.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
        productDTO.setWorkOrderId("WO-TEST-001");
        return productDTO;
    }
    
    /**
     * 创建ECS产品测试数据
     */
    private StandardWorkOrderProductDTO createECSProduct(Long subOrderId) {
        StandardWorkOrderProductDTO productDTO = new StandardWorkOrderProductDTO();
        productDTO.setId(1002L);
        productDTO.setSubOrderId(subOrderId);
        productDTO.setProductType(ProductTypeEnum.ECS.getCode());
        productDTO.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
        productDTO.setWorkOrderId("WO-TEST-002");
        return productDTO;
    }
    
    /**
     * 使用反射设置私有字段
     */
    private void setField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            log.error("设置字段失败: {}", fieldName, e);
        }
    }
}
