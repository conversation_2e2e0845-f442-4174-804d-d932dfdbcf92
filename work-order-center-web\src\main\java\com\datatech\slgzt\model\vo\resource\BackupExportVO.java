package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-06-06 16:53
 **/
@Data
public class BackupExportVO {

    @ExcelExportHeader(value = "策略名称")
    private String deviceName;

    @ExcelExportHeader(value = "备份类型")
    private String backupType;

    @ExcelExportHeader(value = "备份频率")
    private String frequency;

    @ExcelExportHeader(value = "星期")
    private Integer daysOfWeek;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private String resourceApplyTime;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;
}
