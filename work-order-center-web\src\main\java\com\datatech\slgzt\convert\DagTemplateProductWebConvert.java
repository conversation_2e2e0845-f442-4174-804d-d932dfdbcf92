package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.DagTemplateProductDTO;
import com.datatech.slgzt.model.query.DagTemplateProductQuery;
import com.datatech.slgzt.model.req.dag.DagTemplateProductCreateReq;
import com.datatech.slgzt.model.req.dag.DagTemplateProductPageReq;
import com.datatech.slgzt.model.req.dag.DagTemplateProductUpdateReq;
import com.datatech.slgzt.model.vo.dag.DagTemplateProductVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * DAG模板产品Web层对象转换器
 */
@Mapper(componentModel = "spring")
public interface DagTemplateProductWebConvert {
    
    DagTemplateProductDTO req2dto(DagTemplateProductCreateReq req);

    DagTemplateProductDTO req2dto(DagTemplateProductUpdateReq req);

    DagTemplateProductQuery req2query(DagTemplateProductPageReq req);

    DagTemplateProductVO dto2vo(DagTemplateProductDTO dto);

    List<DagTemplateProductVO> dto2voList(List<DagTemplateProductDTO> dtoList);
} 