package com.datatech.slgzt.model.nostander;

import lombok.Data;

import java.util.List;

/**
 * vpc-子网(非任务中心)
 */
@Data
public class VpcSubnetUnTaskModel {

    //子网id，绑定时必传
    // todo 不需要
    private String subnetId;
    // todo 不需要
    private String id;


    private String dagId;
    /**
     * 子网网段
     */
    // todo 不需要
    private String cidr;

    /**
     * 子网可用区编码
     */
    // todo 不需要
    private String azCode;

    /**
     * 子网ip
     */
    // todo 不需要
    private String gatewayIp;

    /**
     * 子网名称
     */
    private String subnetName;

    /**
     * 是否创建IPv6子网
     * false（默认）：不创建
     * true：创建
     */
    // todo 不需要
    private Boolean ipv6Enable = false;

    /**
     * 子网是否开启dhcp功能false（默认）不开启 true开启
     */
    // todo 不需要
    private Boolean dhcpEnable = false;

    /**
     * 子网描述  非必传
     */
    // todo 不需要
    private String description;

    /**
     * 子网dns服务器地址1
     */
    // todo 不需要
    private String primaryDns;

    /**
     * 子网dns服务器地址2
     */
    // todo 不需要
    private String secondaryDns;

    /**
     * 开始ip
     */
    private String startIp;

    /**
     * 子网掩码
     */
    private String netmask;

    /**
     * Array<String> 非必传		dns服务器地址，推荐最多支持2个IP
     */
    // todo 不需要
    private List<String> dnsNameServices;

    // todo 不需要
    private String level2InstanceId;

    // todo 不需要
    private String instanceId;
    private String uuid;
}
