package com.datatech.slgzt.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BaseVdeviceInfoModel implements Serializable {

    @JSONField(name="phase")
    private String phase;

    @JSONField(name="allocation_id")
    private String allocationId;

    @JSONField(name="fork_from_id")
    private String forkFromId;

    @JSONField(name="vdevices")
    private List<BaseVdevice> vdevices;



}

