package com.datatech.slgzt.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月14日 13:56:32
 */
@Data
@Accessors(chain = true)
public class TaskStatusExt {

    //创建状态
    private String createStatus;

    //创建任务id
    private String createTaskId;

    //删除状态
    private String deleteStatus;

    //删除任务id
    private String deleteTaskId;

    //修改状态
    private String updateStatus;
    //修改任务id
    private String updateTaskId;
    //更新快照
    private String updateSnapshot;
}
