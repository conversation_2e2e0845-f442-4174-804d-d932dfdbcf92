package com.datatech.slgzt.model.req.slb;

import com.datatech.slgzt.model.dto.SlbListenerDTO;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import com.datatech.slgzt.model.vo.slb.SlbListenerVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * SLB监听器VO
 */
@Data
@Accessors(chain = true)
public class SlbListenerCreateReq {

    /**
     * 监听名称
     */
    private String listenerName;

    /**
     * VPC ID
     */
    private String vpcId;

    /**
     * VPC名称
     */
    private String vpcName;

    /**
     * 资源详情ID
     */
    private Long slbResourceDetailId;

    /**
     * SLB设备ID
     */
    private String slbDeviceId;

    /**
     * 运行状态
     */
    private String runningStatus;

    /**
     * SLB监听器协议
     */
    private SlbListenerDTO.SlbListenerProtocolModel slbListenerProtocolModel;

    /**
     * SLB健康检查
     */
    private SlbListenerDTO.SlbHealthCheckModel slbHealthCheckModel;

    private SlbListenerServerGroupDTO slbListenerServerGroup;


} 