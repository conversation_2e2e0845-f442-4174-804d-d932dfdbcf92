package com.datatech.slgzt.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/16 12:12
 * @description TODO
 */
@Data
public class BaseVdevice {
    @JSONField(name="uuid")
    private String uuid;

    @JSONField(name="ip")
    private String ip;

    @JSONField(name="port")
    private String port;

    @JSONField(name="device_id")
    private String deviceId;

    @JSONField(name="pindex")
    private String pindex;


    @JSONField(name="vindex")
    private String vindex;
    @JSONField(name="ratio")
    private String ratio;
    @J<PERSON>NField(name="memory")
    private String memory;

    @JSONField(name="phase")
    private String phase;
    @J<PERSON>NField(name="confirmation_time")
    private String confirmationTime;
}