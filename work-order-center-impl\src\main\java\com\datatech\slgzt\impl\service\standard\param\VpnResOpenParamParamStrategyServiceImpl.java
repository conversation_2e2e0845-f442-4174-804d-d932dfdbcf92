package com.datatech.slgzt.impl.service.standard.param;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.BackupModel;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import com.datatech.slgzt.model.nostander.VpnModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.standard.ResOpenParamStrategyService;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: vpn创建参数封装
 * @author: LK
 * @create: 2025-06-10 10:26
 **/
@Service
public class VpnResOpenParamParamStrategyServiceImpl implements ResOpenParamStrategyService {

    @Override
    public List<ResOpenReqModel.ProductOrder> assembleParam(ResOpenOpm opm) {
        ArrayList<ResOpenReqModel.ProductOrder> productOrders = Lists.newArrayList();
        List<VpnModel> vpnModelList = opm.getVpnModelList();
        for(VpnModel vpnModel : vpnModelList) {
            ResOpenReqModel.ProductOrder vpnProductOrder = new ResOpenReqModel.ProductOrder();
            vpnProductOrder.setProductOrderId(vpnModel.getId().toString());
            vpnProductOrder.setProductOrderType("VPN_CREATE");
            vpnProductOrder.setProductType("vpn_create");
            vpnProductOrder.setSubOrderId(opm.getSubOrderId());
            vpnProductOrder.setGId(opm.getGId());
            ResOpenReqModel.Attrs vpnAttrs = new ResOpenReqModel.Attrs();
            vpnAttrs.setGId(opm.getGId());
            vpnAttrs.setRegionCode(vpnModel.getRegionCode());
            vpnAttrs.setBillId(opm.getAccount());
            vpnAttrs.setTenantId(opm.getTenantId());
            vpnAttrs.setAzCode(vpnModel.getAzCode());
            vpnAttrs.setSystemSource("OAC");
            vpnAttrs.setName(vpnModel.getName());
            vpnAttrs.setMaxConnection(vpnModel.getMaxConnection());
            vpnAttrs.setBandwidth(vpnModel.getBandwidth());
            PlaneNetworkModel planeNetworkModel = vpnModel.getPlaneNetworkModel();
            vpnAttrs.setVpcId(planeNetworkModel.getId());
            List<PlaneNetworkModel.Subnet> subnets = planeNetworkModel.getSubnets();
            vpnAttrs.setSubnetId(StreamUtils.findAny(subnets).getSubnetId());
            vpnProductOrder.setAttrs(vpnAttrs);
            productOrders.add(vpnProductOrder);
        }
        return productOrders;
    }

    @Override
    public ProductTypeEnum register() {
        return ProductTypeEnum.VPN;
    }
}
