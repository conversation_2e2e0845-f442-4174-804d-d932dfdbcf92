package com.datatech.slgzt.controller;

import com.datatech.slgzt.convert.ManagerViewConvert;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.CustomCountDTO;
import com.datatech.slgzt.model.dto.CustomDTO;
import com.datatech.slgzt.model.dto.CustomTenantDTO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import com.datatech.slgzt.model.query.CustomQuery;
import com.datatech.slgzt.model.req.custom.CustomPageReq;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.managerView.*;
import com.datatech.slgzt.service.ManagerViewService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 客户经理视图控制器
 * @author: LK
 * @create: 2025-07-01 15:50
 **/
@RestController
@RequestMapping("/managerView")
public class ManagerViewController {

    @Resource
    private ManagerViewService managerViewService;

    @Resource
    private ManagerViewConvert convert;

    @GetMapping("/countCustom")
    public CommonResult<CustomCountVO> countCustom() {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        //超级管理员或者资源管理员查看所有数据，不需要传id
        if (roles.contains("super_admin") || roles.contains("general_admin") || roles.stream().anyMatch(role -> role.startsWith("operation_group"))) {
            currentUserId = null;
        }
        CustomCountDTO customCountDTO = managerViewService.customCount(currentUserId);
        return CommonResult.success(convert.convert(customCountDTO));
    }

    @GetMapping("/customs")
    public CommonResult<List<CustomTenantVO>> customDetail() {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        //超级管理员或者资源管理员查看所有数据，不需要传id
        if (roles.contains("super_admin") || roles.contains("general_admin") || roles.stream().anyMatch(role -> role.startsWith("operation_group"))) {
            currentUserId = null;
        }
        List<CustomTenantDTO> customTenantDTOS = managerViewService.customDetail(currentUserId);
        return CommonResult.success(convert.convertCustomTenants(customTenantDTOS));
    }

    @GetMapping("/top5vCPUGroupByCustom")
    public CommonResult<List<CustomCPUVO>> selectTop5vCPUGroupByCustom() {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        //超级管理员或者资源管理员查看所有数据，不需要传id
        if (roles.contains("super_admin") || roles.contains("general_admin") || roles.stream().anyMatch(role -> role.startsWith("operation_group"))) {
            currentUserId = null;
        }
        List<VMResourcePerformanceDTO> vmResourcePerformanceDTOS = managerViewService.selectTop5vCPUGroupByCustom(currentUserId);
        return CommonResult.success(convert.convert(vmResourcePerformanceDTOS));
    }

    @GetMapping("/totalProductTop5")
    public CommonResult<List<CustomTenantVO>> totalProductTop5() {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        //超级管理员或者资源管理员查看所有数据，不需要传id
        if (roles.contains("super_admin") || roles.contains("general_admin") || roles.stream().anyMatch(role -> role.startsWith("operation_group"))) {
            currentUserId = null;
        }
        List<CustomTenantDTO> customTenantDTOS = managerViewService.totalProductTop5(currentUserId);
        return CommonResult.success(convert.convertCustomTenants(customTenantDTOS));
    }

    @GetMapping("/top5MemGroupByCustom")
    public CommonResult<List<CustomCPUVO>> top5MemGroupByCustom() {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        //超级管理员或者资源管理员查看所有数据，不需要传id
        if (roles.contains("super_admin") || roles.contains("general_admin") || roles.stream().anyMatch(role -> role.startsWith("operation_group"))) {
            currentUserId = null;
        }
        List<VMResourcePerformanceDTO> vmResourcePerformanceDTOS = managerViewService.selectTop5MemGroupByCustom(currentUserId);
        return CommonResult.success(convert.convert(vmResourcePerformanceDTOS));
    }

    @GetMapping("/top5Resource")
    public CommonResult<List<ResourceVO>> selectTop5Resource(@RequestParam("orderType") String orderType) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        //超级管理员或者资源管理员查看所有数据，不需要传id
        if (roles.contains("super_admin") || roles.contains("general_admin") || roles.stream().anyMatch(role -> role.startsWith("operation_group"))) {
            currentUserId = null;
        }
        List<VMResourcePerformanceDTO> vmResourcePerformanceDTOS = managerViewService.top5Resource(currentUserId, orderType);
        return CommonResult.success(convert.convertResources(vmResourcePerformanceDTOS));
    }

    @GetMapping("/customDetail")
    public CommonResult<CustomVO> getCustomDetail(@RequestParam("customId") String customId) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        CustomDTO customDTO = managerViewService.getCustomDetail(customId);
        return CommonResult.success(convert.convert(customDTO));
    }

    @GetMapping("/top5ResourceOfCustom")
    public CommonResult<List<ResourceVO>> top5ResourceOfCustom(@RequestParam("customId") String customId,
                                                               @RequestParam("orderType") String orderType) {
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        List<VMResourcePerformanceDTO> vmResourcePerformanceDTOS = managerViewService.top5ResourceOfCustom(customId, orderType);
        return CommonResult.success(convert.convertResources(vmResourcePerformanceDTOS));
    }

    @PostMapping("/customList")
    public CommonResult<PageResult<CustomVO>> getCustomList(@RequestBody CustomPageReq req) {
        CustomQuery customQuery = convert.convertReq(req);
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        //超级管理员或者资源管理员查看所有数据，不需要传id
        if (roles.contains("super_admin") || roles.contains("general_admin") || roles.stream().anyMatch(role -> role.startsWith("operation_group"))) {
            currentUserId = null;
        }
        customQuery.setCurrentUserId(currentUserId);
        PageResult<CustomDTO> page = managerViewService.page(customQuery);
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }

}
