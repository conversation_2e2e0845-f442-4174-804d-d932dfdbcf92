package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.DeviceVirtualInfoDO;
import com.datatech.slgzt.model.BaseVdevice;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.query.DeviceMetricQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Objects;


@Mapper(componentModel = "spring")
public interface DeviceVirtualInfoConvert {
    /**
     * 显卡基本信息转换成dto
     * @param deviceVirtualInfoDO
     * @return
     */
    DeviceGpuInfoDTO baseInfo2Dto(DeviceVirtualInfoDO deviceVirtualInfoDO);

    /**
     * DTO 转换成DO
     * @param deviceVirtualInfoDTO
     * @return
     */
    DeviceVirtualInfoDO dto2do(DeviceVirtualInfoDTO deviceVirtualInfoDTO);


    /**
     * 查询条件
     * @param deviceInfoQuery
     * @return
     */
    DeviceMetricQuery query2do(DeviceInfoQuery deviceInfoQuery);


    /**
     * DO -> DTO
     * @param deviceVirtualInfoDO
     * @return
     */
    DeviceVirtualInfoDTO do2Dto(DeviceVirtualInfoDO deviceVirtualInfoDO);

    /**
     * 虚拟显卡转换DTO
     * @param baseVdevices
     * @return
     */
    @Mapping(target = "deviceId", source = "uuid")
    @Mapping(target = "deviceIp", source = "ip")
    @Mapping(target = "deviceIndex", source = "pindex")
    @Mapping(target = "deviceVirtualIndex", source = "vindex")
    @Mapping(target = "physicalDeviceId", source = "deviceId")
    @Mapping(target = "memory", source = "memory" )
    DeviceVirtualInfoDTO baseDevice2Dto(BaseVdevice baseVdevices);


    DeviceVirtualInfoDTO info2VirtualDto(DeviceGpuInfoDTO deviceInfoDTO);


}