package com.cloud.marginal.layoutcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.StateEnum;
import com.cloud.marginal.enums.layout.StatusEnum;
import com.cloud.marginal.enums.layout.TaskRelTypeEnum;
import com.cloud.marginal.layoutcenter.config.Taskconfig;
import com.cloud.marginal.layoutcenter.service.LayoutService;
import com.cloud.marginal.layoutcenter.service.layoutdb.*;
import com.cloud.marginal.model.dto.layout.LayoutOrder;
import com.cloud.marginal.model.dto.layout.ProductOrder;
import com.cloud.marginal.model.entity.layout.*;
import com.cloud.marginal.utils.UuidUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service("layoutDefaultServiceImpl")
@Slf4j
public class LayoutDefaultServiceImpl implements LayoutService {

    @Resource
    private LayoutTaskDefService layoutTaskDefService;

    @Resource
    private TasksRelDefService tasksRelDefService;

    @Resource
    private TaskParamDefService taskParamDefService;

    @Resource
    private LayoutTaskNodeService layoutTaskNodeService;

    @Resource
    private TasksRelService tasksRelService;

    @Resource
    private LayoutParamService layoutParamService;

    @Resource
    private TaskParamService taskParamService;

    @Resource
    private LayoutTaskService layoutTaskService;

    @Autowired
    @Qualifier(value = "asyncServiceExecutor")
    private Executor asyncServiceExecutor;

    @Resource
    private LayoutService layoutDefaultServiceImpl;

    @Resource
    private LayoutService layoutServiceImpl;

    @Resource
    private Taskconfig taskconfig;


    @Override
    public CecResult layoutTaskInit(LayoutOrder layoutOrder) {
        log.info("task layout init start");
        Date curDate = new Date();
        List<LayoutTask> mainTaskList = new ArrayList<>();
        List<LayoutTaskNode> taskNodeList = new ArrayList<>();
        List<TasksRel> tasksRelList = new ArrayList<>();
        List<LayoutApi> layoutApiList = new ArrayList<>();
        List<TaskNodeApi> taskNodeApiList = new ArrayList<>();
        List<LayoutParam> layoutParamList = new ArrayList<>();
        List<TaskParam> taskParamList = new ArrayList<>();

        // 临时兼容
        if (StringUtils.isEmpty(layoutOrder.getProductOrders().get(0).getSubOrderId()) && !StringUtils.isEmpty((layoutOrder.getSubOrderId()))) {
            layoutOrder.getProductOrders().forEach(productOrder -> productOrder.setSubOrderId(layoutOrder.getSubOrderId()));
        }
        // 按照子订单id进行分组
        Map<String, List<ProductOrder>> collect = layoutOrder.getProductOrders().stream().collect(Collectors.groupingBy(ProductOrder::getSubOrderId));

        // 查询模板的所有任务
        final List<LayoutTaskNode> layoutTaskNodeList = layoutTaskDefService.generateTaskNodeByDefault(layoutOrder.getBusinessCode());
        if (ObjectUtils.isEmpty(layoutTaskNodeList)) {
            throw new BusinessException("模板:" + layoutOrder.getBusinessCode() + "找不到任务");
        }
        collect.forEach((subOrderId, productOrderList) -> {
            // 深拷贝
            LayoutOrder layoutOrderCopy = JSONObject.parseObject(JSONObject.toJSONString(layoutOrder), LayoutOrder.class);
            layoutOrderCopy.setSubOrderId(subOrderId);
            layoutOrderCopy.setProductOrders(productOrderList);

            // 主任务
            LayoutTask mainTask = this.generateMainTask(curDate, layoutOrderCopy);
            mainTaskList.add(mainTask);

            // 子任务列表
            List<LayoutTaskNode> taskNodes = new ArrayList<>();

            // 产品订单生成子任务
            productOrderList.forEach(productOrder -> {
                LayoutTaskNode productTaskNode = this.generateProductTaskNode(productOrder, curDate, layoutOrderCopy.getBusinessCode(), mainTask.getId(), layoutTaskNodeList);
                taskNodes.add(productTaskNode);
            });

            // 创建一个主任务节点加入到任务节点集合里就能统一创建任务依赖关系。
            // 创建完依赖关系和任务与api关系后删除主任务节点，主任务是存主任务表的
            LayoutTaskNode mainTaskNode = this.generateMainTaskNode(mainTask.getId());
            taskNodes.add(mainTaskNode);

            // 创建附件任务，如通知任务,操作类型(挂载/绑定)任务(示例：创建A任务依赖B与C任务存在)
            List<LayoutTaskNode> followTaskNodeList = this.generateFollowTaskNode(taskNodes, layoutOrderCopy.getBusinessCode(), mainTask.getId(), curDate);
            taskNodes.addAll(followTaskNodeList);

            // 获取模板下任务与任务的关联(示例：执行A任务依赖B与C任务执行完成)
            List<TaskRel> taskCodeRelList = tasksRelDefService.getTaskRelListByTemplateCode(layoutOrderCopy.getBusinessCode());
            // 获取动态任务的关联
            Map<String, List<Map<String, String>>> dynamicTask = taskconfig.getDynamicTask();
            dynamicTask.putAll(taskconfig.getDynamicExecuteTask());
            dynamicTask.forEach((followTaskCode, dependencyTaskCodes) ->
                    dependencyTaskCodes.forEach(dependencyTaskCode -> {
                        String type = dependencyTaskCode.get("type");
                        // 指定为创建依赖的则不能加入执行依赖中
                        if (!"CREATE".equals(type)) {
                            TaskRel taskRel = new TaskRel();
                            taskRel.setTaskCode(followTaskCode);
                            taskRel.setRelType(TaskRelTypeEnum.EXECUTE);
                            taskRel.setRelTaskCode(dependencyTaskCode.get("code"));
                            taskRel.setRelState(8);
                            if (!StringUtils.isEmpty(dependencyTaskCode.get("count"))) {
                                taskRel.setRelCount(dependencyTaskCode.get("count"));
                            }
                            taskCodeRelList.add(taskRel);
                        }
                    }));

            // 生成任务关联关系(执行时依赖)
            List<TasksRel> tasksRels = this.generateTasksRel(curDate, taskNodes, taskCodeRelList);
            tasksRelList.addAll(tasksRels);

            // 生成编排参数和编排参数与子任务的关联关系
            LayoutParamDef layoutParamDef = taskParamDefService.getLayoutParamDefByTemplateCode(layoutOrderCopy.getBusinessCode());
            LayoutParamAndMainTaskParamRel layoutParamAndMainTaskParamRel = this.generateLayoutParamAndSubTaskRel(curDate, mainTask, layoutParamDef, layoutOrderCopy);
            layoutParamList.add(layoutParamAndMainTaskParamRel.getLayoutParam());
            taskParamList.add(layoutParamAndMainTaskParamRel.getMainTaskParamRel());

            // 删除主任务
            taskNodes.remove(mainTaskNode);
            // 设置任务运行时code
            taskNodes.forEach(t -> {
                if (!StringUtils.isEmpty(t.getCurrentTaskCode())) {
                    t.setTaskCode(t.getCurrentTaskCode());
                    t.setMonitorCount(360);
                }
            });
            taskNodeList.addAll(taskNodes);
        });

        // 入库
        layoutDefaultServiceImpl.initSave(mainTaskList, taskNodeList, tasksRelList, layoutApiList, taskNodeApiList, layoutParamList, taskParamList);

        // 异步递归执行,只执行1个,超过则通过定时任务分片执行
        asyncServiceExecutor.execute(() -> layoutServiceImpl.layoutTaskExecut(mainTaskList.get(0).getId()));

        log.info("task layout init end");
        return CecResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initSave(List<LayoutTask> mainTaskList, List<LayoutTaskNode> taskNodeList, List<TasksRel> tasksRelList, List<LayoutApi> layoutApiList, List<TaskNodeApi> taskNodeApiList, List<LayoutParam> layoutParamList, List<TaskParam> taskParamList) {
        layoutTaskService.saveBatch(mainTaskList);
        layoutTaskNodeService.saveBatch(taskNodeList);
        tasksRelService.saveBatch(tasksRelList);
        layoutParamService.saveBatch(layoutParamList);
        taskParamService.saveBatch(taskParamList);
    }


    /**
     * 生成主任务节点，只用作增加主任务的依赖关系
     *
     * @param id 主任务id
     */
    private LayoutTaskNode generateMainTaskNode(String id) {
        LayoutTaskNode mainTaskNode = new LayoutTaskNode();
        mainTaskNode.setId(id);
        mainTaskNode.setTaskCode("MASK_TASK");
        return mainTaskNode;
    }


    /**
     * 生成编排参数和编排参数与子任务的关联关系
     *
     * @param curDate        当前时间
     * @param mainTask       主任务
     * @param layoutParamDef 编排参数定义
     * @param layoutOrder    编排订单
     */
    private LayoutParamAndMainTaskParamRel generateLayoutParamAndSubTaskRel(Date curDate, LayoutTask mainTask, LayoutParamDef layoutParamDef, LayoutOrder layoutOrder) {
        String paramJsonStr = JSONObject.toJSONString(layoutOrder);
        LayoutParamAndMainTaskParamRel layoutParamAndSubTaskRel = new LayoutParamAndMainTaskParamRel();
        LayoutParam layoutParam = new LayoutParam().setId(UuidUtil.generateId())
                .setParamName(layoutParamDef == null ? "DEFAULT" : layoutParamDef.getParamName())
                .setParamCode(layoutParamDef == null ? "COMMON" : layoutParamDef.getParamCode())
                .setBeanName(layoutParamDef == null ? "LayoutOrder" : layoutParamDef.getBeanName())
                .setCreatedTime(curDate)
                .setRevision(0)
                .setStatus(StatusEnum.VALID.getCode())
                .setParamValue(paramJsonStr);
        TaskParam mainTaskParamRels = new TaskParam().setId(UuidUtil.generateId())
                .setParamId(layoutParam.getId())
                .setMasterTaskId(mainTask.getId())
                .setCreatedTime(curDate)
                .setStatus(StatusEnum.VALID.getCode());
        layoutParamAndSubTaskRel.setLayoutParam(layoutParam);
        layoutParamAndSubTaskRel.setMainTaskParamRel(mainTaskParamRels);
        return layoutParamAndSubTaskRel;
    }


    /**
     * 生成任务关联关系(执行时依赖)
     *
     * @param curDate         当前时间
     * @param taskNodes       任务列表
     * @param taskCodeRelList 任务编码关联关系
     */
    private List<TasksRel> generateTasksRel(Date curDate, List<LayoutTaskNode> taskNodes, List<TaskRel> taskCodeRelList) {
        List<String> bindExist = new ArrayList<>();
        List<String> productNotice = new ArrayList<>();
        List<TasksRel> tasksRels = new ArrayList<>();
        taskNodes.forEach(layoutTaskNode -> {
            String taskNodeCode = StringUtils.isEmpty(layoutTaskNode.getCurrentTaskCode())
                    ? layoutTaskNode.getTaskCode() : layoutTaskNode.getCurrentTaskCode();
            List<String> bindCodeExist = new ArrayList<>();
            Boolean productNoticeNotice = false;
            for (TaskRel taskRel : taskCodeRelList) {
                // 第一层if获取的是节点任务对象
                if (taskNodeCode.equals(taskRel.getTaskCode()) &&
                        taskRel.getRelType().equals(TaskRelTypeEnum.EXECUTE)) {

                    for (LayoutTaskNode taskNode : taskNodes) {
                        String relTaskNodeCode = StringUtils.isEmpty(taskNode.getCurrentTaskCode())
                                ? taskNode.getTaskCode() : taskNode.getCurrentTaskCode();
                        // 第二层if获取的是依赖的任务对象
                        if (taskRel.getRelType().equals(TaskRelTypeEnum.EXECUTE) &&
                                (relTaskNodeCode.equals(taskRel.getRelTaskCode()) ||
                                        relTaskNodeCode.split("_")[1].equals(taskRel.getRelTaskCode())
                                )
                                ) {

                            // 非通知任务的处理
                            if (!taskNodeCode.contains("NOTICE")) {
                                // 1.限制条件:任务的执行只能依赖一次同类任务
                                if (bindCodeExist.contains(relTaskNodeCode)) {
                                    continue;
                                }
                                bindCodeExist.add(relTaskNodeCode);
                                // 2.限制特定条件(relCount=ONE)下:多个同类任务的执行不能重复依赖同一个任务
                                if ("ONE".equals(taskRel.getRelCount())) {
                                    if (bindExist.contains(taskNodeCode + taskNode.getId())) {
                                        continue;
                                    }
                                    bindExist.add(taskNodeCode + taskNode.getId());
                                }
                            } else {
                                // 产品通知任务的处理
                                if (!"MASK_NOTICE".equals(taskNodeCode)) {
                                    // 产品通知任务只能依赖一个产品任务，且改产品任务不能被产品通知任务重复依赖
                                    if (productNotice.contains(relTaskNodeCode) || productNoticeNotice) {
                                        continue;
                                    }
                                    productNoticeNotice = true;
                                    productNotice.add(relTaskNodeCode);
                                }
                            }

                            TasksRel tasksRel = new TasksRel().setId(UuidUtil.generateId())
                                    .setTaskId(layoutTaskNode.getId()) // 节点任务
                                    .setRelTaskId(taskNode.getId())  //依赖的任务
                                    .setStatus(StatusEnum.VALID.getCode())
                                    .setCreatedTime(curDate)
                                    .setRelType(taskRel.getRelType())
                                    .setDescription(taskRel.getDescription())
                                    .setRelState(taskRel.getRelState());
                            tasksRels.add(tasksRel);
                        }
                    }
                }
            }
        });
        return tasksRels;
    }

    /**
     * 生成产品订单任务
     *
     * @param productOrder 产品订单
     * @param curDate      当前时间
     * @param templateCode 模板编码
     * @param mainTaskId   主任务id
     */
    private LayoutTaskNode generateProductTaskNode(ProductOrder productOrder, Date curDate, String templateCode, String mainTaskId, final List<LayoutTaskNode> layoutTaskNodeList) {
        LayoutTaskNode taskNode = null;
        for (LayoutTaskNode tn : layoutTaskNodeList) {
            if (productOrder.getProductOrderType().contains(tn.getTaskCode())) {
                // 深拷贝
                taskNode = JSONObject.parseObject(JSONObject.toJSONString(tn), LayoutTaskNode.class);
                break;
            }
        }
        log.info("==========={}",null==taskNode);
        taskNode.setMasterTaskId(mainTaskId)
                .setId(UuidUtil.generateId())
                .setCreatedTime(curDate)
                .setStatus(StatusEnum.VALID.getCode())
                .setOrderId(productOrder.getProductOrderId())
                .setState(StateEnum.UNPROCESSED)
                .setRevision(0)
                .setProductType(productOrder.getProductType().toUpperCase())
                .setgId(productOrder.getgId());
        taskNode.setCurrentTaskCode(productOrder.getProductOrderType());

        return taskNode;
    }

    /**
     * 生成附加任务(创建时依赖)
     *
     * @param layoutTaskNodeList 子任务列表
     * @param templateCode       模板编码
     */
    private List<LayoutTaskNode> generateFollowTaskNode(List<LayoutTaskNode> layoutTaskNodeList, String templateCode, String mainTaskId, Date curDate) {
        // 根据模板获取创建类型的依赖任务列表
        List<FollowTaskDef> followTaskDefList = layoutTaskDefService.getFollowTaskDefByTemplateCode(templateCode);
        List<LayoutTaskNode> followTaskNodeList = new ArrayList<>();

        // 按照任务id分组,一个创建任务(自动创建的任务)有一个或多个前置依赖
        // key: 附加任务id
        // values: 附加任务所依赖的任务列表
        Map<String, List<FollowTaskDef>> collect = followTaskDefList.stream().collect(Collectors.groupingBy(FollowTaskDef::getFollowTaskId));
        collect.forEach((followTaskId, followTaskDefs) -> {
            // 前置依赖是否都满足,且如果满足的依赖有多个,则生成多个附加任务
            boolean followTaskIsCreatable = true;
            int count = 1;
            for (FollowTaskDef followTaskDef : followTaskDefs) {
                int i = this.taskNodeIsContainFollowTaskDependencyTask(followTaskDef.getDependencyTaskCode(), layoutTaskNodeList);
                if (i == 0) {
                    followTaskIsCreatable = false;
                    break;
                }
                if (i > count) {
                    count = i;
                }
            }
            if (followTaskIsCreatable) {
                LayoutTaskNode layoutTaskNode = layoutTaskDefService.generateTaskNodeByTemplateCodeAndTaskCode(templateCode, followTaskDefs.get(0).getFollowTaskCode());
                for (int i = 0; i < count; i++) {
                    LayoutTaskNode node = JSONObject.parseObject(JSONObject.toJSONString(layoutTaskNode), LayoutTaskNode.class);
                    node.setRevision(0)
                            .setId(UuidUtil.generateId())
                            .setMasterTaskId(mainTaskId)
                            .setCreatedTime(curDate)
                            .setStatus(StatusEnum.VALID.getCode())
                            .setState(StateEnum.UNPROCESSED);
                    // 默认附加任务都是通知任务
                    if (StringUtils.isEmpty(node.getProductType())) {
                        node.setProductType("NOTICE");
                    }
                    followTaskNodeList.add(node);
                }
            }
        });

        // 加入动态任务
        // key:附加任务编码
        // values: 附加任务所依赖的任务编码列表
        taskconfig.getDynamicTask().forEach((followTaskCode, dependencyTaskCodes) -> {
            // 前置依赖是否都满足,且如果满足的依赖有多个,则生成多个附加任务
            boolean followTaskIsCreatable = true;
            int count = 1;
            for (Map<String, String> dependencyTaskCode : dependencyTaskCodes) {
                int i = this.taskNodeIsContainFollowTaskDependencyTask(dependencyTaskCode.get("code"), layoutTaskNodeList);
                if (i == 0) {
                    followTaskIsCreatable = false;
                    break;
                }
                if (i > count) {
                    count = i;
                }
            }
            if (followTaskIsCreatable) {
                String ftCode = "";
                if (followTaskCode.contains("BIND")) {
                    ftCode = "BIND";
                }
                if (followTaskCode.contains("UNBIND")) {
                    ftCode = "UNBIND";
                }
                LayoutTaskNode layoutTaskNode = layoutTaskDefService.generateTaskNodeByTemplateCodeAndTaskCode(templateCode, ftCode);
                for (int i = 0; i < count; i++) {
                    LayoutTaskNode node = JSONObject.parseObject(JSONObject.toJSONString(layoutTaskNode), LayoutTaskNode.class);
                    node.setRevision(0)
                            .setId(UuidUtil.generateId())
                            .setMasterTaskId(mainTaskId)
                            .setCreatedTime(curDate)
                            .setStatus(StatusEnum.VALID.getCode())
                            .setState(StateEnum.UNPROCESSED)
                            // 产品类型取主产品类型(产品类型_操作类型_从产品类型)
                            .setProductType(followTaskCode.split("_")[0])
                            .setCurrentTaskCode(followTaskCode);
                    followTaskNodeList.add(node);
                }
            }
        });
        return followTaskNodeList;
    }


    /**
     * 判断任务节点中是否包含附加任务所依赖的任务
     *
     * @param dependencyTaskCode 附加任务所依赖的任务的编号
     * @param layoutTaskNodeList 任务节点集合
     */
    private int taskNodeIsContainFollowTaskDependencyTask(String dependencyTaskCode, List<LayoutTaskNode> layoutTaskNodeList) {
        int i = 0;
        for (LayoutTaskNode layoutTaskNode : layoutTaskNodeList) {
            if (dependencyTaskCode.equals(layoutTaskNode.getTaskCode()) ||
                    dependencyTaskCode.equals(layoutTaskNode.getCurrentTaskCode())) {
                i++;
            }
        }
        return i;
    }

    /**
     * 生成主任务
     *
     * @param createDate  创建时间
     * @param layoutOrder 编排订单
     */
    private LayoutTask generateMainTask(Date createDate, LayoutOrder layoutOrder) {
        LayoutTask layoutTask = layoutTaskDefService.generateMainTaskByTemplateCode(layoutOrder.getBusinessCode());
        return layoutTask.setId(UuidUtil.generateId())
                .setSubOrderId(layoutOrder.getSubOrderId())
                .setCreatedTime(createDate)
                .setState(StateEnum.UNPROCESSED)
                .setStatus(StatusEnum.VALID.getCode())
                .setRevision(0)
                .setSourceExtType(layoutOrder.getSourceExtType())
                .setTaskSource(layoutOrder.getTaskSource());
    }


    @Data
    private class LayoutParamAndMainTaskParamRel {
        /**
         * 编排参数
         */
        private LayoutParam layoutParam;

        /**
         * 子任务与编排参数的关联
         */
        private TaskParam mainTaskParamRel;
    }

}
