package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.RdsWhiteDO;
import com.datatech.slgzt.model.dto.RdsWhiteDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单Manager转换器
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Mapper(componentModel = "spring")
public interface RdsWhiteManagerConvert {

    /**
     * DTO转DO
     */
    RdsWhiteDO dto2do(RdsWhiteDTO dto);

    /**
     * DO转DTO
     */
    RdsWhiteDTO do2dto(RdsWhiteDO entity);

    /**
     * DO列表转DTO列表
     */
    List<RdsWhiteDTO> do2dto(List<RdsWhiteDO> list);
} 