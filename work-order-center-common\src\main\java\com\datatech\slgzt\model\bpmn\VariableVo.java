package com.datatech.slgzt.model.bpmn;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@Accessors(chain = true)
public class VariableVo {

    /**
     * 流程实例ID
     */
    @NotNull(message = "流程实例id不能为空")
    private String processInstanceId;

    /**
     * 架构管理员key:schema
     * 业务部门领导key:business
     * 云中心领导key:cloud
     */
    private Map<String, Object> variables;
}
