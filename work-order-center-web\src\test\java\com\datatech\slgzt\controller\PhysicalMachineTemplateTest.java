package com.datatech.slgzt.controller;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import org.apache.poi.ss.usermodel.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 物理机Excel模板生成测试
 * 验证数值字段是否正确设置为Excel数值类型
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@DisplayName("物理机Excel模板生成测试")
class PhysicalMachineTemplateTest {

    @TempDir
    Path tempDir;

    @Test
    @DisplayName("验证Excel模板中数值字段的类型设置")
    void testExcelTemplateNumericFields() throws Exception {
        // 创建GoodsController实例
        GoodsController controller = new GoodsController();
        
        // 生成临时文件路径
        File tempFile = tempDir.resolve("test_template.xlsx").toFile();
        String filePath = tempFile.getAbsolutePath();
        
        // 调用私有方法生成模板（需要使用反射）
        java.lang.reflect.Method method = GoodsController.class.getDeclaredMethod("createPhysicalMachineTemplate", String.class);
        method.setAccessible(true);
        method.invoke(controller, filePath);
        
        // 验证文件是否生成
        assertTrue(tempFile.exists(), "Excel模板文件应该被成功创建");
        assertTrue(tempFile.length() > 0, "Excel模板文件不应该为空");
        
        // 使用Apache POI读取Excel文件验证单元格类型
        try (FileInputStream fis = new FileInputStream(tempFile);
             Workbook workbook = WorkbookFactory.create(fis)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Row dataRow = sheet.getRow(1); // 示例数据行（第2行，索引为1）
            
            assertNotNull(dataRow, "示例数据行应该存在");
            
            // 验证表头行（第1行）都是文本类型
            Row headerRow = sheet.getRow(0);
            assertNotNull(headerRow, "表头行应该存在");
            
            for (int i = 0; i < 18; i++) {
                Cell headerCell = headerRow.getCell(i);
                assertNotNull(headerCell, "表头单元格 " + i + " 应该存在");
                assertEquals(CellType.STRING, headerCell.getCellType(), 
                    "表头单元格 " + i + " 应该是文本类型");
            }
            
            // 验证示例数据行中的文本字段
            verifyTextCell(dataRow, 0, "示例物理机名称", "物理机名称");
            verifyTextCell(dataRow, 1, "已交维", "交维状态");
            verifyTextCell(dataRow, 2, "BCLinux for Euler 21.10", "系统版本");
            verifyTextCell(dataRow, 3, "NPU", "显卡类型");
            verifyTextCell(dataRow, 4, "910B", "显卡型号");
            
            // 验证数值字段 - 这些应该是数值类型
            verifyNumericCell(dataRow, 5, 8.0, "显卡数量");
            verifyNumericCell(dataRow, 6, 192.0, "CPU核心数");
            verifyNumericCell(dataRow, 7, 2048.0, "内存(GB)");
            verifyNumericCell(dataRow, 8, 96960.0, "硬盘(GB)");
            
            // 验证其他文本字段
            verifyTextCell(dataRow, 9, "**************", "IP地址");
            verifyTextCell(dataRow, 10, "两年", "申请时长");
            verifyTextCell(dataRow, 11, "网络运维AI+Paas平台", "业务系统");
            verifyTextCell(dataRow, 12, "平台云", "所属云");
            verifyTextCell(dataRow, 13, "平台云-萧山02", "资源池");
            verifyTextCell(dataRow, 14, "2024年6月21日", "开通时间");
            verifyTextCell(dataRow, 15, "2026年6月21日", "到期时间");
            verifyTextCell(dataRow, 16, "示例申请人", "申请人");
            verifyTextCell(dataRow, 17, "唯一id", "配置项编号");
        }
    }

    @Test
    @DisplayName("验证Excel模板可以被hutool正确读取")
    void testExcelTemplateReadability() throws Exception {
        // 创建GoodsController实例
        GoodsController controller = new GoodsController();
        
        // 生成临时文件路径
        File tempFile = tempDir.resolve("test_template_read.xlsx").toFile();
        String filePath = tempFile.getAbsolutePath();
        
        // 调用私有方法生成模板
        java.lang.reflect.Method method = GoodsController.class.getDeclaredMethod("createPhysicalMachineTemplate", String.class);
        method.setAccessible(true);
        method.invoke(controller, filePath);
        
        // 使用hutool读取Excel文件
        ExcelReader reader = ExcelUtil.getReader(tempFile);
        
        // 验证表头
        java.util.List<Object> headers = reader.readRow(0);
        assertEquals(18, headers.size(), "应该有18个表头");
        assertEquals("物理机名称", headers.get(0).toString());
        assertEquals("显卡数量", headers.get(5).toString());
        assertEquals("CPU核心数", headers.get(6).toString());
        assertEquals("内存(GB)", headers.get(7).toString());
        assertEquals("硬盘(GB)", headers.get(8).toString());
        
        // 验证示例数据行
        java.util.List<Object> dataRow = reader.readRow(1);
        assertEquals(18, dataRow.size(), "示例数据行应该有18个字段");
        
        // 验证数值字段可以被正确读取为数值
        assertEquals("示例物理机名称", dataRow.get(0).toString());
        
        // 数值字段应该能被解析为数值
        Object gpuNum = dataRow.get(5);
        assertTrue(gpuNum instanceof Number || "8".equals(gpuNum.toString()), 
            "显卡数量应该是数值类型或可转换为数值");
        
        Object cpuNum = dataRow.get(6);
        assertTrue(cpuNum instanceof Number || "192".equals(cpuNum.toString()), 
            "CPU核心数应该是数值类型或可转换为数值");
        
        Object memoryNum = dataRow.get(7);
        assertTrue(memoryNum instanceof Number || "2048".equals(memoryNum.toString()), 
            "内存应该是数值类型或可转换为数值");
        
        Object diskNum = dataRow.get(8);
        assertTrue(diskNum instanceof Number || "96960".equals(diskNum.toString()), 
            "硬盘应该是数值类型或可转换为数值");
        
        reader.close();
    }

    @Test
    @DisplayName("验证Excel模板的列数和基本结构")
    void testExcelTemplateStructure() throws Exception {
        // 创建GoodsController实例
        GoodsController controller = new GoodsController();
        
        // 生成临时文件路径
        File tempFile = tempDir.resolve("test_template_structure.xlsx").toFile();
        String filePath = tempFile.getAbsolutePath();
        
        // 调用私有方法生成模板
        java.lang.reflect.Method method = GoodsController.class.getDeclaredMethod("createPhysicalMachineTemplate", String.class);
        method.setAccessible(true);
        method.invoke(controller, filePath);
        
        // 使用Apache POI验证基本结构
        try (FileInputStream fis = new FileInputStream(tempFile);
             Workbook workbook = WorkbookFactory.create(fis)) {
            
            assertEquals(1, workbook.getNumberOfSheets(), "应该只有一个工作表");
            
            Sheet sheet = workbook.getSheetAt(0);
            assertEquals(2, sheet.getLastRowNum() + 1, "应该有2行数据（表头+示例）");
            
            Row headerRow = sheet.getRow(0);
            assertEquals(18, headerRow.getLastCellNum(), "表头应该有18列");
            
            Row dataRow = sheet.getRow(1);
            assertEquals(18, dataRow.getLastCellNum(), "示例数据行应该有18列");
        }
    }

    /**
     * 验证文本单元格
     */
    private void verifyTextCell(Row row, int cellIndex, String expectedValue, String fieldName) {
        Cell cell = row.getCell(cellIndex);
        assertNotNull(cell, fieldName + " 单元格应该存在");
        assertEquals(CellType.STRING, cell.getCellType(), 
            fieldName + " 应该是文本类型");
        assertEquals(expectedValue, cell.getStringCellValue(), 
            fieldName + " 的值应该正确");
    }

    /**
     * 验证数值单元格
     */
    private void verifyNumericCell(Row row, int cellIndex, double expectedValue, String fieldName) {
        Cell cell = row.getCell(cellIndex);
        assertNotNull(cell, fieldName + " 单元格应该存在");
        assertEquals(CellType.NUMERIC, cell.getCellType(), 
            fieldName + " 应该是数值类型");
        assertEquals(expectedValue, cell.getNumericCellValue(), 0.001, 
            fieldName + " 的数值应该正确");
    }
}
