package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EcsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.VpcParam;
import com.cloud.marginal.mapper.layout.ResourceDetailMapper;
import com.cloud.marginal.model.dto.edge.*;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 虚拟机管理接口适配
 */
@Component
@Slf4j
public class VirtualMachineMgAdapter extends BaseNorthInterfaceAdapter {

    @Resource
    private ResourceDetailMapper resourceDetailMapper;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 资源操作topic
     */
    private final static String RESOURCE_OPERATE_TOPIC = "prod_resource_operate_topic";

    /**
     * 创建云主机
     */
    public TaskVO createVirtualMachine(String taskId, Integer taskSource) {
        log.info("createVirtualMachine start");
        CreateVirtualMachineDTO createVirtualMachineDTO = generateCreateVmDto(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateVirtualMachine(),
                null,
                createVirtualMachineDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "create virtual machine");
        return tasksVoResult.getEntity();
    }

    private CreateVirtualMachineDTO generateCreateVmDto(LayoutParam layoutParam) {
        CreateVirtualMachineDTO createVirtualMachineDTO = new CreateVirtualMachineDTO();
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.ECS_CREATE.getCode());
        EcsParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);

        // 基础参数设置
        createVirtualMachineDTO.setAzCode(ecsParam.getAzCode());
        createVirtualMachineDTO.setTenantId(layoutOrderParam.getTenantId());
        createVirtualMachineDTO.setRegionCode(layoutOrderParam.getRegionCode());
        createVirtualMachineDTO.setFlavorModelCode(ecsParam.getFlavorCode());
        createVirtualMachineDTO.setFlavorId(ecsParam.getFlavorId());
        createVirtualMachineDTO.setVmName(ecsParam.getVmName());
        createVirtualMachineDTO.setAlias(ecsParam.getAlias());
        createVirtualMachineDTO.setImageId(ecsParam.getImageId());
        createVirtualMachineDTO.setOrderId(ecsOrder.getProductOrderId());
        createVirtualMachineDTO.setUserName(ecsParam.getUserName());
        createVirtualMachineDTO.setPasswordPolicy(ecsParam.getPasswordPolicy());
        createVirtualMachineDTO.setAdminPass(ecsParam.getAdminPass());
        createVirtualMachineDTO.setgId(ecsParam.getgId());
        createVirtualMachineDTO.setOutInstanceId(ecsParam.getOutInstanceId());
        createVirtualMachineDTO.setUse(ecsParam.getUse());
        createVirtualMachineDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        createVirtualMachineDTO.setHostId(ecsParam.getHostId());
        createVirtualMachineDTO.setStoragePoolId(ecsParam.getStoragePoolId());

        // 设置系统盘
        createVirtualMachineDTO.setSysDiskSize(ecsParam.getRootVolume().getSysDiskSize());
        createVirtualMachineDTO.setSysDiskType(ecsParam.getRootVolume().getSysDiskType());

        // 设置安全组
        List<String> securityGroupIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ecsParam.getSecurityGroupIds())) {
            ecsParam.getSecurityGroupIds().forEach(item -> {
                securityGroupIds.add(item.getSecurityGroupId());
            });
            createVirtualMachineDTO.setSecurityGroupIds(securityGroupIds);
        }

        // 设置网络
        // 网卡或者网卡中的子网id为空的情况
        if (CollUtil.isEmpty(ecsParam.getNics()) || StringUtils.isEmpty(ecsParam.getNics().get(0).getSubnetId())) {
            if (ecsParam.getVpcInfo() != null) {
                // 指定了存在的vpc网络场景
                CreateVirtualMachineDTO.VpcInfo vpcInfo = new CreateVirtualMachineDTO.VpcInfo();
                vpcInfo.setVpcId(ecsParam.getVpcInfo().getVpcId());
                vpcInfo.setSubnets(getSubnets(ecsParam.getVpcInfo().getSubnets()));
                vpcInfo.setNetworkIds(ecsParam.getVpcInfo().getNetworkIds());
                createVirtualMachineDTO.setVpcInfo(vpcInfo);
            } else {
                // 创建子网设置(与云主机一并创建)
                if (!ObjectUtils.isEmpty(ecsParam.getSubnetInfo())) {
                    EcsParam.SubnetInfo subnetInfo = ecsParam.getSubnetInfo();
                    CreateVirtualMachineDTO.SubnetInfo info = new CreateVirtualMachineDTO.SubnetInfo();
                    BeanUtil.copyProperties(subnetInfo, info);
                    info.setName(subnetInfo.getSubnetName());
                    createVirtualMachineDTO.setSubnetInfo(info);
                } else {
                    // 组合创建网络场景(与云主机分开创建再组合)
                    ProductOrderParam vpcOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.VPC_CREATE.getCode());
                    VpcParam vpcParam = JSONObject.parseObject(vpcOrder.getAttrs(), VpcParam.class);
                    CreateVirtualMachineDTO.VpcInfo vpcInfo = new CreateVirtualMachineDTO.VpcInfo();
                    vpcInfo.setVpcId(vpcParam.getId());
                    createVirtualMachineDTO.setVpcInfo(vpcInfo);
                }
            }
        }

        // 指定网卡设置
        if (!CollectionUtils.isEmpty(ecsParam.getNics())) {
            List<EcsParam.Nic> nics = ecsParam.getNics();
            List<CreateVirtualMachineDTO.Nic> nets = new ArrayList<>();
            for (EcsParam.Nic nic : nics) {
                CreateVirtualMachineDTO.Nic net = new CreateVirtualMachineDTO.Nic();
                net.setSubnetId(nic.getSubnetId());
                net.setIpAddress(nic.getIpAddress());
                nets.add(net);
            }
            createVirtualMachineDTO.setNics(nets);
        }

        return createVirtualMachineDTO;
    }


    private List<CreateVirtualMachineDTO.SubnetInfo> getSubnets(List<EcsParam.Subnet> subnets) {
        List<CreateVirtualMachineDTO.SubnetInfo> result = new ArrayList<>();
        if (subnets != null) {
            subnets.forEach(item -> {
                CreateVirtualMachineDTO.SubnetInfo subnetInfo = new CreateVirtualMachineDTO.SubnetInfo();
                subnetInfo.setIp(item.getSubnetIp());
                subnetInfo.setSubnetId(item.getSubnetId());
                subnetInfo.setIp(item.getIpAddress());
                result.add(subnetInfo);
            });
        }
        return result;
    }

    /**
     * 变更云主机规格
     */
    public TaskVO modifyVirtualMachine(String taskId, Integer taskSource) {
        log.info("modifyVirtualMachine start");
        ModifyVmFlavorDTO modifyVmFlavorDTO = generateModifyVmDto(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getModifyVirtualMachine(),
                null,
                modifyVmFlavorDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "modify virtual machine");
        return tasksVoResult.getEntity();
    }

    private ModifyVmFlavorDTO generateModifyVmDto(LayoutParam layoutParam) {
        ModifyVmFlavorDTO modifyVmFlavorDTO = new ModifyVmFlavorDTO();
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.ECS_MODIFY.getCode());
        EcsParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);

        modifyVmFlavorDTO.setTenantId(layoutOrderParam.getTenantId());
        //变更的规格
        modifyVmFlavorDTO.setFlavorModelCode(ecsParam.getFlavorCode());
        modifyVmFlavorDTO.setFlavorId(ecsParam.getFlavorId());
        log.info("------------" + ecsParam.getResourceId());
        modifyVmFlavorDTO.setVmId(ecsParam.getResourceId());
        return modifyVmFlavorDTO;
    }


    /**
     * 删除云主机
     */
    public TaskVO deleteVirtualMachine(String taskId, Integer taskSource) {
        log.info("deleteVirtualMachine start");
        DeleteVirtualMachineDTO deleteVirtualMachineDTO = generateDeleteVmDto(getLayoutParam(taskId));
        unBindEip(getLayoutParam(taskId));
        unMountVolume(deleteVirtualMachineDTO);
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteVirtualMachine(),
                null,
                deleteVirtualMachineDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "delete virtual machine");
        return tasksVoResult.getEntity();
    }

    private DeleteVirtualMachineDTO generateDeleteVmDto(LayoutParam layoutParam) {

        DeleteVirtualMachineDTO deleteVirtualMachineDTO = new DeleteVirtualMachineDTO();
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.ECS_DELETE.getCode());
        EcsParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);

        deleteVirtualMachineDTO.setTenantId(layoutOrderParam.getTenantId());

        log.info("------------" + ecsParam.getResourceId());
        deleteVirtualMachineDTO.setVmId(ecsParam.getResourceId());
        return deleteVirtualMachineDTO;
    }

    /**
     * 操作云主机
     */
    public CecResult operateVirtualMachine(OperateVirtualMachineDTO dto) {
        log.info("operateVirtualMachine start");
        try {
            CecResult<String> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getOperateVirtualMachine(),
                    null, dto,
                    new TypeReference<CecResult<String>>() {
                    },
                    null);
            checkResultThrowExceptionIfFail(tasksVoResult, "operate virtual machine");
            return tasksVoResult;
        } catch (Exception e) {
            TaskVO vo = new TaskVO();
            vo.setResourceType("ECS");
            vo.setOperationType(VmOperationEnum.getByAlias(dto.getOperationType()).getCode());
            vo.setResourceId(dto.getInstanceId());
            vo.setStatus("ERROR");
            vo.setInstanceId(dto.getInstanceId());
            vo.setMessage(e.getMessage());
            kafkaTemplate.send(RESOURCE_OPERATE_TOPIC, JSONObject.toJSONString(vo));
            throw e;
        }
    }



    /**
     * 操作云主机
     */
    public CecResult resetPwd(ResetVmPwdDTO dto) {
        log.info("resetPwd start");
        CecResult<String> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getResetPwd(),
                null, dto,
                new TypeReference<CecResult<String>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "resetPwd");
        return tasksVoResult;
    }

    public void unMountVolume(DeleteVirtualMachineDTO deleteVirtualMachineDTO) {
        //云主机回收，需卸载所有数据盘
        String vmId = deleteVirtualMachineDTO.getVmId();
        log.info("云主机id==》{}", vmId);
        List<String> list = resourceDetailMapper.selectVolumesByVimId(vmId);
        log.info("云主机挂载的数据盘：{}", list);
        if (!CollectionUtils.isEmpty(list)) {
            List<String> taskIds = new ArrayList<>();
            list.forEach(id -> {
                OperationVolumeDTO volumeAttachParam = new OperationVolumeDTO();
                volumeAttachParam.setTenantId(deleteVirtualMachineDTO.getTenantId());
                volumeAttachParam.setVmId(vmId);
                volumeAttachParam.setVolumeId(id);
                volumeAttachParam.setVolumeOperationType("UNMOUNT");
                CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getAttachVolume(),
                        null,
                        volumeAttachParam,
                        new TypeReference<CecResult<TaskVO>>() {
                        },
                        null);
                taskIds.add(tasksVoResult.getEntity().getId());
            });
            //循环
            for (int i = 0; i < 10; i++) {
                Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(taskIds);
                if (notSuccessNum == 0) {
                    return;
                }
            }
        }
    }

    public void unBindEip(LayoutParam layoutParam) {
        //云主机回收，解绑eip
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam ecsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.ECS_DELETE.getCode());
        EcsParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);
        String vmId = ecsParam.getResourceId();
        log.info("云主机id==》{}", vmId);
        List<String> list = resourceDetailMapper.selectEipsByDeviceId(vmId);
        log.info("云主机绑定的eip：{}", list);
        if (!CollectionUtils.isEmpty(list)) {
            List<String> taskIds = new ArrayList<>();
            list.forEach(id -> {
                UnBindEipDTO unBindEipDTO = new UnBindEipDTO();
                //计费号
                unBindEipDTO.setTenantId(layoutOrderParam.getTenantId());
                //regionCode
                unBindEipDTO.setRegionCode(layoutOrderParam.getRegionCode());
                unBindEipDTO.setPublicId(id);
                //解绑对象
                unBindEipDTO.setDeviceId(vmId);
                //解绑对象类型
                unBindEipDTO.setDeviceType("ECS");
                CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getUnBindEip(),
                        null,
                        unBindEipDTO,
                        new TypeReference<CecResult<TaskVO>>() {
                        },
                        null);
                taskIds.add(tasksVoResult.getEntity().getId());
            });
            //循环
            for (int i = 0; i < 10; i++) {
                Integer notSuccessNum = resourceDetailMapper.countNotSuccessNum(taskIds);
                if (notSuccessNum == 0) {
                    return;
                }
            }
        }
    }

    private enum VmOperationEnum {
//    VM_CREATE("VM_CREATE", "云主机创建"),
//    VM_SHUTDOWN("VM_SHUTDOWN", "云主机关机"),
//    VM_STARTUP("VM_STARTUP", "云主机开机"),
//    VM_REBOOT("VM_REBOOT", "云主机重启"),
//    VM_RESIZE("VM_RESIZE", "云主机更改规格"),
//    VM_DELETE("VM_DELETE", "云主机删除"),
//    VM_PEND("VM_PEND", "云主机挂起"),
//    VM_RECOVERY("VM_RECOVERY", "云主机恢复"),
//    VM_DIS_DIMENSION("VM_DIS_DIMENSION","云主机退维"),
//
//    VM_RESETPWD("VM_RESETPWD","修改密码"),
//
//    /**
//     * 云主机挂载
//     */
//    VM_MOUNT_PORT("VM_MOUNT_PORT", "云主机挂载虚拟网卡"),
//    VM_UNMOUNT_PORT("VM_UNMOUNT_PORT", "云主机卸载虚拟网卡"),
//    VM_MOUNT_VOLUME("VM_MOUNT_VOLUME", "云主机挂载硬盘"),
//    VM_UNMOUNT_VOLUME("VM_UNMOUNT_VOLUME", "云主机卸载硬盘")

        START("VM_STARTUP", "START", "开机"),
        STOP("VM_SHUTDOWN", "STOP", "关机"),
        PAUSE("PAUSE", "PAUSE", "暂停"),
        UNPAUSE("UNPAUSE", "UNPAUSE", ""),
        SUSPEND("VM_PEND", "SUSPEND", "挂起"),
        RESUME("RESUME", "RESUME", "恢复"),
        LOCK("LOCK", "LOCK", "锁定"),
        UNLOCK("UNLOCK", "UNLOCK", "解锁"),
        SOFTREBOOT("SOFTREBOOT", "SOFTREBOOT", "软启动"),
        REBOOT("VM_REBOOT", "RESTART", "重启"),
        MIGRATE("MIGRATE", "MIGRATE", ""),
        LIVEMIGRATE("LIVEMIGRATE", "LIVEMIGRATE", ""),
        REBUILD("REBUILD", "REBUILD", "重建"),
        RESETPWD("RESETPWD", "RESETPWD", "重置密码"),
        DELETE("DELETE", "DELETE", "回收"),
        BIND_SG("BIND_SG", "BIND_SG", "绑定安全组"),
        UNBIND_SG("UNBIND_SG", "UNBIND_SG", "解绑安全组"),
        OPERATE("OPERATE", "OPERATE", "操作"),
        /**
         * =========安全组和规则操作===============
         */
        SECURITY_GROUP("SECURITY_GROUP", "SECURITY_GROUP", "安全组创建"),
        SECURITY_GROUP_RULE("SECURITY_GROUP_RULE","SECURITY_GROUP_RULE","安全组规则创建");

        private final String code;

        private final String alias;

        private final String desc;

        public String getCode() {
            return code;
        }

        public String getAlias() {
            return alias;
        }

        public String getDesc() {
            return desc;
        }

        VmOperationEnum(String code, String alias, String desc) {
            this.code = code;
            this.alias = alias;
            this.desc = desc;
        }

        public static VmOperationEnum getByAlias(String alias) {
            if (!StringUtils.isEmpty(alias)) {
                for (VmOperationEnum value : values()) {
                    if (value.getAlias().equals(alias)) {
                        return value;
                    }
                }
            }
            return OPERATE;
        }
    }
}
