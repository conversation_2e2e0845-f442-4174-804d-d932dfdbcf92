package com.datatech.slgzt.model.dto;

import lombok.Data;

/**
 * 活跃统计聚合结果DTO
 */
@Data
public class ActiveStatisticsAggDTO {
    
    /**
     * 时间点（日：0-23小时，月：1-31日，年：1-12月）
     */
    private String timePoint;
    
    /**
     * 活跃用户数
     */
    private Long activeUserCount;
    
    /**
     * 用户登录数
     */
    private Long userLoginCount;
    
    /**
     * 点击量
     */
    private Long clickCount;
    
    /**
     * API访问量
     */
    private Long apiAccessCount;
} 