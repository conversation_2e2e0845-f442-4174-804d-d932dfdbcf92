package com.datatech.slgzt.model.vo.device;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Desc
 * <AUTHOR>
 * @DATA 2025-06-11
 */
@Data
@Accessors(chain = true)
public class DeviceMetricDetailInfoVO  implements Serializable {
    private Long id;


    /**
     * 资源池名称
     */
    private String regionName;


    /**
     * 业务系统名称
     */
    private String businessSystemName;

//====================设备基本信息=======================
    /**
     * GPU/NPU卡序列号/uuid
     */
    private String deviceId;


    /**
     * 设备显存大小
     */
    private Integer memory;


    /**
     * 算力利用率
     */
    private Double gpuUtilPercent;

    /**
     * 显存利用率
     */
    private Double memUtilpercent;


    /**
     * 任务数
     */
    private Integer allocationCount;

    /**
     * 温度
     */
    private Double devGpuTemp;
}
