package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.model.query.RecoveryWorkOrderQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface DgRecoveryOrderManager {



    String createWorkOrder(DgRecoveryOrderDTO reconveryWorkOrderDTO);

    PageResult<DgRecoveryOrderDTO> page(DgRecoveryOrderQuery query);

    DgRecoveryOrderDTO getById(String id);


    void update(DgRecoveryOrderDTO orderDTO);

    List<DgRecoveryOrderDTO> list(DgRecoveryOrderQuery query);
}
