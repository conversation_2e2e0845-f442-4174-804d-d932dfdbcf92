package com.datatech.slgzt.model.req.slb;

import lombok.Data;

/**
 * SLB监听器服务组分页查询请求
 */
@Data
public class SlbListenerServerGroupPageReq {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    
    /**
     * 分组名称
     */
    private String groupName;

    private String groupType;

    private Long slbResourceDetailId;
    
    /**
     * SLB监听器ID
     */
    private String slbListenerId;
} 