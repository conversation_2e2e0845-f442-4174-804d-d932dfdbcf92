package com.datatech.slgzt.enums.network;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/2/17
 */

@Getter
@AllArgsConstructor
public enum NetworkStatusEnum {
    // 回收状态 0 工单未发起回收 1 资源待回收 2 回收中 3 回收完成 4 回收失败
    SUCCESS("SUCCESS", "成功"),
    EXECUTING("EXECUTING", "开通中"),
    ERROR("ERROR", "失败"),
    CALLBACK("CALLBACK", "等待云枢线下开通"),
    PENDING("PENDING", "等待开通"),
    ;

    private final String type;

    private final String remark;

    public static String getRemarkByType(String type) {
        if (type == null) {
            return null;
        }

        NetworkStatusEnum[] values = NetworkStatusEnum.values();
        for (NetworkStatusEnum value : values) {
            if (value.type.equals(type)) {
                return value.remark;
            }
        }

        return null;
    }
}
