package com.datatech.slgzt.model.vo.recovery.export;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: flink
 * @author: LK
 * @create: 2025-06-30 15:02
 **/
@Data
public class FlinkExportVO {

    @ExcelExportHeader("Flink名称")
    private String flinkName;

    /**
     * cpu核数
     */
    @ExcelExportHeader("cpu核数")
    @JsonProperty("vCpus")
    private String vCpus;

    /**
     * 内存大小
     */
    @ExcelExportHeader("内存大小")
    private String ram;


    /**
     * 申请时长
     */
    @ExcelExportHeader("申请时长")
    private String applyTimeCn;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;
}
