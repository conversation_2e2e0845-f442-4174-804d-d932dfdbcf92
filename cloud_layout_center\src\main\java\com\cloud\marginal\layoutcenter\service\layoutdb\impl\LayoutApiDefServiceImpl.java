package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.ccmp.exceptioncenter.common.utils.UuidUtil;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.StatusEnum;
import com.cloud.marginal.model.dto.layout.SaveApiDef;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutApiDefService;
import com.cloud.marginal.mapper.layout.LayoutApiDefMapper;
import com.cloud.marginal.mapper.layout.TaskApiDefMapper;
import com.cloud.marginal.model.dto.layout.ApiDefDto;
import com.cloud.marginal.model.entity.layout.LayoutApiDef;
import com.cloud.marginal.model.entity.layout.TaskApiDef;
import com.cloud.marginal.model.vo.layout.ApiDefVO;
import com.cloud.marginal.utils.AssertUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 编排API配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class LayoutApiDefServiceImpl extends ServiceImpl<LayoutApiDefMapper, LayoutApiDef> implements LayoutApiDefService {

    @Resource
    LayoutApiDefMapper layoutApiDefMapper;
    @Resource
    TaskApiDefMapper taskApiDefMapper;


    @Override
    public CecResult createApiDef(SaveApiDef saveApiDef) {
        LambdaQueryWrapper<LayoutApiDef> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(LayoutApiDef::getApiCode, saveApiDef.getApiCode());
        LayoutApiDef apiDefOld = layoutApiDefMapper.selectOne(lambdaQueryWrapper);
        AssertUtil.isTrue(apiDefOld==null,"API编码不能重复");

        LayoutApiDef apiDef = new LayoutApiDef();
        BeanUtils.copyProperties(saveApiDef,apiDef);

        apiDef.setId(UuidUtil.getUUID());
        apiDef.setStatus(StatusEnum.VALID.getCode());
        apiDef.setCreatedTime(new Date());
        Boolean result = this.save(apiDef);
        if (result) {
            return CecResult.success();
        }else{
            return CecResult.failure("创建异常，请稍后重试！");
        }
    }

    @Override
    public CecResult updateApiDef(SaveApiDef saveApiDef) {
        if(StrUtil.isEmpty(saveApiDef.getId())){
            throw new BusinessException("主键不能为空!");
        }

        LambdaQueryWrapper<LayoutApiDef> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(LayoutApiDef::getApiCode, saveApiDef.getApiCode());
        lambdaQueryWrapper.ne(LayoutApiDef::getId, saveApiDef.getId());
        LayoutApiDef apiDef = layoutApiDefMapper.selectOne(lambdaQueryWrapper);
        AssertUtil.isTrue(apiDef==null,"API编码不能重复");

        LayoutApiDef layoutApiDefOld = layoutApiDefMapper.selectById(saveApiDef.getId());

        BeanUtil.copyProperties(saveApiDef,layoutApiDefOld);

        int result = layoutApiDefMapper.updateById(layoutApiDefOld);
        if (result > 0) {
            return CecResult.success();
        }else{
            return CecResult.failure("修改异常，请稍后重试！");
        }
    }

    @Override
    public CecResult deleteApiDef(String apiDefId) {
        //删除时校验编排任务API中是否有关联关系，否则不让删除
        List<TaskApiDef> taskApiDefs = taskApiDefMapper.selectList(new LambdaQueryWrapper<TaskApiDef>()
                .eq(TaskApiDef::getApiId,apiDefId));
        if(!CollUtil.isEmpty(taskApiDefs)){
            throw new BusinessException("该编排配置有关联的任务，不能删除!");
        }

        LayoutApiDef layoutApiDef = layoutApiDefMapper.selectById(apiDefId);
        layoutApiDef.setStatus(StatusEnum.INVALID.getCode());
        //逻辑删除
        int result = layoutApiDefMapper.updateById(layoutApiDef);
        if (result > 0) {
            return CecResult.success();
        }else{
            return CecResult.failure("删除异常，请稍后重试！");
        }
    }

    @Override
    public List<ApiDefVO> listApiDef(String apiName) {

        LambdaQueryWrapper<LayoutApiDef> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(LayoutApiDef::getStatus,StatusEnum.VALID.getCode());
        lambdaQueryWrapper.like(LayoutApiDef::getApiName, apiName);
        lambdaQueryWrapper.orderByDesc(LayoutApiDef::getCreatedTime);
        List<LayoutApiDef>  apiDefs = layoutApiDefMapper.selectList(lambdaQueryWrapper);

        List<ApiDefVO> apiDefVOs = new ArrayList<>();
        apiDefs.forEach(apiDef->{
            ApiDefVO apiDefVO = new ApiDefVO();
            BeanUtils.copyProperties(apiDef,apiDefVO);
            apiDefVOs.add(apiDefVO);
        });
        return apiDefVOs;

    }

    @Override
    public CecPage<ApiDefVO> pageApiDef(ApiDefDto apiDefDto) {
        LambdaQueryWrapper<LayoutApiDef> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(LayoutApiDef::getStatus,StatusEnum.VALID.getCode());
        lambdaQueryWrapper.like(LayoutApiDef::getApiName, apiDefDto.getApiName());
        lambdaQueryWrapper.orderByDesc(LayoutApiDef::getCreatedTime);

        Page<LayoutApiDef>  apiDefs = layoutApiDefMapper.selectPage(new Page(apiDefDto.getPageNum(),apiDefDto.getPageSize()),lambdaQueryWrapper);
        List<ApiDefVO> apiDefVOs = new ArrayList<>();
        apiDefs.getRecords().forEach(apiDef->{
            ApiDefVO apiDefVO = new ApiDefVO();
            BeanUtils.copyProperties(apiDef,apiDefVO);
            apiDefVOs.add(apiDefVO);
        });
        return new CecPage(apiDefVOs, apiDefs.getCurrent(), apiDefs.getSize(), apiDefs.getTotal());
    }
}
