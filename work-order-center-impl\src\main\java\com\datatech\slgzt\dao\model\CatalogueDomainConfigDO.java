package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 目录域配置DO
 */
@Data
@TableName("WOC_CATALOGUE_DOMAIN_CONFIG")
public class CatalogueDomainConfigDO {

    @TableField("BUSINESS_CODE")
    private String businessCode;

    @TableField("DOMAIN_CODE")
    private String domainCode;

} 