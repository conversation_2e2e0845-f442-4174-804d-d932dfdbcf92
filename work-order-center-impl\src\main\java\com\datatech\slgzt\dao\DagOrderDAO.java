package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DagOrderMapper;
import com.datatech.slgzt.dao.model.DagOrderDO;
import com.datatech.slgzt.model.query.DagOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * DAG工单 DAO接口
 */
@Repository
public class DagOrderDAO {

    @Resource
    private DagOrderMapper dagOrderMapper;

    public String insert(DagOrderDO dagOrderDO) {
        dagOrderMapper.insert(dagOrderDO);
        return dagOrderDO.getId();
    }

    public void update(DagOrderDO dagOrderDO) {
        dagOrderMapper.updateById(dagOrderDO);
    }

    public void delete(String id) {
        dagOrderMapper.deleteById(id);
    }

    public DagOrderDO getById(String id) {
        return dagOrderMapper.selectById(id);
    }

    public List<DagOrderDO> list(DagOrderQuery query) {
        return dagOrderMapper.selectList(Wrappers.<DagOrderDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), DagOrderDO::getBusinessSystemId, query.getBusinessSystemId())
                .eq(ObjNullUtils.isNotNull(query.getStatus()), DagOrderDO::getStatus, query.getStatus())
                .like(ObjNullUtils.isNotNull(query.getTemplateName()), DagOrderDO::getTemplateName, query.getTemplateName())
                .orderByDesc(DagOrderDO::getCreatedTime)
        );
    }
} 