package com.datatech.slgzt.model.req.nonstander;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import lombok.Data;

import java.util.List;

/**
 * 标准资源开通请求
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月11日 10:39:30
 */
@Data
public class NonStanderWorkOrderResOpenReq {

    /**
     * 非标资源工单id
     */
    private String standardWorkOrderId;

    /**
     * 需要开启的资源id
     */
    private List<Long> openResIds;

    /**
     * 开启资源的类型
     * @see ProductTypeEnum
     * 目前就是 gsc esc
     */
    private String openResType;

    /**
     * 绑定的多平面模型
     */
    private List<PlaneNetworkModel> planeNetworkModelList;


    /**
     * azCode
     */
    private String azCode;

    /**
     * azId
     */
    private String azId;

    /**
     * azName
     */
    private String azName;

    private String templateCode;

}
