package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/2
 */

@Getter
@AllArgsConstructor
public enum BusinessExceptionEnum {

    SUCCESS(200, "操作成功"),
    FAILED(1000, "操作失败，请稍后再试"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(1002, "没有相关权限"),
    NOT_FOUND(1003, "请求不存在"),
    METHOD_NOT_ALLOWED(1004, "请求方式不正确"),
    VALIDATE_FAILED(1005, "参数检验失败"),
    CMDB_INSTANCE_FAILED(1006, "同步cmdb信息失败，请联系管理员"),
    CMDB_UPDATE_FAILED(1007, "查询租户信息失败，请联系管理员"),
    DELETE_CMDB_INSTANCE_FAILED(1010, "删除cmdb信息失败，请联系管理员"),
    YUNSHU_UPPER_FAILED(1007, "同步信息到云枢失败，请联系管理员"),
    BUSINESS_SYSTEM_ISNULL(1008, "未查找到业务系统，请联系管理员"),
    VPC_CREATA_ERROR(1009, "vpc开通异常"),
    INSTANCE_ID_NULL_ERROR(1010, "instanceId参数为空"),
    ;

    public final int code;

    public final String message;
}
