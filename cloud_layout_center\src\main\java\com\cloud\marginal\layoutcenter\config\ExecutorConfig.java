package com.cloud.marginal.layoutcenter.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/**
 * 线程池配置类
 */
@Configuration
@EnableAsync
public class ExecutorConfig {
    private static final Logger logger = LoggerFactory.getLogger(ExecutorConfig.class);

    @Bean
    public Executor asyncServiceExecutor() {
        logger.info("开始初始化线程池");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        logger.info("核心线程数:"+Runtime.getRuntime().availableProcessors());
        //配置最大线程数
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 5);
        //配置队列最大长度
        executor.setQueueCapacity(Runtime.getRuntime().availableProcessors() * 10);
        //配置线程池名称
        executor.setThreadNamePrefix("asyncExecutor-layout-");

        // 当最大线程数和队列已满的时候，使用AbortPolicy(拒绝)策略，直接抛出RejectedExecutionException异常
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //执行初始化
        executor.initialize();
        logger.info("线程池初始化结束");
        return executor;
    }

    @Bean
    public ScheduledThreadPoolExecutor scheduledServiceExecutor() {
        ScheduledThreadPoolExecutor scheduledThreadPoolExecutor =
                new ScheduledThreadPoolExecutor(Runtime.getRuntime().availableProcessors());
        return scheduledThreadPoolExecutor;
    }
}
