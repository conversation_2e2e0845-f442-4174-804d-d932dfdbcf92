package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 企业工单表
 */
@Data
@TableName("WOC_CORPORATE_ORDER")
public class CorporateOrderDO {

    @TableField("ID")
    private String id;

    @TableField("ORDER_CODE")
    private String orderCode;

    @TableField("ORDER_AMOUNT")
    private BigDecimal orderAmount;

    @TableField("CREATE_BY")
    private Long createBy;

    @TableField("CREATE_BY_NAME")
    private String createByName;

    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("JOB_EXECUTION_ID")
    private Long jobExecutionId;

    /**
     * 租户名称
     */
    @TableField("TENANT_NAME")
    private String tenantName;

    /**
     * 租户id
     */
    @TableField("TENANT_ID")
    private Long tenantId;


    /**
     * 业务系统ID
     */
    @TableField("BUSINESS_SYSTEM_ID")
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;
} 