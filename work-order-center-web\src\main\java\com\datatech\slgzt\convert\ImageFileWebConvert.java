package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.ImageFileDTO;
import com.datatech.slgzt.model.query.ImageFileQuery;
import com.datatech.slgzt.model.req.image.ImageFileCreateReq;
import com.datatech.slgzt.model.req.image.ImageFilePageReq;
import com.datatech.slgzt.model.vo.image.ImageFileVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 11:27
 **/
@Mapper(componentModel = "spring")
public interface ImageFileWebConvert {

    @Mapping(target = "osName", source = "osType")
    @Mapping(target = "totalParts", source = "totalChunksCount")
    @Mapping(target = "format", source = "fileName", qualifiedByName = "extractFormatFromFileName")
    @Mapping(target = "size", source = "fileSize", qualifiedByName = "convertSize")
    ImageFileDTO convert(ImageFileCreateReq req);

    @Named("extractFormatFromFileName")
    default String extractFormatFromFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return null;
    }

    @Named("convertSize")
    default String convertSize(String fileSize) {
        long size = Long.parseLong(fileSize);
        if (size <= 0) return "0B";
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        // 确保不超过最大单位(TB)
        digitGroups = Math.min(digitGroups, units.length - 1);
        // 格式化数字，保留2位小数
        DecimalFormat df = new DecimalFormat("#.##");
        double value = size / Math.pow(1024, digitGroups);
        return df.format(value) + " " + units[digitGroups];
    }



    ImageFileQuery convert(ImageFilePageReq req);

    ImageFileVO convert(ImageFileDTO dto);
}
