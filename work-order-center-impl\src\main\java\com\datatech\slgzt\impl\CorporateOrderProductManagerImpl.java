package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.CorporateOrderProductManagerConvert;
import com.datatech.slgzt.dao.CorporateOrderProductDAO;
import com.datatech.slgzt.dao.model.CorporateOrderProductDO;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CorporateOrderProductManagerImpl implements CorporateOrderProductManager {

    @Resource
    private CorporateOrderProductDAO corporateOrderProductDAO;

    @Resource
    private CorporateOrderProductManagerConvert corporateOrderProductManagerConvert;

    @Override
    public List<CorporateOrderProductDTO> list(CorporateOrderProductQuery query) {
        List<CorporateOrderProductDO> list = corporateOrderProductDAO.list(query);
        return corporateOrderProductManagerConvert.dos2DTOs(list);
    }

    @Override
    public PageInfo<CorporateOrderProductDTO> page(CorporateOrderProductQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<CorporateOrderProductDO> list = corporateOrderProductDAO.list(query);
        return new PageInfo<>(corporateOrderProductManagerConvert.dos2DTOs(list));
    }

    @Override
    public void insert(CorporateOrderProductDTO dto) {
        CorporateOrderProductDO corporateOrderProductDO = corporateOrderProductManagerConvert.dto2do(dto);
        corporateOrderProductDAO.insert(corporateOrderProductDO);
    }

    @Override
    public void update(CorporateOrderProductDTO dto) {
        CorporateOrderProductDO corporateOrderProductDO = corporateOrderProductManagerConvert.dto2do(dto);
        corporateOrderProductDAO.updateById(corporateOrderProductDO);
    }

    @Override
    public void delete(Long id) {
        corporateOrderProductDAO.delete(id);
    }

    @Override
    public void deleteByWorkOrderId(String workOrderId) {
        corporateOrderProductDAO.deleteByWorkOrderId(workOrderId);
    }

    @Override
    public CorporateOrderProductDTO getById(Long id) {
        CorporateOrderProductDO corporateOrderProductDO = corporateOrderProductDAO.getById(id);
        return corporateOrderProductManagerConvert.do2dto(corporateOrderProductDO);
    }

    @Override
    public CorporateOrderProductDTO getByGid(String gid) {
        CorporateOrderProductDO corporateOrderProductDO = corporateOrderProductDAO.getByGid(gid);
        return corporateOrderProductManagerConvert.do2dto(corporateOrderProductDO);
    }

    @Override
    public CorporateOrderProductDTO getBySubOrderId(Long subOrderId) {
        CorporateOrderProductDO corporateOrderProductDO = corporateOrderProductDAO.getBySubOrderId(subOrderId);
        return corporateOrderProductManagerConvert.do2dto(corporateOrderProductDO);
    }

    @Override
    public void updateStatusById(Long id, String status) {
        CorporateOrderProductDTO dagProductDTO = new CorporateOrderProductDTO();
        dagProductDTO.setId(id);
        dagProductDTO.setOpenStatus(status);
        corporateOrderProductDAO.updateById(corporateOrderProductManagerConvert.dto2do(dagProductDTO));
    }

    @Override
    public void updateStatusByParentId(Long id, String status) {
        CorporateOrderProductDTO dagProductDTO = new CorporateOrderProductDTO();
        dagProductDTO.setParentProductId(id);
        dagProductDTO.setOpenStatus(status);
        corporateOrderProductDAO.updateByParentId(corporateOrderProductManagerConvert.dto2do(dagProductDTO));
    }
} 