package com.datatech.slgzt.model.vo.vpc;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.dto.ExtendResourceDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VpcOrderResult extends ExtendResourceDetail implements Serializable {
    private static final long serialVersionUID = 599388800911149645L;

    private String id;
    @ExcelExportHeader(value = "VPC名称")
    private String vpcName;
    @ExcelExportHeader(value = "子网个数")
    private Integer subnetNum;
    private String orderId;
    @ExcelExportHeader(value = "工单编号")
    private String orderCode;
    private String status;
    @ExcelExportHeader(value = "创建时间")
    private String createdTime;
    @ExcelExportHeader(value = "申请人")
    private String userName;
    @ExcelExportHeader(value = "资源池")
    private String poolName;
    @ExcelExportHeader(value = "租户")
    private String tenantName;
    @ExcelExportHeader(value = "网段")
    private String cidr;
    private String billId;
    private String catalogueDomainCode;
   // @ExcelExportHeader("云类型")
    private String catalogueDomainName;
    private String domainCode;
   // @ExcelExportHeader("云平台")
    private String domainName;

    private String azCode;

    private String sourceType;
    /**
     * 回收状态 0 工单未发起回收 1 资源待回收 2 回收中 3 回收完成 4 回收失败
     */
    private Integer recoveryStatus;

    /**
     * 回收状态描述
     */
    private String recoveryStatusCn;
    private String message;
    private String regionCode;

    /**
     * 租户是否确认回收 0 未确认 1 确认
     */
    private Integer confirmOrNot;

    private List<VpcSubnetOrderResult> vpcSubnetOrderList;

    private Integer deleted;

    private String vpcId;

    private String startIp;

    private String netmask;

    private String subnetName;

    private String type = "vpc";

    private Long businessSysId;
    private String businessSysName;
    private Integer vpcType;
    private Long tenantId;

    private String description;
}
