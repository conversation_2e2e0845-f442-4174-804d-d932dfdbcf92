package com.datatech.slgzt.model.req.couldPort;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 云端口
 */
@Data
public class ClouldPortPageReq {


    private Integer pageNum;

    private Integer pageSize;


    /**
     * 云区域编码
     */
    private String regionCode;

    /**
     * 云平台类型
     */
    private String catalogueDomainName;

    
    /**
     * 云区域id
     */
    private String regionId;


    /**
     * 云平台编码
     */
    private String platformCode;


    /**
     * 业务系统ID
     */
    private String businessSystemId;


    /**
     * 业务系统ID
     */
    private String businessSystemName;


    /**
     * 云端口名称
     */
    private String cloudPortName;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * VPC名称
     */
    private String vpcName;

    /**
     * vlan ID
     */
    private String vlanId;


    /**
     * azCode
     */
    private String azCode;

    /**
     * azCode 名称
     */
    private String azCodeName;

    /**
     * vpcId
     */
    private String vpcId;

    /**
     * 创建BGP的外部接口地址
     */
    private String srcIp;


    /**
     * 创建BGP的对端邻居地址
     */
    private String peerIp;

    /**
     * peer口令
     */
    private String peerPassword;


    /**
     *
     */
    private String peerAsNumber;


    /**
     * peer口令
     */
    private String remoteCidr;


    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
}
