package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 10:42
 **/
@Data
public class ImageFileDTO {

    private Long id;

    /**
     * 镜像文件名称
     * 示例: "Windows Server 2019"
     */
    private String imageName;

    /**
     * 操作系统类型
     * 示例: "Windows", "Linux"等
     */
    private String osName;

    /**
     * 操作系统版本号
     * 示例: "Server 2019", "Ubuntu 20.04"等
     */
    private String osVersion;

    /**
     * 镜像文件大小
     */
    private String size;

    /**
     * 镜像文件格式
     * 示例: "iso", "img", "qcow2"等
     */
    private String format;

    /**
     * 上传时间
     * 格式: yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime uploadTime;

    /**
     * 上传是否完成标志
     * true-已完成 false-未完成
     */
    private Boolean uploadCompleted = false;

    /**
     * 上传进度百分比
     * 范围: 0-100
     */
    private Integer progress;

    /**
     * 文件MD5校验值
     * 用于文件完整性校验
     */
    private String md5;

    /**
     * 文件下载URL
     * 当文件上传完成后生成
     */
    private String downloadUrl;

    /**
     * 总分片数
     * 用于断点续传场景
     */
    private int totalParts;

    /**
     * 文件名称
     */
    private String fileName;

}
