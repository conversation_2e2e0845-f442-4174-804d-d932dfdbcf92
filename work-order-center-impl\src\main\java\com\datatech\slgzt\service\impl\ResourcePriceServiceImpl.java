package com.datatech.slgzt.service.impl;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.model.dto.ResourcePriceCalculateDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.service.ResourcePriceService;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ResourcePriceServiceImpl implements ResourcePriceService {

    @Value("${http.resourceCenterUrl}")
    private String resourceCenter;

    @Override
    public BigDecimal calculateVmPrice(EcsModel vmModel) {
        ResourcePriceCalculateDTO request = new ResourcePriceCalculateDTO();
        request.setResourceType("VM");
        request.setBillType(vmModel.getBillType());

        ResourcePriceCalculateDTO.VmPriceInfo vmInfo = new ResourcePriceCalculateDTO.VmPriceInfo();
        vmInfo.setNum(vmModel.getOpenNum());
        vmInfo.setFlavorId(vmModel.getFlavorId());
        vmInfo.setSysVolType(vmModel.getSysDiskType());
        vmInfo.setSysVolSize(vmModel.getSysDiskSize());
        if (vmModel.getBindPublicIp()) {
            vmInfo.setBandwidth(vmModel.getEipModelList().get(0).getBandwidth());
        }
        if (vmModel.getMountDataDisk()){
            List<ResourcePriceCalculateDTO.VolumePriceInfo> volList=new ArrayList<>();
            vmModel.getMountDataDiskList().forEach(disk -> {
                ResourcePriceCalculateDTO.VolumePriceInfo volumePriceInfo = new ResourcePriceCalculateDTO.VolumePriceInfo();
                volumePriceInfo.setVolType(disk.getSysDiskType());
                volumePriceInfo.setVolSize(disk.getSysDiskSize());
                volList.add(volumePriceInfo);
            });
            vmInfo.setDataVolList(volList);
        }
        request.setVmInfo(vmInfo);
        //调用服务计算
        return calculatePrice(request);
    }

    @Override
    public BigDecimal calculateVolumePrice(EvsModel evsModel) {
        ResourcePriceCalculateDTO request = new ResourcePriceCalculateDTO();
        request.setResourceType("VOL");
        request.setBillType(evsModel.getBillType());
        ResourcePriceCalculateDTO.VolumePriceInfo vol = new ResourcePriceCalculateDTO.VolumePriceInfo();
        vol.setVolType(evsModel.getSysDiskType());
        vol.setVolSize(evsModel.getSysDiskSize()*evsModel.getOpenNum());
        List<ResourcePriceCalculateDTO.VolumePriceInfo> volInfoList = Lists.newArrayList(vol);
        request.setVolInfo(volInfoList);
        return calculatePrice(request);
    }

    @Override
    public BigDecimal calculateObsPrice(ObsModel obsModel) {
        ResourcePriceCalculateDTO request = new ResourcePriceCalculateDTO();
        request.setResourceType("OBS");
        request.setBillType(obsModel.getBillType());
        ResourcePriceCalculateDTO.ObsPriceInfo obsInfo = new ResourcePriceCalculateDTO.ObsPriceInfo();
        obsInfo.setBucketSize(obsModel.getStorageDiskSize());
        obsInfo.setBucketNum(obsModel.getOpenNum());
        List<ResourcePriceCalculateDTO.ObsPriceInfo> obsInfoList = Lists.newArrayList(obsInfo);
        request.setObsInfo(obsInfoList);
        return calculatePrice(request);
    }

    @Override
    public BigDecimal calculateSlbPrice(SlbModel slbModel) {


        ResourcePriceCalculateDTO request = new ResourcePriceCalculateDTO();
        request.setResourceType("SLB");
        request.setBillType(slbModel.getBillType());
        ResourcePriceCalculateDTO.SlbPriceInfo slbInfo = new ResourcePriceCalculateDTO.SlbPriceInfo();
        slbInfo.setFlavorId(slbModel.getFlavorId());
        if (slbModel.getBindPublicIp()) {
            slbInfo.setBandwidth(slbModel.getEipModelList().get(0).getBandwidth());
        }

        List<ResourcePriceCalculateDTO.SlbPriceInfo> slbInfoList = Lists.newArrayList(slbInfo);
        request.setSlbInfo(slbInfoList);
        return calculatePrice(request);
    }

    @Override
    public BigDecimal calculateNatPrice(NatGatwayModel natGatwayModel) {
        ResourcePriceCalculateDTO request = new ResourcePriceCalculateDTO();
        request.setResourceType("NAT");
        request.setBillType(natGatwayModel.getBillType());
        ResourcePriceCalculateDTO.NatPriceInfo natPriceInfo = new ResourcePriceCalculateDTO.NatPriceInfo();
        natPriceInfo.setFlavorId(natGatwayModel.getFlavorId());
        if (natGatwayModel.getBindPublicIp()) {
            natPriceInfo.setBandwidth(natGatwayModel.getEipModelList().get(0).getBandwidth());
        }
        List<ResourcePriceCalculateDTO.NatPriceInfo> natInfoList = Lists.newArrayList(natPriceInfo);
        request.setNatInfo(natInfoList);
        return calculatePrice(request);
    }

    @Override
    public BigDecimal calculateVpnPrice(VpnModel vpnModel) {
        ResourcePriceCalculateDTO request = new ResourcePriceCalculateDTO();
        request.setResourceType("VPN");
        request.setBillType(vpnModel.getBillType());
        ResourcePriceCalculateDTO.VpnPriceInfo vpnInfo = new ResourcePriceCalculateDTO.VpnPriceInfo();
        vpnInfo.setBandwidth(vpnModel.getBandwidth());
        vpnInfo.setNum(vpnModel.getOpenNum());
        List<ResourcePriceCalculateDTO.VpnPriceInfo> vpnInfoList = Lists.newArrayList(vpnInfo);
        request.setVpnInfo(vpnInfoList);
        return calculatePrice(request);
    }

    private BigDecimal calculatePrice(ResourcePriceCalculateDTO request) {
        //调用服务计算
        String url = resourceCenter + "/v1/cloud/resourcecenter/eip/bind";
        Mapper responseMapper = OkHttpsUtils.http()
                                            .sync(url)
                                            .bodyType(OkHttps.JSON)
                                            .setBodyPara(JSON.toJSONString(request))
                                            .post()
                                            .getBody()
                                            .toMapper();
              // 处理结果数据
        Mapper dataMapper = responseMapper.getMapper("entity");
        return BigDecimal.valueOf(dataMapper.getDouble("price"));
    }
} 