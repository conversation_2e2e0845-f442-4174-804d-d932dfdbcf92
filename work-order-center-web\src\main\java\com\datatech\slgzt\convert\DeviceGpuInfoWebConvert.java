package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.req.device.DeviceDomainPageReq;
import com.datatech.slgzt.model.vo.device.DeviceGpuBaseInfoVO;
import com.datatech.slgzt.model.vo.device.DeviceMetricDetailInfoVO;
import com.datatech.slgzt.model.vo.device.DeviceMetricSplitDetailInfoVO;
import com.datatech.slgzt.model.vo.device.DevicePhysicalInfoVO;
import org.mapstruct.Mapper;


/**
 * @Desc 显卡转换器
 * <AUTHOR>
 * @DATA 2025-06-12
 */
@Mapper(componentModel = "spring")
public interface DeviceGpuInfoWebConvert {

    DeviceInfoQuery convert(DeviceDomainPageReq req);



    DevicePhysicalInfoVO convert(DeviceGpuInfoDTO req);

    DeviceGpuBaseInfoVO dto2vo(DeviceGpuInfoDTO req);


    DeviceMetricDetailInfoVO dto2DetailVO(DeviceGpuInfoDTO req);


    DeviceMetricSplitDetailInfoVO dto2SpiltVO(DeviceVirtualInfoDTO req);

}