package com.datatech.slgzt.model.tem;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 11:03:57
 */
@Data
public class DagVpcModel {

    private String regionCode;
    /**
     * 区域信息
     */
    private String azCode;         // 可用区编码
    private String domainCode;     // 域编码
    private String domainName;     // 域名称
    private String catalogueDomainCode; // 目录域编码
    private String catalogueDomainName; // 目录域名称

    /**
     * VPC基本信息
     */
    private String vpcName;

    /**
     * 网络配置
     */
    private String cidr;           // IPv4网段
    private String ipv6Cidr;      // IPv6网段

    /**
     * 其他配置
     */
    private String plane;         // 平面
    private String detail;        // 详情
    private Integer vpcType;      // VPC类型

    /**
     * 子网配置
     */
    private List<TemVpcSubnetModel> subnetDTOList;
}
