package com.datatech.slgzt.model.vo.vpc;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class VpcSubnetOrderResult implements Serializable {
    private static final long serialVersionUID = 599388800911149645L;

    private String id;

    private Date createdTime;

    private Date updatedTime;


    private String subnetName;

    private String vpcId;

    private String azCode;

    private String dnsNames;


    private String cidr;

    private String startIp;

    private String endIp;

    private Integer deleted;

    private String netmask;

    private String description;


    private Integer recoveryStatus;


    private String message;

    private String level2InstanceId;

    private String instanceId;

    private String status;

    private String uuid;



}
