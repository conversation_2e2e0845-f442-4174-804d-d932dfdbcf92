package com.datatech.slgzt.model.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * GPU报表Excel导出DTO
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
public class GpuReportExcelDTO {

    @ExcelIgnore
    private String regionId;

    @ExcelIgnore
    private String deviceId;

    @ExcelProperty("数据时间")
    private String dataTime;

    @ExcelProperty("所属云")
    private String domainName;

    @ExcelProperty("资源池")
    private String regionName;

    @ExcelProperty("业务系统")
    private String businessSystemName;

    @ExcelIgnore
    private String deviceType;

    @ExcelProperty("算力利用率(%)")
    private BigDecimal gpuUtilPercent;

    @ExcelProperty("显存利用率(%)")
    private BigDecimal memUtilPercent;

    @ExcelProperty("所属裸金属/虚拟机")
    private String deviceName;

    @ExcelProperty("申请人")
    private String applyUserName;

    @ExcelProperty("IP地址")
    private String deviceIp;

    @ExcelProperty("型号")
    private String modelName;

    @ExcelProperty("温度(℃)")
    private BigDecimal temperature;

    @ExcelIgnore
    private LocalDateTime createTime;
} 