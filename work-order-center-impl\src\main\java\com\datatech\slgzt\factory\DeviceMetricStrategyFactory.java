package com.datatech.slgzt.factory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DeviceMetricStrategyFactory {
    private static final Map<String, Object> beanMap = new ConcurrentHashMap<>();

    public static void registerBean(String resourceCode, Object bean) {
        beanMap.put(resourceCode, bean);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBean(String resourceCode, Class<T> type) {
        return (T) beanMap.get(resourceCode);
    }

    public static Object getBean(String resourceCode) {
        return beanMap.get(resourceCode);
    }
}
