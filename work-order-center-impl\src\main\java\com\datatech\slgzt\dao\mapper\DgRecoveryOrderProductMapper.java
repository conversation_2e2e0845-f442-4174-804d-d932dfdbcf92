package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.DgRecoveryOrderDO;
import com.datatech.slgzt.dao.model.order.DgRecoveryOrderProductDO;
import com.datatech.slgzt.dao.model.order.RecoveryWorkOrderProductDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: NetworkGoodsMapper
 * @Author: suxin
 * @Date: 2025/3/24
 * @Description:
 */
public interface DgRecoveryOrderProductMapper extends BaseMapper<DgRecoveryOrderProductDO> {



}

