package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.xieyun.XieyunNamespaceCreateOpm;
import com.datatech.slgzt.model.xieyun.XieyunOrgQuotaOpm;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@Accessors(chain = true)
public class XieYunNamespaceDTO {

    private String xieYunNamespaceId;

    private String namespaceName;

    private String namespaceDesc;

    private String xieYunOrgId;

    private String xieYunProjectId;

    private String clusterName;

    private String nodePoolName;

    private String cpuValue;

    private String memoryValue;

    private String ipPool;

    private List<XieyunNamespaceCreateOpm> xieYunNamespaceCreateOpmList;
}

