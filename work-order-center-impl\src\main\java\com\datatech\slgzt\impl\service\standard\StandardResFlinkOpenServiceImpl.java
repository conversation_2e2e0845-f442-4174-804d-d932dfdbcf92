package com.datatech.slgzt.impl.service.standard;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.config.FiProperties;
import com.datatech.slgzt.enums.GlobalExceptionEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.fi.FlinkCreateTO;
import com.datatech.slgzt.model.nostander.FlinkModel;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.concurrent.Executor;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-03-14 10:52
 **/
@Service
@Slf4j
public class StandardResFlinkOpenServiceImpl implements StandardResOpenService {

    @Resource(name = "asyncTaskExecutor")
    private Executor executor;

    @Resource
    private StandardWorkOrderProductManager productManager;
    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;
    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private FiProperties fiProperties;

    @SneakyThrows
    @Override
    public void openStandardResource(StandardWorkOrderProductDTO productDTO) {
        String productType = productDTO.getProductType();
        //开通资源类型要是G
        Precondition.checkArgument(ProductTypeEnum.FLINK.getCode().equals(productType), "开通资源类型不是flink");
        //把开通的网络资源挂载到对应的附加字段里去
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(productDTO.getWorkOrderId());
        FlinkModel flinkModel = JSON.parseObject(productDTO.getPropertySnapshot(), FlinkModel.class);

//{
//    "name": "test_1205",
//    "id": "",
//    "description": "测试",
//    "childrenTenant": [],
//    "hasChildren": false,
//    "leafTenant": true,
//    "parentId": "",
//    "resourceModel": {
//        "calcResource": {
//            "capacitySchedulerProperties": {},
//            "resourceType": "CALC_RESOURCE",
//            "relationType": "SHARE",
//            "configMode": "SUPER",
//            "serviceName": "Yarn",
//            "superSchedulerProperties": {
//                "resourceAllocations": [
//                    {
//                        "resourceAllocation": {
//                            "resourceWeight": 20,
//                            "maxCpuVCores": "50",
//                            "maxResourcePercent": "0",
//                            "maxResourceValue": "1000",
//                            "minCpuVCores": "20",
//                            "minResourcePercent": "0",
//                            "minResourceValue": "100",
//                            "reservedCpuVCores": 0,
//                            "reservedResourcePercent": "0",
//                            "reservedResourceValue": 0
//                        }
//                    }
//                ]
//            }
//        },
//        "serviceResources": [],
//        "storageResource": []
//    }
//}
        FlinkCreateTO fiObj = new FlinkCreateTO();
        fiObj.setName(flinkModel.getFlinkName());
        fiObj.setDescription(flinkModel.getFlinkName());
        fiObj.setChildrenTenant(Lists.newArrayList());
        fiObj.setHasChildren(false);
        fiObj.setLeafTenant(true);
        fiObj.setParentId("");
        fiObj.setId("");

        FlinkCreateTO.ResourceModel resourceModel = new FlinkCreateTO.ResourceModel();
        fiObj.setResourceModel(resourceModel);

        FlinkCreateTO.CalcResource calcResource = new FlinkCreateTO.CalcResource();
        calcResource.setCapacitySchedulerProperties(Maps.newHashMap());
        calcResource.setResourceType("CALC_RESOURCE");
        calcResource.setRelationType("SHARE");
        calcResource.setConfigMode("SUPER");
        calcResource.setServiceName("Yarn");
        resourceModel.setCalcResource(calcResource);
        resourceModel.setServiceResources(Lists.newArrayList());
        resourceModel.setStorageResource(Lists.newArrayList());

        FlinkCreateTO.SuperSchedulerProperties superSchedulerProperties = new FlinkCreateTO.SuperSchedulerProperties();
        calcResource.setSuperSchedulerProperties(superSchedulerProperties);

        FlinkCreateTO.ResourceAllocations resourceAllocations = new FlinkCreateTO.ResourceAllocations();
        superSchedulerProperties.setResourceAllocations(Lists.newArrayList(resourceAllocations));

        FlinkCreateTO.ResourceAllocation resourceAllocation = new FlinkCreateTO.ResourceAllocation();
        resourceAllocation.setResourceWeight(20);
        resourceAllocation.setMaxResourcePercent("0");
        resourceAllocation.setMinResourcePercent("0");
        resourceAllocation.setReservedCpuVCores(0);
        resourceAllocation.setReservedResourcePercent("0");
        resourceAllocation.setReservedResourceValue(0);
        resourceAllocation.setMinCpuVCores(flinkModel.getVCpus());
        resourceAllocation.setMaxCpuVCores(flinkModel.getVCpus());
        resourceAllocation.setMinResourceValue(flinkModel.getRam());
        resourceAllocation.setMaxResourceValue(flinkModel.getRam());
        resourceAllocations.setResourceAllocation(resourceAllocation);

        //------------------产品参数设置结束-------------------------------------------------------
        //todo 把对应的产品都改成开通中状态,成功后下面状态修改开通中
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        // 异步
        executor.execute(() -> {
            try {
                callFi(fiObj, orderDTO, flinkModel, productDTO);
            } catch (Exception e) {
                StandardWorkOrderProductDTO updateDto = new StandardWorkOrderProductDTO();
                updateDto.setId(productDTO.getId());
                updateDto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                updateDto.setMessage(e.getMessage());
                productManager.update(updateDto);
                log.error("资源开通，call fi--调用fi初始化异常", e);
            }
        });
    }

    private void callFi(FlinkCreateTO to, StandardWorkOrderDTO orderDTO, FlinkModel flinkModel, StandardWorkOrderProductDTO productDTO)
            throws NoSuchAlgorithmException, KeyManagementException {
        log.info("资源开通，call fi create flink--调用fi初始化start--goodsId={}", JSON.toJSON(orderDTO.getId()));

        // curl -XPOST -i -k --basic -u 管理员⽤⼾名:密码 -HContent-type:application/json
        Mapper dataMapper = OkHttpsUtils.httpIgnoreHttpsCertificate(120)
                .sync(fiProperties.getRegionCode2flinkCreateUrlMap().get(flinkModel.getRegionCode()))
                .bodyType(OkHttps.JSON)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization",
                        "Basic " + Base64.getEncoder().encodeToString(
                                (fiProperties.getUsername() + ":" + fiProperties.getPassword()).getBytes()))
                .setBodyPara(JSON.toJSONString(to))
                .post()
                .getBody()
                .toMapper();
        log.info("fi结果: {}", dataMapper);
        String errorCode = dataMapper.getString("errorCode");
        if (errorCode != null) {
            String errorMessage = dataMapper.getString("errorMessage");
            String errorDescription = dataMapper.getString("errorDescription");
            log.warn("资源开通失败，call fi，to:{}, productDTO:{}, errorCode:{}, errorMessage:{}, errorDescription:{}",
                    to, productDTO, errorCode, errorMessage, errorDescription);
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(),
                    String.format("资源开通失败，call fi--编排中心初始化返回结果失败, errorCode=%s, errorMessage=%s, errorDescription=%s",
                            errorCode, errorMessage, errorDescription));
        }

        StandardWorkOrderProductDTO updateDto = new StandardWorkOrderProductDTO();
        updateDto.setId(productDTO.getId());
        updateDto.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
        productManager.update(updateDto);

        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setType(ProductTypeEnum.FLINK.getCode());
        resourceDetailDTO.setOrderId(orderDTO.getId());
        resourceDetailDTO.setOrderCode(orderDTO.getOrderCode());
        resourceDetailDTO.setBillId(orderDTO.getBillId());
        resourceDetailDTO.setDeviceName(to.getName());
        resourceDetailDTO.setTenantId(orderDTO.getTenantId());
        resourceDetailDTO.setTenantName(orderDTO.getTenantName());
        resourceDetailDTO.setBusinessSysId(orderDTO.getBusiSystemId());
        resourceDetailDTO.setBusinessSysName(orderDTO.getBusinessSystemName());
        resourceDetailDTO.setDomainCode(orderDTO.getDomainCode());
        resourceDetailDTO.setDomainName(orderDTO.getDomainName());
        resourceDetailDTO.setModuleId(orderDTO.getModuleId());
        resourceDetailDTO.setModuleName(orderDTO.getModuleName());
        resourceDetailDTO.setStatus(1);
        resourceDetailDTO.setApplyUserName(orderDTO.getCreatedUserName());
        resourceDetailDTO.setCloudPlatform(orderDTO.getDomainName());
        resourceDetailDTO.setResourcePoolId(String.valueOf(flinkModel.getRegionId()));
        resourceDetailDTO.setResourcePoolCode(flinkModel.getRegionCode());
        resourceDetailDTO.setResourcePoolName(flinkModel.getRegionName());
        resourceDetailDTO.setCreateTime(LocalDateTime.now());
        resourceDetailDTO.setResourceApplyTime(orderDTO.getCreateTime());
        resourceDetailDTO.setEffectiveTime(LocalDateTime.now());
        resourceDetailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), flinkModel.getApplyTime()));
        resourceDetailDTO.setApplyTime(flinkModel.getApplyTime());
        resourceDetailDTO.setDeviceStatus("ACTIVE");
        resourceDetailDTO.setSourceExtType(OrderTypeEnum.SUBSCRIBE.getCode());
        resourceDetailDTO.setSourceType(OrderTypeEnum.SUBSCRIBE.getPrefix());
//        resourceDetailDTO.setDeviceId(dataMapper.getString("id"));
        resourceDetailDTO.setSpec(String.format("%dC%dGB", flinkModel.getVCpus(), flinkModel.getRam()));
        resourceDetailDTO.setOrderType(OrderTypeEnum.SUBSCRIBE.getCode());
        resourceDetailManager.saveResourceDetail(resourceDetailDTO);
        log.info("资源开通，call fi--调用fi初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));

    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.FLINK;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }

    /**
     * 查询obs服务是否存在
     * @return
     */

}
