package com.datatech.slgzt.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
public class DeviceVirtualInfoDTO  implements Serializable {
    private Long id;


    private String areaCode;
//====================基本信息=======================
    /**
     * 云类型名称
     */
    private String catalogueDomainName;

    /**
     * 云类型编码
     */
    private String catalogueDomainCode;

    /**
     * 云平台名称
     */
    private String domainName;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 资源池id
     * 当工单是线下开通的时候，资源池id是空的
     */
    private Long regionId;

    /**
     * 资源池Code
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;


    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

//====================设备基本信息=======================
    /**
     * GPU/NPU卡序列号/uuid
     */
    private String deviceId;

    /**
     * GPU/NPU卡型号
     */
    private String deviceType;

    /**
     * 显卡索引
     */

    private Integer deviceIndex;

    /**
     * 虚拟卡index
     */
    private Integer deviceVirtualIndex;

    /**
     * 显卡名称
     */
    private String deviceName;

    /**
     * 显卡厂家
     */
    private String deviceVendor;

    /**
     * 显卡监控状态
     */
    private Boolean healthCheck;

    /**
     * 显卡驱动版本
     */
    private String driverVersion;

    /**
     * 显卡节点IP
     */
    private String deviceIp;




    private String physicalDeviceId;



    /**
     * 设备显存大小
     */
    private Integer memory;

    /**
     * 算力利用率
     */
    private Double gpuUtilPercent;

    /**
     * 显存利用率
     */
    private Double memUtilpercent;

    /**
     * 显存大小 显存大小（GB）
     */
    private Integer memoryUsage;

    /**
     * 算力能耗top
     */
    private Double devPowerUsage;

    /**
     * 任务数
     */
    private Integer allocationCount;

    private String modelName;

    private String sourceType;


    private String lastPeriod;


}
