package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.StandardResourceDetailConsumerConvert;
import com.datatech.slgzt.dao.mapper.ObsOpenTaskMapper;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkIpAddressMapper;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.DagProductQuery;
import com.datatech.slgzt.service.IntegratedPlatformService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月12日 15:20:59
 */
@Slf4j
@Service
public class DagResourceDetailConsumer {


    @Resource
    private DagOrderManager orderManager;

    @Resource
    private StaticProductStockManager staticProductStockManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VmMapper vmMapper;

    @Resource
    private CpcProjectManager cpcProjectManager;

    @Resource
    private DagProductManager productManager;

    @Resource
    private ObsOpenTaskMapper obsOpenTaskMapper;
    @Resource
    private NetworkIpAddressMapper networkIpAddressMapper;

    @Resource
    private StandardResourceDetailConsumerConvert converter;
    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;

    public void consumeResourceMessage(List<ResourceDetailDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        //处理集合封装资源详情
        Map<Long, ResourceDetailDTO> gooodId2IdentityMap = StreamUtils.toMap(list, ResourceDetailDTO::getGoodsOrderId);
        List<ResourceDetailDTO> needInsertList = new ArrayList<>();
        log.info("监听任务消息: 编排工单对象列表={}", JSON.toJSON(gooodId2IdentityMap));
        List<DagProductDTO> productDTOS = productManager.list(new DagProductQuery().setSubOrderIds(gooodId2IdentityMap.keySet()));
        if (CollectionUtil.isNotEmpty(productDTOS)) {
            productDTOS.forEach(productDTO -> {
                //去detail表中找到对应的数据 如果已经存在则不处理
                if (resourceDetailManager.getByGoodsOrderId(productDTO.getSubOrderId()) != null) {
                    log.info("kafka资源开通回调，待更新detail表数据已存在：产品表的 subOrderId={}", productDTO.getSubOrderId());
                    //暂时做个补偿更新
                    obsOpenTaskMapper.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
                    return;
                }
                ResourceDetailDTO detail = gooodId2IdentityMap.get(productDTO.getSubOrderId());
                DagOrderDTO orderDTO = orderManager.getById(productDTO.getOrderId());
                if (Objects.nonNull(orderDTO)) {
                    detail.setType(productDTO.getProductType());
                    detail.setOrderId(orderDTO.getId());
                    detail.setOrderCode(orderDTO.getOrderCode());
                    detail.setTenantId(orderDTO.getTenantId());
                    detail.setTenantName(orderDTO.getTenantName());
                    detail.setAzCode(orderDTO.getAzCode());
                    detail.setAzName(orderDTO.getAzName());
                    detail.setAzId(orderDTO.getAzId());
                    detail.setBusinessSysName(orderDTO.getBusinessSystemName());
                    detail.setApplyUserId(orderDTO.getCreatedBy());
                    detail.setApplyUserName(orderDTO.getCreator());
                    detail.setCreateTime(LocalDateTime.now());
                    detail.setStatus(1);
                    detail.setBillId(orderDTO.getBillId());
                    detail.setCloudPlatform(orderDTO.getDomainName());
                    detail.setBusinessSysId(Long.valueOf(orderDTO.getBusinessSystemId()));
                    detail.setDomainCode(orderDTO.getDomainCode());
                    detail.setDomainName(orderDTO.getDomainName());
                    detail.setModuleId(orderDTO.getModuleId());
                    detail.setModuleName(orderDTO.getModuleName());
                    detail.setSourceType(SourceTypeEnum.DAG.getPrefix());
                }
                switch (ProductTypeEnum.getByCode(detail.getType())) {
                    case ECS:
                    case GCS:
                    case MYSQL:
                    case REDIS:
                        coverEcs(detail, productDTO, needInsertList);
                        break;
                    case EIP:
                        coverEip(detail, productDTO);
                        break;
                    case EVS:
                        convertEvs(detail, productDTO);
                        break;
                    case OBS:
                        coverObs(detail, productDTO);
                        //加个补充逻辑，更新工单为开通成功
                        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
                        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
                        obsOpenTaskMapper.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
                        log.info("restart batch, {}", orderDTO.getJobExecutionId());
                        //重启流程
                        kafkaTemplate.send(BatchRestartConsumer.DAG_BATCH_RESTART, KafkaMessage.of(new BatchRestartModel()
                                .setJobExecutionId(orderDTO.getJobExecutionId())
                                .setRestartOnly(true)));
                        break;
                    case SLB:
                        coverSlb(detail, productDTO, needInsertList);
                        break;
                    case NAT:
                        coverNat(detail, productDTO, needInsertList);
                        break;
                    case VPN:
                        convertVpn(detail, productDTO);
                        break;
                    default:
                }
                needInsertList.add(detail);

            });
        }

        log.info("kafka资源开通回调，待更新detail表数据：{}", list);
        needInsertList.forEach(o -> {
            try {
                Long id = resourceDetailManager.saveResourceDetail(o);
            } catch (Exception e) {
                //这里最好入库对象 做补偿
                log.error("kafka资源开通回调，待更新detail表数据异常：{}", JSON.toJSONString(o), e);
            }
        });
    }

    public void coverEcs(ResourceDetailDTO detail, DagProductDTO productDTO, List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
        updateNetwork(escModel.getPlaneNetworkModel(), detail.getDeviceId());
        List<PlaneNetworkModel> planeNetworkModel = escModel.getPlaneNetworkModel();
        log.debug("coverEcs list:{}", JSONObject.toJSONString(planeNetworkModel));
        //如果domainCode创新池查询项目名称
        if (CatalogueDomain.INNOVATION.getCode().equals(detail.getDomainCode())) {
            String projectId = vmMapper.getByDeviceId(detail.getDeviceId());
            //获取项目名称
            String projectName = cpcProjectManager.getById(projectId);
            //detail中已知任务中心会传值过来的字段,goodsOrderId,deviceName,eip,bandWidth,spec,deviceId,resourcePoolId,resourcePoolName,deviceStatus,resourceApplyTime
            detail.setProjectName(projectName);
        }
        detail.setVpcId(Joiner.on(",")
                .skipNulls()
                .join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getId)));
        detail.setVpcName(Joiner.on(",")
                .skipNulls()
                .join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getName)));
        //子网可能出现一个以上的情况，用|分隔 每个网络模型的子网id用逗号分隔
        String subnetId = planeNetworkModel.stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetId)
                        )
                )
                .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        String subnetName = planeNetworkModel.stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetName)
                        )
                )
                .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        detail.setSubnetId(subnetId);
        detail.setSubnetName(subnetName);
        detail.setApplyTime(escModel.getApplyTime());//已转换成one_month
        detail.setResourcePoolId(escModel.getRegionId().toString());
        detail.setResourcePoolCode(escModel.getRegionCode());
        detail.setResourcePoolName(escModel.getRegionName());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), escModel.getApplyTime()));
        detail.setNetworkModelSnapshot(JSON.toJSONString(escModel.getPlaneNetworkModel()));
        //修改下带宽适配问题
        detail.setBandWidth(bandWidth(detail.getBandWidth()));
        //evs
        if (StringUtils.isNotBlank(detail.getVolumeId())) {
            List<String> list = Arrays.asList(detail.getVolumeId().split(","));
            for (int i = 0; i < list.size(); i++) {
                ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, escModel);
                detailDTO.setType(ProductTypeEnum.EVS.getCode());
                detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
                detailDTO.setDeviceId(list.get(i));
                detailDTO.setVolumeId(list.get(i));
                detailDTO.setDataDisk(detail.getDataDisk().split(",")[i]);
                detailDTO.setDeviceStatus("USED");
                detailDTO.setVmId(detail.getDeviceId());
                detailDTO.setEcsName(detail.getDeviceName());
                detailDTO.setSourceType(SourceTypeEnum.DAG.getPrefix());
                detailDTO.setBillType(escModel.getBillType());
                detailDTO.setChargeType(escModel.getChargeType());
                needInsertList.add(detailDTO);
            }
        }
        //eip
        if (StringUtils.isNotBlank(detail.getEipId())) {
            ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, escModel);
            detailDTO.setDeviceName(detail.getDeviceName() + "-eip");
            detailDTO.setRelatedDeviceId(detail.getDeviceId());
            detailDTO.setRelatedDeviceName(detail.getDeviceName());
            detailDTO.setRelatedDeviceType(detail.getType());
            detailDTO.setType(ProductTypeEnum.EIP.getCode());
            detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
            detailDTO.setDeviceId(detail.getEipId());
            detailDTO.setEipId(detail.getEipId());
            detailDTO.setEip(detail.getEip());
            detailDTO.setBandWidth(detail.getBandWidth());
            detailDTO.setDeviceStatus("ACTIVE");
            detailDTO.setVmId(detail.getDeviceId());
            detailDTO.setEcsName(detail.getDeviceName());
            detailDTO.setBillType(escModel.getBillType());
            detailDTO.setChargeType(escModel.getChargeType());
            detailDTO.setSourceType(SourceTypeEnum.DAG.getPrefix());
            needInsertList.add(detailDTO);
        }
    }

    private void updateNetwork(List<PlaneNetworkModel> list, String deviceId) {
        log.debug("updateNetwork deviceId:{},list:{}", deviceId, JSONObject.toJSONString(list));
        for (PlaneNetworkModel planeNetworkModel : list) {
            for (PlaneNetworkModel.Subnet subnet : planeNetworkModel.getSubnets()) {
                List<IpAddress> ipAddresses = networkIpAddressMapper.selectIpBySubnetId(deviceId, subnet.getSubnetId());
                for (IpAddress ipAddress : ipAddresses) {
                    if (ipAddress.getType().equals("MANAGE")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setManageIpv4(ipAddress.getIp());
                        } else {
                            subnet.setManageIpv6(ipAddress.getIp());
                        }
                    } else if (ipAddress.getType().equals("BUSINESS")) {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setBusinessIpv4(ipAddress.getIp());
                        } else {
                            subnet.setBusinessIpv6(ipAddress.getIp());
                        }
                    } else {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setAddressIpv4(ipAddress.getIp());
                        } else {
                            subnet.setAddressIpv6(ipAddress.getIp());
                        }
                    }
                }
            }
        }
        log.debug("updateNetwork end list:{}", JSONObject.toJSONString(list));
    }

    public void convertEvs(ResourceDetailDTO detail, DagProductDTO productDTO) {
        if (detail.getEffectiveTime() == null && detail.getCreateTime() != null) {
            detail.setEffectiveTime(detail.getCreateTime());
        }
        String propertySnapshot = productDTO.getPropertySnapshot();
        EvsModel dataDiskModel = JSON.parseObject(propertySnapshot, EvsModel.class);
        if (StringUtils.isNotEmpty(dataDiskModel.getVmId())) {
            //挂载成功时,detail才会传值过来的字段ecsName
            if (StringUtils.isEmpty(detail.getEcsName())) {
                detail.setMountOrNot("挂载执行中");
            } else {
                detail.setMountOrNot("是");
            }
            //如果是挂载了云主机，更新对应的云主机挂盘id和数据盘容量
            ResourceDetailDTO ecsDetail = resourceDetailManager.getByDeviceId(dataDiskModel.getVmId());
            String volumeId = Stream.of(ecsDetail.getVolumeId(), detail.getDeviceId())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            String dataDisk = Stream.of(ecsDetail.getDataDisk(), detail.getDataDisk())
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            ecsDetail.setVolumeId(volumeId);
            ecsDetail.setDataDisk(dataDisk);
            resourceDetailManager.updateById(ecsDetail);
        } else {
            detail.setMountOrNot("否");
        }

        detail.setApplyTime(dataDiskModel.getApplyTime());
        detail.setResourcePoolId(dataDiskModel.getRegionId().toString());
        detail.setResourcePoolCode(dataDiskModel.getRegionCode());
        detail.setResourcePoolName(dataDiskModel.getRegionName());
        detail.setVmId(dataDiskModel.getVmId());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), dataDiskModel.getApplyTime()));
    }

    private void coverEip(ResourceDetailDTO detail, DagProductDTO productDTO) {
        if (detail.getEffectiveTime() == null && detail.getCreateTime() != null) {
            detail.setEffectiveTime(detail.getCreateTime());
        }
        String propertySnapshot = productDTO.getPropertySnapshot();
        EipModel eipModel = JSON.parseObject(propertySnapshot, EipModel.class);
        if (StringUtils.isNotEmpty(eipModel.getVmId())) {
            detail.setRelatedDeviceName(detail.getEcsName());
            detail.setRelatedDeviceId(detail.getVmId());
            detail.setRelatedDeviceType(ProductTypeEnum.ECS.getCode());
            //挂载成功时,detail才会传值过来的字段ecsName
            if (StringUtils.isEmpty(detail.getEcsName())) {
                detail.setMountOrNot("挂载执行中");
            } else {
                detail.setMountOrNot("是");
            }
            //如果是挂载了云主机，更新对应的eip和带宽
            resourceDetailManager.evsAddEipInfoByDeviceId(eipModel.getVmId(),
                    detail.getDeviceId(), detail.getEip(), detail.getBandWidth());
        } else {
            detail.setMountOrNot("否");
        }

        detail.setApplyTime(eipModel.getApplyTime());
        detail.setBandWidth(eipModel.getBandwidth().toString());
        detail.setResourcePoolId(eipModel.getRegionId().toString());
        detail.setResourcePoolName(eipModel.getRegionName());
        detail.setVmId(eipModel.getVmId());
        detail.setEffectiveTime(LocalDateTime.now());
        // 变更时候需要，先将eip的eipId设置成自己
        detail.setEipId(detail.getDeviceId());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), eipModel.getApplyTime()));
//        detail.setEcsName(eipModel.getVmName());
        detail.setDeviceStatus("ACTIVE");
        detail.setBandWidth(bandWidth(detail.getBandWidth()));

//
//        //修改资源表
//        detail.setRelatedDeviceId(detail.getDeviceId());
////        detail.setRelatedDeviceType(detail.getDeviceType());
//        detail.setRelatedDeviceName(targetDTO.getDeviceName());
//        resourceDetailManager.updateById(resourceDetailDTO);
//        //更新target中的eipId
//        ResourceDetailDTO targetResourceDetailDTO = new ResourceDetailDTO();
//        targetResourceDetailDTO.setId(targetDTO.getId());
//        targetResourceDetailDTO.setEipId(detailDTO.getDeviceId());
//        targetResourceDetailDTO.setEip(detailDTO.getEip());
//        targetResourceDetailDTO.setBandWidth(detailDTO.getBandWidth());
//        resourceDetailManager.updateById(targetResourceDetailDTO);
////        cmdbReportService.updateInstanceEcsByResource(resourceDetailManager.getById(targetResourceDetailDTO.getId()));

    }


    public void coverNat(ResourceDetailDTO detail, DagProductDTO productDTO, List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        NatGatwayModel natGatwayModel = JSON.parseObject(propertySnapshot, NatGatwayModel.class);
        detail.setSpec(natGatwayModel.getFlavorName());
        if (natGatwayModel.getBindPublicIp()) {
            detail.setBandWidth(StreamUtils.findAny(natGatwayModel.getEipModelList()).getBandwidth().toString());
        }
        detail.setVpcName(natGatwayModel.getPlaneNetworkModel().getName());
        detail.setVpcId(natGatwayModel.getPlaneNetworkModel().getId());
        PlaneNetworkModel planeNetworkModel = natGatwayModel.getPlaneNetworkModel();
        List<PlaneNetworkModel.Subnet> subnets = planeNetworkModel.getSubnets();
        detail.setSubnetName(StreamUtils.findAny(subnets).getSubnetName());
        detail.setSubnetId(StreamUtils.findAny(subnets).getSubnetId());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setApplyTime(natGatwayModel.getApplyTime());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), natGatwayModel.getApplyTime()));
        detail.setResourcePoolId(natGatwayModel.getRegionId().toString());
        detail.setResourcePoolCode(natGatwayModel.getRegionCode());
        detail.setResourcePoolName(natGatwayModel.getRegionName());
        detail.setNetworkModelSnapshot(JSON.toJSONString(natGatwayModel.getPlaneNetworkModel()));
        detail.setBandWidth(bandWidth(detail.getBandWidth()));
        detail.setAzId(ObjNullUtils.isNotNull(natGatwayModel.getAzId()) ? natGatwayModel.getAzId().toString() : null);
        detail.setAzName(natGatwayModel.getAzName());
        detail.setAzCode(natGatwayModel.getAzCode());

        if (detail.getEipId() != null) {
            ResourceDetailDTO eipDetailDTO = converter.slb2Eip(detail);
            eipDetailDTO.setSourceType(SourceTypeEnum.DAG.getPrefix());
            eipDetailDTO.setBillType(natGatwayModel.getBillType());
            eipDetailDTO.setChargeType(natGatwayModel.getChargeType());
            needInsertList.add(eipDetailDTO);
        }
    }

    public void coverSlb(ResourceDetailDTO detail, DagProductDTO productDTO, List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        SlbModel slbModel = JSON.parseObject(propertySnapshot, SlbModel.class);
        detail.setSpec(slbModel.getFlavorName());
        if (slbModel.getBindPublicIp()) {
            detail.setBandWidth(StreamUtils.findAny(slbModel.getEipModelList()).getBandwidth().toString());
        }
        detail.setVpcName(slbModel.getPlaneNetworkModel().getName());
        detail.setVpcId(slbModel.getPlaneNetworkModel().getId());
        PlaneNetworkModel planeNetworkModel = slbModel.getPlaneNetworkModel();
        List<PlaneNetworkModel.Subnet> subnets = planeNetworkModel.getSubnets();
        detail.setSubnetName(StreamUtils.findAny(subnets).getSubnetName());
        detail.setSubnetId(StreamUtils.findAny(subnets).getSubnetId());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setApplyTime(slbModel.getApplyTime());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), slbModel.getApplyTime()));
        detail.setResourcePoolId(slbModel.getRegionId().toString());
        detail.setResourcePoolCode(slbModel.getRegionCode());
        detail.setResourcePoolName(slbModel.getRegionName());
        detail.setNetworkModelSnapshot(JSON.toJSONString(slbModel.getPlaneNetworkModel()));
        detail.setBandWidth(bandWidth(detail.getBandWidth()));
        detail.setAzId(ObjNullUtils.isNotNull(slbModel.getAzId()) ? slbModel.getAzId().toString() : null);
        detail.setAzName(slbModel.getAzName());
        detail.setAzCode(slbModel.getAzCode());
        if (detail.getEipId() != null) {
            ResourceDetailDTO eipDetailDTO = converter.slb2Eip(detail);
            eipDetailDTO.setSourceType(SourceTypeEnum.DAG.getPrefix());
            eipDetailDTO.setBillType(slbModel.getBillType());
            eipDetailDTO.setChargeType(slbModel.getChargeType());
            needInsertList.add(eipDetailDTO);
        }
        Long azId = slbModel.getAzId();
        if (azId != null) {
            staticProductStockManager.decreaseStockByAzAndType(azId.toString(), "slb");
        } else {
            log.error("slb的azId为空,slbModel:{}", slbModel);
        }
    }

    public void coverObs(ResourceDetailDTO detail, DagProductDTO productDTO) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        ObsModel obsModel = JSON.parseObject(propertySnapshot, ObsModel.class);
        detail.setSpec(obsModel.getStorageDiskType() + " " + obsModel.getStorageDiskSize() + "GB");
        detail.setApplyTime(obsModel.getApplyTime());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), obsModel.getApplyTime()));
        detail.setAzId(ObjNullUtils.isNotNull(obsModel.getAzId()) ? obsModel.getAzId().toString() : null);
        detail.setAzName(obsModel.getAzName());
        detail.setAzCode(obsModel.getAzCode());
    }

    public void convertVpn(ResourceDetailDTO detail, DagProductDTO productDTO) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        VpnModel vpnModel = JSON.parseObject(propertySnapshot, VpnModel.class);
        detail.setApplyTime(vpnModel.getApplyTime());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), vpnModel.getApplyTime()));
    }

    public ResourceDetailDTO getResourceDetailDTO(ResourceDetailDTO detail, EcsModel escModel) {
        ResourceDetailDTO detailDTO = new ResourceDetailDTO();
        detailDTO.setOrderId(detail.getOrderId());
        detailDTO.setOrderCode(detail.getOrderCode());
        detailDTO.setTenantId(detail.getTenantId());
        detailDTO.setTenantName(detail.getTenantName());
        detailDTO.setBusinessSysName(detail.getBusinessSysName());
        detailDTO.setApplyUserId(detail.getApplyUserId());
        detailDTO.setApplyUserName(detail.getApplyUserName());
        detailDTO.setCreateTime(LocalDateTime.now());
        detailDTO.setStatus(1);
        detailDTO.setBillId(detail.getBillId());
        detailDTO.setCloudPlatform(detail.getDomainName());
        detailDTO.setBusinessSysId(detail.getBusinessSysId());
        detailDTO.setDomainCode(detail.getDomainCode());
        detailDTO.setDomainName(detail.getDomainName());
        detailDTO.setModuleId(detail.getModuleId());
        detailDTO.setModuleName(detail.getModuleName());
        detailDTO.setApplyTime(escModel.getApplyTime());
        detailDTO.setResourcePoolId(escModel.getRegionId().toString());
        detailDTO.setResourcePoolCode(escModel.getRegionCode());
        detailDTO.setResourcePoolName(escModel.getRegionName());
        detailDTO.setEffectiveTime(LocalDateTime.now());
        detailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), escModel.getApplyTime()));
        detailDTO.setResourceApplyTime(detail.getResourceApplyTime());
        detailDTO.setAzCode(detail.getAzCode());
        detailDTO.setAzName(detail.getAzName());
        detailDTO.setAzId(detail.getAzId());
        detailDTO.setEcsName(detail.getDeviceName() + "(" + detail.getIp() + ")");
        detailDTO.setMountOrNot("是");
        detailDTO.setVmId(detail.getDeviceId());
        return detailDTO;
    }


    private String bandWidth(String bandWidth) {
        if (ObjNullUtils.isNull(bandWidth)) {
            return null;
        }

        String trimmedBandWidth = bandWidth.trim();
        // 如果已经以Mbps或kbps结尾，则直接返回原值
        if (trimmedBandWidth.toLowerCase().endsWith("mbps")) {
            return trimmedBandWidth;
        }

        // 尝试解析为数字，如果是纯数字则添加单位
        return bandWidth + "Mbps";
    }

}
