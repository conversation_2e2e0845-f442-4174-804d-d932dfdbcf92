package com.datatech.slgzt.impl.manager.xieyun.local;

import com.datatech.slgzt.convert.XieYunBeanManagerConvert;
import com.datatech.slgzt.dao.container.XieYunProjectDAO;
import com.datatech.slgzt.dao.model.container.XieYunProjectDO;
import com.datatech.slgzt.manager.xieyun.local.XieyunProjectLocalManager;
import com.datatech.slgzt.model.dto.XieYunProjectDTO;
import com.datatech.slgzt.model.query.container.XieYunProjectQuery;
import com.datatech.slgzt.utils.StreamUtils;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 谐云本地项目处理
 *
 * @Author: liupeihan
 * @Date: 2025/4/15
 */

@Service
public class XieyunProjectLocalManagerImpl implements XieyunProjectLocalManager {

    @Resource
    private XieYunProjectDAO projectDAO;

    @Resource
    private XieYunBeanManagerConvert convert;

    @Override
    public void insert(XieYunProjectDTO projectDTO) {
        XieYunProjectDO xieYunProjectDO = convert.projectDTO2DO(projectDTO);
        xieYunProjectDO.setCreatedTime(LocalDateTime.now());
        projectDAO.insert(xieYunProjectDO);
    }

    @Override
    public void updateByProjectId(XieYunProjectDTO projectDTO) {
        XieYunProjectDO projectDO = convert.projectDTO2DO(projectDTO);
        projectDAO.updateByProjectId(projectDO);
    }
    
    @Override
    public List<XieYunProjectDTO> list(XieYunProjectQuery query) {
        return StreamUtils.mapArray(projectDAO.list(query), convert::projectDO2DTO);
    }
}

