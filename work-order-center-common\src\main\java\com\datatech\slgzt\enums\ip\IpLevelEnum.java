package com.datatech.slgzt.enums.ip;


public enum IpLevelEnum {
    IP2("IP2", "I2_IP"),
    IP3("IP3", "I3_IP");


    private final String code;

    private final String desc;

    IpLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }
}
