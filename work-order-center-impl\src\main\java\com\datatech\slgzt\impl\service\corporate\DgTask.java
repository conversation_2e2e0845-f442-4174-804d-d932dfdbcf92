package com.datatech.slgzt.impl.service.corporate;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.VmOperationEnum;
import com.datatech.slgzt.manager.DgRecoveryOrderManager;
import com.datatech.slgzt.manager.DgRecoveryOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.model.recovery.RecoveryEcsModel;
import com.datatech.slgzt.service.corporate.DgRecoveryOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class DgTask {

    @Resource
    private DgRecoveryOrderService orderService;
    @Resource
    private DgRecoveryOrderManager orderManager;
    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Scheduled(cron = "#{@scheduledTaskProperties.dgRecoveryCron}")
    public void dgRecovery() {
        log.info("dgRecovery start");
        // 查询所有没执行过的对公回收单(exccutionId=null)
        orderManager.list(new DgRecoveryOrderQuery().setJobExecutionIdNull(true)).forEach(i -> {
            try {
                // 找到7天以上的, 进行回收
                if (i.getCreateTime().plusHours(7 * 24).isBefore(LocalDateTime.now())) {
                    orderService.recovery(i.getId());
                }
            } catch (Exception e) {
                log.error("dgRecovery failed, order:{}, error message:{}", i, ExceptionUtils.getStackTrace(e));
            }
        });
        log.info("dgRecovery end");
    }

    @Scheduled(cron = "#{@scheduledTaskProperties.dgShutdownCron}")
    public void dgShutdown() {
        log.info("dgShutdown start");
        // 查询所有没执行过的对公回收单(exccutionId=null)
        orderManager.list(new DgRecoveryOrderQuery().setJobExecutionIdNull(true)).forEach(i -> {
            try {
                List<DgRecoveryOrderProductDTO> productDTOs = productManager.listByWorkOrderId(i.getId());
                productDTOs.forEach(j -> {
                    // 如果是ecs或者gcs，解析snapshot，尝试关机
                    if (ProductTypeEnum.ECS.getCode().equals(j.getProductType())
                            || ProductTypeEnum.GCS.getCode().equals(j.getProductType())) {
                        RecoveryEcsModel ecsModel = JSONObject.parseObject(j.getPropertySnapshot(), RecoveryEcsModel.class);
                        ResourceDetailDTO detailDTO = resourceDetailManager.selectByIdNoStatus(ecsModel.getResourceDetailId());
                        if (detailDTO != null && "RUNING".equals(detailDTO.getDeviceStatus())) {
                            VmOperateQuery operate = new VmOperateQuery();
                            operate.setId(detailDTO.getId());
                            operate.setOperationType(VmOperationEnum.STOP.getAlias());
                            operate.setOrderId(detailDTO.getOrderId());
                            resourceDetailManager.operateVm(operate);
                        }
                    }
                });
            } catch (Exception e) {
                log.error("dgShutdown failed, order:{}, error message:{}", i, ExceptionUtils.getStackTrace(e));
            }
        });
        log.info("dgShutdown end");
    }
}
