package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.ImageFileDTO;
import com.datatech.slgzt.model.query.ImageFileQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 11:18
 **/
public interface ImageFileManager {

    void insert(ImageFileDTO dto);

    void updateById(ImageFileDTO dto);

    ImageFileDTO getById(Long id);

    PageResult<ImageFileDTO> page(ImageFileQuery query);

    void deleteById(Long id);

    void updateByMd5(String mad5, String preSignedObjectUrl, Boolean uploadCompleted);
}
