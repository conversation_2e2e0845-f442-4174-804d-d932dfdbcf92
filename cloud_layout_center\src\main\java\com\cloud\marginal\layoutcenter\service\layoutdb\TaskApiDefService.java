package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.model.entity.layout.LayoutApi;
import com.cloud.marginal.model.entity.layout.LayoutApiExt;
import com.cloud.marginal.model.entity.layout.TaskApiDef;

/**
 * <p>
 * 编排任务与API关系配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface TaskApiDefService extends IService<TaskApiDef> {

    LayoutApiExt generateApi(String templateCode, String taskCode);

    TaskApiDef getSingle(String templateId, String taskId, String apiId);

    void delete(String id);

    CecResult createTaskApiDef(TaskApiDef taskApiDef);

    CecResult updateTaskApiDef(TaskApiDef taskApiDef);

    CecResult deleteTaskApiDef(String apiDefId);

    CecPage<TaskApiDef> pageTaskApiDef(Integer pageNum, Integer pageSize);

}
