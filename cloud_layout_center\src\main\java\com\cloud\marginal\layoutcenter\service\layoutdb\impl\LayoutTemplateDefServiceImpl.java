package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.enums.layout.StatusEnum;
import com.cloud.marginal.enums.layout.TaskRelTypeEnum;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskDefService;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTemplateDefService;
import com.cloud.marginal.mapper.layout.LayoutTemplateDefMapper;
import com.cloud.marginal.mapper.layout.TasksRelDefMapper;
import com.cloud.marginal.mapper.layout.TemplateTaskDefMapper;
import com.cloud.marginal.model.dto.layout.LayoutTemplateDto;
import com.cloud.marginal.model.entity.layout.LayoutTaskDef;
import com.cloud.marginal.model.entity.layout.LayoutTemplateDef;
import com.cloud.marginal.model.entity.layout.TasksRelDef;
import com.cloud.marginal.model.entity.layout.TemplateTaskDef;
import com.cloud.marginal.model.vo.layout.LayoutTemplateVo;
import com.cloud.marginal.utils.AssertUtil;
import com.cloud.marginal.utils.UuidUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 编排模板配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class LayoutTemplateDefServiceImpl extends ServiceImpl<LayoutTemplateDefMapper, LayoutTemplateDef> implements LayoutTemplateDefService {

    @Resource
    private LayoutTemplateDefMapper layoutTemplateDefMapper;
    @Autowired
    private LayoutTaskDefService layoutTaskDefService;
    @Resource
    private TemplateTaskDefMapper templateTaskDefMapper;
    @Resource
    private TasksRelDefMapper tasksRelDefMapper;

    @Override
    @Transactional
    public void create(LayoutTemplateDef layoutTemplateDef){
        LayoutTemplateDef template = getByCode(layoutTemplateDef.getTemplateCode());
        if (template != null){
           throw new BusinessException("模板编码不能重复");
        }
        layoutTemplateDef.setId(UuidUtil.generateId());
        layoutTemplateDef.setCreatedTime(new Date());
        layoutTemplateDef.setUpdatedTime(new Date());
        layoutTemplateDef.setStatus(StatusEnum.VALID.getCode());
        layoutTemplateDefMapper.insert(layoutTemplateDef);

        // 模板主任务
        TemplateTaskDef mastTask = insertMaskTask(layoutTemplateDef);

        //模板主任务通知任务
        TemplateTaskDef noticeTask = insertMaskTaskNotice(layoutTemplateDef);

        // 主任务状态依赖
        insertMastNoticeRel(mastTask,noticeTask);

    }

    @Override
    @Transactional
    public void update(LayoutTemplateDef layoutTemplateDef) {
        if (StrUtil.isEmpty(layoutTemplateDef.getId())){
            throw new BusinessException("主键不能为空");
        }
        LayoutTemplateDef template = getByCode(layoutTemplateDef.getTemplateCode());
        if (template != null && !template.getId().equals(layoutTemplateDef.getId())){
            throw new BusinessException("模板编码不能重复");
        }
        layoutTemplateDefMapper.updateById(layoutTemplateDef);
    }

    @Override
    public LayoutTemplateDef getByCode(String code) {
        LambdaQueryWrapper<LayoutTemplateDef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LayoutTemplateDef::getStatus,StatusEnum.VALID.getCode());
        wrapper.eq(LayoutTemplateDef::getTemplateCode,code);
        List<LayoutTemplateDef> layoutTemplateDefs = layoutTemplateDefMapper.selectList(wrapper);
        if(CollUtil.isNotEmpty(layoutTemplateDefs)){
            return layoutTemplateDefs.get(0);
        }
        return null;
    }

    @Override
    public CecPage<LayoutTemplateVo> getPage(LayoutTemplateDto template) {
        Page<LayoutTemplateDef> page = new Page<>(template.getPageNum(),template.getPageSize());
        LambdaQueryWrapper<LayoutTemplateDef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LayoutTemplateDef::getStatus,StatusEnum.VALID.getCode());
        wrapper.like(StrUtil.isNotEmpty(template.getTemplateName()),LayoutTemplateDef::getTemplateName,template.getTemplateName());
        wrapper.orderByDesc(LayoutTemplateDef::getCreatedTime);
        page = layoutTemplateDefMapper.selectPage(page, wrapper);

        List<LayoutTemplateVo> record = BeanUtil.copyToList(page.getRecords(),LayoutTemplateVo.class);
        return  new CecPage<>(record,template.getPageNum(),template.getPageSize(),page.getTotal());
    }


    @Override
    public List<LayoutTemplateVo> getList(String name) {
        LambdaQueryWrapper<LayoutTemplateDef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LayoutTemplateDef::getStatus,StatusEnum.VALID.getCode());
        wrapper.like(StrUtil.isNotEmpty(name),LayoutTemplateDef::getTemplateName,name);
        wrapper.orderByDesc(LayoutTemplateDef::getCreatedBy);
        List<LayoutTemplateDef> templates = layoutTemplateDefMapper.selectList(wrapper);
        List<LayoutTemplateVo> record = BeanUtil.copyToList(templates,LayoutTemplateVo.class);
        return record;
    }

    @Override
    @Transactional
    public void remove(String templateId) {
        LambdaQueryWrapper<LayoutTemplateDef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LayoutTemplateDef::getId,templateId);
        LayoutTemplateDef templateDef = layoutTemplateDefMapper.selectOne(wrapper);
        AssertUtil.notNull(templateDef,"编排模板不存在");
        templateDef.setStatus(StatusEnum.INVALID.getCode());
        layoutTemplateDefMapper.updateById(templateDef);
    }


    private void insertMastNoticeRel(TemplateTaskDef mastTask, TemplateTaskDef noticeTask) {
        TasksRelDef tasksRel = new TasksRelDef();
        tasksRel.setId(UuidUtil.generateId());
        tasksRel.setTemplateId(mastTask.getId());
        tasksRel.setTaskId(noticeTask.getTaskId());
        tasksRel.setRelTaskId(mastTask.getTaskId());
        tasksRel.setDescription("主任务状态通知任务创建依赖主任务创建任务");
        tasksRel.setRelType(TaskRelTypeEnum.CREATE);
        tasksRel.setRelTaskId(mastTask.getTaskId());
        tasksRel.setCreatedTime(new Date());
        tasksRel.setUpdatedTime(new Date());
        tasksRel.setStatus(StatusEnum.VALID.getCode());
        tasksRelDefMapper.insert(tasksRel);
    }

    private TemplateTaskDef insertMaskTaskNotice(LayoutTemplateDef template) {
        LayoutTaskDef task = layoutTaskDefService.getCode("MASK_NOTICE");
        TemplateTaskDef templateTask = new TemplateTaskDef();
        templateTask.setId(UuidUtil.generateId());
        templateTask.setTaskId(task.getId());
        templateTask.setTemplateId(template.getId());
        templateTask.setRetryCount(3);
        templateTask.setRelRule("MULTIPLE");
        templateTask.setStatus(StatusEnum.VALID.getCode());
        templateTask.setCreatedTime(new Date());
        templateTask.setUpdatedTime(new Date());
        templateTaskDefMapper.insert(templateTask);
        return templateTask;
    }

    private TemplateTaskDef insertMaskTask(LayoutTemplateDef template) {
        LayoutTaskDef task = layoutTaskDefService.getCode("MASK_TASK");
        TemplateTaskDef templateTask = new TemplateTaskDef();
        templateTask.setId(UuidUtil.generateId());
        templateTask.setTaskId(task.getId());
        templateTask.setTemplateId(template.getId());
        templateTask.setRetryCount(0);
        templateTask.setRelRule("ONE_REQUIRED");
        templateTask.setStatus(StatusEnum.VALID.getCode());
        templateTask.setCreatedTime(new Date());
        templateTask.setUpdatedTime(new Date());
        templateTaskDefMapper.insert(templateTask);
        return templateTask;
    }

}
