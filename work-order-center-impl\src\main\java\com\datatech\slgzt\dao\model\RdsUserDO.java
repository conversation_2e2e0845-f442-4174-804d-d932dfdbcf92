package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: 数据库用户DO
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Data
@TableName("WOC_RDS_USER")
public class RdsUserDO {

    /**
     * 主键ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 数据库主键ID
     */
    @TableField("RDS_ID")
    private String rdsId;

    /**
     * 用户名称
     */
    @TableField("USER_NAME")
    private String userName;

    /**
     * 密码
     */
    @TableField("PASSWORD")
    private String password;

    /**
     * 唯一标识
     */
    @TableField("GID")
    private String gid;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    /**
     * 状态（1有效 0删除）
     */
    @TableField("ENABLED")
    @TableLogic(value = "1", delval = "0")
    private Integer enabled;
} 