package com.datatech.slgzt.enums.ip;


public enum IpTypeEnum {
    DCN("DCN", "DCN"),
    CARRIER("CARRIER", "承载网"),
    CMNET("CMNET", "CMNET"),
    PRIVATE_NET("PRIVATE_NET", "私网"),
    OTHER("OTHER", "其他");


    private final String code;

    private final String desc;

    IpTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }

    public static IpTypeEnum getByDesc(String value) {
        IpTypeEnum[] values = values();
        for (IpTypeEnum ipTypeEnum : values) {
            if (ipTypeEnum.getDesc().equals(value)) {
                return ipTypeEnum;
            }
        }
        return null;
    }
}
