package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

@Data
public class ObsParam {
    /**
     * 订单id不能为空
     */
    private String orderId;

    /**
     * 云区域编码不能为空
     */
    private String regionCode;
    /**
     * 计费号
     */
    private String billId;
    /**
     * 集团客户编码
     */
    private String groupId;
    private String projectId;
    private Integer quota;
    private Integer createDefaultBucket = 0;
    private ObsBucket createBucket;

    private  String billingPlan;

    @Data
    public static class ObsBucket {

        /**
         * 存储桶名称
         * */
        private String bucketName;
        private Integer bucketSize;
        private Integer writeProtect;
        private Integer multiVersion;
        /**
         * 存储类型
         * */
        private String storageType;
        private String strategy;
        private String storagePolicy;
        private String encryptionAlgorithms;
    }

    /**
     * 实例Id，修改删除时需传
     * */
    private String gId;
}
