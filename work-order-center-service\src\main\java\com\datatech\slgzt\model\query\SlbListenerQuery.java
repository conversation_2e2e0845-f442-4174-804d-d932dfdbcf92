package com.datatech.slgzt.model.query;

import lombok.Data;

/**
 * SLB监听器查询条件
 */
@Data
public class SlbListenerQuery {


    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    /**
     * 监听器名称
     */
    private String listenerName;
    
    /**
     * VPC ID
     */
    private String vpcId;
    
    /**
     * SLB设备ID
     */
    private String slbDeviceId;

    /**
     * slbId
     */
    private Long slbResourceDetailId;


    /**
     * 运行状态
     */
    private String runningStatus;
} 