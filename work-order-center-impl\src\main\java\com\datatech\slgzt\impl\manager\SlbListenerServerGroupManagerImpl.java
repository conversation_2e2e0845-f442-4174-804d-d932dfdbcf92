package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.SlbListenerServerGroupManagerConvert;
import com.datatech.slgzt.dao.SlbListenerServerGroupDAO;
import com.datatech.slgzt.dao.model.SlbListenerServerGroupDO;
import com.datatech.slgzt.manager.SlbListenerServerGroupManager;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import com.datatech.slgzt.model.query.SlbListenerServerGroupQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SLB监听器服务组Manager实现类
 */
@Service
public class SlbListenerServerGroupManagerImpl implements SlbListenerServerGroupManager {

    @Resource
    private SlbListenerServerGroupDAO slbListenerServerGroupDAO;
    
    @Resource
    private SlbListenerServerGroupManagerConvert convert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SlbListenerServerGroupDTO dto) {
        SlbListenerServerGroupDO entity = convert.dto2do(dto);
        entity.setCreateTime(LocalDateTime.now());
        slbListenerServerGroupDAO.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SlbListenerServerGroupDTO dto) {
        SlbListenerServerGroupDO entity = convert.dto2do(dto);
        slbListenerServerGroupDAO.update(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        slbListenerServerGroupDAO.delete(id);
    }

    @Override
    public SlbListenerServerGroupDTO getById(String id) {
        SlbListenerServerGroupDO entity = slbListenerServerGroupDAO.getById(id);
        return convert.do2dto(entity);
    }

    @Override
    public List<SlbListenerServerGroupDTO> listByListenerId(String listenerId) {
        List<SlbListenerServerGroupDO> list = slbListenerServerGroupDAO.listByListenerId(listenerId);
        return list.stream()
            .map(convert::do2dto)
            .collect(Collectors.toList());
    }

    @Override
    public PageResult<SlbListenerServerGroupDTO> page(SlbListenerServerGroupQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<SlbListenerServerGroupDO> list = slbListenerServerGroupDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }
} 