package com.cloud.marginal.layoutcenter.config;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 自定义线程池拒绝策略
 **/
@Slf4j
public class MyRejectHandler implements RejectedExecutionHandler {

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        try {
            log.info("自定义拒绝策略执行");
            // 默认的offer方法没有阻塞效果,由blockingqueue的offer改成put阻塞方法
            executor.getQueue().put(r);
        } catch (InterruptedException e) {
            log.error("线程池拒绝策略执行异常",e);
        }
    }
}
