package com.cloud.marginal.layoutcenter.service;

import com.cloud.marginal.layoutcenter.fusecloud.utils.MethodNode;
import com.cloud.marginal.layoutcenter.fusecloud.utils.MethodChainCallUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

@Component
public class MyTestService {
    @Autowired
    MethodChainCallUtil methodChainCallUtil;

    public void templateCall() throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        MethodNode m1 = new MethodNode();
        m1.setFullTypeName("com.cloud.marginal.arrangecenter.fusecloud.adapter.TestAdapter");
        m1.setMethodName("m1");
        Map<String,String> resultToNextNodeParam = new HashMap<>();
        resultToNextNodeParam.put("m1name","name");
        resultToNextNodeParam.put("age","age");
        resultToNextNodeParam.put("m1birthday","birthday");
        m1.setResultToNextNodeParamMapping(resultToNextNodeParam);

        MethodNode m2 = new MethodNode();
        m2.setFullTypeName("com.cloud.marginal.arrangecenter.fusecloud.adapter.TestAdapter");
        m2.setMethodName("m2");
        resultToNextNodeParam = new HashMap<>();
        resultToNextNodeParam.put("m2result","result");
        m2.setResultToNextNodeParamMapping(resultToNextNodeParam);
        HashMap methodParam = new HashMap();
        methodParam.put("tenantId","111");
        m2.setMethodParam(methodParam);

        MethodNode m3 = new MethodNode();
        m3.setFullTypeName("com.cloud.marginal.arrangecenter.fusecloud.adapter.TestAdapter");
        m3.setMethodName("m3");

        m1.setNextNode(m2);
        m2.setNextNode(m3);

        System.out.println(methodChainCallUtil.call(m1));
    }

    public String sayHello(){
        return "hello";
    }
}
