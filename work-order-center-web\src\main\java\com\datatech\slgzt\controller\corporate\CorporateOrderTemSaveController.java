package com.datatech.slgzt.controller.corporate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.CorporateOrderTempDTO;
import com.datatech.slgzt.model.req.corporate.CorporateOrderTempCreateReq;
import com.datatech.slgzt.model.req.corporate.CorporateOrderTempDelReq;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.corporate.CorporateOrderTempSaveService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 对公暂存接口
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月10日 14:15:56
 */
@RestController
@RequestMapping("/corporateOrderTemSave")
public class CorporateOrderTemSaveController {



    @Resource
    private CorporateOrderTempSaveService corporateOrderTempSaveService;

    @Resource
    private TenantManager tenantManager;



    /**
     * 创建
     */
    @RequestMapping(value = "/create",method = RequestMethod.POST)
    public CommonResult<Long> create(@RequestBody CorporateOrderTempCreateReq req) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "用户未登录");
        JSONObject jsonObject = req.getOrderJson();
        //获取所有keys 解析
        Set<String> keys = jsonObject.keySet();
        ArrayList<Long> ids = Lists.newArrayList();
        keys.forEach(key -> {
            String type = corporateOrderTempSaveService.parseKey(key);
            JSONArray jsonArray = jsonObject.getJSONArray(key);
            for (int i = 0; i < jsonArray.size(); i++) {
                //这里就是每个的实体类了
                JSONObject objData = jsonArray.getJSONObject(i);
               // JSONObject orderJson = objData.getJSONObject("orderJson");
                Long l = corporateOrderTempSaveService.handleCreate(type, req.getTenantId(), objData);
                ids.add(l);
            }
        });
        if ("TEMP".equals(currentUser.getAccountType())){
            corporateOrderTempSaveService.addTenantToUser(req.getTenantId(), currentUser.getId().toString());
        }
        //因为目前只有一个id 所以就返回第一个
        return CommonResult.success(StreamUtils.findAny(ids));
    }


    /**
     * 删除
     */
    @RequestMapping(value = "/delete",method = RequestMethod.POST)
    public CommonResult<Integer> delete(@RequestBody CorporateOrderTempDelReq req) {
        corporateOrderTempSaveService.handleDelete(req.getId(),req.getType(),req.getTenantId());
        return CommonResult.success(null);
    }

    /**
     * 删除全部
     */
    @RequestMapping(value = "/deleteAll",method = RequestMethod.POST)
    public CommonResult<Integer> deleteAll(@RequestBody CorporateOrderTempDelReq req) {
        corporateOrderTempSaveService.handleDeleteAll(req.getTenantId());
        return CommonResult.success(null);
    }

    /**
     * 获取当前用户的暂存对象
     */
    @RequestMapping(value = "/getcount",method = RequestMethod.POST)
    public CommonResult<Integer> getcount() {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser.getId(), "用户未登录");
        List<Long> tenantIds = tenantManager.listRelTenantIdByUserId(currentUser.getId());
        if (ObjNullUtils.isNull(tenantIds)) {
            return CommonResult.success(0);
        }
        List<CorporateOrderTempDTO> tempSave = corporateOrderTempSaveService.getTempSave(StreamUtils.toStr(tenantIds));
        int totalElements = 0;
        for (CorporateOrderTempDTO dto : tempSave) {
            JSONObject orderJson = dto.getOrderJson();
            if (orderJson != null) {
                for (Object value : orderJson.values()) {
                    if (value instanceof List) {
                        totalElements += ((List<?>) value).size();
                    }
                }
            }
        }
        return CommonResult.success(totalElements);
    }

    /**
     * 获取当前用户的暂存对象
     */
    @RequestMapping(value = "/getTempSave",method = RequestMethod.POST)
    public CommonResult<List<CorporateOrderTempDTO>> getTempSave() {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser.getId(), "用户未登录");
        List<Long> tenantIds = tenantManager.listRelTenantIdByUserId(currentUser.getId());
        if (ObjNullUtils.isNull(tenantIds)) {
            return CommonResult.success(Lists.newArrayList());
        }
        List<CorporateOrderTempDTO> tempSave = corporateOrderTempSaveService.getTempSave(StreamUtils.toStr(tenantIds));
        return CommonResult.success(tempSave);
    }
}
