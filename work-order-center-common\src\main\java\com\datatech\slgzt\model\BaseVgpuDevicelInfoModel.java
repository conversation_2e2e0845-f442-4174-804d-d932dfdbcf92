package com.datatech.slgzt.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class BaseVgpuDevicelInfoModel {
    /**
     * 显卡UUID
     */
    @JSONField(name="uuid")
    private String uuid;
    /**
     *  IP
     */
    @JSONField(name="ip")
    private String ip;
    /**
     * 端口
     */
    @JSONField(name="port")
    private String port;

    /**
     *物理显卡设备ID
     */
    @JSONField(name="device_id")
    private String deviceId;

    /**
     * 物理显卡 编号
     */
    @JSONField(name="pindex")
    private String pindex;

    /**
     * 虚拟卡 编号
     */
    @JSONField(name="vindex")
    private String vindex;




}
