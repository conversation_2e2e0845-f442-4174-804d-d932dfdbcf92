package com.datatech.slgzt.dao.model.container;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@TableName("XIE_YUN_NAMESPACE")
public class XieYunNamespaceDO {

    @TableField("ID")
    private String id;

    /**
     * 谐云命名空间id
     */
    @TableField("XIE_YUN_NAMESPACE_ID")
    private String xieYunNamespaceId;

    /**
     * 谐云项目id
     */
    @TableField("XIE_YUN_PROJECT_ID")
    private String xieYunProjectId;

    /**
     * 谐云组织id
     */
    @TableField("XIE_YUN_ORG_ID")
    private String xieYunOrgId;

    /**
     * 命名空间名称
     */
    @TableField("NAMESPACE_NAME")
    private String namespaceName;

    /**
     * 命名空间描述
     */
    @TableField("NAMESPACE_DESC")
    private String namespaceDesc;

    /**
     * 目前固定，网络
     */
    @TableField("IP_POOL")
    private String ipPool;

    /**
     * 集群名称
     */
    @TableField("CLUSTER_NAME")
    private String clusterName;

    /**
     * 节点名
     */
    @TableField("NODE_POOL_NAME")
    private String nodePoolName;

    /**
     * cpu值
     */
    @TableField("CPU_VALUE")
    private String cpuValue;

    /**
     * 内存值
     */
    @TableField("MEMORY_VALUE")
    private String memoryValue;

//    /**
//     * 资源数据
//     */
//    @TableField("XIE_YUN_NAMESPACE_CREATE_OPM_LIST")
//    private String xieYunNamespaceCreateOpmList;

    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;

    /**
     * 1：删除,0：正常
     */
    @TableField("DELETED")
    private Boolean deleted;

}

