package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: 裸金属回收模型
 * @author: LK
 * @create: 2025-06-30 15:02
 **/
@Data
public class RecoveryKafkaModel extends BaseReconveryProductModel {

    /**
     * 名称
     */
    private String name;

    /**
     * 副本
     */
    private String replication;

    /**
     * 保留时间（天）
     */
    private String retainTime;

    /**
     * 数据流量
     */
    private String dataFlow;

    /**
     * 数据存储总量
     */
    private String dataStorageTotal;

}
