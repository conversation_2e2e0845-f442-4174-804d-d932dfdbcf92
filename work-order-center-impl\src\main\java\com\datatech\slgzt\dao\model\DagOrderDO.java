package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DAG工单 DO
 */
@Data
@TableName("WOC_DAG_ORDER")
public class DagOrderDO {
    /**
     * 主键ID
     */
    @TableField("ID")
    private String id;

    @TableField("ORDER_CODE")
    private String orderCode;

    /**
     * domainCode
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    @TableField("DOMAIN_NAME")
    private String domainName;


    @TableField("CATALOGUE_DOMAIN_CODE")
    private String catalogueDomainCode;

    @TableField("CATALOGUE_DOMAIN_NAME")
    private String catalogueDomainName;


    /**
     * 模块ID
     */
    @TableField("MODULE_ID")
    private Long moduleId;

    /**
     * 所属业务模块
     */
    @TableField("MODULE_NAME")
    private String moduleName;

    /**
     * 模版名称
     */
    @TableField("DAG_NAME")
    private String templateName;

    /**
     * 模版ID
     */
    @TableField("TEMPLATE_ID")
    private String templateId;

    /**
     * 资源池id
     */
    @TableField("REGION_ID")
    private String regionId;

    /**
     * 资源池名称
     */
    @TableField("REGION_NAME")
    private String regionName;

    /**
     * 资源池code
     */
    @TableField("REGION_CODE")
    private String regionCode;

    /**
     * azId
     */
    @TableField("AZ_ID")
    private String azId;

    /**
     * az名称
     */
    @TableField("AZ_CODE")
    private String azCode;

    /**
     * az名称
     */
    @TableField("AZ_NAME")
    private String azName;

    /**
     * DAG状态
     */
    @TableField("STATUS")
    private String status;

    /**
     * 业务系统ID
     */
    @TableField("BUSINESS_SYSTEM_ID")
    private String businessSystemId;

    /**
     * 业务系统名称
     */
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;
    /**
     * 租户ID
     */
    @TableField("TENANT_ID")
    private Long tenantId;
    /**
     * 租户名称
     */
    @TableField("TENANT_NAME")
    private String tenantName;

    @TableField("BILL_ID")
    private String billId;

    @TableField("CUSTOM_NO")
    private String customNo;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @TableField("CREATED_BY")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;

    //Job执行ID
    @TableField("JOB_EXECUTION_ID")
    private Long jobExecutionId;

    /**
     * 是否已删除
     */
    @TableField("DELETED")
    private Boolean deleted;
}
