package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.query.ExportTaskQuery;
import com.datatech.slgzt.utils.PageResult;

/**
 * 导出任务Manager接口
 */
public interface ExportTaskManager {
    
    /**
     * 创建导出任务
     *
     * @return
     */
    String createTask(ExportTaskDTO dto);
    
    /**
     * 更新导出任务
     */
    void updateTask(ExportTaskDTO dto);
    
    /**
     * 根据ID查询
     */
    ExportTaskDTO getById(String id);


    /**
     * 删除
     */
    void deleteById(String id);

    /**
     * 分页查询
     */
    PageResult<ExportTaskDTO> page(ExportTaskQuery query);
} 