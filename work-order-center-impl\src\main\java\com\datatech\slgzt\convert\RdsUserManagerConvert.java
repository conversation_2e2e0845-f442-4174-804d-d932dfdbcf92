package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.RdsUserDO;
import com.datatech.slgzt.model.dto.RdsUserDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库用户Manager转换器
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Mapper(componentModel = "spring")
public interface RdsUserManagerConvert {

    /**
     * DTO转DO
     */
    RdsUserDO dto2do(RdsUserDTO dto);

    /**
     * DO转DTO
     */
    RdsUserDTO do2dto(RdsUserDO entity);

    /**
     * DO列表转DTO列表
     */
    List<RdsUserDTO> do2dto(List<RdsUserDO> list);
} 