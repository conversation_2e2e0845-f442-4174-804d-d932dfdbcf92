package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * DAG工单查询条件
 */
@Data
@Accessors(chain = true)
public class DagOrderQuery {
    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * DAG状态
     */
    private String status;

    /**
     * 业务系统ID
     */
    private String businessSystemId;
} 