package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 虚拟机性能数据表查询条件
 */
@Data
@Accessors(chain = true)
public class VMResourcePerformanceQuery {
    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    /**
     * 资源详情id
     */
    private Long resourceDetailId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private String resourceDetailType;

    /**
     * 云平台名称
     */
    private String domainName;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 可用区名称
     */
    private String azName;

    /**
     * 可用区编码
     */
    private String azCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 客户id
     */
    private String customId;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 客户创建人
     */
    private String customCreatedBy;

    /**
     * 云主机名称
     */
    private String ckHostName;

    /**
     * 云主机ip
     */
    private String ckIp;

    /**
     * 云主机cpu利用率
     */
    private Double ckCpuUtil;

    /**
     * 云主机内存利用率
     */
    private Double ckMemUtil;

    /**
     * 云主机磁盘读IOPS
     */
    private Double ckDiskReadIops;

    /**
     * 云主机磁盘写IOPS
     */
    private Double ckDiskWriteIops;

    /**
     * 云主机容量
     */
    private Double ckCapacity;

    /**
     * 云主机已用容量
     */
    private Double ckCapacityUsed;

    /**
     * 云主机容量利用率
     */
    private Double ckCapacityUtil;

    /**
     * CK中性能数据的最后更新时间
     */
    private LocalDateTime ckLastedTime;
} 