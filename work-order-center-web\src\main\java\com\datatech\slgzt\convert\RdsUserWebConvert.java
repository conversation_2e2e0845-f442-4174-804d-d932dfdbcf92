package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.RdsUserDTO;
import com.datatech.slgzt.model.dto.RdsUserOperateDTO;
import com.datatech.slgzt.model.query.RdsUserQuery;
import com.datatech.slgzt.model.req.rdsuser.*;
import com.datatech.slgzt.model.vo.RdsUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @program: workordercenterproject
 * @description: 数据库用户Web转换器
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Mapper(componentModel = "spring")
public interface RdsUserWebConvert {

    /**
     * 分页请求转查询对象
     */
    @Mapping(target = "userName", source = "username")
    RdsUserQuery convert(RdsUserPageReq req);

    /**
     * 新增请求转DTO
     */
    RdsUserDTO convert(RdsUserCreateReq req);

    /**
     * 更新请求转DTO
     */
    RdsUserOperateDTO convert(RdsUserUpdateReq req);

    /**
     * DTO转VO
     */
    RdsUserVO convert(RdsUserDTO dto);
} 