package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.VMResourcePerformanceMapper;
import com.datatech.slgzt.dao.model.VMResourcePerformanceDO;
import com.datatech.slgzt.model.query.VMResourcePerformanceQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 虚拟机性能数据表DAO
 * <AUTHOR>
 */
@Repository
public class VMResourcePerformanceDAO {

    @Resource
    private VMResourcePerformanceMapper vmResourcePerformanceMapper;

    public Long insert(VMResourcePerformanceDO vmResourcePerformanceDO) {
        vmResourcePerformanceMapper.insert(vmResourcePerformanceDO);
        return vmResourcePerformanceDO.getResourceDetailId();
    }

    public void update(VMResourcePerformanceDO vmResourcePerformanceDO) {
        vmResourcePerformanceMapper.updateById(vmResourcePerformanceDO);
    }

    public void delete(Long resourceDetailId) {
        vmResourcePerformanceMapper.deleteById(resourceDetailId);
    }

    public VMResourcePerformanceDO getById(Long resourceDetailId) {
        return vmResourcePerformanceMapper.selectById(resourceDetailId);
    }

    public List<VMResourcePerformanceDO> list(VMResourcePerformanceQuery query) {
        return vmResourcePerformanceMapper.selectList(Wrappers.<VMResourcePerformanceDO>lambdaQuery()
                        .eq(ObjNullUtils.isNotNull(query.getResourceDetailId()), VMResourcePerformanceDO::getResourceDetailId, query.getResourceDetailId())
                        .eq(ObjNullUtils.isNotNull(query.getDeviceId()), VMResourcePerformanceDO::getDeviceId, query.getDeviceId())
                        .eq(ObjNullUtils.isNotNull(query.getDeviceName()), VMResourcePerformanceDO::getDeviceName, query.getDeviceName())
                        .eq(ObjNullUtils.isNotNull(query.getResourceDetailType()), VMResourcePerformanceDO::getResourceDetailType, query.getResourceDetailType())
                        .eq(ObjNullUtils.isNotNull(query.getDomainName()), VMResourcePerformanceDO::getDomainName, query.getDomainName())
                        .eq(ObjNullUtils.isNotNull(query.getDomainCode()), VMResourcePerformanceDO::getDomainCode, query.getDomainCode())
                        .eq(ObjNullUtils.isNotNull(query.getAzName()), VMResourcePerformanceDO::getAzName, query.getAzName())
                        .eq(ObjNullUtils.isNotNull(query.getAzCode()), VMResourcePerformanceDO::getAzCode, query.getAzCode())
//                .eq(ObjNullUtils.isNotNull(query.getVmInstanceUuid()), VMResourcePerformanceDO::getVmInstanceUuid, query.getVmInstanceUuid())
//                .eq(ObjNullUtils.isNotNull(query.getVmDeleted()), VMResourcePerformanceDO::getVmDeleted, query.getVmDeleted())
                        .eq(ObjNullUtils.isNotNull(query.getCkHostName()), VMResourcePerformanceDO::getCkHostName, query.getCkHostName())
                        .eq(ObjNullUtils.isNotNull(query.getCkIp()), VMResourcePerformanceDO::getCkIp, query.getCkIp())
//                .ge(ObjNullUtils.isNotNull(query.getCkLastedTimeStart()), VMResourcePerformanceDO::getCkLastedTime, query.getCkLastedTimeStart())
//                .le(ObjNullUtils.isNotNull(query.getCkLastedTimeEnd()), VMResourcePerformanceDO::getCkLastedTime, query.getCkLastedTimeEnd())

        );
    }

    public List<VMResourcePerformanceDO> selectTop5vCPUGroupByCustom(List<String> customIds) {
        return vmResourcePerformanceMapper.selectTop5vCPUGroupByCustom(customIds);
    }

    public List<VMResourcePerformanceDO> selectTop5MemGroupByCustom(List<String> customIds) {
        return vmResourcePerformanceMapper.selectTop5MemGroupByCustom(customIds);
    }

    public List<VMResourcePerformanceDO> selectTop5IORateGroupByCustom(List<String> customIds) {
        return vmResourcePerformanceMapper.selectTop5IORateGroupByCustom(customIds);
    }

    public List<VMResourcePerformanceDO> selectTop5Resource(List<String> customIds, String orderType) {
        LambdaQueryWrapper<VMResourcePerformanceDO> wrapper = Wrappers.<VMResourcePerformanceDO>lambdaQuery()
                .in(ObjNullUtils.isNotNull(customIds), VMResourcePerformanceDO::getCustomId, customIds);
        // 根据orderType设置排序字段和方向
        applyOrderBy(wrapper, orderType);
        // 限制返回5条记录
        wrapper.last("LIMIT 5");
        List<VMResourcePerformanceDO> list = vmResourcePerformanceMapper.selectList(wrapper);
        list.forEach(item -> {
            switch (orderType) {
                case "vCPU":
                    item.setTopPercent(item.getCkCpuUtil());
                    break;
                case "MEM":
                    item.setTopPercent(item.getCkMemUtil());
                    break;
                case "IO":
                    BigDecimal readIops = item.getCkDiskReadIops() != null ? item.getCkDiskReadIops() : BigDecimal.ZERO;
                    BigDecimal writeIops = item.getCkDiskWriteIops() != null ? item.getCkDiskWriteIops() : BigDecimal.ZERO;
                    item.setTopPercent(readIops.max(writeIops));
                    break;
                default:
                    item.setTopPercent(item.getCkCpuUtil());
            }
        });
        return list;
    }

    private void applyOrderBy(LambdaQueryWrapper<VMResourcePerformanceDO> wrapper, String orderType) {
        if (ObjNullUtils.isNull(orderType)) {
            // 默认按创建时间降序
            wrapper.orderByDesc(VMResourcePerformanceDO::getCreateTime);
            return;
        }
        // 根据orderType设置排序字段
        switch (orderType) {
            case "vCPU":
                wrapper.orderByDesc(VMResourcePerformanceDO::getCkCpuUtil);
                break;
            case "MEM":
                wrapper.orderByDesc(VMResourcePerformanceDO::getCkMemUtil);
                break;
            case "IO":
                wrapper.last("ORDER BY " +
                        "CASE WHEN NVL(CK_DISK_READ_IOPS,0) >= NVL(CK_DISK_WRITE_IOPS,0) " +
                        "THEN CK_DISK_READ_IOPS ELSE CK_DISK_WRITE_IOPS END DESC");
                break;
            default:
                wrapper.orderByDesc(VMResourcePerformanceDO::getCreateTime);
        }
    }
}