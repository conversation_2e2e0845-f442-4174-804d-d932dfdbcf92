package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 16:59:06
 */
@Data
@Accessors(chain = true)
public class NonStanderWorkOrderProductQuery {


    private String orderId;

    private String workOrderId;

    private List<Long> ids;

    //父类id
    private Long parentId;

    //产品类型
    private String productType;

    /**
     * 主产品的gid列表
     */
    private List<Long> gids;

    private Collection<Long> subOrderIds;

}
