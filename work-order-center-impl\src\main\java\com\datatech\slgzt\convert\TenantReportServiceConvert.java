package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.TenantReportDO;
import com.datatech.slgzt.model.report.RegionTenantExcelDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TenantReportServiceConvert {

    default RegionTenantExcelDTO convert(TenantReportDO tenantReportDO) {
        RegionTenantExcelDTO excelDTO = new RegionTenantExcelDTO();
        excelDTO.setRegionId(tenantReportDO.getRegionId());
        excelDTO.setDataTime(tenantReportDO.getDataTime());
        excelDTO.setCmdbRegionName(tenantReportDO.getCmdbRegionName());
        excelDTO.setRegionName(tenantReportDO.getRegionName());
        excelDTO.setDeptName(tenantReportDO.getDeptName());
        excelDTO.setBusinessSystemName(tenantReportDO.getBusinessSystemName());
        excelDTO.setPrincipalName(tenantReportDO.getPrincipalName());
        excelDTO.setTenantName(tenantReportDO.getTenantName());
        excelDTO.setSystemLevel(tenantReportDO.getSystemLevel());
        excelDTO.setHasDisasterRecovery(tenantReportDO.getHasDisasterRecovery());
        excelDTO.setProjectName(tenantReportDO.getProjectName());
        excelDTO.setVcpuNum(tenantReportDO.getVcpuNum());
        excelDTO.setMemory(tenantReportDO.getMemory());
        excelDTO.setStorage(tenantReportDO.getStorage());
        excelDTO.setVcpuUtil(tenantReportDO.getVcpuUtil());
        excelDTO.setMemoryUtil(tenantReportDO.getMemoryUtil());
        excelDTO.setStorageUtil(tenantReportDO.getStorageUtil());
        excelDTO.setGpuNum(tenantReportDO.getGpuNum());
        excelDTO.setEipBandwidth(tenantReportDO.getEipBandwidth());
        excelDTO.setObsStorage(tenantReportDO.getObsStorage());
        excelDTO.setBusinessSystemCreateTime(tenantReportDO.getBusinessSystemCreateTime());
        excelDTO.setBusinessSystemExpireTime(tenantReportDO.getBusinessSystemExpireTime());
        excelDTO.setBillIdStatus(tenantReportDO.getBillIdStatus());
        excelDTO.setBillId(tenantReportDO.getBillId());
        excelDTO.setCreateTime(tenantReportDO.getCreatedAt());
        return excelDTO;
    }
} 