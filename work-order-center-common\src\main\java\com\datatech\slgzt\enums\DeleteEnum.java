package com.datatech.slgzt.enums;

/**
 * 状态枚举值
 *
 * <AUTHOR>
 * @Date: 2024/11/18 15:40
 */
public enum DeleteEnum {

    NORMAL(0, "生效"),
    DELETE(1, "失效")
    ;

    private final Integer code;
    private final String message;

    DeleteEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer code() {
        return code;
    }

    public String message() {
        return message;
    }
}
