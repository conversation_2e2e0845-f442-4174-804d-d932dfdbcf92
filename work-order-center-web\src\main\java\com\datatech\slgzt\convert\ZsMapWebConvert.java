package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.req.device.DeviceGpuInfoExportReq;
import com.datatech.slgzt.model.vo.device.DeviceCardMetricsVO;
import com.datatech.slgzt.model.vo.device.DeviceGpuInfoExportVO;
import com.datatech.slgzt.model.vo.device.DeviceMetricsListVO;
import com.datatech.slgzt.model.vo.device.DevicePagePhysicalVO;
import com.datatech.slgzt.model.vo.device.DevicePageVirtualVO;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Mapper(componentModel = "spring")
public interface ZsMapWebConvert {

    DevicePagePhysicalVO convert(DeviceGpuInfoDTO deviceGpuInfoDTO);

    DevicePageVirtualVO convert(DeviceVirtualInfoDTO deviceGpuInfoDTO);


    @Named("dddd")
    default DeviceMetricsListVO convert(DeviceCardMetricsDTO deviceGpuInfoDTO){
        DeviceMetricsListVO deviceCardMetricsVO = new DeviceMetricsListVO();
        deviceCardMetricsVO.setGpuUtilPercent(BigDecimal.valueOf(deviceGpuInfoDTO.getGpuUtilPercent()).setScale(2, RoundingMode.HALF_UP));
        deviceCardMetricsVO.setMemUtilpercent(BigDecimal.valueOf(deviceGpuInfoDTO.getMemUtilpercent()).setScale(2, RoundingMode.HALF_UP));
        deviceCardMetricsVO.setGpuTime(deviceGpuInfoDTO.getCreatedAt());
        deviceCardMetricsVO.setCreatedAt(deviceGpuInfoDTO.getCreatedAt());
        deviceCardMetricsVO.setDeviceId(deviceGpuInfoDTO.getDeviceId());
        return deviceCardMetricsVO;
    }

    /**
     * 导出请求转换为查询对象
     */
    DeviceInfoQuery convert(DeviceGpuInfoExportReq req);

    /**
     * 转换为导出VO
     */
    default DeviceGpuInfoExportVO convertToExportVO(DeviceGpuInfoDTO dto) {
        DeviceGpuInfoExportVO exportVO = new DeviceGpuInfoExportVO();
        exportVO.setAreaCode(dto.getAreaCode());
        exportVO.setRegionName(dto.getRegionName());
        exportVO.setBusinessSystemName(dto.getBusinessSystemName());
        exportVO.setDeviceId(dto.getDeviceId());
        exportVO.setModelName(dto.getModelName());
        exportVO.setDeviceIp(dto.getDcnNetAddr());
        exportVO.setCatalogueDomainName(dto.getCatalogueDomainName());
        exportVO.setDeptName(dto.getDeptName());
        
        // 处理分配状态显示
        if ("1".equals(dto.getInUsed())) {
            exportVO.setInUsed("已分配");
        } else {
            exportVO.setInUsed("未分配");
        }
        
        // 处理显存大小，转换为GB
        if (ObjNullUtils.isNotNull(dto.getMemory())) {
            exportVO.setMemory(new BigDecimal(dto.getMemory()));
        }
        
        return exportVO;
    }
}
