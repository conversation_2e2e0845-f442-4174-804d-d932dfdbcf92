package com.datatech.slgzt.model.req.dag;

import com.datatech.slgzt.model.dto.DagTemplateProductDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DAG模板产品创建请求
 */
@Data
public class DagTemplateProductCreateReq {

    private String id;

    private List<String> domainCodeList;

    private List<String> catalogueDomainCodeList;

    private List<String> reginCodeList;





} 