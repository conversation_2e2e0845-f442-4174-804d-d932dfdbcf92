package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.model.entity.layout.LayoutParamDef;
import com.cloud.marginal.model.entity.layout.TaskParamDef;

/**
 * <p>
 * 编排模板与参数关系配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface TaskParamDefService extends IService<TaskParamDef> {
    /**
     * 通过模板编号获取编排参数化定义
     * @param templateCode 模板编号
     * @return
     */
    LayoutParamDef getLayoutParamDefByTemplateCode(String templateCode);
}
