package com.datatech.slgzt.utils;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月15日 15:25:34
 */
public class RetryUtils {



    /**
     * 无参数版本的重试方法
     */
    public static <T> T retryGet(Supplier<T> callback) {
        T result = callback.get();
        if (ObjNullUtils.isNull(result)) {
            try {
                Thread.sleep(1000); // 等待1秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            result = callback.get();
        }
        return result;
    }

}
