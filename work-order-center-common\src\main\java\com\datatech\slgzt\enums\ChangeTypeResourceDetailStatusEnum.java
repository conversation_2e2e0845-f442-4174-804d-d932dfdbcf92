package com.datatech.slgzt.enums;

import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * resource detail表用的
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/2/17
 */

@Getter
@AllArgsConstructor
public enum ChangeTypeResourceDetailStatusEnum {
    UN_CHANGE("un_change", "未变更"),
    BE_CHANGING("be_changing", "变更中"),
    ;

    private final String type;

    private final String remark;

    /**
     * 通过type获取enum
     *
     * @param type
     * @return
     */
    public static ChangeTypeResourceDetailStatusEnum getByType(String type) {
        if (ObjNullUtils.isNotNull(type)) {
            for (ChangeTypeResourceDetailStatusEnum value : values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
        }
        return ChangeTypeResourceDetailStatusEnum.UN_CHANGE;
    }

    public String getType() {
        return type;
    }

    public String getRemark() {
        return remark;
    }
}
