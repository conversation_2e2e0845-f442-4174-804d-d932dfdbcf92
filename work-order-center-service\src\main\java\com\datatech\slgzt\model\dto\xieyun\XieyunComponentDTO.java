package com.datatech.slgzt.model.dto.xieyun;

import lombok.Data;

import java.util.List;


/**
 * http://188.104.190.135/caas-core/organizations/3851066601860947968/projects/1910264226588708864/apps
 * POST
 * Header Skyview-Project-Id: 1910264226588708864
 * Header Amp-Organ-Id: 3851066601860947968
 * Body
 * {
 *     "name": "cloud-resource-center-jh",
 *     "clusterName": "eic-wz-cluster",
 *     "namespace": "jjx-test-namespace-123",
 *     "version": "1.0",
 *     "components": [
 *         {
 *             "clusterName": "eic-wz-cluster",
 *             "namespace": "jjx-test-namespace-123",
 *             "name": "test-con",
 *             "replicas": 1,
 *             "initContainers": [],
 *             "containers": [
 *                 {
 *                     "type": "normal",
 *                     "name": "aaaaa",
 *                     "imagePullPolicy": "Always",
 *                     "resourcePerformance": "basic",
 *                     "limits": {
 *                         "cpu": 0.1,
 *                         "memory": 128
 *                     },
 *                     "requests": {
 *                         "cpu": 0.1,
 *                         "memory": 128
 *                     },
 *                     "image": "dddd",
 *                     "ports": [
 *                         {
 *                             "protocol": "TCP",
 *                             "port": "1111"
 *                         }
 *                     ]
 *                 }
 *             ],
 *             "networkCards": [
 *                 {
 *                     "main": true,
 *                     "networkType": "calico",
 *                     "poolModeType": "block",
 *                     "ipFamily": "IPv4",
 *                     "networkAreaName": null,
 *                     "networkAreaNickName": null,
 *                     "networkIpPoolName": "default-ippool",
 *                     "networkIpPoolNickName": "default-ippool",
 *                     "ipPoolType": "random",
 *                     "ipNum": null,
 *                     "ips": [],
 *                     "existIpNum": 0,
 *                     "createWay": "pool",
 *                     "distributeWay": null,
 *                     "ipLockedPriority": true
 *                 }
 *             ],
 *             "nodeAffinity": null,
 *             "podAffinity": null,
 *             "podAntiAffinity": null,
 *             "podAntiSelf": null,
 *             "imagesPullSecret": null,
 *             "dnsPolicy": "ClusterFirst",
 *             "search": null,
 *             "nameservers": null,
 *             "hosts": null,
 *             "sysctl": null,
 *             "options": null,
 *             "type": "hc.deployment"
 *         }
 *     ],
 *     "canvas": "{\"nodes\":[],\"links\":[]}"
 * }
 */

/**
 * 谐云容器模型
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月14日 13:48:31
 */
@Data
public class XieyunComponentDTO {

    //id
    private Long id;

    //谐云容器id
    private String xieyunContainerId;

    //谐云应用id
    private String xieyunAppId;

    //appId
    private Long appId;

    //集群名称
    private String clusterName;

    //命名空间名称
    private String namespace;

    //容器名称
    private String name;

    //副本数
    private Integer replicas;

    //类型 hc.deployment 容器类型固定
    private String type;

    //实例容器列表 todo
    private String initContainers;

    //容器列表详情
    //容器类型normal
    private String containerType;

    //容器名称
    private String containerName;

    //镜像拉取策略 always never ifNotPresent
    private String imagePullPolicy;

    //资源配置 basic 基本性能  normal 一般性能 high 高强性能 custom 自定义
    private String resourcePerformance;

    //limits 资源上限
    private ResourcePerformance limits;

    //requests 资源请求
    private ResourcePerformance requests;

    //容器端口
    private List<ContainerPort> ports;

    //image镜像
    private String image;

    //networkCards 网卡信息
    private NetworkCard networkCard;

    ////挂载目录
    //private List<VolumeMount> volumeMounts;
    //
    ////env 环境变量
    //private List<Env> env;
    //
    ////exec 执行命令
    //private List<Exec> exec;
    //
    ////livenessProbe 存活探针
    //private LivenessProbe livenessProbe;
    //
    ////readinessProbe 就绪探针
    //private String readinessProbe;
    //
    //
    ////日志编码
    //private String logEncoding;
    //
    ////时区
    //private String timeZone;
    //
    ////isLogStdout 是否输出日志到标准输出
    //private Boolean isLogStdout;
    //
    ////logPathList
    //private List<String> logPathList;
    //
    ////lifecycle 生命周期
    //private Lifecycle lifecycle;

    //----------------------------下面的参数可以空-----------------------------------------------

    //nodeAffinity节点亲和性
    private String nodeAffinity;

    //podAffinity pod亲和性
    private String podAffinity;

    //podAntiAffinity pod反亲和性
    private String podAntiAffinity;

    //podAntiSelf pod反亲和性
    private String podAntiSelf;

    //imagesPullSecret镜像拉取秘钥
    private String imagesPullSecret;

    //dnsPolicy clusterFirst default clusterFirstWithHostNet
    private String dnsPolicy;

    private String search;
    private String nameservers;
    private String hosts;
    private String sysctl;
    private String options;


    @Data
    public static class ResourcePerformance {
        //cpu
        private String cpu;

        //内存
        private String memory;

    }

    @Data
    public static class ContainerPort {
        //容器端口
        private String port;
        //协议 tcp udp
        private String protocol;
    }


    @Data

    //挂载目录
    public static class VolumeMount {
        /**
         * {
         * "type": "secret",
         * "secret": {
         * "refName": "default-token-gtptd",
         * "partialMount": false
         * },
         * "mount": [
         * {
         * "path": "/path/test/",
         * "readOnly": true,
         * "hotLoading": true
         * }
         * ]
         * }
         */
        private String type;
    }


    @Data
    public static class Env {
        /**
         * {
         * "type": "Equal",
         * "name": "",
         * "key": "test",
         * "value": "test",
         * "nameList": [],
         * "valList": []
         * }
         */
        //类型 Equal
        private String type;
        private String name;
        private String key;
        private String value;
    }

    @Data
    public static class Exec {
        /**
         * {
         * "command": [
         * "java -jar"
         * ],
         * "args": []
         * },
         */
        //命令
        private List<String> command;

        //参数
        private List<String> args;
    }


    @Data
    public static class LivenessProbe {
        /**
         * {
         * "checkWay": "http",
         * "successThreshold": 1,
         * "port": 8080,
         * "httpGet": {
         * "port": 8080,
         * "path": "http://127.0.0.1"
         * },
         * "path": "http://127.0.0.1",
         * "initialDelaySeconds": 60,
         * "timeoutSeconds": 30,
         * "periodSeconds": 30,
         * "failureThreshold": 3
         * }
         */

        //检查方式 http tcp
        private String checkWay;
        //tcpSocket
        private TcpSocket tcpSocket;

        //httpGet
        private HttpGet httpGet;

        //端口
        private Integer port;

        private String path;
        //初始延迟秒数
        private Integer initialDelaySeconds;
        //超时秒数
        private Integer timeoutSeconds;
        //周期秒数
        private Integer periodSeconds;
        //成功阈值
        private Integer successThreshold;
        //失败阈值
        private Integer failureThreshold;


    }

    @Data
    public static class ReadinessProbe {
        /**
         * {
         *                         "checkWay": "tcp",
         *                         "tcpSocket": {
         *                             "port": 8080
         *                         },
         *                         "port": 8080,
         *                         "initialDelaySeconds": 30,
         *                         "timeoutSeconds": 25,
         *                         "periodSeconds": 20,
         *                         "successThreshold": 3,
         *                         "failureThreshold": 3
         *                     }
         */
        //检查方式 http tcp
        private String checkWay;
        //tcpSocket
        private TcpSocket tcpSocket;

        //httpGet
        private HttpGet httpGet;

        //端口
        private Integer port;

        private String path;
        //初始延迟秒数
        private Integer initialDelaySeconds;
        //超时秒数
        private Integer timeoutSeconds;
        //周期秒数
        private Integer periodSeconds;
        //成功阈值
        private Integer successThreshold;
        //失败阈值
        private Integer failureThreshold;
    }


    @Data
    public static class HttpGet {
        //端口
        private Integer port;
        //路径
        private String path;

        //host
        private String host;
    }

    @Data
    public static class TcpSocket {
        //端口
        private Integer port;
    }

    @Data
    public static class Lifecycle {
        //preStop
        private LifecycleOption preStop;

        //postStart
        private LifecycleOption postStart;


    }

    @Data
    public static class LifecycleOption {
        /**
         * {
         *                             "checkWay": "http",
         *                             "host": "127.0.0.1",
         *                             "httpGet": {
         *                                 "host": "127.0.0.1",
         *                                 "port": 9099,
         *                                 "path": "/path"
         *                             },
         *                             "port": 9099,
         *                             "path": "/path"
         *                         }
         */
        private String checkWay;
        //tcpSocket
        private TcpSocket tcpSocket;
        //httpGet
        private HttpGet httpGet;
        //端口
        private Integer port;
        private String path;


    }

    @Data
    public static class NetworkCard  {

        /**
         * {
         *                     "main": true,
         *                     "networkType": "calico",
         *                     "poolModeType": "block",
         *                     "ipFamily": "IPv4",
         *                     "networkAreaName": null,
         *                     "networkAreaNickName": null,
         *                     "networkIpPoolName": "default-ippool",
         *                     "networkIpPoolNickName": "default-ippool",
         *                     "ipPoolType": "random",
         *                     "ipNum": null,
         *                     "ips": [],
         *                     "existIpNum": 0,
         *                     "createWay": "pool",
         *                     "distributeWay": null,
         *                     "ipLockedPriority": true
         *                 }
         */

        //主网卡
        private Boolean main;

        //网络类型
        private String networkType;

        //poolModeType block random static block 静态分配 random 随机分配 static 静态分配
        private String poolModeType;
        //ipFamily IPv4 IPv6
        private String ipFamily;
        //网络区域名称
        private String networkAreaName;
        //网络区域昵称
        private String networkAreaNickName;
        //网络IP池名称
        private String networkIpPoolName;
        //网络IP池昵称
        private String networkIpPoolNickName;
        //IP池类型
        private String ipPoolType;
        //IP数量
        private Integer ipNum;
        //IP列表
        private List<String> ips;
        //存在的IP数量
        private Integer existIpNum;
        //创建方式 pool ipPoolType 为static时，该字段为ipPoolType，否则为pool
        private String createWay;
        //分配方式
        private String distributeWay;
        //IP锁定优先级
        private Boolean ipLockedPriority;


    }

}
