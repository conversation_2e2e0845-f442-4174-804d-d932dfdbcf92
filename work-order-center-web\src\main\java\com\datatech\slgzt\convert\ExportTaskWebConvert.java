package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.opm.ExportTaskOpm;
import com.datatech.slgzt.model.query.ExportTaskQuery;
import com.datatech.slgzt.model.req.export.ExportTaskPageReq;
import com.datatech.slgzt.model.req.export.ExportTaskSaveReq;
import com.datatech.slgzt.model.vo.export.ExportTaskVO;
import org.mapstruct.Mapper;

/**
 * 导出任务Web转换器
 */
@Mapper(componentModel = "spring")
public interface ExportTaskWebConvert {

    /**
     * 分页请求转查询对象
     */
    ExportTaskQuery convert(ExportTaskPageReq req);


    ExportTaskOpm convert(ExportTaskSaveReq req);
    /**
     * DTO转VO
     */
    ExportTaskVO convert(ExportTaskDTO dto);
} 