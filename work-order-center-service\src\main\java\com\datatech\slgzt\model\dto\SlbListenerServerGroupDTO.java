package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.TaskStatusExt;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description SLB监听器服务组DTO
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月13日 13:44:52
 */
@Data
public class SlbListenerServerGroupDTO {

    private Long slbResourceDetailId;

    //分组名称
    private String groupName;

    //分组ID
    private String id;

    //slb监听器ID
    private String slbListenerId;

    //slb监听器名称
    private String slbListenerName;

    //0：虚拟服务器组（默认）；1：主备服务器组；
    private String groupType;

    //0：正常；1：操作中；
    private String status;

    //主服务器切换备用服务器最少可用台数  vmware平台参数，取值：
    //0：不启动主备服务组切换（默认）；
    //1：主服务器组可用设备少于1台切换备用服务器组，以此类推；
    private Integer groupPriorityGroup;


    private List<SlbListenerServerInfoModel> serverInfoModelList;

    private TaskStatusExt taskStatusExt;
    //创建时间
    private LocalDateTime createTime;


    @Data
    public static class SlbListenerServerInfoModel{
        //云主机设备id
        private String deviceId;

        //云主机资源详情id
        private Long resourceDetailId;
        //端口
        private Integer port;

        private String privateIp;

        //Vmware主备服务器组必填，取值：
        //0：不设置（默认）；
        //1：设置为主服务器；
        //2：设置为备服务器；
        private String priorityGroup;

    }

}
