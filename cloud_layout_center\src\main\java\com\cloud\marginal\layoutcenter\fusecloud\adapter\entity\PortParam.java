package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;
import java.util.List;

@Data
public class PortParam {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 云区域code
     */
    private String regionCode;

    /**
     * 虚拟网卡名称
     */
    private String name;

    /**
     * 虚拟网卡所属的网络id
     */
    private String networksId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 管理网络状态
     */
    private Boolean state;

    /**
     * ip信息
     */
    private List<PortParam.FixedIp> fixedIps;

    /**
     * 安全组信息
     */
    private List<String> securityGroups;

    /**
     * 网卡的mac地址
     */
    private String macAddress;

    /**
     * 放行信息
     */
    private List<PortParam.AllowedAddressPair> allowedAddressPairs;


    @Data
    public static class FixedIp {
        /**
         * 子网id
         */
        private String subnetId;
        /**
         * 网卡ip地址
         */
        private String ipAddress;
    }

    @Data
    public static class AllowedAddressPair {

        /**
         * 放行ip
         */
        private String ipAddress;
    }
}
