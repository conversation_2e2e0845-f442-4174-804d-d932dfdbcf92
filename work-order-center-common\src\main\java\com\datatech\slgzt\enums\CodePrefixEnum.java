package com.datatech.slgzt.enums;


import org.apache.commons.lang3.StringUtils;

/**
 * 编码前缀枚举类
     * 操作订单：O[Q/C]yyyyMMddHHmmss[4位随机]
     * 商品订单：G[Q/C]yyyyMMddHHmmss[4位随机]
     * 产品订单：P[Q/C]yyyyMMddHHmmss[4位随机]
     * 服务订单：S[Q/C]yyyyMMddHHmmss[4位随机]
 * <AUTHOR>
 */
public enum CodePrefixEnum {

    /**
     * 开通资源订单
     */
    OC("OC", "操作订单"),
    GC("GC", "商品订单"),
    PC("PC", "产品订单"),
    SC("SC", "服务订单"),
    BG("GB", "变更订单"),

    /**
     * 变更资源订单
     */
    OR("OR", "操作订单"),
    GR("GR", "商品订单"),
    PR("PR", "产品订单"),
    SR("SR", "服务订单"),


    /**
     * 延期/回收资源订单
     */
    OM("OM", "操作订单"),
    GM("GM", "商品订单"),
    PM("PM", "产品订单"),
    SM("SM", "服务订单")
    ;
    private final String code;
    private final String desc;

    CodePrefixEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static CodePrefixEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (CodePrefixEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}