package com.datatech.slgzt.strategy.bpmn;

import com.datatech.slgzt.dao.mapper.ActHiTaskMapper;
import com.datatech.slgzt.enums.ActivitiStatusEnum;
import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.enums.OrderStatusEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.factory.ActivityStrategyFactory;
import com.datatech.slgzt.model.bpmn.*;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
import com.datatech.slgzt.model.sms.SmsSendModel;
import com.datatech.slgzt.service.bpmn.ActivityApiService;
import com.datatech.slgzt.service.bpmn.ActivityBaseStrategy;
import com.datatech.slgzt.service.producer.SmsProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 非标资源开通策略
 *
 * @Author: liupeihan
 */
@Slf4j
@Service
public class NonStanderWorkFlowStrategy implements ActivityBaseStrategy, InitializingBean {

    @Autowired
    private ActivityApiService activityApiService;

    @Resource
    private SmsProducer smsProducer;

    @Resource
    private ActHiTaskMapper taskMapper;

    @Override
    public String instanceRun(BaseWorkOrder workOrder, ActivityEnum.ActivityProcessEnum processEnum, String currentRoleCode, Long resourceUserId) {
        NonStanderWorkOrderDTO orderDTO = (NonStanderWorkOrderDTO) workOrder;
        Map<String, Object> assigneeMap = new HashMap<>();
        assigneeMap.put("userId", workOrder.getCreatedBy());
        assigneeMap.put("responseScheme", AuthorityCodeEnum.RESPONSE_SCHEME_MANAGER.code());
        assigneeMap.put("govAdmin", AuthorityCodeEnum.PROVINCE_GOV_ADMIN.code());
        assigneeMap.put("business_leader_2", orderDTO.getBusinessDepartLeaderId());
        assigneeMap.put("govLeader", AuthorityCodeEnum.PROVINCE_GOV_LEADER.code());
        assigneeMap.put("cloud_leader_2", AuthorityCodeEnum.CLOUD_LEADER_2.code());
        assigneeMap.put("xiaoyun", AuthorityCodeEnum.XIAOYUN.code());
        assigneeMap.put("openWay", Boolean.TRUE.equals(orderDTO.getOfflineOpen()) ? 0 : 1);

        RunProcessVo vo = new RunProcessVo()
                .setProcessKey(processEnum.getCode())
                .setUserId(workOrder.getCreatedBy())
                .setOrderId(workOrder.getId())
                .setAssignee(assigneeMap);
        return activityApiService.runProcessInstance(vo);
    }

    @Override
    public void complete(TaskSubmitDto taskSubmitDto, BaseWorkOrder workOrder) {
        activityApiService.completeTask(taskSubmitDto);
        // 消息发送
        NonStanderWorkOrderDTO orderDTO = (NonStanderWorkOrderDTO) workOrder;
        ActivityTaskTreeVo taskVo = activityApiService.queryTasksTreeByProIntId(orderDTO.getActivitiId());
        SmsSendModel model = new SmsSendModel();
        model.setOrderCode(orderDTO.getOrderCode());
        model.setCurrentNode(taskVo.getCurrentTaskBpmnName());
        model.setActivityStatus(workOrder.getActivityStatus());
        model.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(
                taskVo.getCurrentTaskBpmnName(), OrderStatusEnum.EXAMINING.getCode()));
        model.setCreatedOrderUserId(orderDTO.getCreatedBy());
        model.setOrderType(OrderTypeEnum.NON_STANDARD.getCode());
        assembleModel(taskVo.getCurrentTaskBpmnName(), orderDTO, model);
        smsProducer.sendMessage(orderDTO.getId(), model);
    }

    @Override
    public ActivityTaskVo taskNodes(String activityId) {
        return null;
    }

    @Override
    public ActivityTaskTreeVo taskNodesTree(String activityId) {
        return activityApiService.queryTasksTreeByProIntId(activityId);
    }

    @Override
    public TaskNodeDTO nextTaskNodes(String activityId) {
        return activityApiService.queryNextTasksByProIntId(activityId);
    }

    @Override
    public void stop(BaseWorkOrder workOrder, Long userId) {
        NonStanderWorkOrderDTO orderDTO = (NonStanderWorkOrderDTO) workOrder;
        String taskId = activityApiService.stopProcessInstance(orderDTO.getActivitiId(), userId);
        taskMapper.updateAssigneeByTaskId(userId, taskId, orderDTO.getActivitiId());
    }

    @Override
    public TaskTreeNodeDTO taskNodesTreeByKey(ActivityEnum.ActivityProcessEnum processEnum) {
        return activityApiService.queryTasksTreeByProKey(processEnum.getCode());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ActivityStrategyFactory.registerBean(ActivityEnum.ActivityProcessEnum.NON_STANDARD_PROCESS.getCode(), this);
    }

    private void assembleModel(String currentTask, NonStanderWorkOrderDTO orderDTO, SmsSendModel model) {
        if (StringUtils.isNotEmpty(currentTask)) {
            if (ActivitiStatusEnum.USER_TASK.getNode().equals(currentTask)
                    || ActivitiStatusEnum.AUTODIT_END.getNode().equals(currentTask)) {
                model.setUserId(orderDTO.getCreatedBy());
                return;
            }
            if (ActivitiStatusEnum.RESPONSE_SCHEME_MANAGER.getNode().equals(currentTask)) {
                model.setRoles(Collections.singletonList(AuthorityCodeEnum.RESPONSE_SCHEME_MANAGER.code()));
                return;
            }
            if (ActivitiStatusEnum.PROVINCE_GOV_ADMIN.getNode().equals(currentTask)) {
                model.setRoles(Collections.singletonList(AuthorityCodeEnum.PROVINCE_GOV_ADMIN.code()));
                return;
            }
            if (ActivitiStatusEnum.BRANCH_LEADER.getNode().equals(currentTask)) {
                model.setUserId(orderDTO.getBusinessDepartLeaderId());
                return;
            }
            if (ActivitiStatusEnum.PROVINCE_GOV_LEADER.getNode().equals(currentTask)) {
                model.setRoles(Collections.singletonList(AuthorityCodeEnum.PROVINCE_GOV_LEADER.code()));
                return;
            }
            if (ActivitiStatusEnum.isXiaoYunNode(currentTask)) {
                model.setRoles(Collections.singletonList(AuthorityCodeEnum.XIAOYUN.code()));
                return;
            }
            if (ActivitiStatusEnum.CLOUD_RESOURCE_LEADER.getNode().equals(currentTask)) {
                //云资源部领导-则直接传对应的审批人id
                //todo 需要确定
                model.setRoles(Collections.singletonList(AuthorityCodeEnum.CLOUD_LEADER_2.code()));
                return;
            }
        }
    }
}

