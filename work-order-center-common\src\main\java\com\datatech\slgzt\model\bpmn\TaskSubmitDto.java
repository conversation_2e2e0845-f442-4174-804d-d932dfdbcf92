package com.datatech.slgzt.model.bpmn;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 任务提交信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TaskSubmitDto {

    /**
     * 流程实例ID
     */
    @NotNull(message = "流程实例id不能为空")
    private String processInstanceId;

    /**
     * 审核消息 1:审核通过 0 审核驳回（可再次提交） 2审核拒绝（关单）
     */
    @NotNull(message = "审核信息不能为空")
    private int message;

    @NotNull(message = "是否是云中心不能为空")
    private int isCloud;

    @NotNull(message = "是否有延期不能为空")
    private int flag;
    /**
     * mainId 审核人
     */
    @NotNull(message = "审核人/审核角色不能为空")
    private String assignee;

    /**
     * 是否达到需要领导审批标准
     */
    @NotNull(message = "是否达到需要领导审批标准不能为空")
    private int isUpperLimit;

    /**
     * 审核意见
     */
    private String auditOption;

    /**
     * 审核人名字
     */
    @NotNull(message = "审核人姓名不能为空")
    private String userName;

    /**
     * 动态设置下个节点审核人 不必传
     */
    private String nextAssignee;

    /**
     * 流程节点编码
     */
    private String nodeCode;


}
