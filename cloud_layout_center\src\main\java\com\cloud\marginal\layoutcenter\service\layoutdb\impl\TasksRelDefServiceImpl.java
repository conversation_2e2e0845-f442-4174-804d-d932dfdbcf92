package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.ccmp.exceptioncenter.common.utils.UuidUtil;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.enums.layout.*;
import com.cloud.marginal.layoutcenter.service.layoutdb.TasksRelDefService;
import com.cloud.marginal.mapper.layout.TasksRelDefMapper;
import com.cloud.marginal.model.dto.layout.SaveTasksRelDto;
import com.cloud.marginal.model.dto.layout.TasksRelPageDto;
import com.cloud.marginal.model.entity.layout.TaskRel;
import com.cloud.marginal.model.entity.layout.TasksRelDef;
import com.cloud.marginal.model.vo.layout.TaskRelVO;
import com.cloud.marginal.utils.AssertUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 编排任务关系配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class TasksRelDefServiceImpl extends ServiceImpl<TasksRelDefMapper, TasksRelDef> implements TasksRelDefService {

    @Override
    public List<TaskRel> getTaskRelListByTemplateCode(String templateCode) {
        return this.baseMapper.getTaskRelListByTemplateCode(templateCode);
    }

    @Override
    @Transactional
    public void create(@Validated SaveTasksRelDto tasksRelDto) {
        if(tasksRelDto.getTaskId().equals(tasksRelDto.getRelTaskId())){
            throw new BusinessException("任务无法依赖自己！");
        }
        LambdaQueryWrapper<TasksRelDef> wrapper = new LambdaQueryWrapper<TasksRelDef>()
                .eq(TasksRelDef::getTemplateId, tasksRelDto.getTemplateId())
                .eq(TasksRelDef::getTaskId, tasksRelDto.getTaskId())
                .eq(TasksRelDef::getRelTaskId, tasksRelDto.getRelTaskId())
                .eq(TasksRelDef::getStatus, StatusEnum.VALID.getCode());
        List<TasksRelDef> tasksRels = this.baseMapper.selectList(wrapper);
        if(CollUtil.isNotEmpty(tasksRels)){
            throw new BusinessException("当前任务中已存在相同的任务关联关系，请勿重复关联");
        }
        TasksRelDef tasksRelDef = new TasksRelDef();
        BeanUtils.copyProperties(tasksRelDto,tasksRelDef);
        tasksRelDef.setId(UuidUtil.getUUID());
        tasksRelDef.setRelType(TaskRelTypeEnum.EXECUTE);
        tasksRelDef.setCreatedTime(new Date());
        if(tasksRelDto.getDepType().equals(TaskRelClassEnum.TASK_REL_DEP.getCode())){ //关系依赖
            tasksRelDef.setRelState(8);
        }else if(tasksRelDto.getDepType().equals(TaskRelClassEnum.TASK_NOTICE.getCode())){ //任务通知
            tasksRelDef.setRelState(24);
        }
        this.save(tasksRelDef);

        if(tasksRelDto.getRelType().equals(TaskRelEnum.DYNAMIC_DEPENDENCE.getCode())) {//动态依赖 还需插入create
            TasksRelDef tasksRelDefCre = new TasksRelDef();
            BeanUtils.copyProperties(tasksRelDto, tasksRelDefCre);
            tasksRelDefCre.setId(UuidUtil.getUUID());
            tasksRelDefCre.setRelType(TaskRelTypeEnum.CREATE);
            tasksRelDefCre.setCreatedTime(new Date());
            this.save(tasksRelDefCre);
        }
    }

    @Override
    @Transactional
    public void remove(String tasksRelId) {
        TasksRelDef tasksRelDef = this.baseMapper.selectById(tasksRelId);
        AssertUtil.isTrue(ObjectUtil.isNotEmpty(tasksRelDef),"该关系不存在，无法删除");
        LambdaUpdateWrapper<TasksRelDef> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TasksRelDef::getTemplateId,tasksRelDef.getTemplateId());
        wrapper.eq(TasksRelDef::getTaskId,tasksRelDef.getTaskId());
        wrapper.eq(TasksRelDef::getRelTaskId,tasksRelDef.getRelTaskId());
        wrapper.set(TasksRelDef::getStatus, StatusEnum.INVALID.getCode());

        this.update(wrapper);
    }

    @Override
    public CecPage<TaskRelVO> getPage(TasksRelPageDto tasksRelPageDto) {
        Page<TaskRelVO> page = new Page<>(tasksRelPageDto.getPageNum(),tasksRelPageDto.getPageSize());

        Page<TaskRelVO> pages =  this.baseMapper.getPage(page,
                tasksRelPageDto.getTemplateId(),
                tasksRelPageDto.getTaskName());

        return new CecPage(pages.getRecords(),pages.getCurrent(),pages.getSize(),pages.getTotal());
    }
}
