package com.datatech.slgzt.model.req.couldPort;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云端口新增请求
 */
@Data
public class CloudPortCreateReq implements Serializable {

    private String id;


    /**
     * 云区域编码
     */
    private String regionCode;

    /**
     * 云区域编码
     */
    private String regionName;
    /**
     * 云平台类型
     */
    private String catalogueDomainCode;


    private String catalogueDomainName;
    /**
     * 云区域id
     */
    private String regionId;


    /**
     * 云平台编码
     */
    private String platformCode;


    private String platformName;

    /**
     * 可用区编码
     */
    private String azCode;


    private String azName;
    /**
     * 可用区 名称
     */
    private String azCodeName;

    /**
     * 云端口名称
     */
    private String cloudPortName;


    /**
     * vpn名称
     */
    private String vpcName;

    /**
     * vpn id
     */
    private String vlanId;




    /**
     * 互联的VLAN ID
     */
    private String vpcId;

    /**
     * 创建BGP的对端邻居地址
     */
    private String peerIp;

    /**
     * 创建BGP的外部接口地址
     */
    private String srcIp;

    /**
     * peer口令
     */
    private String peerPassword;

    private String peerAsNumber;


    private String remoteCidr;

    private LocalDateTime createTime;

//    regionCode	1	String	云区域编码
//    billId	1	String	计费号
//    azCode	1	String	可用区编码
//    vpcId	?	String	VPCid
//    vlanId	1	String	互联的VLAN ID
//    peerIp	1	String	创建BGP的对端邻居地址
//    srcIp	1	String	创建BGP的外部接口地址
//    peerPassword	1	String	peer口令
//    peerAsNumber	?	String	华为平台必填
//    name	1	String	名称
//    remoteCidr	?	String	浪潮必填



} 