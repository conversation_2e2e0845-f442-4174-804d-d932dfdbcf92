package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.VMResourcePerformanceDO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface VMResourcePerformanceManagerConvert {

    VMResourcePerformanceDTO do2dto(VMResourcePerformanceDO vmResourcePerformanceDO);

    List<VMResourcePerformanceDTO> dos2dtos(List<VMResourcePerformanceDO> vmResourcePerformanceDO);

    VMResourcePerformanceDO dto2do(VMResourcePerformanceDTO vmResourcePerformanceDTO);
}