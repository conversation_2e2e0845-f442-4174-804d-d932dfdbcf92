package com.cloud.marginal.layoutcenter.service;

import com.cloud.marginal.model.dto.layout.LayoutOrder;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.model.entity.layout.*;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;

import java.util.List;

public interface LayoutService {

    /**
     * 编排任务初始化
     * jiangjinxin
     * 执行顺序描述概述
     * 1.入参业务调用方 主参数包括 bussinessCode=数据库里的templateCode 例子ECS_COMBINATION_SUBSCRIBE
     * 还有 productList对象集合<-list里面就是具体要创建的产品 比如ESC 可能会联合创建 ECS EVS eip等 但是能创建的条件一定是在这个templateCode配置范围的 具体方法看com.cloud.marginal.layoutcenter.service.impl.LayoutServiceImpl#generateProductTaskNode(com.cloud.marginal.model.dto.layout.ProductOrder, java.util.Date, java.lang.String, java.lang.String)
     *
     *
     */
    CecResult layoutTaskInit(LayoutOrder layoutOrder);

    /**
     * 编排任务执行
     */
    default CecResult layoutTaskExecut(String masterTaskId){return null;};

    /**
     * 执行任务
     */
    default void taskExecute(LayoutTaskVO layoutTaskVO){};

    /**
     * 处理中任务补偿执行
     */
    default void processingTaskExecute(LayoutTaskVO layoutTaskVO){};

    /**
     * 执行中的子任务监控
     */
    default void taskMonitor(LayoutTaskVO layoutTaskVO){};

    void initSave(List<LayoutTask> mainTaskList, List<LayoutTaskNode> taskNodeList, List<TasksRel> tasksRelList, List<LayoutApi> layoutApiList, List<TaskNodeApi> taskNodeApiList, List<LayoutParam> layoutParamList, List<TaskParam> taskParamList);

}
