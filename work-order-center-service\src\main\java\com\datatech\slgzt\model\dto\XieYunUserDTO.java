package com.datatech.slgzt.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 容器-用户类
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@Accessors(chain = true)
public class XieYunUserDTO {

    private String id;

    private String xieYunUserId;

    private String username;

    private String name;

    private String mobile;

    private String email;

    //创建时间
    private LocalDateTime createdTime;

    //更新时间
    private LocalDateTime updatedTime;

    //1：删除,0：正常
    private boolean deleted;
}

