package com.datatech.slgzt.impl.service;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.manager.VirtualIpManager;
import com.datatech.slgzt.model.dto.VirtualIpDTO;
import com.datatech.slgzt.service.VirtualIpService;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月09日 16:37:52
 */
@Slf4j
@Service
public class VirtualIpServiceImpl implements VirtualIpService {

    @Resource
    private VirtualIpManager virtualIpManager;

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;

    /**
     * 创建虚拟IP
     *
     * @param virtualIpDTO
     * @return
     */
    @Override
    public void createVirtualIp(VirtualIpDTO virtualIpDTO) {
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/eip/unbind";
        //获取子网ID
        String subnetId = virtualIpDTO.getSubnetId();
        //获取VIpName
        String vipName = virtualIpDTO.getVipName();
        //获取Vip
        String vip = virtualIpDTO.getIpAddress();
        //获取全局唯一编码
        String gId = virtualIpDTO.getGid();
        HashMap<String, Object> params = new HashMap<>();
        params.put("subnetId", subnetId);
        params.put("vipName", vipName);
        params.put("vip", vip);
        params.put("gId", gId);
        log.info("发送创建虚拟IP请求: {}", JSON.toJSONString(params));
        //调用平台服务创建虚拟IP
        Mapper responseMapper = OkHttpsUtils.http()
                                            .sync(url)
                                            .bodyType(OkHttps.JSON)
                                            .setBodyPara(JSON.toJSONString(params))
                                            .post()
                                            .getBody()
                                            .toMapper();
        log.info("发送创建虚拟IP请求: {}", responseMapper.toString());
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                "发送创建虚拟IP请求失败: " + responseMapper.getString("message"));
        virtualIpManager.add(virtualIpDTO);
    }

    /**
     * 删除虚拟ip
     */
    @Override
    public void deleteVirtualIp(String id) {
        VirtualIpDTO virtualIpDTO = virtualIpManager.getById(id);
        Precondition.checkArgument(virtualIpDTO != null, "虚拟IP不存在");
        //获取虚拟IP的ID
        String deviceId = virtualIpDTO.getDeviceId();
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/eip/unbind";
        HashMap<String, Object> params = new HashMap<>();
        params.put("vipId", deviceId);
        log.info("发送删除虚拟IP请求: {}", JSON.toJSONString(params));
        //调用平台服务删除虚拟IP
        Mapper responseMapper = OkHttpsUtils.http()
                                            .sync(url)
                                            .bodyType(OkHttps.JSON)
                                            .setBodyPara(JSON.toJSONString(params))
                                            .post()
                                            .getBody()
                                            .toMapper();
        log.info("发送删除虚拟IP请求: {}", responseMapper.toString());
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                "发送删除虚拟IP请求失败: " + responseMapper.getString("message"));
        //调用成功后拿到返回的任务ID
        Mapper mapper = responseMapper.getMapper("entity");
        String taskId = mapper.getString("id");
        //获取状态 如果是执行中 返回
        // 成功：SUCCESS
        // 失败: ERROR
        //执行中: EXECUTION
        String status = mapper.getString("status");
        Precondition.checkArgument(!"ERROR".equals(status), "删除虚拟IP失败: " + responseMapper.getString("message"));
        //如果是SUCCESS 则删除虚拟IP
        if ("SUCCESS".equals(status)) {
            virtualIpManager.delete(id);
        }
    }
}
