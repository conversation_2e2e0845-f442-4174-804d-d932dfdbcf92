package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DeviceGpuInfoMapper;
import com.datatech.slgzt.dao.model.DeviceGpuInfoDO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Desc DeviceGpuInfoDAO
 * <AUTHOR>
 * @DATA 2025-06-12
 */
@Repository
public class DeviceGpuInfoDAO {

    @Resource
    private DeviceGpuInfoMapper mapper;

    public DeviceGpuInfoDO getById(String id) {
        return mapper.selectById(id);
    }

    public DeviceGpuInfoDO getByDeviceId(String deviceId) {
        return StreamUtils.findAny(mapper.selectList(Wrappers.<DeviceGpuInfoDO>lambdaQuery()
                                                     .like(DeviceGpuInfoDO::getDeviceId, deviceId)));
    }

    public void insert(DeviceGpuInfoDO deviceGpuInfoDO) {
        mapper.insert(deviceGpuInfoDO);
    }


    public List<String> groupModelName(){
        return mapper.groupModelName();
    }


    public void updateById(DeviceGpuInfoDO deviceGpuInfoDO) {
        mapper.updateById(deviceGpuInfoDO);
    }

    public void updateLastByDeviceId(DeviceGpuInfoDO deviceGpuInfoDO) {
        mapper.update(null, Wrappers.<DeviceGpuInfoDO>lambdaUpdate()
                                    .set(DeviceGpuInfoDO::getLastPeriod, deviceGpuInfoDO.getLastPeriod())
                                    .like(DeviceGpuInfoDO::getDeviceId, deviceGpuInfoDO.getDeviceId()));
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }

    public List<DeviceGpuInfoDO> selectDeviceGpuInfoList(DeviceGpuInfoDTO query){
        return mapper.selectList(Wrappers.<DeviceGpuInfoDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), DeviceGpuInfoDO::getDomainCode, query.getDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getCatalogueDomainCode()), DeviceGpuInfoDO::getCatalogueDomainCode, query.getCatalogueDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getRegionCode()), DeviceGpuInfoDO::getRegionCode, query.getRegionCode())
                .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), DeviceGpuInfoDO::getBusinessSystemId, query.getBusinessSystemId()));
    }

    public List<DeviceGpuInfoDO> selectList(DeviceInfoQuery query){
        return mapper.selectList(Wrappers.<DeviceGpuInfoDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getAreaCode()), DeviceGpuInfoDO::getAreaCode, query.getAreaCode())
                .eq(ObjNullUtils.isNotNull(query.getSourceType()), DeviceGpuInfoDO::getSourceType, query.getSourceType())
                .eq(ObjNullUtils.isNotNull(query.getSliceStatus()), DeviceGpuInfoDO::getSliceStatus, query.getSliceStatus())
                .eq(ObjNullUtils.isNotNull(query.getInUsed()), DeviceGpuInfoDO::getInUsed, query.getInUsed())
                .eq(ObjNullUtils.isNotNull(query.getDncIp()), DeviceGpuInfoDO::getDcnNetAddr, query.getDncIp())
                .eq(ObjNullUtils.isNotNull(query.getDeviceType()), DeviceGpuInfoDO::getDeviceType, query.getDeviceType())
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), DeviceGpuInfoDO::getDomainCode, query.getDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getModelName()), DeviceGpuInfoDO::getModelName, query.getModelName())
                .in(ObjNullUtils.isNotNull(query.getDeviceIds()), DeviceGpuInfoDO::getDeviceId, query.getDeviceIds())
                .eq(ObjNullUtils.isNotNull(query.getCatalogueDomainCode()), DeviceGpuInfoDO::getCatalogueDomainCode, query.getCatalogueDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getRegionCode()), DeviceGpuInfoDO::getRegionCode, query.getRegionCode())
                .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), DeviceGpuInfoDO::getBusinessSystemId, query.getBusinessSystemId())
                .isNotNull(query.getGpuSort(), DeviceGpuInfoDO::getLastPeriod)
                .last(query.getGpuSort(),"order by CAST(COALESCE(json_value(LAST_PERIOD, '$.gpuUtilPercent'), '-1') AS DECIMAL) DESC")
        );
    }



}
