package com.datatech.slgzt.manager.xieyun.local;

import java.util.List;

import com.datatech.slgzt.model.dto.XieYunProjectDTO;
import com.datatech.slgzt.model.query.container.XieYunProjectQuery;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/15
 */
public interface XieyunProjectLocalManager {

    void insert(XieYunProjectDTO projectDTO);

    void updateByProjectId(XieYunProjectDTO projectDTO);
    
    List<XieYunProjectDTO> list(XieYunProjectQuery query);
}
