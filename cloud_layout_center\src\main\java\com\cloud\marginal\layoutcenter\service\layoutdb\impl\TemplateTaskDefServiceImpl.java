package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.marginal.enums.layout.StatusEnum;
import com.cloud.marginal.layoutcenter.service.layoutdb.TemplateTaskDefService;
import com.cloud.marginal.mapper.layout.TemplateTaskDefMapper;
import com.cloud.marginal.model.entity.layout.TemplateTaskDef;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 编排模板与任务关系配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class TemplateTaskDefServiceImpl extends ServiceImpl<TemplateTaskDefMapper, TemplateTaskDef> implements TemplateTaskDefService {

    @Resource
    private TemplateTaskDefMapper templateTaskDefMapper;

    @Override
    public TemplateTaskDef getSingle(String templateId, String taskId) {
        LambdaQueryWrapper<TemplateTaskDef> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TemplateTaskDef::getTemplateId,templateId);
        wrapper.eq(TemplateTaskDef::getTaskId,taskId);
        wrapper.eq(TemplateTaskDef::getStatus, StatusEnum.VALID.getCode());
        List<TemplateTaskDef> templateTasks = templateTaskDefMapper.selectList(wrapper);
        if(CollUtil.isNotEmpty(templateTasks)){
            return templateTasks.get(0);
        }
        return null;
    }

    @Override
    public void delete(String id) {
        LambdaUpdateWrapper<TemplateTaskDef> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(TemplateTaskDef::getStatus,StatusEnum.INVALID.getCode());
        wrapper.eq(TemplateTaskDef::getId,id);
        this.update(wrapper);
    }
}
