package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.edge.ResourceTypeEnum;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.base.BaseService;
import com.cloud.marginal.layoutcenter.factory.LaoutServiceFactory;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.*;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.dto.EipBindDeviceDto;
import com.cloud.marginal.model.dto.edge.*;
import com.cloud.marginal.model.entity.edge.IpAddress;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Eip管理适配
 */
@Component
@Slf4j
public class IpAddressMgAdapter extends BaseNorthInterfaceAdapter implements BaseService {

    @Resource
    private LaoutServiceFactory laoutServiceFactory;

    @Override
    public void afterPropertiesSet() throws Exception {
        laoutServiceFactory.register("EIP", this);
    }

    @Override
    public TaskVO handler(LayoutTaskVO layoutTaskVO) {
        String id = layoutTaskVO.getId();
        String taskCode = layoutTaskVO.getTaskCode();
        Integer taskSource = layoutTaskVO.getTaskSource();
        switch (taskCode) {
            case "EIP_CREATE":
                return this.createEip(id, taskSource);
            case "EIP_DELETE":
                return this.deleteEip(id, taskSource);
            case "EIP_UNBIND_SLB":
            case "EIP_UNBIND_ECS":
                return this.unBindEip(id, taskSource);
            case "EIP_MODIFY":
                return this.modifyEip(id, taskSource);
            case "EIP_BIND":
                return this.bindEip(id, taskSource);
            default:
                if (taskCode.contains("EIP_BIND")) {
                    return this.bindEip(id, taskSource);
                }
                throw new BusinessException("产品类型错误");
        }
    }

    /**
     * 创建弹性ip
     */
    public TaskVO createEip(String taskId, Integer taskSource) {
        log.info("createEip start");
        EipDTO eipDTO = generateCreateEipDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateEip(),
                null,
                eipDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create eip");
        return tasksVoResult.getEntity();
    }

    /**
     * 删除弹性IP
     */
    public TaskVO deleteEip(String taskId, Integer taskSource) {
        log.info("deleteEip start");
        DeleteEipDTO deleteEipDTO = generateDeleteEipDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteEip(),
                null,
                deleteEipDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "delete eip");
        return tasksVoResult.getEntity();
    }

    /**
     * 解绑eip
     */
    public TaskVO unBindEip(String taskId, Integer taskSource) {
        log.info("unBindEip start");
        UnBindEipDTO unBindEipDTO = generateUnBindEipDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getUnBindEip(),
                null,
                unBindEipDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "unBind eip");
        return tasksVoResult.getEntity();
    }

    /**
     * 变更弹性ip
     */
    public TaskVO modifyEip(String taskId, Integer taskSource) {
        log.info("modifyEip start");
        ModifyEipDTO modifyEipDTO = generateModifyEipDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getModifyEip(),
                null,
                modifyEipDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "modify eip");
        return tasksVoResult.getEntity();
    }

    /**
     * 绑定弹性ip至资源上
     */
    public TaskVO bindEip(String taskId, Integer taskSource) {
        log.info("bindEip start");
        BindEipDTO bindEipDTO = generateBindEipDto(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getBindEip(),
                null,
                bindEipDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "vm bind eip");
        return tasksVoResult.getEntity();
    }

    /**
     * 生成北向接口创建eip的参数
     *
     * @param layoutParam 编排参数
     */
    private EipDTO generateCreateEipDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam eipOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_CREATE.getCode());
        EipParam eipParam = JSONObject.parseObject(eipOrder.getAttrs(), EipParam.class);

        EipDTO eipDTO = new EipDTO();
        eipDTO.setTenantId(layoutOrderParam.getTenantId());
        eipDTO.setRegionCode(layoutOrderParam.getRegionCode());
        eipDTO.setOutInstanceId(eipParam.getOutInstanceId());
        eipDTO.setName(StringUtils.isEmpty(eipParam.getEipName()) ? randomEipName() : eipParam.getEipName());
        eipDTO.setOrderId(eipOrder.getProductOrderId());
        eipDTO.setBandwidth(eipParam.getBandwidth());
        eipDTO.setgId(eipParam.getgId());
        eipDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        return eipDTO;
    }

    /**
     * 生成北向接口云主机绑定eip的参数
     *
     * @param layoutParam 编排参数
     */
    private BindEipDTO generateBindEipDto(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        // 获取绑定参数
        EipBindDeviceDto eipBindDeviceDto = getEipBindDeviceDto(layoutOrderParam.getProductOrders());

        // 公共参数设置
        BindEipDTO bindEipDTO = new BindEipDTO();
        bindEipDTO.setTenantId(layoutOrderParam.getTenantId());
        bindEipDTO.setGroupId(layoutOrderParam.getCustomId());
        bindEipDTO.setRegionCode(layoutOrderParam.getRegionCode());
        bindEipDTO.setBillId(layoutOrderParam.getAccount());
        // 绑定参数设置
        bindEipDTO.setVpcId(eipBindDeviceDto.getVpcId());
        bindEipDTO.setPublicIp(eipBindDeviceDto.getPublicIp());
        bindEipDTO.setDeviceId(eipBindDeviceDto.getDeviceId());
        bindEipDTO.setDeviceType(eipBindDeviceDto.getDeviceType());
        if (bindEipDTO.getDeviceType().equals(ResourceTypeEnum.VM.getCode()) ||
                bindEipDTO.getDeviceType().equals(ResourceTypeEnum.GPU.getCode())) {
            bindEipDTO.setVmId(eipBindDeviceDto.getDeviceId());
        }

        return bindEipDTO;
    }

    private EipBindDeviceDto getEipBindDeviceDto(List<ProductOrderParam> productOrders) {
        EipBindDeviceDto eipBindDeviceDto = new EipBindDeviceDto();
        for (ProductOrderParam productOrder : productOrders) {
            // 云主机创建
            if (productOrder.getProductOrderType().equals(ProductOrderTypeEnum.ECS_CREATE.toString())) {
                String ecsCreateCode = ProductOrderTypeEnum.ECS_CREATE.getCode();
                ProductOrderParam ecsOrder = getProductOrder(productOrders, ecsCreateCode);
                EcsParam ecsParam = JSONObject.parseObject(ecsOrder.getAttrs(), EcsParam.class);
                // 设置设备ID/类型/公网IP/VPCID
                eipBindDeviceDto.setDeviceId(ecsParam.getId());
                eipBindDeviceDto.setDeviceType(ResourceTypeEnum.VM.getCode());
                eipBindDeviceDto.setPublicIp(ecsParam.getPublicIp());
                if (ecsParam.getVpcInfo() != null) {
                    eipBindDeviceDto.setVpcId(ecsParam.getVpcInfo().getVpcId());
                } else {
                    ProductOrderParam vpcOrder = getProductOrder(productOrders, ProductOrderTypeEnum.VPC_CREATE.getCode());
                    VpcParam vpcParam = JSONObject.parseObject(vpcOrder.getAttrs(), VpcParam.class);
                    eipBindDeviceDto.setVpcId(vpcParam.getId());
                }
                break;
            }
            // SLB创建
            if (productOrder.getProductOrderType().equals(ProductOrderTypeEnum.SLB_CREATE.toString())) {
                String slbCreateCode = ProductOrderTypeEnum.SLB_CREATE.getCode();
                ProductOrderParam slbOrder = getProductOrder(productOrders, slbCreateCode);
                SlbParam slbParam = JSONObject.parseObject(slbOrder.getAttrs(), SlbParam.class);
                // 设置设备ID/类型/VPCID
                eipBindDeviceDto.setDeviceId(slbParam.getGId());
                eipBindDeviceDto.setDeviceType(ResourceTypeEnum.SLB.getCode());
                eipBindDeviceDto.setVpcId(slbParam.getVpcId());
                break;
            }
            // TODO 其它与EIP绑定相关的资源创建
        }

        for (ProductOrderParam productOrder : productOrders) {
            // EIP创建
            if (productOrder.getProductOrderType().equals(ProductOrderTypeEnum.EIP_CREATE.toString())) {
                String eipCreateCode = ProductOrderTypeEnum.EIP_CREATE.getCode();
                ProductOrderParam eipOrder = getProductOrder(productOrders, eipCreateCode);
                EipParam eipParam = JSONObject.parseObject(eipOrder.getAttrs(), EipParam.class);

                // eip创建绑定已有云主机场景
                if (null == eipBindDeviceDto.getDeviceId()) {
                    eipBindDeviceDto.setDeviceId(eipParam.getEcsResourceId());
                    eipBindDeviceDto.setDeviceType(ResourceTypeEnum.VM.getCode());
                }
                // 设置公网ip
                if (!StringUtils.isEmpty(eipParam.getPublicIp())) {
                    eipBindDeviceDto.setPublicIp(eipParam.getPublicIp());
                } else if (!StringUtils.isEmpty(eipParam.getId())) {
                    eipBindDeviceDto.setPublicIp(getPublicIpByEipId(eipParam.getId()));
                } else {
                    throw new RuntimeException("bind eip lost eipId and ip.fill eipId or ip,please!");
                }
                break;
            }
        }
        return eipBindDeviceDto;
    }


    /**
     * 生成北向接口删除eip的参数
     *
     * @param layoutParam 编排参数
     */
    private DeleteEipDTO generateDeleteEipDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam eipOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_DELETE.getCode());
        EipParam eipParam = JSONObject.parseObject(eipOrder.getAttrs(), EipParam.class);
        log.info("----------" + JSONObject.toJSONString(eipParam));
        DeleteEipDTO deleteEipDTO = new DeleteEipDTO();
        deleteEipDTO.setTenantId(layoutOrderParam.getTenantId());
        deleteEipDTO.setRegionCode(layoutOrderParam.getRegionCode());
        if (StringUtils.isEmpty(eipParam.getResourceId())) {
            throw new RuntimeException("delete eip lost eipId and ip.fill eipId or ip,please!");
        } else {
            //资源中心id
            deleteEipDTO.setInstanceId(eipParam.getResourceId());
        }
        return deleteEipDTO;
    }

    private UnBindEipDTO generateUnBindEipDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam eipOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_DELETE.getCode());
        EipParam eipParam = JSONObject.parseObject(eipOrder.getAttrs(), EipParam.class);
        log.info("----------" + JSONObject.toJSONString(eipParam));
        UnBindEipDTO unBindEipDTO = new UnBindEipDTO();
        unBindEipDTO.setTenantId(layoutOrderParam.getTenantId());
        unBindEipDTO.setRegionCode(layoutOrderParam.getRegionCode());
        if (StringUtils.isEmpty(eipParam.getResourceId())) {
            throw new RuntimeException("unBind eip lost eipId and ip.fill eipId or ip,please!");
        } else {
            //资源中心id
            unBindEipDTO.setPublicId(eipParam.getResourceId());
            //不一定是云主机，可能是nat，slb
            unBindEipDTO.setDeviceType(eipParam.getType());
            unBindEipDTO.setDeviceId(eipParam.getEcsResourceId());
        }
        return unBindEipDTO;
    }

    private ModifyEipDTO generateModifyEipDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam eipOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_MODIFY.getCode());
        EipParam eipParam = JSONObject.parseObject(eipOrder.getAttrs(), EipParam.class);

        ModifyEipDTO modifyEipDTO = new ModifyEipDTO();
        modifyEipDTO.setTenantId(layoutOrderParam.getTenantId());
        modifyEipDTO.setRegionCode(layoutOrderParam.getRegionCode());
        modifyEipDTO.setBandwidth(eipParam.getBandwidth());

        if (!StringUtils.isEmpty(eipParam.getResourceId())) {
            modifyEipDTO.setInstanceId(eipParam.getResourceId());
        } else {
            throw new RuntimeException("modify eip lost eipId and ip.fill eipId or ip,please!");
        }
        return modifyEipDTO;
    }

    private String getPublicIpByEipId(String eipId) {
        String queryEipUrl = northInterfaceAddress.getQueryEip() + "?eipId={eipId}";
        Map<String, Object> pathParam = new HashMap<>();
        pathParam.put("eipId", eipId);
        CecResult<IpAddress> cecPageCecResult = northInterfaceHttpRequestUtil.get(queryEipUrl, null, new TypeReference<CecResult<IpAddress>>() {
        }, pathParam);
        IpAddress entity = cecPageCecResult.getEntity();
        return entity.getIp();
    }

    private String randomEipName() {
        Long timestamp = System.currentTimeMillis();
        return "eip" + timestamp + new Random().nextInt(100);
    }

}
