package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.query.DagTemplateQuery;
import com.datatech.slgzt.model.req.dag.DagTemplateCreateReq;
import com.datatech.slgzt.model.req.dag.DagTemplatePageReq;
import com.datatech.slgzt.model.req.dag.DagTemplateProductCreateReq;
import com.datatech.slgzt.model.req.dag.DagTemplateUpdateReq;
import com.datatech.slgzt.model.vo.dag.DagTemplateVO;
import org.mapstruct.Mapper;

/**
 * DAG模板Web层对象转换器
 */
@Mapper(componentModel = "spring")
public interface DagTemplateWebConvert {
    
    /**
     * CreateReq转DTO
     */
    DagTemplateDTO req2dto(DagTemplateCreateReq req);

    DagTemplateDTO req2dto(DagTemplateProductCreateReq req);

    /**
     * UpdateReq转DTO
     */
    DagTemplateDTO req2dto(DagTemplateUpdateReq req);

    /**
     * PageReq转Query
     */
    DagTemplateQuery req2query(DagTemplatePageReq req);

    /**
     * DTO转VO
     */
    DagTemplateVO dto2vo(DagTemplateDTO dto);
} 