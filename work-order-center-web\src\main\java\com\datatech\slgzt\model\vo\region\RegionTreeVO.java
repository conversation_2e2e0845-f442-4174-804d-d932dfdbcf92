package com.datatech.slgzt.model.vo.region;

import lombok.Data;

import java.util.List;

/**
 * 资源池树形结构VO
 */
@Data
public class RegionTreeVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 云区域名称
     */
    private String name;

    /**
     * 云区域编码
     */
    private String code;

    /**
     * 子节点列表
     */
    private List<CloudPlatformVO> children;

    @Data
    public static class CloudPlatformVO {
        /**
         * 主键
         */
        private Long id;

        /**
         * 云区域名称
         */
        private String name;

        /**
         * 云区域编码
         */
        private String code;

        /**
         * 子节点列表
         */
        private List<RegionVO> children;
    }
} 