package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.VipParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.resource.api.vip.dto.CreateVipRcDTO;
import com.cloud.resource.api.vip.dto.DeleteVipRcDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 虚拟IP接口适配
 */
@Component
@Slf4j
public class VipMgAdapter extends BaseNorthInterfaceAdapter {

    /**
     * 创建虚拟IP
     */
    public TaskVO createVip(String taskId, Integer taskSource) {
        log.info("createVip start");
        CreateVipRcDTO createVipRcDTO = generateCreateVipRcDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateVip(),
                null,
                createVipRcDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create vip");
        return tasksVoResult.getEntity();
    }

    /**
     * 删除虚拟IP
     */
    public TaskVO deleteVip(String taskId, Integer taskSource) {
        log.info("deleteVip start");
        DeleteVipRcDTO deleteVipRcDTO = generateDeleteVip(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteVip(),
                null,
                deleteVipRcDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "delete vip");
        return tasksVoResult.getEntity();
    }


    /**
     * 生成北向接口删除虚拟IP的参数
     *
     * @param layoutParam 编排参数
     */
    private DeleteVipRcDTO generateDeleteVip(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam productOrderParam = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.VIP_DELETE.getCode());
        VipParam vipParam = JSONObject.parseObject(productOrderParam.getAttrs(), VipParam.class);
        // 参数设置
        DeleteVipRcDTO deleteVipRcDTO = new DeleteVipRcDTO();
        deleteVipRcDTO.setBillId(vipParam.getBillId());
        deleteVipRcDTO.setGroupId(vipParam.getGroupId());
        deleteVipRcDTO.setInstanceId(vipParam.getInstanceId());
        deleteVipRcDTO.setOptUuid(layoutOrderParam.getOptUuid());
        deleteVipRcDTO.setRegionCode(vipParam.getRegionCode());
        deleteVipRcDTO.setSystemSource(vipParam.getSystemSource());
        return deleteVipRcDTO;
    }

    /**
     * 生成北向接口创建虚拟IP的参数
     *
     * @param layoutParam 编排参数
     */
    private CreateVipRcDTO generateCreateVipRcDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam productOrderParam = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.VIP_CREATE.getCode());
        VipParam vipParam = JSONObject.parseObject(productOrderParam.getAttrs(), VipParam.class);
        // 参数设置
        CreateVipRcDTO createVipRcDTO = new CreateVipRcDTO();
        createVipRcDTO.setBillId(vipParam.getBillId());
        createVipRcDTO.setGroupId(vipParam.getGroupId());
        createVipRcDTO.setOptUuid(layoutOrderParam.getOptUuid());
        createVipRcDTO.setRegionCode(vipParam.getRegionCode());
        createVipRcDTO.setSubnetId(vipParam.getSubnetId());
        createVipRcDTO.setSystemSource(vipParam.getSystemSource());
        createVipRcDTO.setVip(vipParam.getVip());
        createVipRcDTO.setVipName(vipParam.getVipName());
        createVipRcDTO.setVpcId(vipParam.getVpcId());
        createVipRcDTO.setgId(vipParam.getGId());
        return createVipRcDTO;
    }
}
