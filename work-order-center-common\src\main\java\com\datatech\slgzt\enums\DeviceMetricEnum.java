package com.datatech.slgzt.enums;

/**
 * 显卡指标枚举
 * <AUTHOR>
 */
public enum DeviceMetricEnum {

    /**
     * 1获取GPU的SM时钟频率（MHz）
     */
    DCGM_FI_DEV_SM_CLOCK("DCGM_FI_DEV_SM_CLOCK", "GPU的SM时钟频率（MHz）"),

    /**
     *  2获取GPU的内存时钟频率（MHz）
     */
    DCGM_FI_DEV_MEM_CLOCK("DCGM_FI_DEV_MEM_CLOCK","GPU的内存时钟频率（MHz）"),


    /**
     * 3获取GPU的内存温度（单位：C）
     */
    DCGM_FI_DEV_MEMORY_TEMP("DCGM_FI_DEV_MEMORY_TEMP","获取GPU的内存温度（单位：C）"),


    /**
     *   4获取GPU的GPU温度（单位：C）
     */
    DCGM_FI_DEV_GPU_TEMP("DCGM_FI_DEV_GPU_TEMP", "GPU的GPU温度（单位：C）"),


    /**
     * 5获取GPU的功率消耗（W）
     */
    DCGM_FI_DEV_POWER_USAGE("DCGM_FI_DEV_POWER_USAGE", "获取GPU的功率消耗（W）"),


    /**
     * 6获取GPU的启动后的总能耗（mJ）
     */
    DCGM_FI_DEV_TOTAL_ENERGY_CONSUMPTION("DCGM_FI_DEV_TOTAL_ENERGY_CONSUMPTION", "获取GPU的启动后的总能耗（mJ）"),


    /**
     * 7获取GPU的PCIe重试总数
     */
    DCGM_FI_DEV_PCIE_REPLAY_COUNTER("DCGM_FI_DEV_PCIE_REPLAY_COUNTER","GPU的PCIe重试总数"),

    /**
     * 8  获取GPU的GPU利用率（%）
     */
    DCGM_FI_DEV_GPU_UTIL("DCGM_FI_DEV_GPU_UTIL","GPU的GPU利用率（%）"),


    /**
     * 9 GPU的内存利用率
     */
    DCGM_FI_DEV_MEM_COPY_UTIL("DCGM_FI_DEV_MEM_COPY_UTIL", "GPU的内存利用率"),


    //=====================虚拟VGPU 指标===============

    /**
     * vGPU的实时算力利用率
     */
    ORIONX_VGPU_UTILIZATION("ORIONX_VGPU_UTILIZATION","vGPU的实时算力利用率"),
    /**
     * vGPU的实时显存使用大小
     */
    ORIONX_VGPU_MEMORY_USAGE("ORIONX_VGPU_MEMORY_USAGE","vGPU的实时显存使用大小"),
    /**
     *  vGPU分配的显存大小
     */
    ORIONX_VGPU_MEMORY_TOTAL("ORIONX_VGPU_MEMORY_TOTAL","vGPU分配的显存大小"),

    /**
     * 获取vGPU分配的算力大小
     */
    ORIONX_VGPU_RATIO("ORIONX_VGPU_RATIO","vGPU分配的算力大小"),


    //========NPU 芯片===============

    /**
     * NPU 利用率
     */
    NPU_CHIP_INFO_UTILIZATION("npu_chip_info_utilization","NPU 利用率"),
    /**
     * NPU 温度
     */
    NPU_CHIP_INFO_TEMPERATURE("npu_chip_info_temperature","NPU 温度"),


    /**
     * NPU 任务
     */
    MACHINE_NPU_NUMS("npu_chip_info_process_info"," NPU 任务"),

    NPU_CHIP_INFO_TOTAL_MEMORY("npu_chip_info_total_memory", "昇腾AI处理器DDR内存总量"),


    NPU_CHIP_INFO_USED_MEMORY("npu_chip_info_used_memory", "昇腾AI处理器DDR内存使用量"),


    /**
     * NPU 使用内存
     */
    NPU_CHIP_INFO_HBM_USED_MEMORY("npu_chip_info_hbm_used_memory","NPU 使用内存"),

    /**
     * NPU 总内存
     */
    NPU_CHIP_INFO_HBM_TOTAL_MEMORY("npu_chip_info_hbm_total_memory","NPU 总内存"),

    /**
     * NPU 功耗
     */
    NPU_CHIP_INFO_POWER("npu_chip_info_power","NPU 功耗"),

    /**
     * 未知
     */
    UNKNOWN("unknown", "-");

    private final String code;
    private final String desc;

    DeviceMetricEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }



    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
