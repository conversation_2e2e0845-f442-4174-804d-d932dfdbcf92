package com.datatech.slgzt.model.vo.slb;

import com.datatech.slgzt.model.TaskStatusExt;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SLB监听器服务组VO
 */
@Data
@Accessors(chain = true)
public class SlbListenerServerGroupVO {


    //分组名称
    private String groupName;

    //分组ID
    private String id;

    //slb监听器ID
    private String slbListenerId;

    //slb监听器名称
    private String slbListenerName;

    //0：虚拟服务器组（默认）；1：主备服务器组；
    private String groupType;

    //0：正常；1：操作中；
    private String status;

    //主服务器切换备用服务器最少可用台数  vmware平台参数，取值：
    //0：不启动主备服务组切换（默认）；
    //1：主服务器组可用设备少于1台切换备用服务器组，以此类推；
    private String groupPriorityGroup;

    private List<SlbListenerServerInfoVO> serverInfoModelList;

    private TaskStatusExt taskStatusExt;
    //创建时间
    private LocalDateTime createTime;


    @Data
    public static class SlbListenerServerInfoVO{
        //云主机设备id
        private String deviceId;

        private String deviceName;

        private String regionCode;

        private String regionName;

        private String ip;

        private String eip;

        private String vpcId;

        private String vpcName;

        private String deviceStatus;
        //云主机资源详情id
        private Long resourceDetailId;
        private String domainName;
        //端口
        private Integer port;

    }
} 