package com.cloud.marginal.layoutcenter.controller.template;

import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTemplateDefService;
import com.cloud.marginal.model.dto.layout.LayoutTemplateDto;
import com.cloud.marginal.model.entity.layout.LayoutTemplateDef;
import com.cloud.marginal.model.vo.layout.LayoutTemplateVo;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(VersionConstant.V1+"template")
@Api(tags = "编排模板配置")
public class TemplateDefController {

    @Autowired
    private LayoutTemplateDefService layoutTemplateDefService;

    /**
     * 创建编排模板
     * @param layoutTemplateDef
     * @return
     */
    @PostMapping("/create")
    public CecResult createTemplate(@RequestBody  @Validated LayoutTemplateDef layoutTemplateDef){
        layoutTemplateDefService.create(layoutTemplateDef);
        return CecResult.success();
    }

    /**
     * 更新编排模板
     * @param layoutTemplateDef
     * @return
     */
    @PutMapping("/update")
    public CecResult updateTemplate(@RequestBody  LayoutTemplateDef layoutTemplateDef){
        layoutTemplateDefService.update(layoutTemplateDef);
        return CecResult.success();
    }

    /**
     * 编排模板分页查询
     * @param layoutTemplateDto
     * @return
     */
    @GetMapping("/page")
    public CecResult<CecPage<LayoutTemplateVo>> templateListPage(@ModelAttribute LayoutTemplateDto layoutTemplateDto){
        CecPage<LayoutTemplateVo> layoutTemplateDefCecPage = layoutTemplateDefService.getPage(layoutTemplateDto);
        return CecResult.success(layoutTemplateDefCecPage);
    }

    /**
     * 编排模板查询
     * @return
     */
    @GetMapping("/list")
    public CecResult<List<LayoutTemplateVo>> templateList(@RequestParam String name){
        List<LayoutTemplateVo> layoutTemplateVos = layoutTemplateDefService.getList(name);
        return CecResult.success(layoutTemplateVos);
    }

    /**
     * 逻辑删除
     * @param templateId
     * @return
     */
    @DeleteMapping("/remove")
    public CecResult removeTemplate(@RequestParam(name = "templateId") String templateId){
        layoutTemplateDefService.remove(templateId);
        return CecResult.success();
    }

}
