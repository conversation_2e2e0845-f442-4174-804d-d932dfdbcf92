package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.cloud.marginal.enums.edge.TaskStatusEnum;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 订单中心状态通知管理适配
 */
@Component
@Slf4j
public class OrderNoticeMgAdapterVpc {


    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;


    /**
     * vpc资源信息详情topic
     */
    private final static String WORK_ORDER_VPC_TOPIC = "oac_resource_vpc_detail_topic";

    /**
     * 封装EVS云硬盘资源详情
     *
     * @param taskVO
     */
    public void packageVpcResource(TaskVO taskVO) {
        if (taskVO != null && !TaskStatusEnum.EXECUTING.getCode().equals(taskVO.getStatus())) {
            kafkaTemplate.send(WORK_ORDER_VPC_TOPIC, JSONObject.toJSONString(taskVO));
            log.info("kafka发送VPC消息,topic={},resourceDetailVO={}", WORK_ORDER_VPC_TOPIC, JSONObject.toJSONString(taskVO));
        }

    }

}
