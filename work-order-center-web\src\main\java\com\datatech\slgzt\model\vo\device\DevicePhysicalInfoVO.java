package com.datatech.slgzt.model.vo.device;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Desc
 * <AUTHOR>
 * @DATA 2025-06-11
 */
@Data
@Accessors(chain = true)
public class DevicePhysicalInfoVO  implements Serializable {
    private String deviceType;

    private String modelName;

    private Integer deviceTotal;

    private Integer deviceUsaged;

    private Integer deviceFree;


    private Double deviceComputeTotal;

    private Double deviceComputeUsaged;

    private Double deviceComputeFree;
}
