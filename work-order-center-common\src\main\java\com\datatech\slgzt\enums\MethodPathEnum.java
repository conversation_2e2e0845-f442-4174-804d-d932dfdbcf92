package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 方法调用路由
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/27
 */

@Getter
@AllArgsConstructor
public enum MethodPathEnum {

    RESOURCE_OPEN("/addOpenOrder", "开通资源"),
    RESOURCE_RESUBMIT_OPEN("/resubmitOpenOrder", "资源重新提交"),
    TENANT_SUBMIT_RESOURCE_OPEN("/tenantSubmitSave", "租户提交只生成操作单"),
    DRAFTS_ORDER_OPEN("/addDraftsOrder", "添加草稿工单"),

    ;

    private final String path;

    private final String remark;
}

