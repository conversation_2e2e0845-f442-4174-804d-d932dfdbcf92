package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.DagOrderManagerConvert;
import com.datatech.slgzt.dao.DagOrderDAO;
import com.datatech.slgzt.dao.model.DagOrderDO;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.query.DagOrderQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class DagOrderManagerImpl implements DagOrderManager {
    @Resource
    private DagOrderDAO dagOrderDAO;

    @Resource
    private DagOrderManagerConvert convert;

    @Override
    public String add(DagOrderDTO dto) {
        DagOrderDO entity = convert.dto2do(dto);
        return dagOrderDAO.insert(entity);
    }

    @Override
    public void update(DagOrderDTO dto) {
        DagOrderDO entity = convert.dto2do(dto);
        dagOrderDAO.update(entity);
    }

    @Override
    public void delete(String id) {
        dagOrderDAO.delete(id);
    }

    @Override
    public DagOrderDTO getById(String id) {
        DagOrderDO entity = dagOrderDAO.getById(id);
        return convert.do2dto(entity);
    }

    @Override
    public PageResult<DagOrderDTO> page(DagOrderQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<DagOrderDO> list = dagOrderDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }
} 