package com.datatech.slgzt.service.corporate;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;

import java.util.List;

/**
 * 对公资源回收总接口
 */
public interface DgRecoveryResourceService {

    void recoveryResource(DgRecoveryOrderDTO dto, List<DgRecoveryOrderProductDTO> productDTOs);

    /**
     * 注册
     */
    ProductTypeEnum registerOpenService();
}
