package com.datatech.slgzt.model.req.corporate;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CorporateOrderProductPageReq {

    private String workOrderId;

    private String productType;

    private Long parentProductId;

    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;

    @NotNull(message = "每页大小不能为空")
    private Integer pageSize = 10;
} 