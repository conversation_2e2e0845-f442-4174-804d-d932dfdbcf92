package com.cloud.marginal.layoutcenter.controller.taskdef;

import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskDefService;
import com.cloud.marginal.model.dto.layout.LayoutTaskDto;
import com.cloud.marginal.model.entity.layout.LayoutTaskDef;
import com.cloud.marginal.model.vo.layout.LayoutTaskDefVo;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(VersionConstant.V1+"task")
@Api(tags = "编排任务配置")
public class LayoutTaskDefController {

    @Autowired
    private LayoutTaskDefService layoutTaskDefService;

    /**
     * 新增编排任务
     * @param layoutTaskDef
     * @return
     */
    @PostMapping("/create")
    public CecResult create(@RequestBody @Validated LayoutTaskDef layoutTaskDef){
        layoutTaskDefService.create(layoutTaskDef);
        return CecResult.success();
    }

    /**
     * 更新编排任务
     * @param layoutTaskDef
     * @return
     */
    @PutMapping("/update")
    public CecResult update(@RequestBody @Validated LayoutTaskDef layoutTaskDef){
        layoutTaskDefService.update(layoutTaskDef);
        return CecResult.success();
    }

    /**
     * 编排任务分页列表
     * @param layoutTaskDto
     * @return
     */
    @GetMapping("/page")
    public CecResult<CecPage<LayoutTaskDefVo>> getPage(@ModelAttribute LayoutTaskDto layoutTaskDto){
        return CecResult.success(layoutTaskDefService.getPage(layoutTaskDto));
    }

    /**
     * 编排任务列表
     * @return
     */
    @GetMapping("/list")
    public CecResult<List<LayoutTaskDefVo>> taskDefList(@RequestParam(value = "name",required = false) String name,
                                                        @RequestParam(value = "templateId",required = false) String templateId){
        return CecResult.success(layoutTaskDefService.getList(name,templateId));
    }

    /**
     * 逻辑删除编排模板
     * @param id
     * @return
     */
    @DeleteMapping("/remove")
    public CecResult remove(@RequestParam String id){
        layoutTaskDefService.remove(id);
        return CecResult.success();
    }
}
