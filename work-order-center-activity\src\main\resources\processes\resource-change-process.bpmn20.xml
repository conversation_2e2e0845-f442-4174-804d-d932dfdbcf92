<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:yaoqiang="http://bpmn.sourceforge.net" id="m1632821341533" name="" targetNamespace="http://www.activiti.org/test" exporter="Yaoqiang BPMN Editor" exporterVersion="5.3" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://bpmn.sourceforge.net/schemas/BPMN20.xsd">
  <process id="resource-change-process" name="resource-change-process" processType="None" isClosed="false" isExecutable="true">
    <extensionElements>
      <yaoqiang:description />
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724" />
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1" />
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724" />
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1" />
    </extensionElements>
    <startEvent id="_2" name="StartEvent">
      <outgoing>_12</outgoing>
      <outputSet />
    </startEvent>
    <userTask id="_3" name="user_task" implementation="##unspecified" activiti:assignee="${userId}">
      <incoming>_12</incoming>
      <incoming>_19</incoming>
      <incoming>_24</incoming>
      <incoming>_32</incoming>
      <incoming>_35</incoming>
      <incoming>_60</incoming>
      <outgoing>_13</outgoing>
      <outgoing>_49</outgoing>
    </userTask>
    <userTask id="_4" name="schema_administrator" implementation="##unspecified" activiti:assignee="${schema}">
      <incoming>_13</incoming>
      <incoming>_34</incoming>
      <incoming>_43</incoming>
      <incoming>_47</incoming>
      <incoming>_64</incoming>
      <outgoing>_14</outgoing>
    </userTask>
    <userTask id="_5" name="business2_depart_leader" implementation="##unspecified" activiti:assignee="${business2}">
      <incoming>_39</incoming>
      <incoming>_41</incoming>
      <incoming>_45</incoming>
      <incoming>_62</incoming>
      <outgoing>_18</outgoing>
    </userTask>
    <userTask id="_17" name="business_depart_leader" implementation="##unspecified" activiti:assignee="${business}">
      <extensionElements />
      <incoming>_20</incoming>
      <incoming>_61</incoming>
      <incoming>_44</incoming>
      <outgoing>_30</outgoing>
    </userTask>
    <userTask id="_6" name="cloud_leader" implementation="##unspecified" activiti:assignee="${cloud}">
      <extensionElements>
        <activiti:taskListener expression="${execution.setVariable(&#39;isSkipBusinessNode&#39;, 0)}" event="create" />
      </extensionElements>
      <incoming>_65</incoming>
      <incoming>_66</incoming>
      <incoming>_31</incoming>
      <outgoing>_22</outgoing>
    </userTask>
    <userTask id="_55" name="cloud_leader_2" implementation="##unspecified" activiti:assignee="${cloud_2}">
      <incoming>_23</incoming>
      <outgoing>_58</outgoing>
    </userTask>
    <sequenceFlow id="_12" sourceRef="_2" targetRef="_3" />
    <sequenceFlow id="_14" sourceRef="_4" targetRef="_36" />
    <sequenceFlow id="_39" name="isSkipBusinessNode=1" sourceRef="_36" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">${message==1 &amp;&amp; isSkipBusinessNode==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_18" sourceRef="_5" targetRef="_10" />
    <sequenceFlow id="_19" name="business2 reject" sourceRef="_10" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_20" name="business2 adopt" sourceRef="_10" targetRef="_17">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_31" name="business adopt" sourceRef="_29" targetRef="_6">
      <extensionElements />
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_32" name="business reject" sourceRef="_29" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_35" name="reject" sourceRef="_36" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_22" sourceRef="_6" targetRef="_11" />
    <sequenceFlow id="_24" name="cloud reject" sourceRef="_11" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_60" name="cloud 2 reject" sourceRef="_56" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_61" name="cloud 2 reject" sourceRef="_56" targetRef="_17">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='business_depart_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_62" name="cloud 2 reject" sourceRef="_56" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='business2_depart_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_64" name="cloud 2 reject" sourceRef="_56" targetRef="_4">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='schema_administrator'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_65" name="cloud 2 reject" sourceRef="_56" targetRef="_6">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='cloud_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_13" sourceRef="_3" targetRef="_4" />
    <exclusiveGateway id="_10" name="business de leader examine" gatewayDirection="Diverging">
      <incoming>_18</incoming>
      <outgoing>_19</outgoing>
      <outgoing>_20</outgoing>
      <outgoing>_52</outgoing>
    </exclusiveGateway>
    <exclusiveGateway id="_11" name="cloud leader examine" gatewayDirection="Diverging">
      <incoming>_22</incoming>
      <outgoing>_23</outgoing>
      <outgoing>_24</outgoing>
      <outgoing>_44</outgoing>
      <outgoing>_45</outgoing>
      <outgoing>_47</outgoing>
      <outgoing>_54</outgoing>
    </exclusiveGateway>
    <exclusiveGateway id="_56" name="cloud leader 2 examine" gatewayDirection="Diverging">
      <incoming>_58</incoming>
      <outgoing>_76</outgoing>
      <outgoing>_59</outgoing>
      <outgoing>_60</outgoing>
      <outgoing>_61</outgoing>
      <outgoing>_62</outgoing>
      <outgoing>_64</outgoing>
      <outgoing>_65</outgoing>
      <outgoing>_70</outgoing>
      <outgoing>_73</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="_23" name="cloud adopt" sourceRef="_11" targetRef="_55">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_76" name="cloud adopt resource" sourceRef="_56" targetRef="Activity_02xpr6r">
      <conditionExpression xsi:type="tFormalExpression">${message==1 &amp;&amp; skipAlarmAndShutdown==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_49" name="userTaskClose" sourceRef="_3" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_51" name="tenantTaskClose" sourceRef="_36" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_58" sourceRef="_55" targetRef="_56" />
    <sequenceFlow id="_52" name="business2Close" sourceRef="_10" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_53" name="businessLeaderClose" sourceRef="_29" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_54" name="cloudLeaderClose" sourceRef="_11" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_59" name="cloudLeader2Close" sourceRef="_56" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="_36" name="tenant examine" gatewayDirection="Diverging">
      <incoming>_14</incoming>
      <outgoing>_39</outgoing>
      <outgoing>_35</outgoing>
      <outgoing>_51</outgoing>
      <outgoing>_66</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="_66" name="isSkipBusinessNode=0" sourceRef="_36" targetRef="_6">
      <conditionExpression xsi:type="tFormalExpression">${message==1 &amp;&amp; isSkipBusinessNode==0}</conditionExpression>
    </sequenceFlow>
    <endEvent id="_7" name="EndEvent">
      <incoming>_49</incoming>
      <incoming>_51</incoming>
      <incoming>_52</incoming>
      <incoming>_54</incoming>
      <incoming>_59</incoming>
      <incoming>_80</incoming>
      <incoming>_53</incoming>
      <inputSet />
    </endEvent>
    <sequenceFlow id="_30" sourceRef="_17" targetRef="_29" />
    <exclusiveGateway id="_29" name="business de leader examine" gatewayDirection="Diverging">
      <incoming>_30</incoming>
      <outgoing>_31</outgoing>
      <outgoing>_32</outgoing>
      <outgoing>_53</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="_34" name="business2 reject" sourceRef="_10" targetRef="_4">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='schema_administrator'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_41" name="business reject" sourceRef="_29" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='business2_depart_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_43" name="business reject" sourceRef="_29" targetRef="_4">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='schema_administrator'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_44" name="cloud reject" sourceRef="_11" targetRef="_17">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='business_depart_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_45" name="cloud reject" sourceRef="_11" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='business2_depart_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_47" name="cloud reject" sourceRef="_11" targetRef="_4">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='schema_administrator'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_72" sourceRef="Activity_0xuuoe4" targetRef="Activity_175vj2p" />
    <sequenceFlow id="_70" name="cloud adopt 2" sourceRef="_56" targetRef="Activity_0xuuoe4">
      <conditionExpression xsi:type="tFormalExpression">${message==1 &amp;&amp; skipAlarmAndShutdown==0}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_80" sourceRef="Activity_0t3l2n1" targetRef="_7" />
    <sequenceFlow id="_78" sourceRef="Activity_02xpr6r" targetRef="Activity_0t3l2n1" />
    <sequenceFlow id="_75" sourceRef="Activity_175vj2p" targetRef="Activity_02xpr6r" />
    <sequenceFlow id="_73" sourceRef="_56" targetRef="Activity_175vj2p">
      <conditionExpression xsi:type="tFormalExpression">${message==1 &amp;&amp; skipAlarmAndShutdown==1}</conditionExpression>
    </sequenceFlow>
    <userTask id="Activity_0xuuoe4" name="alarm_suppression" implementation="##unspecified" activiti:assignee="${professional_group}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
        <activiti:assigneeType>static</activiti:assigneeType>
      </extensionElements>
      <incoming>_70</incoming>
      <outgoing>_72</outgoing>
    </userTask>
    <userTask id="Activity_175vj2p" name="shutdown" implementation="##unspecified" activiti:assignee="${creator_and_shutdown}">
      <extensionElements>
        <activiti:assigneeType>static</activiti:assigneeType>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
        <activiti:assigneeType>static</activiti:assigneeType>
      </extensionElements>
      <incoming>_72</incoming>
      <incoming>_73</incoming>
      <outgoing>_75</outgoing>
    </userTask>
    <userTask id="Activity_02xpr6r" name="resource_change" implementation="##unspecified" activiti:assignee="${resource_change}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
        <activiti:assigneeType>static</activiti:assigneeType>
        <activiti:taskListener delegateExpression="${resourceChangeCreateListener}" event="create" />
      </extensionElements>
      <incoming>_75</incoming>
      <incoming>_76</incoming>
      <outgoing>_78</outgoing>
    </userTask>
    <userTask id="Activity_0t3l2n1" name="tenant_task" implementation="##unspecified" activiti:assignee="${userId}">
      <extensionElements>
        <activiti:assigneeType>static</activiti:assigneeType>
        <activiti:assigneeType>static</activiti:assigneeType>
      </extensionElements>
      <incoming>_78</incoming>
      <outgoing>_80</outgoing>
    </userTask>
  </process>
  <bpmndi:BPMNDiagram id="Yaoqiang_Diagram-resource-create-process-1" name="New Diagram" resolution="96">
    <bpmndi:BPMNPlane bpmnElement="resource-change-process">
      <bpmndi:BPMNShape id="Shape-_5" bpmnElement="_5">
        <omgdc:Bounds x="147" y="262" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-71" y="-200" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_17" bpmnElement="_17">
        <omgdc:Bounds x="137" y="402" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-259.5616024187452" y="-141.20672713529848" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_6" bpmnElement="_6">
        <omgdc:Bounds x="317" y="562" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="97" y="-73" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_55" bpmnElement="_55">
        <omgdc:Bounds x="317" y="722" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="97" y="87" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_10" bpmnElement="_10" isMarkerVisible="true">
        <omgdc:Bounds x="164" y="344" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="182" y="326" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_11" bpmnElement="_11" isMarkerVisible="true">
        <omgdc:Bounds x="344" y="654" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-21" y="96" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_56" bpmnElement="_56" isMarkerVisible="true">
        <omgdc:Bounds x="344" y="804" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="88" y="78" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_36" bpmnElement="_36" isMarkerVisible="true">
        <omgdc:Bounds x="245" y="204" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="229" y="233" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_29" bpmnElement="_29" isMarkerVisible="true">
        <omgdc:Bounds x="164" y="494" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="142" y="518" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_4" bpmnElement="_4">
        <omgdc:Bounds x="216" y="122" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-8" y="-153" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_3" bpmnElement="_3">
        <omgdc:Bounds x="216" y="42" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="1" y="-93" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_2" bpmnElement="_2">
        <omgdc:Bounds x="244" y="-16" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="233" y="-40" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_122ttty" bpmnElement="Activity_0xuuoe4">
        <omgdc:Bounds x="317" y="862" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-51.46328920030635" y="142.59530213324888" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0d8gbm5" bpmnElement="Activity_175vj2p">
        <omgdc:Bounds x="317" y="942" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-51.46328920030635" y="222.59530213324888" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ynpshg" bpmnElement="Activity_02xpr6r">
        <omgdc:Bounds x="317" y="1022" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-207.28893289733458" y="302.7969422177774" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_19qrfa6" bpmnElement="Activity_0t3l2n1">
        <omgdc:Bounds x="317" y="1102" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-77.83333333333337" y="733.8333333333334" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_7" bpmnElement="_7">
        <omgdc:Bounds x="534" y="1114" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="525" y="1143" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge__12" bpmnElement="_12" sourceElement="Shape-_2" targetElement="Shape-_3">
        <omgdi:waypoint x="260" y="16" />
        <omgdi:waypoint x="260" y="29" />
        <omgdi:waypoint x="259" y="29" />
        <omgdi:waypoint x="259" y="42" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="253" y="106.34" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__14" bpmnElement="_14" sourceElement="Shape-_4" targetElement="Shape-_36">
        <omgdi:waypoint x="252" y="177" />
        <omgdi:waypoint x="252" y="191" />
        <omgdi:waypoint x="261" y="191" />
        <omgdi:waypoint x="261" y="204" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="262" y="342.34" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__39" bpmnElement="_39" sourceElement="_36" targetElement="_5">
        <omgdi:waypoint x="249" y="224" />
        <omgdi:waypoint x="190" y="250" />
        <omgdi:waypoint x="190" y="262" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="143" y="243" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__18" bpmnElement="_18" sourceElement="Shape-_5" targetElement="Shape-_10">
        <omgdi:waypoint x="180" y="317" />
        <omgdi:waypoint x="180" y="344" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="255" y="523.84" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__19" bpmnElement="_19" sourceElement="_10" targetElement="_3">
        <omgdi:waypoint x="164" y="360" />
        <omgdi:waypoint x="110" y="360" />
        <omgdi:waypoint x="110" y="70" />
        <omgdi:waypoint x="216" y="70" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="70" y="355" width="81" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__20" bpmnElement="_20" sourceElement="_10" targetElement="_17">
        <omgdi:waypoint x="180" y="376" />
        <omgdi:waypoint x="180" y="402" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="140" y="385" width="82" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__31" bpmnElement="_31" sourceElement="Shape-_29" targetElement="Shape-_6">
        <omgdi:waypoint x="194" y="512" />
        <omgdi:waypoint x="290" y="530" />
        <omgdi:waypoint x="338" y="562" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="278" y="539" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__32" bpmnElement="_32" sourceElement="_29" targetElement="_3">
        <omgdi:waypoint x="164" y="510" />
        <omgdi:waypoint x="60" y="510" />
        <omgdi:waypoint x="60" y="69" />
        <omgdi:waypoint x="216" y="69" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="34" y="503" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__35" bpmnElement="_35" sourceElement="_36" targetElement="_3">
        <omgdi:waypoint x="245" y="220" />
        <omgdi:waypoint x="160" y="220" />
        <omgdi:waypoint x="160" y="70" />
        <omgdi:waypoint x="216" y="70" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="184" y="278" width="28" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__22" bpmnElement="_22" sourceElement="Shape-_6" targetElement="Shape-_11">
        <omgdi:waypoint x="354" y="617" />
        <omgdi:waypoint x="354" y="636" />
        <omgdi:waypoint x="360" y="636" />
        <omgdi:waypoint x="360" y="654" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="259.5" y="700.59" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__24" bpmnElement="_24" sourceElement="_11" targetElement="_3">
        <omgdi:waypoint x="344" y="670" />
        <omgdi:waypoint x="0" y="670" />
        <omgdi:waypoint x="0" y="70" />
        <omgdi:waypoint x="216" y="70" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-4" y="149" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__60" bpmnElement="_60" sourceElement="Shape-_56" targetElement="Shape-_3">
        <omgdi:waypoint x="345" y="819" />
        <omgdi:waypoint x="-70" y="819" />
        <omgdi:waypoint x="-70" y="70" />
        <omgdi:waypoint x="216" y="70" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-105" y="398" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__61" bpmnElement="_61" sourceElement="Shape-_56" targetElement="Shape-_17">
        <omgdi:waypoint x="345" y="819" />
        <omgdi:waypoint x="-40" y="819" />
        <omgdi:waypoint x="-40" y="430" />
        <omgdi:waypoint x="137" y="430" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-75" y="597" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__62" bpmnElement="_62" sourceElement="Shape-_56" targetElement="Shape-_5">
        <omgdi:waypoint x="345" y="819" />
        <omgdi:waypoint x="-50" y="819" />
        <omgdi:waypoint x="-50" y="290" />
        <omgdi:waypoint x="147" y="290" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-85" y="517" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__64" bpmnElement="_64" sourceElement="Shape-_56" targetElement="Shape-_4">
        <omgdi:waypoint x="345" y="819" />
        <omgdi:waypoint x="-60" y="819" />
        <omgdi:waypoint x="-60" y="150" />
        <omgdi:waypoint x="216" y="150" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-95" y="441" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__65" bpmnElement="_65" sourceElement="Shape-_56" targetElement="Shape-_6">
        <omgdi:waypoint x="345" y="819" />
        <omgdi:waypoint x="-30" y="819" />
        <omgdi:waypoint x="-30" y="590" />
        <omgdi:waypoint x="317" y="590" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="-65" y="685" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__13" bpmnElement="_13" sourceElement="Shape-_3" targetElement="Shape-_4">
        <omgdi:waypoint x="259" y="97" />
        <omgdi:waypoint x="259" y="122" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="259" y="222.84" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__23" bpmnElement="_23" sourceElement="Shape-_11" targetElement="Shape-_55">
        <omgdi:waypoint x="360" y="686" />
        <omgdi:waypoint x="360" y="722" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="333" y="698" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__48" bpmnElement="_76" sourceElement="Shape-_56" targetElement="BPMNShape_1ynpshg">
        <omgdi:waypoint x="375" y="819" />
        <omgdi:waypoint x="430" y="820" />
        <omgdi:waypoint x="430" y="1050" />
        <omgdi:waypoint x="409" y="1050" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="394" y="942" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__49" bpmnElement="_49" sourceElement="Shape-_3" targetElement="Shape-_7">
        <omgdi:waypoint x="301" y="70" />
        <omgdi:waypoint x="550" y="70" />
        <omgdi:waypoint x="550" y="1114" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="514" y="639" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__51" bpmnElement="_51" sourceElement="Shape-_36" targetElement="Shape-_7">
        <omgdi:waypoint x="277" y="220" />
        <omgdi:waypoint x="550" y="220" />
        <omgdi:waypoint x="550" y="1114" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="510" y="704" width="81" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__58" bpmnElement="_58" sourceElement="Shape-_55" targetElement="Shape-_56">
        <omgdi:waypoint x="360" y="777" />
        <omgdi:waypoint x="360" y="804" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="292.42" y="737.08" width="62" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__52" bpmnElement="_52" sourceElement="Shape-_10" targetElement="Shape-_7">
        <omgdi:waypoint x="196" y="360" />
        <omgdi:waypoint x="550" y="360" />
        <omgdi:waypoint x="550" y="1114" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="512" y="767" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__53" bpmnElement="_53" sourceElement="Shape-_29" targetElement="Shape-_7">
        <omgdi:waypoint x="196" y="510" />
        <omgdi:waypoint x="550" y="510" />
        <omgdi:waypoint x="550" y="1114" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="349" y="502" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__54" bpmnElement="_54" sourceElement="_11" targetElement="_7">
        <omgdi:waypoint x="375" y="669" />
        <omgdi:waypoint x="550" y="669" />
        <omgdi:waypoint x="550" y="1114" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="427" y="661" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__59" bpmnElement="_59" sourceElement="Shape-_56" targetElement="Shape-_7">
        <omgdi:waypoint x="376" y="820" />
        <omgdi:waypoint x="550" y="820" />
        <omgdi:waypoint x="550" y="1114" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="505" y="809" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__66" bpmnElement="_66" sourceElement="Shape-_36" targetElement="Shape-_6">
        <omgdi:waypoint x="273" y="224" />
        <omgdi:waypoint x="340" y="250" />
        <omgdi:waypoint x="357" y="562" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="301" y="396" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__30" bpmnElement="_30" sourceElement="Shape-_17" targetElement="Shape-_29">
        <omgdi:waypoint x="180" y="457" />
        <omgdi:waypoint x="180" y="494" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="431.55" y="610.06" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__34" bpmnElement="_34" sourceElement="Shape-_10" targetElement="Shape-_4">
        <omgdi:waypoint x="164" y="360" />
        <omgdi:waypoint x="120" y="360" />
        <omgdi:waypoint x="120" y="150" />
        <omgdi:waypoint x="216" y="150" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="99" y="169" width="81" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__41" bpmnElement="_41" sourceElement="_29" targetElement="_5">
        <omgdi:waypoint x="164" y="510" />
        <omgdi:waypoint x="80" y="510" />
        <omgdi:waypoint x="80" y="290" />
        <omgdi:waypoint x="147" y="290" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="62" y="333" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__43" bpmnElement="_43" sourceElement="Shape-_29" targetElement="Shape-_4">
        <omgdi:waypoint x="164" y="510" />
        <omgdi:waypoint x="70" y="510" />
        <omgdi:waypoint x="70" y="150" />
        <omgdi:waypoint x="216" y="150" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="51" y="162" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__44" bpmnElement="_44" sourceElement="Shape-_11" targetElement="Shape-_17">
        <omgdi:waypoint x="344" y="670" />
        <omgdi:waypoint x="30" y="670" />
        <omgdi:waypoint x="30" y="430" />
        <omgdi:waypoint x="137" y="430" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="141" y="653" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__45" bpmnElement="_45" sourceElement="_11" targetElement="_5">
        <omgdi:waypoint x="344" y="670" />
        <omgdi:waypoint x="19" y="670" />
        <omgdi:waypoint x="20" y="290" />
        <omgdi:waypoint x="147" y="290" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="46" y="653" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__47" bpmnElement="_47" sourceElement="_11" targetElement="_4">
        <omgdi:waypoint x="344" y="670" />
        <omgdi:waypoint x="10" y="670" />
        <omgdi:waypoint x="10" y="150" />
        <omgdi:waypoint x="216" y="150" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="31" y="395" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__25" bpmnElement="_72">
        <omgdi:waypoint x="360" y="917" />
        <omgdi:waypoint x="360" y="942" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="485.6" y="736.99" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__57" bpmnElement="_70">
        <omgdi:waypoint x="360" y="836" />
        <omgdi:waypoint x="360" y="862" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="327" y="845" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__40" bpmnElement="_80">
        <omgdi:waypoint x="402" y="1130" />
        <omgdi:waypoint x="534" y="1130" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="638.04" y="736.76" width="6" height="19.84" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yfhmr9_di" bpmnElement="_78">
        <omgdi:waypoint x="360" y="1077" />
        <omgdi:waypoint x="360" y="1102" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1d71gwu_di" bpmnElement="_75">
        <omgdi:waypoint x="360" y="997" />
        <omgdi:waypoint x="360" y="1022" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qll01u_di" bpmnElement="_73">
        <omgdi:waypoint x="376" y="820" />
        <omgdi:waypoint x="420" y="820" />
        <omgdi:waypoint x="420" y="970" />
        <omgdi:waypoint x="402" y="970" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
