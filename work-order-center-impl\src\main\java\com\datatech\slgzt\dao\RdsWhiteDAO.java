package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.RdsWhiteMapper;
import com.datatech.slgzt.dao.model.RdsWhiteDO;
import com.datatech.slgzt.model.query.RdsWhiteQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单DAO
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Repository
public class RdsWhiteDAO {

    @Resource
    private RdsWhiteMapper baseMapper;

    /**
     * 查询数据库白名单列表
     */
    public List<RdsWhiteDO> list(RdsWhiteQuery query) {
        return baseMapper.selectList(
            Wrappers.<RdsWhiteDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getId()), RdsWhiteDO::getId, query.getId())
                .eq(ObjNullUtils.isNotNull(query.getRdsId()), RdsWhiteDO::getRdsId, query.getRdsId())
                .like(ObjNullUtils.isNotNull(query.getWhiteName()), RdsWhiteDO::getWhiteName, query.getWhiteName())
                .like(ObjNullUtils.isNotNull(query.getIps()), RdsWhiteDO::getIps, query.getIps())
                .eq(ObjNullUtils.isNotNull(query.getEnabled()), RdsWhiteDO::getEnabled, query.getEnabled())
                .in(ObjNullUtils.isNotNull(query.getIdList()), RdsWhiteDO::getId, query.getIdList())
                .between(
                    ObjNullUtils.isNotNull(query.getStartTime()) && ObjNullUtils.isNotNull(query.getEndTime()),
                    RdsWhiteDO::getCreateTime,
                    query.getStartTime(),
                    query.getEndTime()
                )
                .orderByDesc(RdsWhiteDO::getCreateTime)
        );
    }

    /**
     * 新增数据库白名单
     */
    public void insert(RdsWhiteDO rdsWhiteDO) {
        baseMapper.insert(rdsWhiteDO);
    }

    /**
     * 更新数据库白名单
     */
    public void update(RdsWhiteDO rdsWhiteDO) {
        baseMapper.updateById(rdsWhiteDO);
    }

    /**
     * 删除数据库白名单
     */
    public void delete(Long id) {
        baseMapper.deleteById(id);
    }
} 