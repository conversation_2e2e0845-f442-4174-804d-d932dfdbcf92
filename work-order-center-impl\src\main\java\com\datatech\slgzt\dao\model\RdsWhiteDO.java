package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单DO
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Data
@TableName("WOC_RDS_WHITE")
public class RdsWhiteDO {

    /**
     * 主键ID
     */
    @TableField("ID")
    private Long id;

    /**
     * 数据库主键ID
     */
    @TableField("RDS_ID")
    private String rdsId;

    /**
     * 白名单资源id
     */
    @TableField("INSTANCE_ID")
    private String instanceId;

    /**
     * 白名单名称
     */
    @TableField("WHITE_NAME")
    private String whiteName;

    /**
     * 白名单IP
     */
    @TableField("IPS")
    private String ips;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;

    /**
     * 1有效 0删除
     */
    @TableField("ENABLED")
    @TableLogic(value = "1", delval = "0")
    private Integer enabled;
} 