package com.cloud.marginal.layoutcenter.scheduler;

import com.cloud.marginal.layoutcenter.service.TaskCompensateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class RetryTaskJob extends IJobHandler {

    @Resource
    private TaskCompensateService taskCompensateService;

    /**
     * 主任务补偿处理 5分钟执行一次 处理UNPROCESSED任务
     * 0 0/5 * * * ?
     */
    @XxlJob(value = "mainTaskCompensateHandle")
    public ReturnT<String> mainTaskCompensate(String param){
        log.info("start main task retry.");
        return taskCompensateService.mainTaskCompensate();
    }

    /**
     * 处理中任务补偿处理 5分钟执行一次 处理PROCESSING任务
     * 0 0/5 * * * ?
     */
    @XxlJob(value = "processingTaskCompensateHandle")
    public ReturnT<String> processingTaskCompensate(String param){
        return taskCompensateService.processingTaskCompensate();
    }

    /**
     * 执行中任务监控处理,30S执行一次 处理EXECUTING任务，不想等5分钟的话
     * 0/30 * * * * ?
     */
    @XxlJob(value = "executingTaskHandle")
    public ReturnT<String> executingTaskHandle(String param){
        return taskCompensateService.executingTaskHandle();
    }

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        return ReturnT.SUCCESS;
    }
}
