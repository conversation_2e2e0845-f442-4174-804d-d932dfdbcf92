package com.datatech.slgzt.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @desc 证书类型
 */
public enum CertificateTypeEnum {

    SERVER_CERTIFICATE("server_certificate", "服务器证书"),
    CLIENT_CERTIFICATE("client_certificate", "Ca证书");

    private final String code;
    private final String desc;


    CertificateTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }



    public static String getDescByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .map(CertificateTypeEnum::getDesc)
                .findFirst()
                .orElse("未知证书");
    }
}
