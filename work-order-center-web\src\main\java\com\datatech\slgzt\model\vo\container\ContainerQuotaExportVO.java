package com.datatech.slgzt.model.vo.container;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 容器配额导出VO
 * <AUTHOR>
 * @description 容器配额导出视图对象
 * @date 2025年05月27日
 */
@Data
public class ContainerQuotaExportVO {

    /**
     * 配额名称
     */
    @ExcelExportHeader(value = "配额名称")
    private String cqName;

    /**
     * 容器配额-核心数
     */
    @ExcelExportHeader(value = "vCPU(核)")
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G
     */
    @ExcelExportHeader(value = "内存(GB)")
    private Integer ram;

    /**
     * GPU算力
     */
    @ExcelExportHeader(value = "GPU算力")
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB
     */
    @ExcelExportHeader(value = "GPU显存(GB)")
    private Integer gpuVirtualMemory;

    /**
     * GPU卡数量(个) = 物理GPU卡 + 虚拟GPU卡
     */
    @ExcelExportHeader(value = "GPU卡数量(个)")
    private Integer gpuCardCount;

    /**
     * 云类型名称
     */
    @ExcelExportHeader(value = "云类型")
    private String catalogueDomainName;

    /**
     * 云平台名称
     */
    @ExcelExportHeader(value = "云平台")
    private String domainName;

    /**
     * 资源池名称
     */
    @ExcelExportHeader(value = "资源池")
    private String regionName;

    /**
     * 申请用户名称
     */
    @ExcelExportHeader(value = "申请人")
    private String applyUserName;

    /**
     * 申请时长
     */
    @ExcelExportHeader(value = "申请时长")
    private String applyTime;

    /**
     * 4A账号
     */
    @ExcelExportHeader(value = "4A账号")
    private String a4Account;

    /**
     * 4A账号绑定的手机
     */
    @ExcelExportHeader(value = "4A账号绑定的手机号")
    private String a4Phone;

    /**
     * 业务系统名称
     */
    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    /**
     * 创建时间
     */
    @ExcelExportHeader(value = "创建时间")
    private LocalDateTime createTime;
}
