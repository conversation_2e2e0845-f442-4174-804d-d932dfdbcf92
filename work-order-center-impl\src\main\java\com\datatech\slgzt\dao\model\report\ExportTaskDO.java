package com.datatech.slgzt.dao.model.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 导出任务表实体类
 */
@Data
@TableName("WOC_EXPORT_TASK")
public class ExportTaskDO {
    
    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 报表名称
     */
    @TableField("REPORT_NAME")
    private String reportName;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 业务类型
     */
    @TableField("BUSINESS_TYPE")
    private String businessType;

    /**
     * 统计类型（HOUR/DAY/MONTH）
     */
    @TableField("STAT_TYPE")
    private String statType;

    /**
     * 开始时间
     */
    @TableField("START_TIME")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("END_TIME")
    private LocalDateTime endTime;

    /**
     * 导出字段（JSON格式）
     */
    @TableField("EXPORT_FIELDS")
    private String exportFields;

    /**
     * 查询条件（JSON格式）
     */
    @TableField("QUERY_CONDITION")
    private String queryCondition;

    /**
     * 文件路径
     */
    @TableField("FILE_PATH")
    private String filePath;

    /**
     * 状态（0-生成中，1-完成，2-失败）
     */
    @TableField("STATUS")
    private Integer status;


    @TableField("FILE_NAME")
    private String fileName;
} 