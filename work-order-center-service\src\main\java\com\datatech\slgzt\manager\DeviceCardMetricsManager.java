package com.datatech.slgzt.manager;



import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.query.DeviceMetricQuery;

import java.util.List;

/**
 * 设备监控指标
 */
public interface DeviceCardMetricsManager {

    /**
     * 新增显卡指标
     */
    void create(DeviceCardMetricsDTO dto);


    void saveBatch(List<DeviceCardMetricsDTO> deviceCardMetricsDTOS);

    void delByGpuTime(String gpuTime);

    /**
     * 更新显卡指标
     */
    void update(DeviceCardMetricsDTO dto);

    /**
     * 删除显卡指标
     */
    void delete(Long id);

    /**
     * 批量删除显卡指标
     */
    void deleteBatch(List<Long> ids);

    /**
     * 获取gpu 指标信息
     * @param gpuTime
     * @return
     */
    List<DeviceCardMetricsDTO> selectGpuMetricsDTO(String gpuTime);



    /**
     * 获取gpu 指标信息
     * @param DeviceMetricQuery
     * @return
     */
    List<DeviceCardMetricsDTO> selectGpuMetricsDTO(DeviceMetricQuery deviceMetricQuery);

    List<DeviceCardMetricsDTO> queryAvgDeviceMetrics(DeviceMetricQuery deviceMetricQuery);

    /**
     * 按时间聚合查询GPU指标数据（数据库层面聚合，提升性能）
     * @param deviceIds 设备ID列表
     * @param deviceMetricQuery 查询条件（包含时间范围和聚合类型）
     * @return 聚合后的GPU指标数据
     */
    List<DeviceCardMetricsDTO> queryGpuMetricsAggregated(List<String> deviceIds, DeviceMetricQuery deviceMetricQuery);
}
