package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.RdsParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.resource.api.rds.dto.CreateMysqlRcDto;
import com.cloud.resource.api.rds.dto.DeleteRdsRcDto;
import com.cloud.resource.api.rds.dto.ModifyRdsFlavorRcDto;
import com.cloud.resource.api.rds.dto.ModifyRdsStorageRcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * rds适配管理
 */
@Component
@Slf4j
public class RdsMgAdapter extends BaseNorthInterfaceAdapter {
    /**
     * 创建rds
     */
    public TaskVO createRds(String taskId, Integer taskSource){
        log.info("createRds start");
        CreateMysqlRcDto rdsDTO = generateCreateRdsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateRdsMysql(),
                null,
                rdsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("createRds url is:{}",northInterfaceAddress.getCreateRdsMysql());
        log.info("createRds params is:{}",JSONObject.toJSON(rdsDTO));

        checkResultThrowExceptionIfFail(tasksVOResult,"create rds");
        return tasksVOResult.getEntity();
    }

    /**
     * rds规格变更
     */
    public TaskVO modifyRdsFlavor(String taskId, Integer taskSource){
        log.info("modifyRdsFlavor start");
        ModifyRdsFlavorRcDto rdsDTO = generateModifyRdsFlavorDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getModifyRdsFlavorMysql(),
                null,
                rdsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("modify rds flavor url is:{}",northInterfaceAddress.getModifyRdsFlavorMysql());
        log.info("modify rds flavor params is:{}",JSONObject.toJSON(rdsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"flavor modify rds");
        return tasksVOResult.getEntity();
    }

    /**
     * rds存储变更
     */
    public TaskVO modifyRdsStorage(String taskId, Integer taskSource){
        log.info("modifyRdsStorage start");
        ModifyRdsStorageRcDto rdsDTO = generateModifyRdsStorageDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getModifyRdsStorageMysql(),
                null,
                rdsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("modify rds storage url is:{}",northInterfaceAddress.getModifyRdsStorageMysql());
        log.info("modify rds storage params is:{}",JSONObject.toJSON(rdsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"modify rds storage");
        return tasksVOResult.getEntity();
    }

    /**
     * rds删除
     */
    public TaskVO deleteRds(String taskId, Integer taskSource){
        log.info("delete rds start");
        DeleteRdsRcDto rdsDTO = generateDeleteRdsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteRdsMysql(),
                null,
                rdsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("delete rds url is:{}",northInterfaceAddress.getDeleteRdsMysql());
        log.info("delete rds params is:{}",JSONObject.toJSON(rdsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"delete rds");
        return tasksVOResult.getEntity();
    }




    private CreateMysqlRcDto generateCreateRdsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam rdsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.RDS_CREATE.getCode());
        RdsParam rdsParam = JSONObject.parseObject(rdsOrder.getAttrs(), RdsParam.class);
        CreateMysqlRcDto rdsDTO = new CreateMysqlRcDto();
        BeanUtils.copyProperties(rdsParam,rdsDTO);
        rdsDTO.setOrderId(layoutOrderParam.getSubOrderId());
        rdsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        rdsDTO.setBillId(layoutOrderParam.getAccount());
        rdsDTO.setGroupId(layoutOrderParam.getCustomId());
        rdsDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        if(StrUtil.isNotEmpty(rdsParam.getPort())){
            rdsDTO.setPort(Integer.parseInt(rdsParam.getPort()));

        }
        return rdsDTO;
    }

    private ModifyRdsFlavorRcDto generateModifyRdsFlavorDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam rdsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.RDS_MODIFY_FLAVOR.getCode());
        RdsParam rdsParam = JSONObject.parseObject(rdsOrder.getAttrs(), RdsParam.class);
        ModifyRdsFlavorRcDto rdsDTO = new ModifyRdsFlavorRcDto();
        rdsDTO.setBillId(layoutOrderParam.getAccount());
        rdsDTO.setGroupId(layoutOrderParam.getCustomId());
        rdsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        rdsDTO.setInstanceId(rdsParam.getGId());
        rdsDTO.setFlavorCode(rdsParam.getFlavorCode());
        return rdsDTO;
    }

    private ModifyRdsStorageRcDto generateModifyRdsStorageDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam rdsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.RDS_MODIFY_STORAGE.getCode());
        RdsParam rdsParam = JSONObject.parseObject(rdsOrder.getAttrs(), RdsParam.class);
        ModifyRdsStorageRcDto rdsDTO = new ModifyRdsStorageRcDto();
        rdsDTO.setBillId(layoutOrderParam.getAccount());
        rdsDTO.setGroupId(layoutOrderParam.getCustomId());
        rdsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        rdsDTO.setInstanceId(rdsParam.getGId());
        rdsDTO.setStorageSize(rdsParam.getStorageSize());
        return rdsDTO;
    }
    private DeleteRdsRcDto generateDeleteRdsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam rdsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.RDS_DELETE.getCode());
        RdsParam rdsParam = JSONObject.parseObject(rdsOrder.getAttrs(), RdsParam.class);
        DeleteRdsRcDto rdsDTO = new DeleteRdsRcDto();
        rdsDTO.setBillId(layoutOrderParam.getAccount());
        rdsDTO.setInstanceId(rdsParam.getGId());
        rdsDTO.setGroupId(layoutOrderParam.getCustomId());
        rdsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        return rdsDTO;
    }
}
