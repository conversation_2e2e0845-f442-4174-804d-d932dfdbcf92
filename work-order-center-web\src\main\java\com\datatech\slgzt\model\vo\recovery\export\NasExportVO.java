package com.datatech.slgzt.model.vo.recovery.export;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: nas导出
 * @author: LK
 * @create: 2025-06-12 09:45
 **/
@Data
public class NasExportVO {

    @ExcelExportHeader(value = "nas名称")
    private String nasName;

    @ExcelExportHeader(value = "路径")
    private String path;

    @ExcelExportHeader(value = "存储大小")
    public String storageSize;

    @ExcelExportHeader(value = "申请时长")
    private String applyTimeCn;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;
}
