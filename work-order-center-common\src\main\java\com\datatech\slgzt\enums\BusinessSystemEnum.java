package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务系统枚举
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/12
 */

public class BusinessSystemEnum {

    @Getter
    @AllArgsConstructor
    public enum LevelEnum {

        ONE_LEVEL(1, "核心"),
        TWO_LEVEL(2, "重要"),
        THREE_LEVEL(3, "一般"),
        ;


        private final int level;

        private final String remark;

        public static String getRemarkByLevel(int level) {
            LevelEnum[] values = LevelEnum.values();
            for (LevelEnum value : values) {
                if (level == value.getLevel()) {
                    return value.getRemark();
                }
            }

            return LevelEnum.THREE_LEVEL.remark;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum LifeCycleEnum {

        PROJECT_TYPE(1, "工程"),
        ON_NET_TYPE(2, "在网"),
        DOWN_LINE_TYPE(3, "下线"),
        ;

        private final int code;

        private final String remark;

        public static String getRemarkByCode(Integer code) {
            if (code == null) {
                return LifeCycleEnum.ON_NET_TYPE.remark;
            }

            LifeCycleEnum[] values = values();
            for (LifeCycleEnum value : values) {
                if (code == value.code) {
                    return value.remark;
                }
            }

            return LifeCycleEnum.ON_NET_TYPE.remark;
        }

    }
}

