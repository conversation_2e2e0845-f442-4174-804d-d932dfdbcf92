package com.datatech.slgzt.impl.service.recovery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.DomainCodeEnum;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.layout.ResRecoveryReqModel;
import com.datatech.slgzt.model.recovery.RecoveryEcsModel;
import com.datatech.slgzt.model.recovery.RecoveryEipModel;
import com.datatech.slgzt.model.recovery.RecoveryEvsModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.recovery.RecoveryResourceService;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.UuidUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: workordercenterproject
 * @description: nat组合回收
 * @author: Lzj
 * @create: 2025-03-25 09:59
 **/
@Service
@Slf4j
public class RecoveryMysqlServiceImpl implements RecoveryResourceService {

    @Resource
    private PlatformService platformService;

    @Resource
    private RecoveryWorkOrderProductManager manager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void recoveryResource(RecoveryWorkOrderDTO dto, List<RecoveryWorkOrderProductDTO> recoveryWorkOrderProducts) {

        for (RecoveryWorkOrderProductDTO product : recoveryWorkOrderProducts) {
            ResRecoveryReqModel resRecoveryReqModel = new ResRecoveryReqModel();
            //参数封装
            List<ResRecoveryReqModel.ProductOrder> reqProductList = Lists.newArrayList();
            Boolean syncRecovery = product.getSyncRecovery();
            if (ProductTypeEnum.MYSQL.getCode().equals(product.getProductType())) {
                RecoveryEcsModel recoveryEcsModel = JSONObject.parseObject(product.getPropertySnapshot(), RecoveryEcsModel.class);
                //基础参数封装
                baseParamInit(recoveryEcsModel, resRecoveryReqModel, product, dto);
                //ecs参数填充
                ResRecoveryReqModel.ProductOrder ecsProductOrder = new ResRecoveryReqModel.ProductOrder();
                ecsProductOrder.setGId(recoveryEcsModel.getProductOrderId().toString());
                ecsProductOrder.setProductOrderId(recoveryEcsModel.getProductOrderId().toString());
                ecsProductOrder.setProductOrderType("ECS_DELETE");
                ecsProductOrder.setProductType(ProductTypeEnum.ECS.getCode());
                ecsProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                ResRecoveryReqModel.Attrs ecsAttrs = new ResRecoveryReqModel.Attrs();
                ecsAttrs.setResourceId(recoveryEcsModel.getVmId());
                ecsAttrs.setOptUuid(UuidUtil.getUUID());
                ecsProductOrder.setAttrs(ecsAttrs);
                reqProductList.add(ecsProductOrder);
                //evs参数填充
                List<RecoveryEvsModel> evsModelList = new ArrayList<>();
                //平台云的云硬盘和云主机是一体的，封装参数时不用管云硬盘参数，在下发云主机任务时会删除对应的云硬盘
                if (!DomainCodeEnum.PLF_PROC_NWC_ZJ_PLF_NEW.getCode().equals(recoveryEcsModel.getDomainCode())) {
                    evsModelList = recoveryEcsModel.getEvsModelList();
                }
                //组合回收则封装所有参数，否则仅回收云主机，同时卸载云硬盘和解绑eip
                if (Objects.isNull(syncRecovery) || syncRecovery) {
                    if (CollectionUtil.isNotEmpty(evsModelList)) {
                        evsModelList.forEach(evs -> {
                            ResRecoveryReqModel.ProductOrder evsProductOrder = new ResRecoveryReqModel.ProductOrder();
                            evsProductOrder.setGId(evs.getProductOrderId().toString());
                            evsProductOrder.setProductOrderId(evs.getProductOrderId().toString());
                            evsProductOrder.setProductOrderType("EVS_DELETE");
                            evsProductOrder.setProductType(ProductTypeEnum.EVS.getCode());
                            evsProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                            ResRecoveryReqModel.Attrs evsAttrs = new ResRecoveryReqModel.Attrs();
                            evsAttrs.setResourceId(evs.getDataDiskId());
                            evsAttrs.setEvsId(evs.getDataDiskId());
                            evsAttrs.setVmId(recoveryEcsModel.getVmId());
                            evsProductOrder.setAttrs(evsAttrs);
                            reqProductList.add(evsProductOrder);
                        });
                    }
                    //eip参数填充
                    RecoveryEipModel eipModel = recoveryEcsModel.getEipModel();
                    if (Objects.nonNull(eipModel)) {
                        ResRecoveryReqModel.ProductOrder eipProductOrder = new ResRecoveryReqModel.ProductOrder();
                        eipProductOrder.setGId(eipModel.getProductOrderId().toString());
                        eipProductOrder.setProductOrderId(eipModel.getProductOrderId().toString());
                        eipProductOrder.setProductOrderType("EIP_DELETE");
                        eipProductOrder.setProductType(ProductTypeEnum.EIP.getCode());
                        eipProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                        ResRecoveryReqModel.Attrs eipAttrs = new ResRecoveryReqModel.Attrs();
                        eipAttrs.setType(ProductTypeEnum.ECS.getCode().toUpperCase());
                        eipAttrs.setEcsResourceId(recoveryEcsModel.getVmId());
                        eipAttrs.setResourceId(eipModel.getEipId());
                        eipProductOrder.setAttrs(eipAttrs);
                        reqProductList.add(eipProductOrder);
                    }
                }
            }
            //调用任务中心回收资源
            resRecoveryReqModel.setProductOrders(reqProductList);
            log.info("资源回收，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl);
            Mapper dataMapper= OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(resRecoveryReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "资源回收失败，callLayoutOrder--编排中心初始化返回结果失败");
            log.info("资源回收，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
            //根据是否同步回收更新产品回收状态
            manager.updateStatusByIds(ListUtil.toList(product.getId()), String.valueOf(RecoveryStatusEnum.RECOVERING.getType()));
            if (Objects.isNull(syncRecovery) || syncRecovery) {
                manager.updateStatusByParentId(product.getId(), String.valueOf(RecoveryStatusEnum.RECOVERING.getType()));
            } else {
                manager.updateStatusByParentId(product.getId(), String.valueOf(RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType()));
            }
        }
//        //把对应的产品都改成回收中状态
//        List<Long> ids = recoveryWorkOrderProducts.stream().map(RecoveryWorkOrderProductDTO::getId).collect(Collectors.toList());
//        manager.updateStatusByIds(ids, String.valueOf(RecoveryStatusEnum.RECOVERING.getType()));


    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.MYSQL;
    }


    private void baseParamInit(RecoveryEcsModel recoveryEcsModel,
                               ResRecoveryReqModel resRecoveryReqModel,
                               RecoveryWorkOrderProductDTO product,
                               RecoveryWorkOrderDTO dto) {
        Long tenantId = platformService.getOrCreateTenantId(recoveryEcsModel.getBillId(), recoveryEcsModel.getRegionCode());
        //设置计费号
        resRecoveryReqModel.setAccount(recoveryEcsModel.getBillId());
        //设置业务code;
        resRecoveryReqModel.setSourceExtType(OrderTypeEnum.RECOVERY.getCode());
        //设置业务code
        resRecoveryReqModel.setBusinessCode("ECS_COMBINATION_UNSUBSCRIBE");
        //设置业务系统code
        resRecoveryReqModel.setBusinessSystemCode(dto.getBusinessSystemCode());
        //设置客户id
        resRecoveryReqModel.setCustomId(recoveryEcsModel.getCustomNo());
        //设置区域编码
        resRecoveryReqModel.setRegionCode(recoveryEcsModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resRecoveryReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resRecoveryReqModel.setTenantId(tenantId);
        //设置userId
        resRecoveryReqModel.setUserId(dto.getCreatedBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resRecoveryReqModel.setTaskSource(4);
    }
}
