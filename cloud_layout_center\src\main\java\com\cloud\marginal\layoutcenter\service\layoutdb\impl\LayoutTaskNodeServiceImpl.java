package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.marginal.enums.layout.StateEnum;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskNodeService;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskService;
import com.cloud.marginal.mapper.layout.LayoutTaskNodeMapper;
import com.cloud.marginal.model.entity.layout.LayoutTaskNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 编排任务节点表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
@Slf4j
public class LayoutTaskNodeServiceImpl extends ServiceImpl<LayoutTaskNodeMapper, LayoutTaskNode> implements LayoutTaskNodeService {

    @Resource
    private LayoutTaskNodeMapper layoutTaskNodeMapper;

    @Resource
    private LayoutTaskService layoutTaskService;

    @Override
    @Transactional
    public void retry(String taskId) {
        log.info("task id：{}", taskId);
        LayoutTaskNode task = getByTaskId(taskId);
        log.info("task：{}", JSONObject.toJSONString(task));
        if(task != null && StateEnum.TIMEOUT.toString().equals(task.getState().toString())){
            task.setState(StateEnum.EXECUTING);
            task.setMonitorCount(240);
            task.setRetryCount(3);
            layoutTaskNodeMapper.updateById(task);
            layoutTaskService.retry(task.getMasterTaskId());
            log.info("task updated");
        }
    }

    @Override
    public LayoutTaskNode getByTaskId(String taskId) {
        LambdaQueryWrapper<LayoutTaskNode> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(LayoutTaskNode::getCloudTaskId,taskId);
        List<LayoutTaskNode> nodes = layoutTaskNodeMapper.selectList(wrapper);
        if(CollUtil.isNotEmpty(nodes)){
            return nodes.get(0);
        }
        return null;
    }
}
