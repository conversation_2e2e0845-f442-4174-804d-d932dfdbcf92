package com.datatech.slgzt.model.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 导出任务DTO
 */
@Data
public class ExportTaskDTO {
    
    /**
     * 主键ID
     */
    private String id;

    /**
     * 报表名称
     */
    private String reportName;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 统计类型（HOUR/DAY/MONTH）
     */
    private String statType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 导出字段（JSON格式）
     */
    private String exportFields;

    /**
     * 查询条件（JSON格式）
     */
    private String queryCondition;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 状态（0-生成中，1-完成，2-失败）
     */
    private Integer status;

    private String fileName;



} 