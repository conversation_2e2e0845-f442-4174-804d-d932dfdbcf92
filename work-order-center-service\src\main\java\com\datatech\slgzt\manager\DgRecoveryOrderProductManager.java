package com.datatech.slgzt.manager;

import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;

import java.util.List;

public interface DgRecoveryOrderProductManager {

    void insert(DgRecoveryOrderProductDTO productDTO);


    DgRecoveryOrderProductDTO getById(Long id);

    void update(DgRecoveryOrderProductDTO productDTO);


    List<DgRecoveryOrderProductDTO> getByIds(List<Long> ids);

    List<DgRecoveryOrderProductDTO> listByWorkOrderId(String workOrderId);

    List<DgRecoveryOrderProductDTO> listByResourceDetailId(String resourceDetailId, RecoveryStatusEnum recoveryStatus);

    List<DgRecoveryOrderProductDTO> listChildren(Long id);

    void updateStatusByParentId(Long id, Integer status);

    DgRecoveryOrderProductDTO getBySubOrderId(Long subOrderId);

    void updateHcmByCmdbIds(List<String> configIds, String status);

    void updateHcmByIds(List<Long> configIds, String status);

    void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm);

    void deleteByWorkOrderId(String workOrderId);

    DgRecoveryOrderProductDTO getByCmdbId(String cmdbId) ;

    void updateStatusByIds(List<Long> ids, Integer status);

    void deleteById(Long id);
}
