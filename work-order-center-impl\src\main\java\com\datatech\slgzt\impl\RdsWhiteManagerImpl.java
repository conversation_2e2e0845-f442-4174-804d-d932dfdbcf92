package com.datatech.slgzt.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.convert.RdsWhiteManagerConvert;
import com.datatech.slgzt.dao.RdsWhiteDAO;
import com.datatech.slgzt.dao.model.RdsWhiteDO;
import com.datatech.slgzt.manager.RdsWhiteManager;
import com.datatech.slgzt.model.dto.RdsWhiteDTO;
import com.datatech.slgzt.model.query.RdsWhiteQuery;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单Manager实现类
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Service
@Slf4j
public class RdsWhiteManagerImpl implements RdsWhiteManager {

    @Resource
    private RdsWhiteDAO rdsWhiteDAO;

    @Resource
    private RdsWhiteManagerConvert rdsWhiteManagerConvert;

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterUrl;

    private String addWhiteUrl = "/v1/cloud/resourcecenter/rds/addWhite";

    private String deleteWhiteUrl = "/v1/cloud/resourcecenter/rds/deleteWhite";

    private String updateWhiteUrl = "/v1/cloud/resourcecenter/rds/updateWhite";

    @Override
    public PageResult<RdsWhiteDTO> page(RdsWhiteQuery query) {
        // 1. start page
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        
        // 2. execute query
        List<RdsWhiteDO> list = rdsWhiteDAO.list(query);
        
        // 3. convert and return result
        return PageWarppers.box(new PageInfo<>(list), rdsWhiteManagerConvert::do2dto);
    }

    @Override
    public List<RdsWhiteDTO> list(RdsWhiteQuery query) {
        List<RdsWhiteDO> list = rdsWhiteDAO.list(query);
        return rdsWhiteManagerConvert.do2dto(list);
    }

    @Override
    @Transactional
    public void add(RdsWhiteDTO rdsWhiteDTO) {
        rdsWhiteDTO.setId(IdUtil.getSnowflake().nextId());
        // 设置创建时间
        rdsWhiteDTO.setCreateTime(LocalDateTime.now());
        rdsWhiteDAO.insert(rdsWhiteManagerConvert.dto2do(rdsWhiteDTO));
        //调资源中心创建白名单
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rdsId", rdsWhiteDTO.getRdsId());
        paramMap.put("whiteName", rdsWhiteDTO.getWhiteName());
        paramMap.put("ips", ListUtil.toList(rdsWhiteDTO.getIps().split(",")));
        Mapper dataMapper = OkHttps.sync(resourceCenterUrl + addWhiteUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(paramMap))
                .post()
                .getBody()
                .toMapper();
        log.info("创建数据库白名单响应: {}", dataMapper.toString());
        String successStr = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "创建数据库白名单失败: " + dataMapper.getString("message"));
        // 处理结果数据
        String entity = dataMapper.getString("entity");
        if (!StringUtils.isEmpty(entity)) {
            TaskVO taskVO = JSON.parseObject(entity, TaskVO.class);
            rdsWhiteDTO.setInstanceId(taskVO.getResourceId());
            rdsWhiteDAO.update(rdsWhiteManagerConvert.dto2do(rdsWhiteDTO));
        }
    }

    @Override
    @Transactional
    public void update(RdsWhiteDTO rdsWhiteDTO) {
        Long id = rdsWhiteDTO.getId();
        String ips = rdsWhiteDTO.getIps();
        //根据id获取对象（传递过来的实体参数不全的）
        rdsWhiteDTO = this.getById(id);
        // 设置修改时间
        rdsWhiteDTO.setModifyTime(LocalDateTime.now());
        rdsWhiteDTO.setIps(ips);
        rdsWhiteDAO.update(rdsWhiteManagerConvert.dto2do(rdsWhiteDTO));
        //调用资源中心更新白名单
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", rdsWhiteDTO.getInstanceId());
        paramMap.put("ips", ListUtil.toList(ips.split(",")));
        Mapper dataMapper = OkHttps.sync(resourceCenterUrl + updateWhiteUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(paramMap))
                .post()
                .getBody()
                .toMapper();
        log.info("更新数据库白名单响应: {}", dataMapper.toString());
        String successStr = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "更新数据库白名单失败: " + dataMapper.getString("message"));
    }

    @Override
    @Transactional
    public void delete(Long id) {
        RdsWhiteDTO rdsWhiteDTO = this.getById(id);
        rdsWhiteDAO.delete(id);
        //调用资源中心删除白名单
        //调资源中心创建白名单
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", rdsWhiteDTO.getInstanceId());
        Mapper dataMapper = OkHttps.sync(resourceCenterUrl + deleteWhiteUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(paramMap))
                .post()
                .getBody()
                .toMapper();
        log.info("删除数据库白名单响应: {}", dataMapper.toString());
        String successStr = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "删除数据库白名单失败: " + dataMapper.getString("message"));
    }

    @Override
    public RdsWhiteDTO getById(Long id) {
        RdsWhiteQuery query = new RdsWhiteQuery();
        query.setId(id);
        List<RdsWhiteDO> list = rdsWhiteDAO.list(query);
        if (list.isEmpty()) {
            return null;
        }
        return rdsWhiteManagerConvert.do2dto(list.get(0));
    }
} 