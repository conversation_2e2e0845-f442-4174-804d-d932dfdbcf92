package com.datatech.slgzt;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.merge.LoopMergeStrategy;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

public class ExcelHeaderDemo {
    public static void main(String[] args) {
        // 动态表头定义
        List<List<String>> headers = createDynamicHeaders();

        // 模拟动态数据
        List<Map<String, Object>> data = createDynamicData();

        // 文件输出路径
        String fileName = "动态表头示例.xlsx";

        // 写入文件
        EasyExcel.write(fileName)
                 .head(headers) // 动态表头
                 .registerWriteHandler(new LoopMergeStrategy(2, 0)) // 合并单元格策略
                 .sheet("资源池")
                 .doWrite(createRowData(data, headers));

        System.out.println("Excel 文件生成成功：" + fileName);
    }

    /**
     * 动态生成表头
     */
    private static List<List<String>> createDynamicHeaders() {
        List<List<String>> headers = new ArrayList<>();

        // 顶层分组与子字段
        Map<String, List<String>> groupedHeaders = new LinkedHashMap<>();
        groupedHeaders.put("虚拟资源总量", Arrays.asList("vCPU总量", "vCPU可用总量", "内存总量", "内存可用总量（GB）", "存储（GB）"));
        groupedHeaders.put("虚拟资源已分配总量", Arrays.asList("vCPU已分配", "内存已分配（GB）", "存储已分配（GB）"));
        groupedHeaders.put("虚拟剩余资源总量", Arrays.asList("vCPU剩余", "内存剩余（GB）", "存储剩余（G）"));
        groupedHeaders.put("虚拟资源使用率均值", Arrays.asList("vCPU（%）", "内存（%）", "存储（%）"));
        groupedHeaders.put("虚拟资源使用率峰值", Arrays.asList("vCPU（%）", "内存（%）", "存储（%）"));
        groupedHeaders.put("计算服务器使用率", Arrays.asList("CPU利用率均值", "内存利用率均值", "CPU利用率峰值", "内存利用率峰值"));

        // 动态生成表头
        for (Map.Entry<String, List<String>> entry : groupedHeaders.entrySet()) {
            String groupTitle = entry.getKey(); // 顶层分组标题
            List<String> subHeaders = entry.getValue(); // 子字段列表

            for (String subHeader : subHeaders) {
                List<String> column = new ArrayList<>();
                column.add(groupTitle); // 顶层分组标题
                column.add(subHeader); // 子字段标题
                headers.add(column);
            }
        }
        return headers;
    }

    /**
     * 模拟动态数据
     */
    private static List<Map<String, Object>> createDynamicData() {
        List<Map<String, Object>> data = new ArrayList<>();

        // 模拟两行数据
        Map<String, Object> row1 = new HashMap<>();
        row1.put("vCPU总量", 100);
        row1.put("vCPU可用总量", 80);
        row1.put("内存总量", 256);
        row1.put("内存可用总量（GB）", 200);
        row1.put("存储（GB）", 500);
        row1.put("vCPU已分配", 60);
        row1.put("内存已分配（GB）", 150);
        row1.put("存储已分配（GB）", 300);
        row1.put("vCPU剩余", 40);
        row1.put("内存剩余（GB）", 50);
        row1.put("存储剩余（G）", 200);
        row1.put("vCPU（%）", 15);
        row1.put("内存（%）", 25);
        row1.put("存储（%）", 35);
        row1.put("CPU利用率均值", 50);
        row1.put("内存利用率均值", 60);
        row1.put("CPU利用率峰值", 70);
        row1.put("内存利用率峰值", 80);

        Map<String, Object> row2 = new HashMap<>(row1); // 模拟第二行数据
        row2.put("vCPU总量", 120);
        row2.put("vCPU可用总量", 90);

        data.add(row1);
        data.add(row2);
        return data;
    }

    /**
     * 将动态数据转换为 EasyExcel 的行数据格式
     */
    private static List<List<Object>> createRowData(List<Map<String, Object>> data, List<List<String>> headers) {
        List<List<Object>> rowData = new ArrayList<>();

        for (Map<String, Object> row : data) {
            List<Object> rowValues = new ArrayList<>();
            // 遍历表头，按照表头顺序取值
            for (List<String> header : headers) {
                String field = header.get(header.size() - 1); // 获取字段名（子字段标题）
                rowValues.add(row.getOrDefault(field, ""));
            }
            rowData.add(rowValues);
        }
        return rowData;
    }
}