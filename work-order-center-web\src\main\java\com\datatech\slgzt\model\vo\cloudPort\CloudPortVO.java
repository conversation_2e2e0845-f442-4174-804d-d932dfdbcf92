package com.datatech.slgzt.model.vo.cloudPort;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class CloudPortVO implements Serializable {

    private String id;

    /**
     * 云端口名称
     */
    private String cloudPortName;



    /**
     * 业务系统名称
     */
    private String businessSystemName;



    /**
     * 云平台类型
     */
    private String catalogueDomainCode;

    /**
     * 云平台类型
     */
    private String catalogueDomainName;


    private String platformName;
    /**
     * 云区域编码
     */
    private String regionCode;

    /**
     * 资源池ID
     */
    private String regionId;


    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 可用区
     */
    private String azCode;


    /**
     * 可用区 名称
     */
    private String azName;

    /**
     * VLAN ID
     */
    private String vlanId;


    /**
     * vpc 名称
     */
    private String vpcName;



    /**
     * 创建BGP的外部接口地址
     */
    private String srcIp;


    /**
     * 创建BGP的对端邻居地址
     */
    private String peerIp;

    /**
     * peer口令
     */
    private String peerPassword;


    /**
     * 云平台编码
     */
    private String platformCode;




    private LocalDateTime createTime;
}
