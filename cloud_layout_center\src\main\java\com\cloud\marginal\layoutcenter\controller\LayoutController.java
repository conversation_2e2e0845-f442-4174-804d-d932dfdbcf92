package com.cloud.marginal.layoutcenter.controller;

import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.service.LayoutService;
import com.cloud.marginal.model.dto.layout.LayoutOrder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(VersionConstant.V1)
@Api(tags = "编排中心")
@RefreshScope
public class LayoutController {


    @Resource
    private LayoutService layoutServiceImpl;

    @Resource
    private LayoutService layoutDefaultServiceImpl;

    @Value("${taskconfig.defaultTemplate}")
    private String defaultTemplate;

    /**
     * 编排任务初始化
     */
    @ApiOperation("编排任务初始化")
    @PostMapping("layoutTaskInit")
    public CecResult layoutTaskInit(@Validated @RequestBody LayoutOrder layoutOrder) {
        //订单中心调用
        layoutOrder.setTaskSource(1);
        String businessCode = layoutOrder.getBusinessCode();
        if (defaultTemplate.equals(businessCode)) {
            return layoutDefaultServiceImpl.layoutTaskInit(layoutOrder);
        }
        return layoutServiceImpl.layoutTaskInit(layoutOrder);
    }

    @ApiOperation("工单中心调用任务编排初始化")
    @PostMapping("wokeOrderLayoutTaskInit")
    public CecResult wokeOrderLayoutTaskInit(@Validated @RequestBody LayoutOrder layoutOrder) {
        //工单中心调用
        layoutOrder.setTaskSource(2);
        String businessCode = layoutOrder.getBusinessCode();
        if (defaultTemplate.equals(businessCode)) {
            return layoutDefaultServiceImpl.layoutTaskInit(layoutOrder);
        }
        return layoutServiceImpl.layoutTaskInit(layoutOrder);
    }

    @ApiOperation("工单中心调用任务编排初始化")
    @PostMapping("wokeOrderLayoutTaskInit_new")
    public CecResult wokeOrderLayoutTaskInit_new(@Validated @RequestBody LayoutOrder layoutOrder) {
        //工单中心调用
        layoutOrder.setTaskSource(3);
        String businessCode = layoutOrder.getBusinessCode();
        if (defaultTemplate.equals(businessCode)) {
            return layoutDefaultServiceImpl.layoutTaskInit(layoutOrder);
        }
        return layoutServiceImpl.layoutTaskInit(layoutOrder);
    }

    @ApiOperation("工单中心调用任务编排初始化")
    @PostMapping("wokeOrderLayoutTaskInit_subscribe")
    public CecResult wokeOrderLayoutTaskInit_subscribe(@Validated @RequestBody LayoutOrder layoutOrder) {
        //工单中心标准工单调用
        layoutOrder.setTaskSource(layoutOrder.getTaskSource());
        String businessCode = layoutOrder.getBusinessCode();
        if (defaultTemplate.equals(businessCode)) {
            return layoutDefaultServiceImpl.layoutTaskInit(layoutOrder);
        }
        return layoutServiceImpl.layoutTaskInit(layoutOrder);
    }

    /**
     * 编排任务执行
     *
     * @param masterTaskId 主任务id
     */
    @ApiOperation("编排任务执行")
    @PostMapping("layoutTaskExecut")
    public CecResult layoutTaskExecut(@RequestParam String masterTaskId) {
        return layoutServiceImpl.layoutTaskExecut(masterTaskId);
    }
}
