package com.datatech.slgzt.model.req.container;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 容器配额分页查询请求
 * <AUTHOR>
 * @description 容器配额分页查询参数
 * @date 2025年05月27日
 */
@Data
public class ContainerQuotaPageReq {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 配额名称（支持模糊查询）
     */
    private String cqName;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称（支持模糊查询）
     */
    private String businessSystemName;

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 子订单ID
     */
    private String subOrderId;

    /**
     * 状态
     */
    private String status;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;
    /**
     * 资源池ID（精确匹配）
     */
    private String regionId;
    /**
     * 资源池编码
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 云类型编码（精确匹配）
     */
    private String catalogueDomainCode;

    /**
     * 4A账号（模糊匹配）
     */
    private String a4Account;

    /**
     * 4A账号绑定的手机（模糊匹配）
     */
    private String a4Phone;

    /**
     * 申请用户ID
     */
    private Long applyUserId;

    /**
     * 申请用户名称（模糊查询）
     */
    private String applyUserName;

    /**
     * 申请时长（精确匹配）
     */
    private String applyTime;

    /**
     * 创建时间开始（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String createTimeStart;

    /**
     * 创建时间结束（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String createTimeEnd;

    /**
     * 容器配额-核心数（精确匹配）
     */
    @JsonProperty("vCpus")
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G（精确匹配）
     */
    private Integer ram;
    /**
     * GPU算力（精确匹配）
     */
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB（精确匹配）
     */
    private Integer gpuVirtualMemory;

    /**
     * 物理GPU卡(个)（精确匹配）
     */
    @JsonProperty("gpuVirtualCore")
    private Integer gpuCore;

    /**
     * 虚拟GPU卡(个)（精确匹配）
     */
    @JsonProperty("gpuCore")
    private Integer gpuVirtualCore;

}
