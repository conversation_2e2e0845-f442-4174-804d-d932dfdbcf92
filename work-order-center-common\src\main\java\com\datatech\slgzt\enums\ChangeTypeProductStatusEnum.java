package com.datatech.slgzt.enums;


import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.Getter;

/**
 *
 * product表
 *
 * 云平台枚举
 * <AUTHOR>
 */
@Getter
public enum ChangeTypeProductStatusEnum {

    /**
     * 待更变
     */
    WAIT_CHANGE("wait_change", "待更变"),
    /**
     * 更变中
     */
    CHANGING("changing", "更变中"),
    /**
     * 更变成功
     */
    CHANGE_SUCCESS("change_success", "更变成功"),
    /**
     * 变更失败
     */
    CHANGE_FAIL("change_fail", "变更失败"),
    ;

    private final String code;
    private final String desc;

    ChangeTypeProductStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static ChangeTypeProductStatusEnum getByCode(String code) {
        if (ObjNullUtils.isNotNull((code))) {
            for (ChangeTypeProductStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }

    /**
     * 适配任务中心结果
     * 1是成功
     * 0是失败
     */
    public static ChangeTypeProductStatusEnum adaptTaskCenterResult(Integer result) {
        if (result.equals(1)) {
            return CHANGE_SUCCESS;
        } else {
            return CHANGE_FAIL;
        }
    }
}