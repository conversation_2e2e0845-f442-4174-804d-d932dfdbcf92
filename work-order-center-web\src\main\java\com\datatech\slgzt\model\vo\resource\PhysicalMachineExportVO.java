package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.util.Date;

/**
 * 物理机导出vo
 * <AUTHOR>
 **/
@Data
public class PhysicalMachineExportVO {
    @ExcelExportHeader(value = "裸金属名称")
    private String deviceName;

    @ExcelExportHeader(value = "系统版本")
    private String osVersion;

    @ExcelExportHeader(value = "规格")
    private String spec;

    @ExcelExportHeader(value = "硬盘")
    private String dataDisk;

    // 显卡类型	显卡型号	显卡数量
    //NPU	910B	8
    @ExcelExportHeader(value = "显卡类型")
    private String gpuCardType;

    @ExcelExportHeader(value = "显卡型号")
    private String gpuType;

    @ExcelExportHeader(value = "显卡数量")
    private String gpuNum;

    @ExcelExportHeader(value = "IP")
    private String ip;

    @ExcelExportHeader(value = "申请时长")
    private String applyTime;

    @ExcelExportHeader(value = "租户名称")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String cloudPlatform;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private Date resourceApplyTime;

    @ExcelExportHeader(value = "到期时间")
    private Date expireTime;

    @ExcelExportHeader(value = "计费号")
    private String billId;

//    @ExcelExportHeader(value = "状态")
//    private String deviceStatus;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;
}
