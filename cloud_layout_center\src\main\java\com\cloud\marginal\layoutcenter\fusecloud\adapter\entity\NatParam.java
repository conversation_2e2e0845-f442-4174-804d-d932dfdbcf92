package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
public class NatParam {
    /**
     * 订单id不能为空
     */
    private String orderId;

    /**
     * 云区域编码不能为空
     */
    private String regionCode;
    /**
     * 计费号
     */
    private String billId;
    /**
     * 集团客户编码
     */
    private String groupId;
    private String projectId;
    private String azCode;

    /**
     * NAT网关名称
     */
    private String natName;
    private String description;

    /**
     * 规格编码
     */
    private String flavorCode;
    private String eipId;

    /**
     * VPC编号
     */
    private String vpcId;

    /**
     * 子网编号
     */
    private String subnetId;
    private String gId;

}
