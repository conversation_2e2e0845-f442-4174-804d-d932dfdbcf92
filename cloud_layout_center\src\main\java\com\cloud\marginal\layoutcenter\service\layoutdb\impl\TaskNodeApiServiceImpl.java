package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.marginal.layoutcenter.service.layoutdb.TaskNodeApiService;
import com.cloud.marginal.mapper.layout.TaskNodeApiMapper;
import com.cloud.marginal.model.entity.layout.TaskNodeApi;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 任务节点与API关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class TaskNodeApiServiceImpl extends ServiceImpl<TaskNodeApiMapper, TaskNodeApi> implements TaskNodeApiService {

}
