package com.datatech.slgzt.model.vo.vpc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class NetworkSubnetOrderResult implements Serializable {
    private static final long serialVersionUID = 599388800911149645L;

    private String id;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    private String subnetName;

    private String networkId;

    private String instanceId;

    private String ipVersion;

    private String resourceId;

    private String cidr;

    private String gateway;

    private Integer deleted;

    private String netmask;

    private String message;

    private String description;

    private String status;

    private Integer recoveryStatus;

    private String level2InstanceId;
    private String uuid;
}
