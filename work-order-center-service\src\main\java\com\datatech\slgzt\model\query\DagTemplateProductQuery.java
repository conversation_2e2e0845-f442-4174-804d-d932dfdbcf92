package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * DAG模板产品查询参数
 */
@Data
@Accessors(chain = true)
public class DagTemplateProductQuery {

    private String id;

    private String templateId;

    private String templateName;

    private String businessSystemId;

    private String businessSystemName;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
} 