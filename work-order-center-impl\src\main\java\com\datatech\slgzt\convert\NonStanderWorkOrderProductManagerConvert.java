package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.NonStanderWorkOrderProductDO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface NonStanderWorkOrderProductManagerConvert {

    NonStanderWorkOrderProductDTO do2dto(NonStanderWorkOrderProductDO standardWorkOrderProduct);

    NonStanderWorkOrderProductDO dto2do(NonStanderWorkOrderProductDTO NonStanderWorkOrderProductDTO);

    List<NonStanderWorkOrderProductDTO> dos2DTOs(List<NonStanderWorkOrderProductDO> productDOS);

    List<NonStanderWorkOrderProductDO> dtoList2DOs(List<NonStanderWorkOrderProductDTO> productDOS);
}
