package com.datatech.slgzt.impl.service.recovery;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.service.recovery.RecoveryResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: kafka回收
 * @author: LK
 * @create: 2025-06-10 17:10
 **/
@Slf4j
@Service
public class RecoveryBldRedisServiceImpl implements RecoveryResourceService {

    @Resource
    private RecoveryWorkOrderProductManager manager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    //kafka回收只是删除detail表数据
    @Override
    public void recoveryResource(RecoveryWorkOrderDTO dto, List<RecoveryWorkOrderProductDTO> recoveryWorkOrderProducts) {
        for (RecoveryWorkOrderProductDTO product : recoveryWorkOrderProducts) {
            if (ProductTypeEnum.BLD_REDIS.getCode().equals(product.getProductType())) {
                resourceDetailManager.deleteById(Long.valueOf(product.getResourceDetailId()));
            }
        }
        //把对应的产品都改成回收完成状态
        List<Long> ids = recoveryWorkOrderProducts.stream().map(RecoveryWorkOrderProductDTO::getId).collect(Collectors.toList());
        manager.updateStatusByIds(ids, String.valueOf(RecoveryStatusEnum.RECOVERY_COMPLETE.getType()));
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.BLD_REDIS;
    }

}
