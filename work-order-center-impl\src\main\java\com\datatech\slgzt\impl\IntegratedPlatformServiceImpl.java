package com.datatech.slgzt.impl;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.mapper.TenantMapper;
import com.datatech.slgzt.manager.RegionCapacityMapperManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.dto.RegionCapacityDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.query.RegionCapacityQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.service.IntegratedPlatformService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月12日 16:30:33
 */
@Slf4j
@Service
public class IntegratedPlatformServiceImpl implements IntegratedPlatformService {

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private RegionCapacityMapperManager regionCapacityMapperManager;

    @Value("${http.integratedPlatformUrl}")
    private String integratedPlatformUrl;

    @Resource
    private TenantMapper tenantMapper;

    /**
     * 备案eip
     *
     * @param resourceDetailId
     */
    @Override
    public void recordEip(Long resourceDetailId) {
        try {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(resourceDetailId);
            //获取产品的计费号
            String billId = detailDTO.getBillId();
            if (ObjNullUtils.isNull(billId)) {
                log.error("产品计费号不能为空,resourceDetailId:{}", resourceDetailId);
                return;
            }
            List<ResourceDetailDTO> list = resourceDetailManager.list(new ResourceDetailQuery()
                    .setSourceTypeList(Lists.newArrayList("DG", "FB"))
                    .setType("eip")
                    .setBillId(billId)
            );
            //遍历找出所有EIP 和带宽
            List<ResourceDetailDTO> dtoList = list.stream()
                                                  .filter(i -> ObjNullUtils.isNotNull(i.getEip()))
                                                  .filter(i -> ObjNullUtils.isNotNull(i.getBandWidth()))
                                                  .collect(Collectors.toList());
            List<BusinessInfo> businessInfoList = new ArrayList<>();
            for (ResourceDetailDTO resourceDetailDTO : dtoList) {
                String resourcePoolId = resourceDetailDTO.getResourcePoolId();
                List<RegionCapacityDTO> regionCapacityDTOList = regionCapacityMapperManager.list(new RegionCapacityQuery()
                        .setRegionId(Long.valueOf(resourcePoolId))
                );
                if (ObjNullUtils.isNull(regionCapacityDTOList)) {
                    continue;
                }
                RegionCapacityDTO regionCapacityDTO = regionCapacityDTOList.get(0);
                BusinessInfo businessInfo = new BusinessInfo();
                businessInfo.setIpAddr(resourceDetailDTO.getEip());
                businessInfo.setBandwidth(resourceDetailDTO.getBandWidth());
                businessInfo.setMachineRoomName(regionCapacityDTO.getIdcName());
                businessInfo.setServiceOpenTime(DateUtils.toString(resourceDetailDTO.getCreateTime()));
                businessInfoList.add(businessInfo);
            }
            //如果什么都查询不到 也要调用当做删除了
            Map<String, String> data = tenantMapper.getByBillId(billId);
            if (data.isEmpty()){
                log.info("没有查到对应的集团客户信息,计费号:{}", billId);
                return;
            }
            // 构建请求对象
            Request request = new Request();
            request.setCompanyCode(data.getOrDefault("CUSTOM_NO", "-"));
            request.setUserType(1); // 用户类型: 1-集团客户
            request.setCompanyName(data.getOrDefault("CUSTOM_NAME", "-")); // 集团客户名称
            request.setChargeNo(billId);
            request.setCardType(data.getOrDefault("CERT_TYPE", "-")); // 证件类型: 1-统一社会信用代码
            request.setCardNo(data.getOrDefault("CERT_NUMBER", "-")); // 证件号码
            request.setRegistTime(data.getOrDefault("REGIST_TIME", "-")); // 注册时间
            request.setCityCode(data.getOrDefault("CITY_CODE", "-")); // 地市编码
            request.setCountyCode(data.getOrDefault("AREA_CODE", "-")); // 区县编码
            request.setCompanyAddr(data.getOrDefault("COMPANY_ADDR", "-")); // 单位地址
            request.setInfoSafePeople(data.getOrDefault("SRP_NAME", "-")); // 网络信息安全责任人姓名
            request.setInfoSafeMobilephone(data.getOrDefault("SRP_MOBILE", "-")); // 网络信息安全责任人移动电话
            request.setInfoSafeEmail(data.getOrDefault("SAFE_EMAIL", "-")); // 网络信息安全责任人Email地址
            request.setVirResourceId(data.get("ID")); // 资源id --->这边随便给一个客户表的id
            request.setVirResourceType(0); //
            request.setBusinessInfo(businessInfoList);
            // 调用接口
            String result = OkHttpsUtils.http().sync(integratedPlatformUrl + "/api/res/resmng/cloudRecord/ipCuRecord")
                                        .bodyType(OkHttps.JSON)
                                        .setBodyPara(JSON.toJSONString(request))
                                        .post()
                                        .getBody()
                                        .toString();
            log.info("备案eip请求内容：{}结果:{}",JSON.toJSONString(request), result);
        } catch (Exception e) {
            log.error("备案eip失败:{}",e.getMessage(), e);
        }
    }


    @Data
    public static class Request {

        // 集团客户编号
        private String companyCode;

        // 用户类型
        private int userType;

        // 集团客户名称
        private String companyName;

        // 计费号
        private String chargeNo;

        // 证件类型
        private String cardType;

        // 证件号码
        private String cardNo;

        // 注册时间 (格式: YYYY-MM-DD)
        private String registTime;


        // 地市编码
        private String cityCode;

        // 区县编码
        private String countyCode;

        // 单位地址
        private String companyAddr;

        // 网络信息安全责任人姓名
        private String infoSafePeople;

        // 网络信息安全责任人证件类型 (可选)
        private Integer infoSafeCardtype;

        // 网络信息安全责任人证件号码 (可选)
        private String infoSafeCardno;

        // 网络信息安全责任人固定电话 (可选)
        private String infoSafeFixphone;

        // 网络信息安全责任人移动电话
        private String infoSafeMobilephone;

        // 网络信息安全责任人Email地址
        private String infoSafeEmail;

        // 用户备注信息 (可选)
        private String remark;


        // 机房区域名称 (可选)
        private String machineRoomRegion;

        // 机柜名称 (可选)
        private String cabinetName;

        // 所属区域 (可选)
        private String regionName;

        // 虚拟资源Id
        private String virResourceId;

        // 虚拟资源类型
        private int virResourceType;

        // 业务信息 (数组)
        private List<BusinessInfo> businessInfo;

    }

    @Data
    // 嵌套类: 业务信息
    public static class BusinessInfo {
        private String ipAddr;    // IP地址
        private String bandwidth; // 网络带宽
        // 所属机房
        private String machineRoomName;
        // 服务开通时间
        private String serviceOpenTime;
    }
}
