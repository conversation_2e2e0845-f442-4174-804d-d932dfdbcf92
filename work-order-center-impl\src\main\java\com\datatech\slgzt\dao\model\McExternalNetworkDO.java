package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 外部网络DO
 */
@Data
@TableName("MC_EXTERNAL_NETWORK_T")
public class McExternalNetworkDO {

    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    @TableField("NAME")
    private String name;

    @TableField("REGION_ID")
    private Long regionId;

} 