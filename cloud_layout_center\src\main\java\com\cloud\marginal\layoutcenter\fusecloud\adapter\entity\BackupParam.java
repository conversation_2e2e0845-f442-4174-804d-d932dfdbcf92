package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import com.cloud.resource.api.backup.dto.CreateBackupRcDto;
import lombok.Data;

import java.util.List;

@Data
public class BackupParam {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 云区域code
     */
    private String regionCode;

    /**
     * 计费号
     */
    private String billId;
    /**
     * 集团客户编码
     */
    private String groupId;
    /**
     * 备份服务名称
     */
    private String name;
    /**
     * 全局唯一编码不能为空
     * */
    private String gId;
    /**
     * job
     */
    private Job job;

    @Data
    public static class Job {
        /**
         * 策略名称
         */
        private String jobName;

        /**
         * 备份频率 ：weeks/days
         */
        private String frequency;

        /**
         * 备份开始时间（日期时间格式），若为空则从创建任务时开始
         */
        private String beginBackTime;

        /**
         * 若type=“days”时填写：每隔N天执行一次，取值范围1-30
         */
        private Integer intervalDays;

        /**
         * 若type=“weeks”时填写：每周几执行，1-7分别代表周一到周日，如[1,4]代表每周一和周四执行
         */
        private List<Days> daysOfWeek;


    }

    /**
     * 备份副本保留策略，按数量或时间保留。
     */
    private Retention retention;


    @Data
    public static class Retention {
        /**
         * 保留策略类型：
         * “number”：按数量保留
         * “time”：按时间保留
         */
        private String type;
        /**
         * 根据type类型区分：保留最近N次备份副本或保留最近N天内产生的备份副本，取值范围 0-300，0表示永久保留
         */
        private Integer count;

    }

    /**
     * objectId
     */
    private String objectId;

    /**
     * 订购信息
     */
    private Extendparam extendparam;

    @Data
    public static class Extendparam {
        /**
         * 计费方式
         */
        private String billingPlan;
        /**
         * 订购周期数
         */
        private Integer periodNum;
    }

    /**
     * 外部实例编码
     */
    private String sourceInstanceId;

    /**
     * 外部订单号
     */
    private String sourceOrderCode;

    /**
     * 备份类型
     */
    private Integer backupType;

    @Data
    public static class Days {
        private Integer day;

    }
}
