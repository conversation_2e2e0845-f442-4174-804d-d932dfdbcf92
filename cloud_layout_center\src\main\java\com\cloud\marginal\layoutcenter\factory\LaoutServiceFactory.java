package com.cloud.marginal.layoutcenter.factory;

import com.cloud.marginal.layoutcenter.base.BaseService;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface.BaseNorthInterfaceAdapter;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 根据产品类型获取不同的实现类
 */
@Component
public class LaoutServiceFactory {

    private static Map<String, BaseService> factory = Maps.newConcurrentMap();

    /**
     * 获取service
     * @param productType 产品类型
     */
    public BaseService getService(String productType) {
        return factory.get(productType.toUpperCase());
    }

    /**
     * 注册service
     * @param productType 产品类型
     * @param service      服务
     */
    public void register(String productType, BaseService service) {
        factory.put(productType, service);
    }

}