package com.datatech.slgzt.dao.model.container;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@TableName("XIE_YUN_PROJECT")
public class XieYunProjectDO {

    @TableField("ID")
    private String id;

    /**
     * 谐云项目id
     */
    @TableField("XIE_YUN_PROJECT_ID")
    private String xieYunProjectId;

    /**
     * 谐云组织id
     */
    @TableField("XIE_YUN_ORG_ID")
    private String xieYunOrgId;

    /**
     * 项目名称
     */
    @TableField("PROJECT_NAME")
    private String projectName;

    /**
     * 项目描述
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 集群名称
     */
    @TableField("CLUSTER_NAME")
    private String clusterName;

    /**
     * 节点名称
     */
    @TableField("NODE_POOL_NAME")
    private String nodePoolName;

    /**
     * cpu值
     */
    @TableField("CPU_VALUE")
    private String cpuValue;

    /**
     * 内存值
     */
    @TableField("MEMORY_VALUE")
    private String memoryValue;

//    /**
//     * 资源数据
//     */
//    @TableField("XIE_YUN_PROJECT_QUOTA_OPM_LIST")
//    private String xieYunProjectQuotaOpmList;

    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;

    /**
     * 1：删除,0：正常
     */
    @TableField("DELETED")
    private Boolean deleted;

}

