package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 15:52:25
 */
@Data
@TableName("MC_FLAVOR_PUBLIC_T")
public class FlavorCorporateDO {


    @TableField("ID")
    private String id;

    @TableField("NAME")
    private String name;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("REGION_ID")
    private Long regionId;

    @TableField("DELETED")
    @TableLogic(value = "1", delval = "0")
    private Integer deleted;
    //
    //@TableField("TENANT_ID")
    //private Long tenantId;
    //
    @TableField("RAM")
    private Long ram;

    //@TableField("DISK")
    //private Long disk;
    //
    @TableField("VCPUS")
    private Long vcpus;

    @TableField("SHARES")
    private Integer shares;
    //
    @TableField("TYPE")
    private String type;
    //
    //@TableField("RESOURCE_ID")
    //private String resourceId;
    //
    //@TableField("CREATED_AT")
    //private String createdAt;
    //
    //@TableField("UPDATED_AT")
    //private String updatedAt;
    //
    @TableField("FLAVOR_MODEL_CODE")
    private String flavorModelCode;
    //
    //@TableField("VGPUS")
    //private Long vgpus;
    //
    //@TableField("PGPUS")
    //private String pgpus;
    //
    //@TableField("FPGAS")
    //private Long fpgas;
    //
    //@TableField("FLAVOR_FAMILY")
    //private String flavorFamily;
    //
    //@TableField("FLAVOR_METADATA_ID")
    //private String flavorMetadataId;
    //
    @TableField("AZ_ID")
    private String azId;

    @TableField("FLAVOR_NAME")
    private String flavorName;

    @TableField("GPU_TYPE")
    private String gpuType;




}
