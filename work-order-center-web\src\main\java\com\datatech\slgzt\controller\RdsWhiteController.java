package com.datatech.slgzt.controller;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.RdsWhiteWebConvert;
import com.datatech.slgzt.manager.RdsWhiteManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.RdsWhiteDTO;
import com.datatech.slgzt.model.query.RdsWhiteQuery;
import com.datatech.slgzt.model.req.rdswhite.*;
import com.datatech.slgzt.model.vo.RdsWhiteVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单控制器
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@RestController
@RequestMapping("/rdsWhite")
public class RdsWhiteController {

    @Resource
    private RdsWhiteManager rdsWhiteManager;

    @Resource
    private RdsWhiteWebConvert rdsWhiteWebConvert;

    @PostMapping("/page")
    public CommonResult<PageResult<RdsWhiteVO>> page(@RequestBody RdsWhitePageReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageNum()), "页码不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageSize()), "每页大小不能为空");
        
        RdsWhiteQuery query = rdsWhiteWebConvert.convert(req);
        PageResult<RdsWhiteDTO> page = rdsWhiteManager.page(query);
        return CommonResult.success(PageWarppers.box(page, rdsWhiteWebConvert::convert));
    }

    @PostMapping("/create")
    @OperationLog(description = "新增数据库白名单", operationType = "CREATE")
    public CommonResult<Void> create(@RequestBody RdsWhiteCreateReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getRdsId()), "数据库ID不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getWhiteName()), "白名单名称不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getIps()), "白名单IP不能为空");
        
        RdsWhiteDTO dto = rdsWhiteWebConvert.convert(req);
        rdsWhiteManager.add(dto);
        return CommonResult.success(null);
    }

    @PostMapping("/update")
    @OperationLog(description = "更新数据库白名单", operationType = "UPDATE")
    public CommonResult<Void> update(@RequestBody RdsWhiteUpdateReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getId()), "ID不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getIps()), "白名单IP不能为空");
        
        RdsWhiteDTO dto = rdsWhiteWebConvert.convert(req);
        rdsWhiteManager.update(dto);
        return CommonResult.success(null);
    }

    @PostMapping("/delete")
    @OperationLog(description = "删除数据库白名单", operationType = "DELETE")
    public CommonResult<Void> delete(@RequestBody RdsWhiteDeleteReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getId()), "ID不能为空");
        
        rdsWhiteManager.delete(req.getId());
        return CommonResult.success(null);
    }

    @PostMapping("/detail")
    @OperationLog(description = "查询数据库白名单详情", operationType = "QUERY")
    public CommonResult<RdsWhiteVO> detail(@RequestBody RdsWhiteDetailReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getId()), "ID不能为空");
        
        RdsWhiteDTO dto = rdsWhiteManager.getById(req.getId());
        return CommonResult.success(rdsWhiteWebConvert.convert(dto));
    }

    @GetMapping("/list")
    @OperationLog(description = "查询数据库白名单ip", operationType = "QUERY")
    public CommonResult<List<String>> selectIpsByRdsId(@RequestParam String rdsId) {
        RdsWhiteQuery rdsWhiteQuery = new RdsWhiteQuery();
        rdsWhiteQuery.setRdsId(rdsId);
        List<RdsWhiteDTO> list = rdsWhiteManager.list(rdsWhiteQuery);
        return CommonResult.success(list.stream().map(RdsWhiteDTO::getIps).collect(Collectors.toList()));
    }
} 