package com.datatech.slgzt.controller.external;

import com.datatech.slgzt.convert.ImagesWebConvert;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.manager.ImagesManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ImagesDTO;
import com.datatech.slgzt.model.query.ImagesQuery;
import com.datatech.slgzt.model.req.images.ImagesExReq;
import com.datatech.slgzt.model.req.images.ImagesListReq;
import com.datatech.slgzt.model.vo.images.ImagesTreeVO;
import com.datatech.slgzt.model.vo.images.ImagesVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.ArrayListMultimap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 镜像控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 16:59:23
 */
@RestController
@RequestMapping("/image")
public class ExternalImagesController {

    @Resource
    private ImagesManager imagesManager;


    @Resource
    private ImagesWebConvert convert;

    /**
     * list
     */
    @RequestMapping(value = "/queryImage", method = RequestMethod.GET)
    public CommonResult<PageResult<ImagesVO>> queryImage(ImagesExReq req) {
        PageResult<ImagesDTO> page = imagesManager.queryImage(convert.convert(req));
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }


}
