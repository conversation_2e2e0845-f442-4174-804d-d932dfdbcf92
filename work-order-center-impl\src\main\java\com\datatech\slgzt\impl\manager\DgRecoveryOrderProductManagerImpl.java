package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.DgRecoveryOrderProductManagerConvert;
import com.datatech.slgzt.dao.DgRecoveryOrderProductDAO;
import com.datatech.slgzt.dao.model.order.DgRecoveryOrderProductDO;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.DgRecoveryOrderProductManager;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;
import com.datatech.slgzt.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class DgRecoveryOrderProductManagerImpl implements DgRecoveryOrderProductManager {

    @Resource
    private DgRecoveryOrderProductDAO dao;

    @Resource
    private DgRecoveryOrderProductManagerConvert convert;

    @Override
    public void insert(DgRecoveryOrderProductDTO productDTO) {
        dao.insert(convert.dto2do(productDTO));
    }

    @Override
    public DgRecoveryOrderProductDTO getById(Long id) {
        return convert.do2dto(dao.getById(id));
    }

    @Override
    public void update(DgRecoveryOrderProductDTO productDTO) {
        dao.update(convert.dto2do(productDTO));
    }


    @Override
    public List<DgRecoveryOrderProductDTO> listByWorkOrderId(String workOrderId) {
        return StreamUtils.mapArray(dao.listByWorkOrderId(workOrderId), convert::do2dto);
    }

    @Override
    public List<DgRecoveryOrderProductDTO> listByResourceDetailId(String resourceDetailId, RecoveryStatusEnum recoveryStatus) {
        return StreamUtils.mapArray(dao.listByResourceDetailId(resourceDetailId, recoveryStatus.getType()), convert::do2dto);
    }

    @Override
    public List<DgRecoveryOrderProductDTO> listChildren(Long id) {
        return StreamUtils.mapArray(dao.listChildren(id), convert::do2dto);
    }

    @Override
    public void updateStatusByParentId(Long id, Integer status) {
        DgRecoveryOrderProductDO recoveryWorkOrderProductDO = new DgRecoveryOrderProductDO();
        recoveryWorkOrderProductDO.setParentProductId(id);
        recoveryWorkOrderProductDO.setRecoveryStatus(status);
        dao.updateByParentId(recoveryWorkOrderProductDO);
    }

    @Override
    public DgRecoveryOrderProductDTO getBySubOrderId(Long subOrderId) {
        return convert.do2dto(dao.getBySubOrderId(subOrderId));
    }

    @Override
    public List<DgRecoveryOrderProductDTO> getByIds(List<Long> ids) {
        return StreamUtils.mapArray(dao.getByIds(ids), convert::do2dto);
    }

    @Override
    public void updateHcmByCmdbIds(List<String> configIds, String status) {
        dao.updateHcmByCmdbIds(configIds, status);
    }
    @Override
    public void updateHcmByIds(List<Long> ids, String status) {
        dao.updateHcmByIds(ids, status);
    }


    @Override
    public void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm) {
        dao.updateTenantConfirmByIds(ids, tenantConfirm);
    }

    @Override
    public void deleteByWorkOrderId(String workOrderId) {
        dao.deleteByWorkOrderId(workOrderId);
    }


    public DgRecoveryOrderProductDTO getByCmdbId(String cmdbId) {
        return convert.do2dto(dao.getByCmdbId(cmdbId));
    }

    @Override
    public void updateStatusByIds(List<Long> ids, Integer status) {
        dao.updateStatusByIds(ids, status);
    }

    @Override
    public void deleteById(Long id) {
        dao.deleteById(id);
    }
}

