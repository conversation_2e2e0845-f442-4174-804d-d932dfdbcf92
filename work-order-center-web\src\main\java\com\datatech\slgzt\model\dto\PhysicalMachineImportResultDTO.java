package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 物理机导入结果DTO
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class PhysicalMachineImportResultDTO {

    /**
     * 总行数
     */
    private Integer totalRows;

    /**
     * 成功导入数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failureCount;

    /**
     * 错误详情列表
     */
    private List<ImportErrorDetail> errorDetails;

    /**
     * 导入错误详情
     */
    @Data
    public static class ImportErrorDetail {
        
        /**
         * 行号
         */
        private Integer rowNumber;
        
        /**
         * 字段名
         */
        private String fieldName;
        
        /**
         * 错误原因
         */
        private String errorMessage;
        
        /**
         * 错误的值
         */
        private String errorValue;
    }
}
