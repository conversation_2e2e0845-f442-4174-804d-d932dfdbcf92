package com.datatech.slgzt.impl.service.dag;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datatech.slgzt.convert.DagVpcResOpenServiceConvert;
import com.datatech.slgzt.dao.mapper.network.VpcOrderMapper;
import com.datatech.slgzt.dao.mapper.network.VpcSubnetOrderMapper;
import com.datatech.slgzt.dao.model.vpc.VpcOrder;
import com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.DagResOpenResultDTO;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.vpc.VpcCreateDTO;
import com.datatech.slgzt.model.dto.vpc.VpcOrderDTO;
import com.datatech.slgzt.model.dto.vpc.VpcSubnetCreateDTO;
import com.datatech.slgzt.model.dto.vpc.VpcSubnetDTO;
import com.datatech.slgzt.model.nostander.VpcUnTaskModel;
import com.datatech.slgzt.model.nostander.VpcSubnetUnTaskModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.dag.DagResOpenService;
import com.datatech.slgzt.service.network.VpcMessageService;
import com.datatech.slgzt.utils.Precondition;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 11:30:42
 */
@Service
@Slf4j
public class DagResVpcOpenServiceImpl implements DagResOpenService {

    @Resource
    private PlatformService platformService;

    @Resource
    private DagOrderManager dagOrderManager;


    @Resource
    private VpcOrderMapper vpcMapper;

    @Resource
    private VpcMessageService vpcService;

    @Resource
    private VpcSubnetOrderMapper subnetMapper;
    @Resource
    private DagProductManager productManager;

    @Resource
    private DagVpcResOpenServiceConvert convert;

    @Resource
    private RedissonClient redissonClient;

    private static final String redisKeyPrefix = "dag:vpc:";

    @Override
    public Object openResource(DagProductDTO productDTO) {
        DagResOpenResultDTO resultDTO = new DagResOpenResultDTO();
        resultDTO.setNewOpen(false);
        resultDTO.setSubnetDagId2Id(new HashMap<>());
        //不是只有ESC
        String productType = productDTO.getProductType();
        //开通资源类型要是GPS 或者是ESC
        Precondition.checkArgument(ProductTypeEnum.VPC.getCode().equals(productType), "算力编排不支持该类型开通");
        //把开通的网络资源挂载到对应的附加字段里去
        DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());

        VpcUnTaskModel vpcModel = JSON.parseObject(productDTO.getPropertySnapshot(), VpcUnTaskModel.class);

        //查询vpc是否存在
        LambdaQueryWrapper<VpcOrder> vpcQueryWrapper = new LambdaQueryWrapper<>();
        vpcQueryWrapper
                // todo 这个是order的还是底层的？？
                .eq(VpcOrder::getTenantId, orderDTO.getTenantId())
                .eq(VpcOrder::getRegionCode, orderDTO.getRegionCode())
                .eq(VpcOrder::getIpv4Cidr, vpcModel.getCidr())
                .eq(VpcOrder::getVpcName, vpcModel.getVpcName())
                .eq(VpcOrder::getStatus, "SUCCESS")
                .eq(VpcOrder::getDeleted, 1);
        VpcOrder vpcDO = vpcMapper.selectOne(vpcQueryWrapper);
        // todo 成功，失败
        if (vpcDO != null) {
            for (VpcSubnetUnTaskModel subnetModel : vpcModel.getSubnetDTOList()) {
                LambdaQueryWrapper<VpcSubnetOrder> subnetQueryWrapper = new LambdaQueryWrapper<>();
                subnetQueryWrapper
                        .eq(VpcSubnetOrder::getVpcId, vpcDO.getId())
                        .eq(VpcSubnetOrder::getSubnetName, subnetModel.getSubnetName())
                        .eq(VpcSubnetOrder::getStartIp, subnetModel.getStartIp())
                        .eq(VpcSubnetOrder::getNetmask, subnetModel.getNetmask())
                        .eq(VpcSubnetOrder::getDeleted, 1);
                VpcSubnetOrder subnetDO = subnetMapper.selectOne(subnetQueryWrapper);
                if (subnetDO != null) {
                    resultDTO.getSubnetDagId2Id().put(subnetModel.getDagId(), subnetDO.getId());
                } else {
                    VpcSubnetDTO subnetDTO = convert.subnetModel2dto(subnetModel);
                    // todo 异步，异步成功，异步失败
                    CommonResult<VpcSubnetCreateDTO> subnet = vpcService.createSubnet(subnetDTO, vpcDO.getId());
                    resultDTO.setNewOpen(true);
                    resultDTO.getSubnetDagId2Id().put(subnetModel.getDagId(), subnet.getEntity().getId());
                    RMap<String, String> map = redissonClient.getMap(redisKeyPrefix + vpcDO.getId() + ":" + subnet.getEntity().getId());
                    map.put("subOrderId", String.valueOf(productDTO.getSubOrderId()));
                    map.put("jobExecutionId", orderDTO.getJobExecutionId().toString());
                    map.expire(30L, TimeUnit.DAYS);
                }
            }
            if (!resultDTO.getNewOpen()) {
                productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
                productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
            }
        } else {
            Long bottomTenantId = platformService.getOrCreateTenantId(orderDTO.getBillId(), vpcModel.getRegionCode());
            resultDTO.setNewOpen(true);
            VpcCreateDTO vpcCreateDTO = new VpcCreateDTO();
            vpcCreateDTO.setRegionCode(orderDTO.getRegionCode());
            vpcCreateDTO.setBillId(orderDTO.getBillId());
            vpcCreateDTO.setBusinessSysId(Long.valueOf(orderDTO.getBusinessSystemId()));
            vpcCreateDTO.setBusinessSysName(orderDTO.getBusinessSystemName());
            vpcCreateDTO.setTenantId(orderDTO.getTenantId());
            vpcCreateDTO.setTenantName(orderDTO.getTenantName());
            vpcCreateDTO.setOrderId(orderDTO.getId());
            vpcCreateDTO.setUserId(orderDTO.getCreatedBy());
            vpcCreateDTO.setAzCode(orderDTO.getAzCode());
            vpcCreateDTO.setFunctionalModule(vpcModel.getFunctionalModule());
            vpcCreateDTO.setBottomTenantId(bottomTenantId);
            vpcCreateDTO.setApplyUserId(orderDTO.getCreatedBy());
            vpcCreateDTO.setApplyUserName(orderDTO.getCreator());
            vpcCreateDTO.setSourceType(SourceTypeEnum.DAG.getPrefix());
            vpcCreateDTO.setModuleId(orderDTO.getModuleId());
            vpcCreateDTO.setModuleName(orderDTO.getModuleName());
            vpcCreateDTO.setOrderCode(orderDTO.getOrderCode());
            vpcCreateDTO.setCatalogueDomainCode(orderDTO.getCatalogueDomainCode());
            vpcCreateDTO.setCatalogueDomainName(orderDTO.getCatalogueDomainName());
            vpcCreateDTO.setDomainCode(orderDTO.getDomainCode());
            vpcCreateDTO.setDomainName(orderDTO.getDomainName());

            VpcOrderDTO vpcOrderDTO = convert.vpcModel2dto(vpcModel);
            ArrayList<VpcSubnetDTO> subnetDTOList = new ArrayList<>();
            vpcOrderDTO.setSubnetDTOList(subnetDTOList);
            for (VpcSubnetUnTaskModel vpcSubnetModel : vpcModel.getSubnetDTOList()) {
                VpcSubnetDTO dto = convert.subnetModel2dto(vpcSubnetModel);
                subnetDTOList.add(dto);
            }
            vpcCreateDTO.setNetworks(Collections.singletonList(vpcOrderDTO));
//        vpcCreateDTO.setVpcType(0);
            //------------------调用底层开通接口-------------------------------------------------------
            CommonResult<String> result = vpcService.createVpcDag(vpcCreateDTO);
            Precondition.checkArgument(result.getCode().equals(200),result.getMessage());

            //查询vpc是否存在
            LambdaQueryWrapper<VpcOrder> vpcQueryWrapper2 = new LambdaQueryWrapper<>();
            vpcQueryWrapper2
                    // todo 这个是order的还是底层的？？
                    .eq(VpcOrder::getTenantId, orderDTO.getTenantId())
                    .eq(VpcOrder::getRegionCode, orderDTO.getRegionCode())
                    .eq(VpcOrder::getIpv4Cidr, vpcModel.getCidr())
                    .eq(VpcOrder::getVpcName, vpcModel.getVpcName())
                    .eq(VpcOrder::getDeleted, 1);
            vpcDO = vpcMapper.selectOne(vpcQueryWrapper2);
            List<VpcSubnetOrder> subnetDOs = subnetMapper.selectByVpcId(vpcDO.getId());
            for (VpcSubnetOrder subnetDO : subnetDOs) {
                Optional<VpcSubnetUnTaskModel> subnetModelOptional = vpcModel.getSubnetDTOList().stream().filter(i ->
                        i.getSubnetName().equals(subnetDO.getSubnetName())
                                && i.getStartIp().equals(subnetDO.getStartIp())
                                && i.getNetmask().equals(subnetDO.getNetmask())
                ).findFirst();
                VpcOrder finalVpcDO = vpcDO;
                subnetModelOptional.ifPresent(vpcSubnetModel -> {
                            resultDTO.getSubnetDagId2Id().put(vpcSubnetModel.getDagId(), subnetDO.getId());
                            RMap<String, String> map = redissonClient.getMap(redisKeyPrefix + finalVpcDO.getId() + ":all");
                            map.put("subOrderId", String.valueOf(productDTO.getSubOrderId()));
                            map.put("jobExecutionId", orderDTO.getJobExecutionId().toString());
                            map.expire(30L, TimeUnit.DAYS);
                        }
                );
            }
        }
        resultDTO.setVpcId(vpcDO.getId());
//        // 更新vpc id，kafka回调时需要用它更新product状态
//        DagProductDTO updateDto = new DagProductDTO();
//        updateDto.setId(productDTO.getId());
//        updateDto.setVpcId(vpcDO.getId());
//        productManager.update(updateDto);
        return resultDTO;
    }

    /**
     * 注册
     */
    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.VPC;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
}
