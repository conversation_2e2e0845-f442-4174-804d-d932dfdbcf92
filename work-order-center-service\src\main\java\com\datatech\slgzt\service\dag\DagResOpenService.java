package com.datatech.slgzt.service.dag;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;

/**
 * 非标的资源开通总接口
 * 根据不同的开通类型，有不同的实现
 * 后期如果存在比如开通 OBS 等就会走不同的实现去开通
 * 每个开通前的拿参不同
 */
public interface DagResOpenService {


    Object openResource(DagProductDTO productDTO);

    /**
     * 注册
     * @param dto
     */
    ProductTypeEnum registerOpenService();





    void layoutTaskNotify(OrderStatusNoticeDTO dto);
}
