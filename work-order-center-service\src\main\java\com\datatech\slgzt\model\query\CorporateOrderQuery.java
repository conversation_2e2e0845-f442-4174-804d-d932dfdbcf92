package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class CorporateOrderQuery {

    private String id;

    private String orderCode;

    private Long createBy;

    private List<String> ids;

    private List<Long> tenantIds;

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    private String tenantName;

    private String createByName;

    private LocalDateTime createTimeEnd;

    private LocalDateTime createTimeStart;
} 