package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DgRecoveryOrderProductMapper;
import com.datatech.slgzt.dao.model.order.DgRecoveryOrderProductDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class DgRecoveryOrderProductDAO {

    @Resource
    private DgRecoveryOrderProductMapper mapper;


    public void insert(DgRecoveryOrderProductDO productDO) {
        mapper.insert(productDO);
    }

    public DgRecoveryOrderProductDO getById(Long id) {
        return mapper.selectById(id);
    }

    public void update(DgRecoveryOrderProductDO productDO) {
        mapper.updateById(productDO);
    }


    public List<DgRecoveryOrderProductDO> getByIds(List<Long> ids) {
        return mapper.selectBatchIds(ids);
    }

    /**
     * listByWorkOrderId
     */
    public List<DgRecoveryOrderProductDO> listByWorkOrderId(String workOrderId) {
        return mapper.selectList(Wrappers.<DgRecoveryOrderProductDO>lambdaQuery()
                .eq(DgRecoveryOrderProductDO::getWorkOrderId, workOrderId));
    }

    public List<DgRecoveryOrderProductDO> listByResourceDetailId(String resourceDetailId, Integer recoveryStatus) {
        return mapper.selectList(Wrappers.<DgRecoveryOrderProductDO>lambdaQuery()
                .eq(DgRecoveryOrderProductDO::getResourceDetailId, resourceDetailId)
                .eq(DgRecoveryOrderProductDO::getRecoveryStatus, recoveryStatus));
    }

    public List<DgRecoveryOrderProductDO> listChildren(Long id) {
        return mapper.selectList(Wrappers.<DgRecoveryOrderProductDO>lambdaQuery()
                .eq(DgRecoveryOrderProductDO::getParentProductId, id));
    }

    public void updateByParentId(DgRecoveryOrderProductDO productDO) {
        mapper.update(productDO, Wrappers.<DgRecoveryOrderProductDO>lambdaUpdate()
                .eq(DgRecoveryOrderProductDO::getParentProductId
                        , productDO.getParentProductId()));
    }

    public DgRecoveryOrderProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<DgRecoveryOrderProductDO>lambdaQuery().eq(DgRecoveryOrderProductDO::getSubOrderId, subOrderId));
    }

    public void updateHcmByCmdbIds(List<String> configIds, String status) {
        DgRecoveryOrderProductDO productDO = new DgRecoveryOrderProductDO();
        productDO.setHcmStatus(status);
        mapper.update(productDO, Wrappers.<DgRecoveryOrderProductDO>lambdaUpdate()
                .in(DgRecoveryOrderProductDO::getCmdbId
                        , configIds));
    }

    public void updateHcmByIds(List<Long> ids, String status) {
        DgRecoveryOrderProductDO productDO = new DgRecoveryOrderProductDO();
        productDO.setHcmStatus(status);
        mapper.update(productDO, Wrappers.<DgRecoveryOrderProductDO>lambdaUpdate()
                .in(DgRecoveryOrderProductDO::getId
                        , ids));
    }

    public void updateTenantConfirmByIds(List<Long> ids, Boolean tenantConfirm) {
        DgRecoveryOrderProductDO productDO = new DgRecoveryOrderProductDO();
        productDO.setTenantConfirm(tenantConfirm);
        mapper.update(productDO, Wrappers.<DgRecoveryOrderProductDO>lambdaUpdate()
                .in(DgRecoveryOrderProductDO::getId, ids));
    }

    public void deleteByWorkOrderId(String workOrderId) {
        mapper.delete(Wrappers.<DgRecoveryOrderProductDO>lambdaQuery()
                .eq(DgRecoveryOrderProductDO::getWorkOrderId, workOrderId));
    }

    public DgRecoveryOrderProductDO getByCmdbId(String cmdbId) {
        return mapper.selectOne(Wrappers.<DgRecoveryOrderProductDO>lambdaQuery()
                .eq(DgRecoveryOrderProductDO::getCmdbId, cmdbId));
    }

    public void updateStatusByIds(List<Long> ids, Integer status) {
        DgRecoveryOrderProductDO productDO = new DgRecoveryOrderProductDO();
        productDO.setRecoveryStatus(status);
        mapper.update(productDO, Wrappers.<DgRecoveryOrderProductDO>lambdaUpdate()
                .in(DgRecoveryOrderProductDO::getId, ids));
    }

    public void deleteById(Long id) {
        mapper.deleteById(id);
    }
}
