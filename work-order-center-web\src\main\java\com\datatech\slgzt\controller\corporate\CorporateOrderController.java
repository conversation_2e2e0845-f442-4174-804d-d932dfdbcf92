package com.datatech.slgzt.controller.corporate;

import com.datatech.slgzt.convert.CorporateOrderWebConvert;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.CorporateOrderDTO;
import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import com.datatech.slgzt.model.dto.TenantDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.opm.CorporateOrderCreateOpm;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.datatech.slgzt.model.query.CorporateOrderQuery;
import com.datatech.slgzt.model.req.corporate.CorporateOrderCreateReq;
import com.datatech.slgzt.model.req.corporate.CorporateOrderDetailReq;
import com.datatech.slgzt.model.req.corporate.CorporateOrderPageReq;
import com.datatech.slgzt.model.req.corporate.CorporateOrderPriceCalculateReq;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.corporate.CorporateOrderDetailVO;
import com.datatech.slgzt.model.vo.corporate.CorporateOrderVO;
import com.datatech.slgzt.service.ResourcePriceService;
import com.datatech.slgzt.service.corporate.CorporateOrderService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/corporate/order")
public class CorporateOrderController {

    @Resource
    private CorporateOrderManager corporateOrderManager;

    @Resource
    private CorporateOrderProductManager corporateOrderProductManager;

    @Resource
    private CorporateOrderService corporateOrderService;

    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private ResourcePriceService resourcePriceService;

    @Resource
    private CorporateOrderWebConvert corporateOrderWebConvert;

    @PostMapping("/page")
    public CommonResult<PageResult<CorporateOrderVO>> page(@RequestBody CorporateOrderPageReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageNum()), "页码不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageSize()), "每页大小不能为空");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "当前用户未登录");
        List<Long> tenantIdList = tenantManager.listRelTenantIdByUserId(currentUser.getId());
        CorporateOrderQuery query = corporateOrderWebConvert.convert(req);
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArrayNotNull(oacRoles, UserCenterRoleDTO::getCode);
        if (!roles.contains("super_admin") && roles.stream().noneMatch(role -> role.startsWith("operation_group"))
                && !roles.contains("general_admin")) {
            query.setTenantIds(tenantIdList);
        }
        PageResult<CorporateOrderDTO> page = corporateOrderManager.page(query);
        return CommonResult.success(PageWarppers.box(page, corporateOrderWebConvert::convert));
    }

    @PostMapping("/detail")
    public CommonResult<CorporateOrderDetailVO> detail(@RequestBody CorporateOrderDetailReq req) {
        List<CorporateOrderProductDTO> list = corporateOrderProductManager.list(new CorporateOrderProductQuery()
                .setOrderId(req.getId())
                .setParentId(0L));
        return CommonResult.success(corporateOrderWebConvert.convert(list));
    }


    @PostMapping("/create")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> create(@RequestBody CorporateOrderCreateReq req) {
        // 参数校验
        //获取当前登录用户信息
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "用户信息不能为空");
        //通过登录用户获取用户的业务系统
        TenantDTO tenantDTO = tenantManager.getById(req.getTenantId());
        Precondition.checkArgument(ObjNullUtils.isNotNull(tenantDTO), "用户没有租户");
        CmpAppDTO cmpAppDTO = businessService.selectByTenantId(tenantDTO.getId());
        Precondition.checkArgument(ObjNullUtils.isNotNull(cmpAppDTO), "用户没有业务系统");
        CorporateOrderCreateOpm opm = corporateOrderWebConvert.convert2Opm(req);
        opm.setBusinessSystemId(cmpAppDTO.getSystemId());
        opm.setBusinessSystemName(cmpAppDTO.getSystemName());
        opm.setTenantId(tenantDTO.getId());
        opm.setTenantName(tenantDTO.getName());
        opm.setBillId(tenantDTO.getBillId());
        opm.setCustomNo(tenantDTO.getCustomNo());
        opm.setCreateBy(currentUser.getId());
        opm.setCreateByName(currentUser.getUserName());
        corporateOrderService.createOrder(opm);
        return CommonResult.success();
    }

    //restart
    @PostMapping("/restart")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> restart(@RequestBody CorporateOrderDetailReq req) {
        corporateOrderService.restart(req.getId());
        return CommonResult.success();

    }

    /**
     * 价格计算
     */
    @RequestMapping(value = "/priceCalculate", method = RequestMethod.POST)
    public CommonResult<BigDecimal> priceCalculate(@RequestBody CorporateOrderPriceCalculateReq req) {
        // 参数校验
        //获取当前登录用户信息
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(ObjNullUtils.isNotNull(currentUser), "用户信息不能为空");
        //通过登录用户获取用户的业务系统
        String productType = req.getProductType();
        BigDecimal price = null;
        switch (productType) {
            case "ecs":
                price = resourcePriceService.calculateVmPrice(req.getEcsModel());
                break;
            case "gcs":
                price = resourcePriceService.calculateVmPrice(req.getGcsModel());
                break;
            case "mysql":
                price = resourcePriceService.calculateVmPrice(req.getMysqlModel());
                break;
            case "redis":
                price = resourcePriceService.calculateVmPrice(req.getRedisModel());
                break;
            case "evs":
                price = resourcePriceService.calculateVolumePrice(req.getEvsModel());
                break;
            case "nat":
                price = resourcePriceService.calculateNatPrice(req.getNatModel());
                break;
            case "slb":
                price = resourcePriceService.calculateSlbPrice(req.getSlbModel());
                break;
            case "obs":
                price = resourcePriceService.calculateObsPrice(req.getObsModel());
                break;
            case "vpn":
                price = resourcePriceService.calculateVpnPrice(req.getVpnModel());
                break;
        }
        return CommonResult.success(price);
    }

}