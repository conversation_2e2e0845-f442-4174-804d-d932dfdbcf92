package com.cloud.marginal.layoutcenter.scheduler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.edge.TaskStatusEnum;
import com.cloud.marginal.enums.layout.OrderTypeEnum;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.enums.layout.StateEnum;
import com.cloud.marginal.enums.layout.TaskTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface.ObsMgAdapter;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface.OrderNoticeMgAdapter;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface.OrderNoticeMgAdapterVpc;
import com.cloud.marginal.layoutcenter.fusecloud.utils.OrderCenterHttpRequestUtil;
import com.cloud.marginal.layoutcenter.service.impl.LayoutServiceImpl;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskNodeService;
import com.cloud.marginal.mapper.edge.TasksMapper;
import com.cloud.marginal.mapper.layout.*;
import com.cloud.marginal.model.dto.layout.LayoutOrder;
import com.cloud.marginal.model.dto.layout.ProductOrder;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.entity.layout.LayoutTaskNode;
import com.cloud.marginal.model.vo.edge.ResourceDetailVO;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import com.cloud.marginal.model.vo.layout.RelTaskVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.assertj.core.util.Lists;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.cloud.marginal.enums.edge.TaskTypeEnum.VM_MOUNT_VOLUME;
import static com.cloud.marginal.enums.edge.TaskTypeEnum.VOLUME_CREATE;

/**
 * @program: the_backend_code_of_zyzx
 * @description: 消费资源中心任务消息
 * @author: LK
 * @create: 2024-12-12 13:57
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskConsumer {

    private final LayoutTaskNodeService layoutTaskNodeService;
    private final LayoutTaskNodeMapper layoutTaskNodeMapper;
    private final LayoutParamMapper layoutParamMapper;
    private final TasksRelMapper tasksRelMapper;
    private final LayoutTaskMapper layoutTaskMapper;
    private final LayoutServiceImpl layoutService;
    private final ResourceDetailMapper resourceDetailMapper;
    private final TasksMapper taskMapper;
    private final OrderNoticeMgAdapterVpc orderNoticeMgAdapterVpc;
    private final OrderNoticeMgAdapter orderNoticeMgAdapter;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObsMgAdapter obsMgAdapter;

    private final static String LAYOUT_TASK_CALLBACK_TOPIC = "prod_layout_task_callback_topic";
    private final static String RESOURCE_OPERATE_TOPIC = "prod_resource_operate_topic";
    /**
     * 资源信息详情topic
     */
    private final static String WORK_ORDER_TOPIC = "prod_oac_resource_detail_topic";

    /**
     * 云硬盘开通跟挂载2个类型
     */
    private final static List<String> evsTaskTypeList = Lists.newArrayList(VOLUME_CREATE.getDesc(), VM_MOUNT_VOLUME.getDesc());

    @KafkaListener(groupId = "layout-center-task-group-2", topics = {LAYOUT_TASK_CALLBACK_TOPIC})
    public void consumeResourceMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("topic={},监听任务消息: {}", LAYOUT_TASK_CALLBACK_TOPIC, consumerRecordList.size());
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            TaskVO taskVO = JSONObject.parseObject(record.value(), TaskVO.class);
            log.info("topic={},消息内容：{}，任务状态：{}", LAYOUT_TASK_CALLBACK_TOPIC, taskVO, taskVO.getStatus());
            String resourceType = taskVO.getResourceType();
            //VPC和网络开通发送kafka消息到工单中心
            if ("VPC".equals(resourceType)|| "NETWORK".equals(resourceType)|| "SUBNET".equals(resourceType)) {
                orderNoticeMgAdapterVpc.packageVpcResource(taskVO);
                continue;
            }
            if (TaskStatusEnum.SUCCESS.getCode().equals(taskVO.getStatus())) {
                String operationType = taskVO.getOperationType();
                //云主机操作
                if (ProductOrderTypeEnum.ECS.getCode().equals(resourceType) || operationType.contains("DELETE")) {
                    log.info("操作类型：{}", operationType);
                    kafkaTemplate.send(RESOURCE_OPERATE_TOPIC, JSONObject.toJSONString(taskVO));
                }
                //桶消息回调（未经过任务中心，单独构造任务消息）
                if (ProductOrderTypeEnum.OBS_BUCKET_CREATE.getCode().equals(operationType)) {
                    ResourceDetailVO resourceDetailVO = resourceDetailMapper.selectResourceDetailOfObsByBucketGid(taskVO.getResourceId());
                    resourceDetailVO.setGoodsOrderId(taskVO.getOrderId());
                    resourceDetailVO.setSourceExtType(OrderTypeEnum.SUBSCRIBE.getCode());
                    resourceDetailVO.setType(ProductOrderTypeEnum.OBS.getCode().toLowerCase());
                    log.info("obs桶资源信息：{}", JSONObject.toJSONString(resourceDetailVO));
                    kafkaTemplate.send(WORK_ORDER_TOPIC, JSONObject.toJSONString(resourceDetailVO));
                }
                //根据桶删除成功消息，判断是否可以删除对象存储
                if (ProductOrderTypeEnum.OBS_BUCKET_DELETE.getCode().equals(operationType) && taskVO.getAllowDelete()) {
                    obsMgAdapter.deleteObsV2(taskVO.getAttachResourceId());
                }
                //安全组和安全组规则发送kafka消息到工单中心
                if ("SECURITY_GROUP".equals(resourceType)) {
                    log.info("安全组创建消息：{}", taskVO);
                    kafkaTemplate.send(RESOURCE_OPERATE_TOPIC, JSONObject.toJSONString(taskVO));
                    continue;
                }
                //安全规则创建信息，需要在资源中心封装好发送到kafka
                if ("SECURITY_GROUP_RULE".equals(resourceType)) {
                    String s = orderNoticeMgAdapter.packageSecurityRuleResource(taskVO.getResourceId());
                    taskVO.setData(s);
                    log.info("安全组规则创建消息：{}", taskVO);
                    kafkaTemplate.send(RESOURCE_OPERATE_TOPIC, JSONObject.toJSONString(taskVO));
                    continue;
                }
                //资源中心任务id
                String cloudTaskId = taskVO.getId();
                if (Objects.isNull(cloudTaskId)){
                    continue;
                }
                //查询任务中心对应子任务
                List<LayoutTaskVO> layoutTasks = layoutTaskNodeMapper.getTaskNodesByCloudTaskId(cloudTaskId);
                if (CollectionUtils.isEmpty(layoutTasks)) {
                    log.error("任务中心不存在该任务===cloudTaskId：{}" , cloudTaskId);
                    continue;
                }
                //更新任务中心的表
                for (LayoutTaskVO layoutTaskVO : layoutTasks) {
                    try {
                        this.updateStateByCloudTask(layoutTaskVO, taskVO);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        // 任务状态更新为失败
                        layoutTaskVO.setState(StateEnum.ERROR);
                        layoutTaskVO.setMessage(e.getCause() == null ? e.getMessage() : e.getCause().getMessage());
                    }
                    this.updateByCondition(layoutTaskVO);
                    // 监控成功,马上执行一下任务
                    layoutService.layoutTaskExecut(layoutTaskVO.getMasterTaskId());
                }
            }
        }
    }

    /**
     * 根据底层任务更新状态
     *
     * @param layoutTaskVO   编排任务
     * @param resourceTaskVO 底层任务
     */
    private void updateStateByCloudTask(LayoutTaskVO layoutTaskVO, TaskVO resourceTaskVO) {
        String status = resourceTaskVO.getStatus();
        layoutTaskVO.setCloudTaskId(resourceTaskVO.getId());
        layoutTaskVO.setResourceId(resourceTaskVO.getResourceId());
        layoutTaskVO.setInstanceId(resourceTaskVO.getInstanceId());
        // 状态更新
        if (TaskStatusEnum.SUCCESS.getCode().equals(status)) {
            // 成功
            layoutTaskVO.setState(StateEnum.SUCCESS);
            layoutTaskVO.setEndTime(new Date());
            if (StrUtil.isNotEmpty(layoutTaskVO.getInstanceId())) {
                layoutTaskVO.setInstanceId(layoutTaskVO.getInstanceId());
            }
            // 全局参数更新
            this.updateParam(layoutTaskVO);

        }
        if (TaskStatusEnum.ERROR.getCode().equals(status) || TaskStatusEnum.EXCEPTION.getCode().equals(status)) {
            // 失败
            layoutTaskVO.setState(StateEnum.ERROR);
            layoutTaskVO.setMessage(resourceTaskVO.getMessage());
        }
        if (TaskStatusEnum.EXECUTING.getCode().equals(status)) {
            // 执行中
            layoutTaskVO.setState(StateEnum.EXECUTING);
        }
        if (TaskStatusEnum.TIMEOUT.getCode().equals(status)) {
            // 超时
            layoutTaskVO.setState(StateEnum.TIMEOUT);
            layoutTaskVO.setRetryCount(0);
            layoutTaskVO.setMessage("TASK_TIMEOUT");
        }
    }

    /**
     * 全局参数更新
     */
    private void updateParam(LayoutTaskVO layoutTaskVO) {
        // 自旋更新3次
        int j = 0;
        for (int i = 0; i < 3; i++) {
            LayoutParam layoutParam = layoutParamMapper.getLayoutParamByMainTaskId(layoutTaskVO.getMasterTaskId());
            String paramValue = layoutParam.getParamValue();
            LayoutOrder layoutOrder = JSONObject.parseObject(paramValue, LayoutOrder.class);
            for (ProductOrder productOrder : layoutOrder.getProductOrders()) {
                if (productOrder.getProductOrderId().equals(layoutTaskVO.getOrderId())) {
                    if (!StringUtils.isEmpty(layoutTaskVO.getResourceId())) {
                        HashMap<String, Object> attrs = productOrder.getAttrs();
                        attrs.put("id", layoutTaskVO.getResourceId());
                    }
                }
            }
            layoutParam.setParamValue(JSONObject.toJSONString(layoutOrder));
            layoutParam.setUpdatedTime(new Date());
            j = layoutParamMapper.updateByRevision(layoutParam);
            if (j != 0) {
                break;
            }
        }
        if (j == 0) {
            log.error("全局参数更新失败");
        }
    }

    /**
     * 更新任务
     */
    private void updateByCondition(LayoutTaskVO layoutTaskVO) {
        String taskType = layoutTaskVO.getTaskType();
        layoutTaskVO.setUpdatedTime(new Date());
        if (!layoutTaskVO.getState().toString().equals(StateEnum.SUCCESS.toString()) && layoutTaskVO.getRetryCount() <= 0) {
            // TODO 发送告警
        }

        if (StateEnum.TIMEOUT.toString().equals(layoutTaskVO.getState().toString())) {
            // 更新主任务
            layoutTaskVO.setState(StateEnum.TIMEOUT);
            layoutTaskVO.setRetryCount(0);
        }

        if (layoutTaskVO.getState().toString().equals(StateEnum.ERROR.toString())) {
            layoutTaskVO.setRetryCount(Math.max(layoutTaskVO.getRetryCount() - 1, 0));
            // 当任务重试次数为0时，被依赖的任务全部置为失败
            if (layoutTaskVO.getRetryCount() <= 0) {
                List<RelTaskVO> relTasksVO = tasksRelMapper.selectTaskByRelTaskId(layoutTaskVO.getId());
                for (RelTaskVO relTaskVO : relTasksVO) {
                    LayoutTaskNode node = new LayoutTaskNode();
                    node.setId(relTaskVO.getId());
                    node.setState(StateEnum.ERROR);
                    node.setUpdatedTime(new Date());
                    node.setRetryCount(0);
                    layoutTaskNodeService.updateById(node);
                }
            }
        }


        if (TaskTypeEnum.MASTER.toString().equals(taskType)) {
            layoutTaskMapper.updateByCondition(layoutTaskVO);
        }
        if (TaskTypeEnum.SUB.toString().equals(taskType)) {
            layoutTaskNodeMapper.updateByCondition(layoutTaskVO);
        }

    }

}
