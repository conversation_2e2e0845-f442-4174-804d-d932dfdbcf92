package com.datatech.slgzt.impl.service.recovery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.layout.ResRecoveryReqModel;
import com.datatech.slgzt.model.recovery.RecoveryBackupModel;
import com.datatech.slgzt.model.recovery.RecoveryObsModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.recovery.RecoveryResourceService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: 云备份策略回收
 * @author: LK
 * @create: 2025-06-05 15:59
 **/
@Slf4j
@Service
public class RecoveryBackupServiceImpl implements RecoveryResourceService {

    @Resource
    private PlatformService platformService;

    @Resource
    private RecoveryWorkOrderProductManager manager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void recoveryResource(RecoveryWorkOrderDTO dto, List<RecoveryWorkOrderProductDTO> recoveryWorkOrderProducts) {
        for (RecoveryWorkOrderProductDTO product : recoveryWorkOrderProducts) {
            ResRecoveryReqModel resRecoveryReqModel = new ResRecoveryReqModel();
            //参数封装
            List<ResRecoveryReqModel.ProductOrder> reqProductList = Lists.newArrayList();
            if (ProductTypeEnum.BACKUP.getCode().equals(product.getProductType())) {
                RecoveryBackupModel recoveryBackupModel = JSONObject.parseObject(product.getPropertySnapshot(), RecoveryBackupModel.class);
                //基础参数封装
                baseParamInit(recoveryBackupModel, resRecoveryReqModel, product, dto);
                //backup参数填充
                ResRecoveryReqModel.ProductOrder backupProductOrder = new ResRecoveryReqModel.ProductOrder();
                backupProductOrder.setProductOrderId(recoveryBackupModel.getProductOrderId().toString());
                backupProductOrder.setProductOrderType("BACKUP_DELETE");
                backupProductOrder.setProductType(ProductTypeEnum.BACKUP.getCode());
                backupProductOrder.setSubOrderId(String.valueOf(product.getSubOrderId()));
                ResRecoveryReqModel.Attrs backupAttrs = new ResRecoveryReqModel.Attrs();
                backupAttrs.setResourceId(recoveryBackupModel.getResourceId());
                backupProductOrder.setAttrs(backupAttrs);
                reqProductList.add(backupProductOrder);
            }
            //调用任务中心回收资源
            resRecoveryReqModel.setProductOrders(reqProductList);
            log.info("资源回收，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={},param={}", JSON.toJSON(dto.getId()), layoutCenter + layoutTaskInitUrl, JSON.toJSONString(resRecoveryReqModel));
            Mapper dataMapper= OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(resRecoveryReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "资源回收失败，callLayoutOrder--编排中心初始化返回结果失败");
            log.info("资源回收，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(dto.getId()), JSON.toJSON(dataMapper));
        }
        //把对应的产品都改成回收中状态
        List<Long> ids = recoveryWorkOrderProducts.stream().map(RecoveryWorkOrderProductDTO::getId).collect(Collectors.toList());
        manager.updateStatusByIds(ids, String.valueOf(RecoveryStatusEnum.RECOVERING.getType()));
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.BACKUP;
    }

    private void baseParamInit(RecoveryBackupModel recoveryBackupModel,
                               ResRecoveryReqModel resRecoveryReqModel,
                               RecoveryWorkOrderProductDTO product,
                               RecoveryWorkOrderDTO dto) {
        Long tenantId = platformService.getOrCreateTenantId(recoveryBackupModel.getBillId(), recoveryBackupModel.getRegionCode());
        //设置计费号
        resRecoveryReqModel.setAccount(recoveryBackupModel.getBillId());
        //设置业务code;
        resRecoveryReqModel.setSourceExtType(OrderTypeEnum.RECOVERY.getCode());
        //设置业务code
        resRecoveryReqModel.setBusinessCode("BACKUP_DELETE");
        //设置业务系统code
        resRecoveryReqModel.setBusinessSystemCode(dto.getBusinessSystemCode());
        //设置客户id
        resRecoveryReqModel.setCustomId(recoveryBackupModel.getCustomNo());
        //设置区域编码
        resRecoveryReqModel.setRegionCode(recoveryBackupModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resRecoveryReqModel.setSubOrderId(product.getSubOrderId());
        //设置租户id
        resRecoveryReqModel.setTenantId(tenantId);
        //设置userId
        resRecoveryReqModel.setUserId(dto.getCreatedBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resRecoveryReqModel.setTaskSource(4);
    }
}
