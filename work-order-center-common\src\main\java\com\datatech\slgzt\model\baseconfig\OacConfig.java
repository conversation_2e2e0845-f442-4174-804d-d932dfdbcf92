package com.datatech.slgzt.model.baseconfig;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * config配置表(OacConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-11-18 16:58
 */
@Data
@Accessors(chain = true)
public class OacConfig implements Serializable {
    private static final long serialVersionUID = -89340559849771319L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 配置类型(用于定义一组配置)
     */
    private String configType;
    /**
     * 配置编码
     */
    private String configCode;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 配置值
     */
    private String configValue;
    /**
     * 配置描述
     */
    private String configDesc;
    /**
     * 配置排序(生效于一组配置中的顺序)
     */
    private Long sort;
    /**
     * 创建人
     */
    private Long createdBy;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新人
     */
    private Long updatedBy;
    /**
     * 更新时间
     */
    private Date updatedTime;
    /**
     * 状态
     */
    private Long status;
    /**
     * 配置组
     */
    private String configGroup;
}