package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.RdsUserMapper;
import com.datatech.slgzt.dao.model.RdsUserDO;
import com.datatech.slgzt.model.query.RdsUserQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库用户DAO
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Repository
public class RdsUserDAO {

    @Resource
    private RdsUserMapper baseMapper;

    /**
     * 查询数据库用户列表
     */
    public List<RdsUserDO> list(RdsUserQuery query) {
        return baseMapper.selectList(
            Wrappers.<RdsUserDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getId()), RdsUserDO::getId, query.getId())
                .eq(ObjNullUtils.isNotNull(query.getRdsId()), RdsUserDO::getRdsId, query.getRdsId())
                .like(ObjNullUtils.isNotNull(query.getUserName()), RdsUserDO::getUserName, query.getUserName())
                .eq(ObjNullUtils.isNotNull(query.getGid()), RdsUserDO::getGid, query.getGid())
                .eq(ObjNullUtils.isNotNull(query.getEnabled()), RdsUserDO::getEnabled, query.getEnabled())
                .in(ObjNullUtils.isNotNull(query.getIdList()), RdsUserDO::getId, query.getIdList())
                .between(
                    ObjNullUtils.isNotNull(query.getStartTime()) && ObjNullUtils.isNotNull(query.getEndTime()),
                    RdsUserDO::getCreateTime,
                    query.getStartTime(),
                    query.getEndTime()
                )
                .orderByDesc(RdsUserDO::getCreateTime)
        );
    }

    /**
     * 新增数据库用户
     */
    public void insert(RdsUserDO rdsUserDO) {
        baseMapper.insert(rdsUserDO);
    }

    /**
     * 更新数据库用户
     */
    public void update(RdsUserDO rdsUserDO) {
        baseMapper.updateById(rdsUserDO);
    }

    /**
     * 删除数据库用户
     */
    public void delete(Long id) {
        baseMapper.deleteById(id);
    }
} 