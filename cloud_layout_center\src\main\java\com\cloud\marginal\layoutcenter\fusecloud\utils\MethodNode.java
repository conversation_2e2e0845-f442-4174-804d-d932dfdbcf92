package com.cloud.marginal.layoutcenter.fusecloud.utils;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class MethodNode {

    /**
     * 全类名
     */
    private String fullTypeName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 方法执行的参数
     */
    private HashMap<String,Object> methodParam;

    /**
     * 下一个方法节点
     */
    private MethodNode nextNode;

    /**
     * 当前方法执行结果与下一个方法执行需要的参数映射
     * key->当前方法执行结果的key
     * value->下一个方法需要的参数的key
     */
    private Map<String, String> resultToNextNodeParamMapping;

}