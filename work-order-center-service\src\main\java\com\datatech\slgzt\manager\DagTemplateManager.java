package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.query.DagTemplateQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * DAG模板管理接口
 */
public interface DagTemplateManager {
    
    /**
     * 创建DAG模板
     */
    void create(DagTemplateDTO dto);

    /**
     * 更新DAG模板
     */
    void update(DagTemplateDTO dto);

    /**
     * 删除DAG模板
     */
    void delete(String id);

    /**
     * 获取DAG模板详情
     */
    DagTemplateDTO getById(String id);

    /**
     * 根据名称获取DAG模板
     */
    DagTemplateDTO getByName(String name);

    /**
     * 查询DAG模板列表
     */
    List<DagTemplateDTO> list(DagTemplateQuery query);

    /**
     * 分页查询DAG模板
     */
    PageResult<DagTemplateDTO> page(DagTemplateQuery query);
} 