package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.RouteParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 路由适配管理
 */
@Component
@Slf4j
public class RouteMgAdapter extends BaseNorthInterfaceAdapter{

    /**
     * 创建路由
     */
    public TaskVO createRoute(String taskId, Integer taskSource){
        log.info("createRoute start");
        RouteParam createRouterRcDto = generateCreateRouteDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateRoute(),
                null,
                createRouterRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult,"create Route");
        return tasksVOResult.getEntity();
    }

    private RouteParam generateCreateRouteDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam vpcOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.ROUTE_CREATE.getCode());
        return JSONObject.parseObject(vpcOrder.getAttrs(), RouteParam.class);
    }
}
