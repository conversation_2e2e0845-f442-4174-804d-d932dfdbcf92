package com.datatech.slgzt.enums;


import com.datatech.slgzt.utils.ObjNullUtils;

/**
 *
 * 云平台枚举
 * <AUTHOR>
 */

public enum DomainCodeEnum {

    /**
     * 云区域类型
     */
    PLF_PROV_MOC_ZJ_VMWARE("plf_prov_moc_zj_vmware", "移动边缘云-VMware"),
    CLOUDST_PROV_JOINT("cloudst_prov_joint", "合营云"),
    CLOUDST_PROV_BUILD("cloudst_prov_build", "自建云"),

    PLF_PROC_NWC_ZJ_MNI("plf_prov_nwc_zj_mni", "网络云（创新资源池）"),
    PLF_PROC_NWC_ZJ_NFVO("plf_prov_nwc_zj_nfvo", "网络云（创新资源池）（新增）"),
    PLF_PROC_NWC_ZJ_PLF("plf_prov_nwc_zj_platform", "平台云"),
    PLF_PROC_NWC_ZJ_PLF_NEW("plf_prov_nwc_zj_plf", "平台云"),
    PLF_PROV_NWC_ZJ_ZTE("plf_prov_nwc_zj_zte", "网络边缘云（中兴）"),
    PLF_PROV_NWC_ZJ_HWS("plf_prov_nwc_zj_hws", "网络边缘云（华为）"),
    PLF_PROV_MOC_ZJ_HWS("plf_prov_moc_zj_hws", "移动边缘云（华为）"),
    UNKNOWN("unknown", "-"),
    ;

    private final String code;
    private final String desc;

    DomainCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static DomainCodeEnum getByCode(String code) {
        if (ObjNullUtils.isNotNull((code))) {
            for (DomainCodeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return DomainCodeEnum.UNKNOWN;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 是否是平台云(串行执行下发任务)
     */
    public static boolean isCloudPlatform(String domainCode) {
        return PLF_PROC_NWC_ZJ_PLF.getCode().equals(domainCode) ||
                PLF_PROC_NWC_ZJ_PLF_NEW.getCode().equals(domainCode);
    }

}