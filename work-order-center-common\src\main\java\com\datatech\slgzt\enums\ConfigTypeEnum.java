package com.datatech.slgzt.enums;

import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 配置表类型枚举
 */
@Getter
public enum ConfigTypeEnum {

    /**
     * 资源开通页面
     */
    OPEN_PAGE_SUBMIT("openPageSubmit", "资源开通页面提交模板"),

    /**
     * cpc的固定令牌
     */
    AUTH_TOKEN("authToken", "cpc的固定令牌"),

    /**
     * config配置类型
     */
    ROLE_CODE("role_code", "用户角色"),

    /**
     * 工作流流程
     */
    RESOURCE_CREATE_PROCESS("resource-create-process-1", "资源审批流程"),
    RESOURCE_CREATE_PROCESSOLD("resource-create-process", "旧资源审批流程"),
    RESOURCE_CHANGE_PROCESS("resource-change-process", "变更配置审批流程"),
    RESOURCE_RECOVERY_PROCESS("resource-recovery-process", "回收配置流程"),
    RESOURCE_DELAY_PROCESS("resource-delay-process", "延期配置流程"),
    RESOURCE_DELORDELAY_PROCESS("resource-delordelay-process", "回收/延期流程"),

    /**
     * 产品属性
     */
    IMAGEOS("imageOs", "镜像"),
    IMAGEVERSION("imageVersion", "镜像版本"),
    DATABASEVERSION("databaseVersion", "数据库版本"),
    TIME("time", "开通时间"),
    UNKNOWN("unknown", "-");

    private String code;
    private String message;

    ConfigTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static ConfigTypeEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (ConfigTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }

    /**
     * 根据业务流程查询config配置
     *
     * @return
     */
    public static ConfigTypeEnum findByActivityEnum(ActivityEnum.ActivityProcessEnum activiteProcessEnum) {
        if (activiteProcessEnum == null) {
            return ConfigTypeEnum.UNKNOWN;
        }
        switch (activiteProcessEnum) {
            case RESOURCE_PROCESS:
                return ConfigTypeEnum.RESOURCE_CREATE_PROCESS;
            case RESOURCE_RECOVERY_PROCESS:
                return ConfigTypeEnum.RESOURCE_RECOVERY_PROCESS;
            case RESOURCE_CHANGE_PROCESS:
                return ConfigTypeEnum.RESOURCE_CHANGE_PROCESS;
        }
        return ConfigTypeEnum.UNKNOWN;
    }

}
