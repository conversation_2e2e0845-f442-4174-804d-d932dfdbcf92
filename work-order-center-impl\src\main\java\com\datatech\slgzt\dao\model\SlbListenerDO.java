package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月13日 15:45:50
 */
@Data
@TableName("WOC_SLB_LISTENER")
public class SlbListenerDO implements Serializable {

    private static final long serialVersionUID = 1L;

    //主键ID
    @TableId(value = "ID", type = IdType.ID_WORKER)
    private String id;

    //slb监听的底层资源id
    @TableField("DEVICE_ID")
    private String deviceId;
    //监听名称
    @TableField("LISTENER_NAME")
    private String listenerName;

    //vpcId
    @TableField("VPC_ID")
    private String vpcId;

    //vpcName
    @TableField("VPC_NAME")
    private String vpcName;

    //资源详情ID
    @TableField("SLB_RESOURCE_DETAIL_ID")
    private Long slbResourceDetailId;

    //slb设备id
    @TableField("SLB_DEVICE_ID")
    private String slbDeviceId;

    //运行状态
    @TableField("RUNNING_STATUS")
    private String runningStatus;

    //slb监听器协议
    @TableField("SLB_LISTENER_PROTOCOL")
    private String slbListenerProtocolModel;

    //slb健康检查
    @TableField("SLB_HEALTH_CHECK")
    private String slbHealthCheckModel;

    //服务器组列表JSON
    @TableField("SLB_SERVER_GROUP")
    private String slbServerGroupModelList;

    //状态表示位置 0正常 1操作中
    @TableField("STATUS")
    private String status;

    //创建时间
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("TASK_STATUS_EXT")
    private String taskStatusExt;


}
