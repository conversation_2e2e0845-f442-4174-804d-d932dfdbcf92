package com.datatech.slgzt.dao.model.container;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@TableName("XIE_YUN_REPOSITORY")
public class XieYunRepositoryDO {

    @TableField("ID")
    private String id;

    /**
     * 谐云仓库id
     */
    @TableField("XIE_YUN_REPO_ID")
    private String xieYunRepoId;

    /**
     * 谐云制品id
     */
    @TableField("XIE_YUN_REGISTRY_ID")
    private String xieYunRegistryId;

    /**
     * 谐云组织id
     */
    @TableField("XIE_YUN_ORG_ID")
    private String xieYunOrgId;

    /**
     * 仓库名称
     */
    @TableField("REPO_NAME")
    private String repoName;

    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;

    /**
     * 1：删除,0：正常
     */
    @TableField("DELETED")
    private Boolean deleted;

}

