package com.datatech.slgzt.dao;

import com.datatech.slgzt.dao.mapper.McTaskMapper;
import com.datatech.slgzt.dao.model.McTaskDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月14日 17:23:55
 */
@Repository
public class McTaskDAO {


    @Resource
    private McTaskMapper mapper;


    public McTaskDO getById(String id) {
        return mapper.selectById(id);
    }
}
