package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datatech.slgzt.dao.mapper.ContainerQuotaMapper;
import com.datatech.slgzt.dao.model.container.ContainerQuotaDO;
import com.datatech.slgzt.enums.StatusEnum;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 容器配额DAO
 * <AUTHOR>
 * @description 容器配额数据访问对象
 * @date 2025年05月27日
 */
@Repository
public class ContainerQuotaDAO {

    @Resource
    private ContainerQuotaMapper mapper;

    /**
     * 插入容器配额记录
     * @param containerQuotaDO 容器配额数据对象
     */
    public void insert(ContainerQuotaDO containerQuotaDO) {
        mapper.insert(containerQuotaDO);
    }

    /**
     * 根据ID查询容器配额
     * @param id 主键ID
     * @return 容器配额数据对象
     */
    public ContainerQuotaDO selectById(String id) {
        return mapper.selectById(id);
    }

    /**
     * 根据工单ID查询容器配额列表
     * @param workOrderId 工单ID
     * @return 容器配额列表
     */
    public List<ContainerQuotaDO> selectByWorkOrderId(String workOrderId) {
        LambdaQueryWrapper<ContainerQuotaDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContainerQuotaDO::getWorkOrderId, workOrderId)
                   .eq(ContainerQuotaDO::getEnabled, StatusEnum.NORMAL.code());
        return mapper.selectList(queryWrapper);
    }

    /**
     * 根据子订单ID查询容器配额
     * @param subOrderId 子订单ID
     * @return 容器配额数据对象
     */
    public ContainerQuotaDO selectBySubOrderId(String subOrderId) {
        LambdaQueryWrapper<ContainerQuotaDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContainerQuotaDO::getSubOrderId, subOrderId)
                   .eq(ContainerQuotaDO::getEnabled, StatusEnum.NORMAL.code());
        return mapper.selectOne(queryWrapper);
    }



    /**
     * 查询容器配额列表
     * @param query 查询条件
     * @return 容器配额列表
     */
    public List<ContainerQuotaDO> selectList(ContainerQuotaQuery query) {
        QueryWrapper<ContainerQuotaDO> queryWrapper = buildQueryWrapper(query);
        return mapper.selectList(queryWrapper);
    }

    /**
     * 构建查询条件
     * @param query 查询参数
     * @return 查询包装器
     */
    private QueryWrapper<ContainerQuotaDO> buildQueryWrapper(ContainerQuotaQuery query) {
        QueryWrapper<ContainerQuotaDO> queryWrapper = new QueryWrapper<>();

        // 默认查询启用状态的记录
        queryWrapper.eq("ENABLED", StatusEnum.NORMAL.code());

        // 配额名称模糊查询
        if (StringUtils.hasText(query.getCqName())) {
            queryWrapper.like("CQ_NAME", query.getCqName());
        }

        // 业务系统ID精确查询
        if (ObjNullUtils.isNotNull(query.getBusinessSystemId())) {
            queryWrapper.eq("BUSINESS_SYSTEM_ID", query.getBusinessSystemId());
        }

        // 业务系统ID精确查询
        if (ObjNullUtils.isNotNull(query.getBusinessSystemIds())) {
            queryWrapper.in("BUSINESS_SYSTEM_ID", query.getBusinessSystemIds());
        }

        // 业务系统名称模糊查询
        if (StringUtils.hasText(query.getBusinessSystemName())) {
            queryWrapper.like("BUSINESS_SYSTEM_NAME", query.getBusinessSystemName());
        }

        // 工单ID精确查询
        if (StringUtils.hasText(query.getWorkOrderId())) {
            queryWrapper.eq("WORK_ORDER_ID", query.getWorkOrderId());
        }

        // 状态查询
        if (StringUtils.hasText(query.getStatus())) {
            queryWrapper.eq("STATUS", query.getStatus());
        }

        // 云平台编码查询
        if (StringUtils.hasText(query.getDomainCode())) {
            queryWrapper.eq("DOMAIN_CODE", query.getDomainCode());
        }

        // 资源池编码查询
        if (StringUtils.hasText(query.getRegionCode())) {
            queryWrapper.eq("REGION_CODE", query.getRegionCode());
        }

        // 4A账号模糊查询
        if (StringUtils.hasText(query.getA4Account())) {
            queryWrapper.like("A4_ACCOUNT", query.getA4Account());
        }

        // 申请用户ID精确查询
        if (ObjNullUtils.isNotNull(query.getApplyUserId())) {
            queryWrapper.eq("APPLY_USER_ID", query.getApplyUserId());
        }

        // 申请用户名称模糊查询
        if (StringUtils.hasText(query.getApplyUserName())) {
            queryWrapper.like("APPLY_USER_NAME", query.getApplyUserName());
        }

        // 子订单ID精确查询
        if (StringUtils.hasText(query.getSubOrderId())) {
            queryWrapper.eq("SUB_ORDER_ID", query.getSubOrderId());
        }

        // 云平台名称模糊查询
        if (StringUtils.hasText(query.getDomainName())) {
            queryWrapper.like("DOMAIN_NAME", query.getDomainName());
        }

        // 资源池ID精确查询
        if (StringUtils.hasText(query.getRegionId())) {
            queryWrapper.eq("REGION_ID", query.getRegionId());
        }

        // 资源池名称模糊查询
        if (StringUtils.hasText(query.getRegionName())) {
            queryWrapper.like("REGION_NAME", query.getRegionName());
        }

        // 云类型编码精确查询
        if (StringUtils.hasText(query.getCatalogueDomainCode())) {
            queryWrapper.eq("CATALOGUE_DOMAIN_CODE", query.getCatalogueDomainCode());
        }

        // 4A手机号模糊查询
        if (StringUtils.hasText(query.getA4Phone())) {
            queryWrapper.like("A4_PHONE", query.getA4Phone());
        }

        // 申请时长精确查询
        if (StringUtils.hasText(query.getApplyTime())) {
            queryWrapper.eq("APPLY_TIME", query.getApplyTime());
        }

        // vCpus精确查询
        if (ObjNullUtils.isNotNull(query.getVCpus())) {
            queryWrapper.eq("V_CPUS", query.getVCpus());
        }

        // 内存大小精确查询
        if (ObjNullUtils.isNotNull(query.getRam())) {
            queryWrapper.eq("RAM", query.getRam());
        }

        // GPU算力精确查询
        if (ObjNullUtils.isNotNull(query.getGpuRatio())) {
            queryWrapper.eq("GPU_RATIO", query.getGpuRatio());
        }

        // GPU显存大小精确查询
        if (ObjNullUtils.isNotNull(query.getGpuVirtualMemory())) {
            queryWrapper.eq("GPU_VIRTUAL_MEMORY", query.getGpuVirtualMemory());
        }

        // 物理GPU卡精确查询
        if (ObjNullUtils.isNotNull(query.getGpuCore())) {
            queryWrapper.eq("GPU_CORE", query.getGpuCore());
        }

        // 虚拟GPU卡精确查询
        if (ObjNullUtils.isNotNull(query.getGpuVirtualCore())) {
            queryWrapper.eq("GPU_VIRTUAL_CORE", query.getGpuVirtualCore());
        }

        // 创建时间范围查询
        if (StringUtils.hasText(query.getCreateTimeStart())) {
            queryWrapper.ge("CREATE_TIME", query.getCreateTimeStart());
        }
        if (StringUtils.hasText(query.getCreateTimeEnd())) {
            queryWrapper.le("CREATE_TIME", query.getCreateTimeEnd());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc("CREATE_TIME");

        return queryWrapper;
    }
}
