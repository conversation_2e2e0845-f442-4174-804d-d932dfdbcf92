package com.datatech.slgzt.model.query;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-03 15:03
 **/
@Data
public class CustomQuery {

    private Long currentUserId;

    private Integer pageSize;

    private Integer pageNum;

    private String customName;

    private String customNo;

    private String contactName;

    private String billId;

    private String email;

    private String contactMobile;

    private LocalDateTime createTimeStart;

    private LocalDateTime createTimeEnd;

    private List<String> customIds;
}
