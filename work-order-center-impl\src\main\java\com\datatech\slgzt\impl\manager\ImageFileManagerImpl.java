package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.ImageFileConvert;
import com.datatech.slgzt.dao.ImageFileDAO;
import com.datatech.slgzt.dao.model.ImageFileDO;
import com.datatech.slgzt.manager.ImageFileManager;
import com.datatech.slgzt.model.dto.ImageFileDTO;
import com.datatech.slgzt.model.query.ImageFileQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 11:19
 **/
@Service
public class ImageFileManagerImpl implements ImageFileManager {

    @Resource
    private ImageFileDAO dao;

    @Resource
    private ImageFileConvert convert;

    @Override
    public void insert(ImageFileDTO dto) {
        dao.insert(convert.dto2DO(dto));
    }

    @Override
    public void updateById(ImageFileDTO dto) {
        dao.updateById(convert.dto2DO(dto));
    }

    @Override
    public ImageFileDTO getById(Long id) {
        return convert.do2DTO(dao.getById(id));
    }

    @Override
    public PageResult<ImageFileDTO> page(ImageFileQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ImageFileDO> list = dao.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2DTO);
    }

    @Override
    public void deleteById(Long id) {
        dao.deleteById(id);
    }

    @Override
    public void updateByMd5(String md5, String preSignedObjectUrl, Boolean uploadCompleted) {
        dao.updateByMd5(md5, preSignedObjectUrl, uploadCompleted);
    }
}
