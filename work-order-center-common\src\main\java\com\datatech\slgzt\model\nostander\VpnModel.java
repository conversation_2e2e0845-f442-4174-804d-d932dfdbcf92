package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.io.Serializable;


/**
 * @program: workordercenterproject
 * @description: vpn模型
 * @author: LK
 * @create: 2025-06-10 10:18
 **/
@Data
public class VpnModel extends BaseProductModel {

    /**
     * 开通数量
     */
    private Integer openNum = 1;

    /**
     * vpn名称
     */
    private String name;

    /**
     * 允许同时连接的最大客户端数量
     */
    private Integer maxConnection;

    /**
     * VPC编号
     */
    private String vpcId;

    /**
     * 子网编号
     */
    private String subnetId;

    /**
     * 带宽
     */
    private Integer bandwidth;

    /**
     * 申请时长
     */
    private String applyTime;

    private PlaneNetworkModel planeNetworkModel;
}
