package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 备份策略模型
 * @author: LK
 * @create: 2025-06-04 14:38
 **/
@Data
public class BackupModel extends BaseProductModel {

    /**
     * 策略名称
     */
    private String jobName;

    /**
     * 备份类型 ECS：云主机 EVS：云硬盘
     */
    private String backupType;

    /**
     * 备份频率 weeks/days
     * 若type=“days”时填写，表示每天都备份执行
     */
    private String frequency;

    /**
     * 周数
     * 若type=“weeks”时填写：每周几执行，1-7分别代表周一到周日
     */
    private Integer daysOfWeek;

    /**
     * 需要备份的云主机/云硬盘id
     */
    private List<String> objectIdList;

    /**
     * 云类型
     */
    private String catalogueDomainCode;


    /**
     * 云平台
     */
    private String domainCode;

    private Integer openNum;

}
