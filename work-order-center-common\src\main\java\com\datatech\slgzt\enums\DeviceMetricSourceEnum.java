package com.datatech.slgzt.enums;


/**
 * 显卡指标枚举
 * <AUTHOR>
 */
public enum DeviceMetricSourceEnum {

    /**
     * 驱动科技的物理显卡
     */
    QD_PHYSICAL_SOURCE("QD-physical", "物理显卡"),

    /**
     *  驱动科技的虚拟显卡
     */
    QD_VIRTUAL_SOURCE("QD-virtual","虚拟显卡"),


    /**
     *  内部的物理显卡
     */
    NPU_CHIP_SOURCE("NB-physical","物理显卡"),
    /**
     * 未知
     */
    UNKNOWN("unknown", "-");

    private final String code;
    private final String desc;

    DeviceMetricSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }



    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
