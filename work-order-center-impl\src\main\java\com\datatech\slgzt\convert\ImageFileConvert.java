package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.ImageFileDO;
import com.datatech.slgzt.model.dto.ImageFileDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 11:04
 **/
@Mapper(componentModel = "spring")
public interface ImageFileConvert {

    ImageFileDTO do2DTO(ImageFileDO imageFileDO);

    List<ImageFileDTO> do2DTOs(List<ImageFileDO> imageFileDOs);

    ImageFileDO dto2DO(ImageFileDTO imageFileDTO);
}
