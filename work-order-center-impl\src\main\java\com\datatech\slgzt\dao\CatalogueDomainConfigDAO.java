package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.CatalogueDomainConfigMapper;
import com.datatech.slgzt.dao.model.CatalogueDomainConfigDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 目录域配置DAO
 */
@Repository
public class CatalogueDomainConfigDAO {

    @Resource
    private CatalogueDomainConfigMapper catalogueDomainConfigMapper;

    /**
     * 根据业务编码获取启用的域编码列表
     */
    public List<CatalogueDomainConfigDO> listByBusinessCode(String businessCode) {
        return catalogueDomainConfigMapper.selectList(
            Wrappers.<CatalogueDomainConfigDO>lambdaQuery()
                .eq(CatalogueDomainConfigDO::getBusinessCode, businessCode)
        );
    }

} 