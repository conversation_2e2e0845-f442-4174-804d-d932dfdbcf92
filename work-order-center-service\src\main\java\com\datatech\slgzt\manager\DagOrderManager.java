package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.query.DagOrderQuery;
import com.datatech.slgzt.utils.PageResult;

/**
 * DAG工单管理接口
 */
public interface DagOrderManager {
    /**
     * 新增DAG工单
     */
    String add(DagOrderDTO dto);

    /**
     * 更新DAG工单
     */
    void update(DagOrderDTO dto);

    /**
     * 删除DAG工单
     */
    void delete(String id);

    /**
     * 获取DAG工单详情
     */
    DagOrderDTO getById(String id);

    /**
     * 分页查询DAG工单
     */
    PageResult<DagOrderDTO> page(DagOrderQuery query);
} 