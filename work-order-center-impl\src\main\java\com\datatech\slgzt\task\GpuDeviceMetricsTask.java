package com.datatech.slgzt.task;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.manager.DeviceCardMetricsManager;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.manager.DeviceVirtualInfoManager;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.service.device.DeviceGupInfoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class GpuDeviceMetricsTask {
    @Resource
    private DeviceGupInfoService deviceGupInfoService;

    @Resource
    private DeviceGpuInfoManager deviceGpuInfoManager;

    @Resource
    private DeviceVirtualInfoManager deviceVirtualInfoManager;

    @Resource
    private DeviceCardMetricsManager deviceCardMetricsManager;



    /**
     * 执行完之后等5分钟执行一次
     */
    //@Scheduled(cron = "0 */5 * * * ?")
    public void calculateGpuDeviceMetricUpdateTask() {
        try {
            //插入数据
            List<DeviceCardMetricsDTO> deviceCardMetricList = deviceGupInfoService.queryDeviceCardMetrics();
            deviceCardMetricsManager.saveBatch(deviceCardMetricList);
            //同步后更新每个显卡的指标数据
            deviceCardMetricList.forEach(deviceCardMetricsDTO -> {
                //类型为空跳过
                if (deviceCardMetricsDTO.getDeviceType() == null) {
                    return;
                }
                try {
                    String deviceType = deviceCardMetricsDTO.getDeviceType();
                    if (Lists.newArrayList("VR").contains(deviceType)) {
                        DeviceVirtualInfoDTO dto = new DeviceVirtualInfoDTO();
                        dto.setDeviceId(deviceCardMetricsDTO.getDeviceId());
                        dto.setLastPeriod(JSON.toJSONString(deviceCardMetricsDTO));
                        deviceVirtualInfoManager.updateLastByDeviceId(dto);
                    }
                    if (Lists.newArrayList("NPU", "GPU").contains(deviceType)) {
                        DeviceGpuInfoDTO dto = new DeviceGpuInfoDTO();
                        dto.setDeviceId(deviceCardMetricsDTO.getDeviceId());
                        dto.setLastPeriod(JSON.toJSONString(deviceCardMetricsDTO));
                        deviceGpuInfoManager.updateLastByDeviceId(dto);
                    }
                } catch (Exception e) {
                    log.error("更新显卡指标数据异常", e);
                }
            });
        } catch (Exception e) {
            log.error("更新数据指标", e);
        }
    }


    /**
     * 定时同步qd科技虚拟卡数据
     每天0点执行一次
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void syncQdTechVirtualCardTask() {
        try {
            deviceGupInfoService.syncRemoteDeviceDataInfo();
        } catch (Exception e) {
            log.error("同步qd科技虚拟卡数据异常", e);
        }
    }

}
