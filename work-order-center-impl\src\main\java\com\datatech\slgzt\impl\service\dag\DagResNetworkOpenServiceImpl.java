package com.datatech.slgzt.impl.service.dag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datatech.slgzt.convert.DagNetworkResOpenServiceConvert;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.model.network.NetworkOrder;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.enums.network.NetworkPrefixEnum;
import com.datatech.slgzt.enums.network.NetworkStatusEnum;
import com.datatech.slgzt.handle.ResourceHandle;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.model.DagResOpenResultDTO;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.network.CreateNetworkRcDTO;
import com.datatech.slgzt.model.dto.network.CreateNetworkSubnetRcDTO;
import com.datatech.slgzt.model.nostander.NetworkUnTaskModel;
import com.datatech.slgzt.model.nostander.SubnetModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.dag.DagResOpenService;
import com.datatech.slgzt.utils.GatewayCalculatorUtil;
import com.datatech.slgzt.utils.HttpClientUtil;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * DAG网络资源开通服务实现
 *
 * <AUTHOR>
 * @description 算力编排网络资源开通实现
 * @date 2025年 05月20日 11:30:42
 */
@Service
@Slf4j
public class DagResNetworkOpenServiceImpl implements DagResOpenService {

    @Resource
    private PlatformService platformService;

    @Resource
    private DagOrderManager dagOrderManager;

    @Resource
    private NetworkOrderMapper networkMapper;

    @Resource
    private NetworkSubnetOrderMapper subnetMapper;

    @Resource
    private ResourceHandle resourceHandle;

    @Resource
    private RedissonClient redissonClient;

    private static final String redisKeyPrefix = "dag:network:";

    @Resource
    private DagNetworkResOpenServiceConvert convert;

    @Override
    @Transactional
    public Object openResource(DagProductDTO productDTO) {
        DagResOpenResultDTO resultDTO = new DagResOpenResultDTO();
        resultDTO.setNewOpen(false);
        resultDTO.setSubnetDagId2Id(new HashMap<>());

        // 验证产品类型
        String productType = productDTO.getProductType();
        Precondition.checkArgument(ProductTypeEnum.NETWORK.getCode().equals(productType), "算力编排不支持该类型开通");

        // 获取订单信息
        DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());
        NetworkUnTaskModel networkModel = JSON.parseObject(productDTO.getPropertySnapshot(), NetworkUnTaskModel.class);
        Long tenantId = platformService.getOrCreateTenantId(orderDTO.getBillId(), networkModel.getRegionCode());

        // 查询网络是否存在
        LambdaQueryWrapper<NetworkOrder> networkQueryWrapper = new LambdaQueryWrapper<>();
        networkQueryWrapper
                .eq(NetworkOrder::getTenantId, tenantId)
                .eq(NetworkOrder::getRegionCode, orderDTO.getRegionCode())
                .eq(NetworkOrder::getName, networkModel.getName())
                .eq(NetworkOrder::getStatus,"SUCCESS")
                .eq(NetworkOrder::getDeleted, 1);
        NetworkOrder networkDO = networkMapper.selectOne(networkQueryWrapper);

        if (networkDO != null) {
            for (SubnetModel subnetModel : networkModel.getSubnets()) {
                LambdaQueryWrapper<NetworkSubnetOrder> subnetQueryWrapper = new LambdaQueryWrapper<>();
                subnetQueryWrapper
                        .eq(NetworkSubnetOrder::getNetworkId, networkDO.getId())
                        .eq(NetworkSubnetOrder::getCidr, subnetModel.getCidr())
                        .eq(NetworkSubnetOrder::getIpVersion, subnetModel.getIpVersion())
                        .eq(NetworkSubnetOrder::getDeleted, 1);
                NetworkSubnetOrder subnetDO = subnetMapper.selectOne(subnetQueryWrapper);

                if (subnetDO != null) {
                    resultDTO.getSubnetDagId2Id().put(subnetModel.getDagId(), subnetDO.getId());
                } else {
                    // 不支持
                    throw new RuntimeException("network不支持增加网络");
//                    subnetDO = convertNetworkSubnetDO(networkDO.getId(), networkDO.getName(), subnetModel);
//                    subnetMapper.insert(subnetDO);
//                    createSubnetByHttp(subnetDO, networkDO, orderDTO);
//
//
//                    resultDTO.setNewOpen(true);
//                    resultDTO.getSubnetDagId2Id().put(subnetModel.getDagId(), subnetDO.getId());
//                    // 设置Redis缓存
//                    RMap<String, String> map = redissonClient
//                            .getMap(redisKeyPrefix + networkDO.getId() + ":" + subnetDO.getId());
//                    map.put("subOrderId", String.valueOf(productDTO.getSubOrderId()));
//                    map.put("jobExecutionId", orderDTO.getJobExecutionId().toString());
//                    map.expire(30L, TimeUnit.DAYS);

                }
            }

        } else {
            // 网络不存在，需要创建
            resultDTO.setNewOpen(true);

            // 创建网络订单记录
            networkDO = convertNetworkOrder(networkModel, orderDTO, tenantId);

            // 创建子网订单记录
            List<NetworkSubnetOrder> subnetDOs = convertSubnetDOs(networkDO, networkModel);
            if (!subnetDOs.isEmpty()) {
                subnetMapper.batchInsertNetworkSubnet(subnetDOs);
            }

            // 调用底层接口创建网络
            createNetworkByHttp(networkDO, networkModel, orderDTO);

            // 调用底层接口创建子网
            List<SubnetModel> subnets = networkModel.getSubnets();
            for (int i = 0; i < subnetDOs.size() && i < subnets.size(); i++) {
                NetworkSubnetOrder subnetOrder = subnetDOs.get(i);
                SubnetModel subnetObj = subnets.get(i);


                // com.datatech.slgzt.impl.network.NetworkMessageServiceImpl.consumeNetworkMessage会去创建子网
//                createSubnetByHttp(subnetOrder, networkDO, orderDTO);

                // 设置映射关系
                resultDTO.getSubnetDagId2Id().put(subnetObj.getDagId(), subnetOrder.getId());
                // 设置Redis缓存
                RMap<String, String> map = redissonClient.getMap(redisKeyPrefix + networkDO.getId() + ":" + subnetOrder.getId());
                map.put("subOrderId", String.valueOf(productDTO.getSubOrderId()));
                map.put("jobExecutionId", orderDTO.getJobExecutionId().toString());
                map.expire(30L, TimeUnit.DAYS);
            }

        }

        return resultDTO;
    }

    /**
     * 注册
     */
    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.NETWORK;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {
        // DAG网络开通暂不需要处理布局任务通知
    }

    /**
     * 通过HTTP调用创建网络
     */
    private void createNetworkByHttp(NetworkOrder networkDO, NetworkUnTaskModel networkModel, DagOrderDTO orderDTO) {
        CreateNetworkRcDTO createNetworkRcDto = new CreateNetworkRcDTO();
        createNetworkRcDto.setBillId(networkDO.getAccount());
        createNetworkRcDto.setOrderId(networkDO.getId());
        createNetworkRcDto.setGlobalId(networkDO.getId());
        createNetworkRcDto.setRegionCode(networkDO.getRegionCode());
        createNetworkRcDto.setName(networkDO.getName());
        createNetworkRcDto.setNetworkType(networkDO.getNetworkType());
        if (StringUtils.hasText(networkModel.getVlan())) {
            createNetworkRcDto.setSegmentationId(networkModel.getVlan());
        }
        createNetworkRcDto.setShared(false);
        createNetworkRcDto.setExternal(false);
        createNetworkRcDto.setAdminStateUp(true);
        createNetworkRcDto.setDescription("DAG算力编排创建");
        List<String> aliasZoneIds = new ArrayList<>();
        aliasZoneIds.add(orderDTO.getAzCode());
        createNetworkRcDto.setAliasZoneIds(aliasZoneIds);

        log.info("DAG network创建请求参数 param:{}", JSONObject.toJSONString(createNetworkRcDto));
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getNetworkCreate();
        Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(createNetworkRcDto), getHeaders());
        log.info("DAG network创建返回参数 response:{}", response);

        Integer code = (Integer) response.get("code");
        String json = (String) response.get("json");
        if (code != 200) {
            networkDO.setStatus(NetworkStatusEnum.ERROR.getType());
            networkDO.setOverallStatus(NetworkStatusEnum.ERROR.getType());
            networkDO.setMessage(JSONObject.toJSONString(response));
            log.error("network创建失败 error {}", JSONObject.toJSONString(response));
        } else {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String success = jsonObject.getString("success");
            if (!"1".equals(success)) {
                String message = jsonObject.get("message").toString();
                networkDO.setStatus(NetworkStatusEnum.ERROR.getType());
                networkDO.setOverallStatus(NetworkStatusEnum.ERROR.getType());
                networkDO.setMessage(message);
                log.error("network创建失败 error {}", message);
                throw new RuntimeException("DAG网络创建失败: " + message);
            } else if (jsonObject.containsKey("entity")) {
                // todo 这里什么逻辑？创建的对象在底层已经存在了？那会有kafka回调么，会消费kafka消息，然后创建子网么
                JSONObject entity = jsonObject.getJSONObject("entity");
                String resourceId = entity.getString("resourceId");
                networkDO.setResourceId(resourceId);
            }
        }

        networkMapper.insert(networkDO);
    }

    /**
     * 通过HTTP调用创建子网
     */
    private void createSubnetByHttp(NetworkSubnetOrder subnetDO, NetworkOrder networkDO, DagOrderDTO orderDTO) {
        CreateNetworkSubnetRcDTO rcDto = new CreateNetworkSubnetRcDTO();
        rcDto.setRegionCode(networkDO.getRegionCode());
        rcDto.setBillId(orderDTO.getBillId());
        rcDto.setName(subnetDO.getSubnetName());
        rcDto.setCidr(subnetDO.getCidr());
        rcDto.setIpVersion(getIpVersion(subnetDO.getIpVersion()));
        rcDto.setIpVersionStr(subnetDO.getIpVersion());
        rcDto.setNetGlobalId(networkDO.getId());
        rcDto.setSystemSource(networkDO.getSystemSource());
        rcDto.setGlobalId(subnetDO.getId());
        rcDto.setOrderId(subnetDO.getId());
        rcDto.setGatewayIp(subnetDO.getGateway());

        log.info("DAG network子网创建入参：{}", JSONObject.toJSONString(rcDto));
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getSubnetCreate();
        Map<String, Object> response = HttpClientUtil.post(requestUrl, JSONObject.toJSONString(rcDto), getHeaders());
        log.info("DAG network子网创建返回参数 response:{}", response);

        Integer code = (Integer) response.get("code");
        String json = (String) response.get("json");
        if (code != 200) {
            String message = "子网创建失败";
            if (StringUtils.hasText(json)) {
                JSONObject jsonObject = JSONObject.parseObject(json);
                message = jsonObject.get("message").toString();
            }
            throw new RuntimeException("DAG子网创建失败: " + message);
        }
    }

    /**
     * 获取IP版本号
     */
    private Integer getIpVersion(String ipVersionStr) {
        if ("IPv4".equals(ipVersionStr)) {
            return 4;
        } else {
            return 6;
        }
    }

    /**
     * 获取HTTP请求头
     */
    private Map<String, String> getHeaders() {
        Map<String, String> header = new HashMap<>();
        header.put("RemoteUser", "DAG");
        header.put("Content-Type", "application/json");
        return header;
    }

    /**
     * 创建网络订单记录
     */
    private NetworkOrder convertNetworkOrder(NetworkUnTaskModel networkModel, DagOrderDTO orderDTO, Long tenantId) {
        NetworkOrder networkOrder = new NetworkOrder();
        String id = UuidUtil.getGid(NetworkPrefixEnum.NET.getType());
        networkOrder.setId(id);
        networkOrder.setCreatedBy(orderDTO.getCreatedBy()); // DAG创建标识
        networkOrder.setCreatedTime(new Date());
        networkOrder.setName(networkModel.getName());
        networkOrder.setOrderId(orderDTO.getId());
        networkOrder.setOrderCode(orderDTO.getOrderCode());

        List<SubnetModel> subnets = networkModel.getSubnets();
        networkOrder.setSubnetNum(subnets.size());

        networkOrder.setTenantId(tenantId);
        networkOrder.setAccount(orderDTO.getBillId());
        networkOrder.setRegionCode(orderDTO.getRegionCode());
        networkOrder.setAzCode(orderDTO.getAzCode());
        networkOrder.setSystemSource(networkModel.getSystemSource());
        networkOrder.setCatalogueDomainCode(orderDTO.getCatalogueDomainCode());
        networkOrder.setCatalogueDomainName(orderDTO.getCatalogueDomainName());
        networkOrder.setDomainCode(orderDTO.getDomainCode());
        networkOrder.setDomainName(orderDTO.getDomainName());
        // networkOrder.setFunctionalModule();
        networkOrder.setNetworkType(networkModel.getNetworkType());
        networkOrder.setPlane(networkModel.getPlane());
        networkOrder.setDeleted(1);
        networkOrder.setStatus(NetworkStatusEnum.EXECUTING.getType());
        networkOrder.setOverallStatus(NetworkStatusEnum.EXECUTING.getType());
        networkOrder.setSourceType(SourceTypeEnum.DAG.getPrefix());
        networkOrder.setDescription("DAG算力编排创建");
        networkOrder.setVlanId(networkModel.getVlanId());
        networkOrder.setVlan(networkModel.getVlan());
        networkOrder.setDetail(networkModel.getDetail());
        // todo
        networkOrder.setTenantName("dag");
        networkOrder.setApplyUserName("dag");
        networkOrder.setBusinessSysId(Long.valueOf(orderDTO.getBusinessSystemId()));
        networkOrder.setBusinessSysName(orderDTO.getBusinessSystemName());
        networkOrder.setModuleId(orderDTO.getModuleId());
        networkOrder.setModuleName(orderDTO.getModuleName());
        return networkOrder;
    }

    /**
     * 创建子网订单记录列表
     */
    private List<NetworkSubnetOrder> convertSubnetDOs(NetworkOrder networkOrder, NetworkUnTaskModel networkModel) {
        List<NetworkSubnetOrder> list = new ArrayList<>();

        List<SubnetModel> subnets = networkModel.getSubnets();
        for (SubnetModel subnetObj : subnets) {
            list.add(convertNetworkSubnetDO(networkOrder.getId(), networkOrder.getName(), subnetObj));
        }
        return list;
    }

    private NetworkSubnetOrder convertNetworkSubnetDO(String networkId, String networkName, SubnetModel subnetModel) {
        NetworkSubnetOrder subnetDO = new NetworkSubnetOrder();
        String id = UuidUtil.getGid(NetworkPrefixEnum.SUB.getType());
        subnetDO.setId(id);
        subnetDO.setInstanceId("dag" + id); // DAG标识
        subnetDO.setLevel2InstanceId("dag" + id); // DAG标识
        subnetDO.setNetworkId(networkId);
        subnetDO.setCreatedTime(new Date());
        String cidr = subnetModel.getCidr();
        subnetDO.setSubnetName(networkName + "_" + subnetModel.getIpVersion());
        subnetDO.setCidr(cidr);
        subnetDO.setGateway(GatewayCalculatorUtil.getGateway(cidr));
        subnetDO.setIpVersion(subnetModel.getIpVersion());
        subnetDO.setDeleted(1);
        subnetDO.setStatus(NetworkStatusEnum.PENDING.getType());
        subnetDO.setDescription("DAG算力编排创建");
        subnetDO.setUuid(subnetModel.getUuid());
        return subnetDO;
    }
}
