package com.cloud.marginal.layoutcenter.controller.tasknode;

import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Since 2023/5/15 09:41
 */
@RestController
@RequestMapping(VersionConstant.V1+"/node")
public class TaskNodeContorller {

    @Autowired
    private LayoutTaskNodeService layoutTaskNodeService;

    @PutMapping("/retry")
    public CecResult retry(@RequestParam("taskId") String taskId){
        layoutTaskNodeService.retry(taskId);
        return CecResult.success();
    }
}
