package com.datatech.slgzt.impl;

import com.datatech.slgzt.dao.McTaskDAO;
import com.datatech.slgzt.dao.model.McTaskDO;
import com.datatech.slgzt.service.McTaskService;
import com.datatech.slgzt.utils.Precondition;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月14日 17:26:19
 */
@Service
public class McTaskServiceImpl implements McTaskService {

    @Resource
    private McTaskDAO mcTaskDAO;


    @Override
    public String getById(String id) {
        McTaskDO taskDO = mcTaskDAO.getById(id);
        return taskDO.getStatus();
    }

    @Override
    public void checkStatus(String taskId) {
        final int MAX_RETRIES = 30; // 最多尝试20次，约60秒
        int retryCount = 0;
        try {
            while (retryCount < MAX_RETRIES) {
                Thread.sleep(3000);
                McTaskDO taskDO = mcTaskDAO.getById(taskId);
                String taskStatus = taskDO.getStatus();
                //如果任务状态是成功的
                if ("SUCCESS".equals(taskStatus)) {
                    break;
                }
                //如果是执行中
                if ("EXECUTING".equals(taskStatus)) {
                    retryCount++;
                    continue;
                }
                ArrayList<String> statusList = Lists.newArrayList("SUCCESS","EXECUTING");
                //如果是其他类型报错
                if (!statusList.contains(taskStatus)) {
                    Precondition.checkArgument(false, "底层调用失败: " + taskDO.getMessage());
                }
            }
            // 达到最大尝试次数还未成功
            if (retryCount >= MAX_RETRIES) {
                Precondition.checkArgument(false, "底层调用失败");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("任务等待被中断", e);
        }
    }
}
