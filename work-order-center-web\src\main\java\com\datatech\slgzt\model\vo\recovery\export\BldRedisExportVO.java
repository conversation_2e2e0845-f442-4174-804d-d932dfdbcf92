package com.datatech.slgzt.model.vo.recovery.export;

import com.alibaba.fastjson.annotation.JSONField;
import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: 宝兰德redis
 * @author: LK
 * @create: 2025-07-12 16:28
 **/
@Data
public class BldRedisExportVO {

    /**
     * redis实例名称
     */
    @ExcelExportHeader("实例名称")
    private String name;

    /**
     * redis实例ip
     */
    @ExcelExportHeader("实例ip")
    private String ip;

    /**
     * CPU架构
     */
    @ExcelExportHeader("CPU架构")
    private String cpuArchitecture;

    /**
     * 业务系统名称
     */
    @ExcelExportHeader("业务系统名称")
    private String businessSysName;

    /**
     * 用户名
     */
    @ExcelExportHeader("用户名")
    private String username;

    /**
     * 密码
     */
    @ExcelExportHeader("密码")
    private String password;

    /**
     * 申请时长
     */
    @ExcelExportHeader("申请时长")
    private String applyTimeCn;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;
}
