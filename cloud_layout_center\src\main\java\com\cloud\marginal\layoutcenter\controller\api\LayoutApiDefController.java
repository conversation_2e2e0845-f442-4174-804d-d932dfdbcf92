package com.cloud.marginal.layoutcenter.controller.api;

import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.model.dto.layout.SaveApiDef;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutApiDefService;
import com.cloud.marginal.model.dto.layout.ApiDefDto;
import com.cloud.marginal.model.vo.layout.ApiDefVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(VersionConstant.V1)
public class LayoutApiDefController {

    @Resource
    LayoutApiDefService layoutApiDefService;

    /**
     * 创建编排api配置
     */
    @PostMapping("createApiDef")
    public CecResult createApiDef(@Validated @RequestBody SaveApiDef layoutApiDef){
       return layoutApiDefService.createApiDef(layoutApiDef);
    }

    /**
     * 修改编排api配置
     */
    @PostMapping("updateApiDef")
    public CecResult updateApiDef(@Validated @RequestBody SaveApiDef layoutApiDef){
        return layoutApiDefService.updateApiDef(layoutApiDef);
    }


    /**
     * 删除编排api配置
     */
    @DeleteMapping("deleteApiDef")
    public CecResult deleteApiDef(@RequestParam(name = "apiDefId") String apiDefId){
        return layoutApiDefService.deleteApiDef(apiDefId);
    }
    /**
     * 查询编排api配置列表
     * @param apiName api名称

     */
    @GetMapping("/listApiDef")
    public CecResult<List<ApiDefVO>> listApiDef(@RequestParam(required = false)  String apiName) {

        List<ApiDefVO> apiDefs = layoutApiDefService.listApiDef(apiName);
        return CecResult.success(apiDefs);
    }

    /**
     * 查询编排api配置列表，分页
     */
    @GetMapping("/pageApiDef")
    public CecResult<CecPage<ApiDefVO>> pageApiDef(@ModelAttribute ApiDefDto apiDefDto) {

        return CecResult.success(layoutApiDefService.pageApiDef(apiDefDto));
    }



}
