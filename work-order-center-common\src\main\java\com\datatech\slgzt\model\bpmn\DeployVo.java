package com.datatech.slgzt.model.bpmn;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;


/**
 * 部署所需属性
 */
@Data
@Accessors(chain = true)
public class DeployVo {

    /**
     * 工作流文件路径
     */
    @NotNull(message = "工作流文件路径不能为空")
    private String classpathResource;

    /**
     * 流程名称
     */
    @NotNull(message = "流程定义名称不能为空")
    private String name;

    /**
     * 流程定义key
     */
    @NotNull(message = "流程定义key不能为空")
    private String key;

    /**
     * 流程部署id
     */
    private String deployId;


}
