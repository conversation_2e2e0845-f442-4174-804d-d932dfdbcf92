package com.datatech.slgzt.cloudPort;


import com.datatech.slgzt.model.dto.cloudPort.CloudPortDTO;

import com.datatech.slgzt.model.query.WocCloudPortQuery;
import com.datatech.slgzt.utils.PageResult;
import com.ejlchina.data.Mapper;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-31
 */
public interface IWocCloudPortManager {

    /**
     * 新增
     */
    void create(CloudPortDTO dto);


    /**
     * 删除
     */
    void delete(String id);

    /**
     * 根据id 查询云端口
     * @param id
     * @return
     */

    CloudPortDTO selectOnde(String id);

    /**
     * 更新云端口
     * @param cloudPortDTO
     */
    void update(CloudPortDTO cloudPortDTO);


    Mapper remoteHttpCreate(CloudPortDTO cloudPortDTO);

    /**
     * 云端口详情
     * @param id
     * @return
     */
    CloudPortDTO queryWocCloudPortDetail(String id);

    List<CloudPortDTO> queryList(WocCloudPortQuery wocCloudPortQuery);
    /**
     * 分页查询
     */
    PageResult<CloudPortDTO> page(WocCloudPortQuery query);

}
