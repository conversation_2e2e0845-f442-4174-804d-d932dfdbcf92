package com.datatech.slgzt.model.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月26日 09:09:56
 */
@Data
public class RegionTenantExcelDTO {

    @ExcelIgnore
    private Long regionId;

    @ExcelIgnore
    private String regionCode;


    @ExcelProperty("数据时间")
    private String dataTime;

    @ExcelProperty("虚拟资源池")
    private String cmdbRegionName;

    //资源池
    @ExcelProperty("资源池")
    private String regionName;

    //部门
    @ExcelProperty("部门")
    private String deptName;

    //业务系统名称
    @ExcelProperty("业务系统名称")
    private String businessSystemName;

    //负责人
    @ExcelProperty("负责人")
    private String principalName;

    //租户名称
    @ExcelProperty("租户名称")
    private String tenantName;

    //系统等级
    @ExcelProperty("系统等级")
    private String systemLevel;

    //系统等级
    @ExcelProperty("容灾")
    private String hasDisasterRecovery;

    //项目名称
    @ExcelProperty("项目名称")
    private String projectName;

    //vCPU分配数（单位：核）
    @ExcelProperty("vCPU分配数(核)")
    private Integer vcpuNum = 0;

    //内存分配数（单位:GB）
    @ExcelProperty("内存分配数(GB)")
    private BigDecimal memory= BigDecimal.ZERO;

    //存储分配数（单位：GB）
    @ExcelProperty("存储分配数(GB)")
    private BigDecimal storage= BigDecimal.ZERO;

    //vCpu均值利用率(%)
    @ExcelProperty("vCPU均值利用率(%)")
    private BigDecimal vcpuUtil= BigDecimal.ZERO;

    //内存均值利用率(%)
    @ExcelProperty("内存均值利用率(%)")
    private BigDecimal memoryUtil= BigDecimal.ZERO;

    //存储均值利用率(%)
    @ExcelProperty("存储均值利用率(%)")
    private BigDecimal storageUtil= BigDecimal.ZERO;

    //vCpu峰值利用率(%)
    @ExcelProperty("vCPU峰值利用率(%)")
    private BigDecimal vcpuPeakUtil= BigDecimal.ZERO;

    //内存峰值利用率(%)
    @ExcelProperty("内存峰值利用率(%)")
    private BigDecimal memoryPeakUtil= BigDecimal.ZERO;

    //存储峰值利用率(%)
    @ExcelProperty("存储峰值利用率(%)")
    private BigDecimal storagePeakUtil= BigDecimal.ZERO;

    //GPU数量
    @ExcelProperty("GPU数量")
    private Integer gpuNum= 0;

    //公网带宽(Mb)
    @ExcelProperty("公网带宽(Mb)")
    private Integer eipBandwidth= 0;

    //对象存储(G)
    @ExcelProperty("对象存储(G)")
    private BigDecimal obsStorage= BigDecimal.ZERO;

    //业务系统创建时间
    @ExcelProperty("业务系统创建时间")
    private LocalDateTime businessSystemCreateTime;

    //业务系统到期时间
    @ExcelProperty("业务系统到期时间")
    private LocalDateTime businessSystemExpireTime;

    //计费号状态
    @ExcelProperty("计费号状态")
    private String billIdStatus;

    //计费号
    @ExcelProperty("计费号")
    private String billId;


    @ExcelIgnore
    private LocalDateTime createTime;


}
