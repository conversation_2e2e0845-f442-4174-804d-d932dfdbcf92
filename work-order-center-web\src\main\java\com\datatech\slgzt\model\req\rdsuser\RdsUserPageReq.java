package com.datatech.slgzt.model.req.rdsuser;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: 数据库用户分页查询请求
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Data
public class RdsUserPageReq {

    /**
     * 数据库主键ID
     */
    private String rdsId;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 唯一标识
     */
    private String gid;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态（1有效 0删除）
     */
    private Integer enabled;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页显示条数
     */
    private Integer pageSize = 10;
} 