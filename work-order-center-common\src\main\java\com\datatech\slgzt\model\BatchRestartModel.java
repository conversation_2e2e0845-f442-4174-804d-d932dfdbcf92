package com.datatech.slgzt.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月04日 10:55:18
 */
@Data
@Accessors(chain = true)
public class BatchRestartModel implements Serializable {

    private static final long serialVersionUID = 1L;

    //只能重启
    private boolean restartOnly = false;

    /**
     * 重启批次ID
     */
    private Long jobExecutionId;

    private String subOrderId;

    private Integer orderType;

    private String productType;
//
//    /**
//     * vpc id或者network id
//     * vpc id或者network id
//     * vpc id或者network id
//     */
//    private String vpcId;

    private String openStatus;
    /**
     * 任务失败时的消息
     */
    private String message;


}
