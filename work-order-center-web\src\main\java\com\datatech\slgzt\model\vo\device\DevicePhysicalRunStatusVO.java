package com.datatech.slgzt.model.vo.device;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Desc
 * <AUTHOR>
 * @DATA 2025-06-11
 */
@Data
@Accessors(chain = true)
public class DevicePhysicalRunStatusVO  implements Serializable {

    private String areaCode;


    private List<Model> modelList;


    @Data
    public static class Model{

        private LocalDateTime time;

        /**
         * 算力利用率
         */
        private BigDecimal gpuUtilPercent;

        /**
         * 显存利用率
         */
        private BigDecimal memUtilpercent;

    }



}
