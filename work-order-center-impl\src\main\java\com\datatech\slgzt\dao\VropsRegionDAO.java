package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.VropsRegionMapper;
import com.datatech.slgzt.dao.model.VropsRegionDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月27日 19:23:20
 */
@Repository
public class VropsRegionDAO {

    @Resource
    private VropsRegionMapper mapper;


   public List<VropsRegionDO> listAll() {
       return mapper.selectList(Wrappers.<VropsRegionDO>lambdaQuery()
                                        .eq(VropsRegionDO::getType, "VROPS"));
   }
}
