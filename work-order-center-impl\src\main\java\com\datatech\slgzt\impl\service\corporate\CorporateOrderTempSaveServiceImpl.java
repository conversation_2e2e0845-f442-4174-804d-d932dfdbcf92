package com.datatech.slgzt.impl.service.corporate;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.dto.CorporateOrderTempDTO;
import com.datatech.slgzt.model.dto.TenantDTO;
import com.datatech.slgzt.service.corporate.CorporateOrderTempSaveService;
import com.datatech.slgzt.utils.Precondition;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import org.redisson.api.RKeys;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月10日 14:39:35
 */
@Service
public class CorporateOrderTempSaveServiceImpl implements CorporateOrderTempSaveService {

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private TenantManager tenantManager;

    private final String RESOURCE_KEY = "corporate_order_temp_save";

    private final String TENANT_TEMP_USER_KEY = "corporate_temp_user";
    private final String TENANT_TEMP_TENANT_KEY = "corporate_temp_tenant";

    @Override
    public Long handleCreate(String key, String tenantId, JSONObject orderJson) {
        String redisKey = generateKey(key, tenantId);
        long id = IdUtil.getSnowflake().nextId();
        RMap<Long, JSONObject> resourceMap = redissonClient.getMap(redisKey);
        JSONObject data = new JSONObject();
        data.put("id", id);
        data.put("tenantId", tenantId);
        data.put("goodsType", key);
        data.put("orderJson", orderJson);
        resourceMap.put(id, data);
        return id;
    }


    @Override
    public void handleDelete(Long id, String type, String tenantId) {
        RMap<Long, JSONObject> resourceMap = redissonClient.getMap(generateKey(type, tenantId));
        resourceMap.remove(id);
    }

    @Override
    public void handleDeleteAll(String userId) {
        RKeys keys = redissonClient.getKeys();
        Joiner joiner = Joiner.on(":");
        String pattern = joiner.join(RESOURCE_KEY, userId, "*");
        Iterable<String> matchedKeys = keys.getKeysByPattern(pattern);
        matchedKeys.forEach(keys::delete);
    }

    @Override
    public String parseKey(String key) {
        //先校验下
        Precondition.checkArgument(key.endsWith("List"), "key不合法");
        return key.substring(0, key.length() - 4);
    }


    /**
     * 获取当前用户的暂存对象
     *
     * @param userId
     */
    @Override
    public List<CorporateOrderTempDTO> getTempSave(List<String> tenantIds) {
        List<CorporateOrderTempDTO> resultList = new ArrayList<>();
        for (String tenantId : tenantIds) {
            CorporateOrderTempDTO corporateOrderTempDTO = new CorporateOrderTempDTO();
            Joiner on = Joiner.on(":");
            String key = on.join(RESOURCE_KEY, tenantId, "*");
            RKeys keys = redissonClient.getKeys();
            Iterable<String> matchedKeys = keys.getKeysByPattern(key);
            JSONObject result = new JSONObject();
            matchedKeys.forEach(item -> {
                RMap<Long, String> resourceMap = redissonClient.getMap(item);
                //属性名称用 key最后一个冒号后面的字符串
                int lastColonIndex = item.lastIndexOf(':');
                //这里取出来的是 ecs 这种 要加上List
                String typeName = (lastColonIndex == -1) ? Strings.nullToEmpty(item) : item.substring(lastColonIndex + 1);
                typeName = typeName + "List";
                //数据部分装配到List里
                List<JSONObject> list = new ArrayList<>();
                for (String value : resourceMap.values()) {
                    JSONObject jsonObject = JSON.parseObject(value);
                    Long id = jsonObject.getLong("id");
                    JSONObject orderJson = jsonObject.getJSONObject("orderJson");
                    orderJson.put("tempSaveId", id);
                    list.add(orderJson);
                }
                result.put(typeName, list);
            });
            if (result.isEmpty()){
                continue;
            }
            TenantDTO tenantDTO = tenantManager.getById(Long.valueOf(tenantId));
            corporateOrderTempDTO.setTenantId(tenantId);
            corporateOrderTempDTO.setTenantName(tenantDTO.getName());
            corporateOrderTempDTO.setOrderJson(result);
            resultList.add(corporateOrderTempDTO);
        }
        return resultList;
    }


    @Override
    public void addTenantToUser(String tenantId, String userId) {
        redissonClient.getSet(TENANT_TEMP_USER_KEY + ":" + tenantId).add(userId);
        redissonClient.getSet(TENANT_TEMP_TENANT_KEY + ":" + userId).add(tenantId);
    }

    @Override
    public List<String> getUsersToBeDeleted(String tenantId) {
        // 1. 获取租户关联的所有用户
        RSet<String> tenantUsers = redissonClient.getSet(TENANT_TEMP_USER_KEY + ":" + tenantId);
        Set<String> userIds = tenantUsers.readAll();

        List<String> usersToDelete = new ArrayList<>(); // 存储需要被删除的用户

        // 2. 遍历租户下的每个用户
        for (String userId : userIds) {
            // 2.1 从用户的租户集合中移除当前租户
            RSet<String> userTenants = redissonClient.getSet(TENANT_TEMP_TENANT_KEY + ":" + userId);
            userTenants.remove(tenantId);

            // 2.2 如果用户不再关联其他租户，清理用户数据并加入删除列表
            if (userTenants.isEmpty()) {
                userTenants.delete(); // 删除用户租户集合
                usersToDelete.add(userId); // 将用户加入待删除列表
            }
        }
        // 3. 删除租户的用户集合
        tenantUsers.delete();
        // 4. 返回要被删除的用户列表
        return usersToDelete;
    }

    /**
     * 生成key
     *
     * @param fieldKey
     * @return
     */
    public String generateKey(String type, String tenantId) {
        Joiner joiner = Joiner.on(":");
        return joiner.join(RESOURCE_KEY, tenantId, type);
    }
}
