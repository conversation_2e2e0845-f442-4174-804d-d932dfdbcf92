package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.RdsUserDTO;
import com.datatech.slgzt.model.dto.RdsUserOperateDTO;
import com.datatech.slgzt.model.query.RdsUserQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库用户Manager接口
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
public interface RdsUserManager {

    /**
     * 分页查询数据库用户
     */
    PageResult<RdsUserDTO> page(RdsUserQuery query);

    /**
     * 查询数据库用户列表
     */
    List<RdsUserDTO> list(RdsUserQuery query);

    /**
     * 新增数据库用户
     */
    void add(RdsUserDTO rdsUserDTO);

    /**
     * 更新数据库用户
     */
    void update(RdsUserOperateDTO rdsUserOperateDTO);

    /**
     * 删除数据库用户
     */
    void delete(Long id);

    /**
     * 根据ID查询数据库用户
     */
    RdsUserDTO getById(Long id);
} 