package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datatech.slgzt.model.TaskStatusExt;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月13日 14:06:45
 */
@Data
@TableName("WOC_SLB_CERTIFICATE")
public class SlbCertificateDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.ID_WORKER)
    private String id;

    /**
     * 证书名称
     */
    @TableField("CERTIFICATE_NAME")
    private String certificateName;

    /**
     * 公钥内容
     */
    @TableField("PUBLIC_KEY_CONTENT")
    private String publicKeyContent;

    /**
     * 私钥内容
     */
    @TableField("PRIVATE_KEY_CONTENT")
    private String privateKeyContent;

    /**
     * 证书类型
     */
    @TableField("CERTIFICATE_TYPE")
    private String certificateType;

    /**
     * 域名
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    //domainName
    @TableField("DOMAIN_NAME")
    private String domainName;

    //slblistenerRel 关联json
    @TableField("SLBLISTENER_REL")
    private String slbListenerRel;

    /**
     * 证书资源id
     */
    @TableField("RESOURCE_ID")
    private String resourceId;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 状态
     */
    @TableField("STATUS")
    private String status;

    /**
     * 任务状态
     */
    @TableField("TASK_STATUS_EXT")
    private String taskStatusExt;


    /**
     * 业务系统类型
     */
    @TableField("BUSINESS_SYSTEM_ID")
    private String businessSystemId;


    /**
     * 云类型
     */
    @TableField("CATALOGUE_DOMAIN_CODE")
    private String catalogueDomainCode;



    /**
     * 资源池编码
     */
    @TableField("REGION_ID")
    private String regionId;

    /**
     * 资源池名称
     */
    @TableField("REGION_NAME")
    private String regionName;



    /**
     * 业务系统名称
     */
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;


    @TableField("TENANT_ID")
    private Long tenantId;

    //来源
    @TableField("SOURCE_TYPE")
    private String sourceType;


}
