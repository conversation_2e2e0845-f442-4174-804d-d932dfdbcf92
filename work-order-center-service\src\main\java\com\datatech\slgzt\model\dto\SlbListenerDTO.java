package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.TaskStatusExt;
import lombok.Data;

import java.util.List;

/**
 * slb监听器DTO
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月13日 10:12:48
 */
@Data
public class SlbListenerDTO {

    private String id;

    //slb监听的底层资源id
    private String deviceId;

    //监听名称
    private String listenerName;

    //vpcId
    private String vpcId;

    //vpcName
    private String vpcName;

    //资源详情ID
    private Long slbResourceDetailId;

    private String runningStatus;

    //状态表示位置 0正常 1操作中
    private String status;

    //slb设备id
    private String slbDeviceId;

    private SlbListenerProtocolModel slbListenerProtocolModel;

    private SlbHealthCheckModel slbHealthCheckModel;

    private SlbListenerServerGroupDTO slbListenerServerGroup;

    private TaskStatusExt taskStatusExt;




    @Data
    public static class SlbListenerProtocolModel{

        //协议类型
        private String protocolType;

        //监听端口
        private String listenerPort;

        //服务器证书id
        private String serverCertificateId;

        //ca证书id
        private String certificateId;

        //访问控制
        private String accessControl;

        //调度算法类型 WRR,WLC
        private String lbAlgorithm;

        //是否开启会话保持 0：关闭；1：vmware开启
        private String sessionPersistence;

        //是否cookies植入 1:植入；2:重写；
        private String cookieMethod;

        //
        private String cookieName;
    }

    /**
     * 健康检查
     */
    @Data
    public static class SlbHealthCheckModel{


        //健康检查端口
        private String healthCheckPort;

        //健康检查协议类型
        private String healthCheckProtocolType;

        //健康检查url
        private String healthCheckUrl;

        //健康检查间隔时间
        private String healthCheckIntervalTime;

        //健康检查超时时间
        private String healthCheckTimeoutTime;

        //健康检查阈值
        private String healthCheckThresholdValue;

        //健康检查失败阈值
        private String healthCheckFailureThresholdValue;

        //返回值
        private String healthCheckReturnValue;
    }
}
