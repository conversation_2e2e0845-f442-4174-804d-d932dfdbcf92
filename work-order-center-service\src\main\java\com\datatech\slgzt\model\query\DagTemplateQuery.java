package com.datatech.slgzt.model.query;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * DAG模板查询条件
 */
@Data
public class DagTemplateQuery {
    
    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 创建人ID
     */
    private String creator;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;


    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;
} 