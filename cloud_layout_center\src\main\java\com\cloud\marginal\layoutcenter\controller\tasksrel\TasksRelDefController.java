package com.cloud.marginal.layoutcenter.controller.tasksrel;

import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.service.layoutdb.TasksRelDefService;
import com.cloud.marginal.model.dto.layout.SaveTasksRelDto;
import com.cloud.marginal.model.dto.layout.TasksRelPageDto;
import com.cloud.marginal.model.vo.layout.TaskRelVO;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(VersionConstant.V1+"tasksrel")
@Api(tags = "编排任务关系配置")
public class TasksRelDefController {

    @Resource
    private TasksRelDefService tasksRelDefService;

    /**
     * 创建编排关系
     * @param tasksRelDto
     * @return
     */
    @PostMapping("/create")
    public CecResult create(@RequestBody  @Validated SaveTasksRelDto tasksRelDto){
        tasksRelDefService.create(tasksRelDto);
        return CecResult.success();
    }

    /**
     * 分页查询编排关系
     * @param tasksRelPageDto
     * @return
     */
    @GetMapping("/page")
    public CecResult<CecPage<TaskRelVO>> getPage(@ModelAttribute @Validated TasksRelPageDto tasksRelPageDto){
        CecPage<TaskRelVO> tasksRels = tasksRelDefService.getPage(tasksRelPageDto);
        return CecResult.success(tasksRels);
    }

    /**
     * 删除编排模板
     * @param id
     * @return
     */
    @DeleteMapping("/remove")
    public CecResult remove(@RequestParam(value = "id") String id){
        tasksRelDefService.remove(id);
        return CecResult.success();
    }

}
