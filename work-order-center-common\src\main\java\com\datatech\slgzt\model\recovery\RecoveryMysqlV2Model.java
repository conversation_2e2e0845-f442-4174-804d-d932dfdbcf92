package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 07月07日 16:09:23
 */
@Data
public class RecoveryMysqlV2Model extends BaseReconveryProductModel {


    private String mysqlName;


    private String deviceId;


    //数据库版本
    private String osVersion;

    /**
     * 部署类型
     * ALONE：单机版
     *  COLONY：高可用版
     */
    private String mountOrNot;

    //实例规格
    private String spec;

    //存储大小
    private String sysDisk;

    //ip
    private String ip;
}
