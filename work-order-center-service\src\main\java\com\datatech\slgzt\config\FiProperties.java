package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 谐云配置属性类
 * 统一管理谐云相关的配置项
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "fi")
public class FiProperties {

    private String username;

    private String password;

    private Map<String, String> regionCode2flinkCreateUrlMap;

    private Map<String, String> regionCode2KafkaCreateUrlMap;

    private Map<String, String> regionCode2EsCreateUrlMap;

    private String kafkaCreateUrl;

    private String esCreateUrl;
}
