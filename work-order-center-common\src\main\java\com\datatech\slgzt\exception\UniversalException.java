package com.datatech.slgzt.exception;


import com.datatech.slgzt.enums.GlobalExceptionEnum;
import lombok.Data;

@Data
public class UniversalException extends RuntimeException {

    private Integer code;

    private String message;

    private UniversalException(GlobalExceptionEnum enums) {
        this.code = enums.getCode();
        this.message = enums.getMessage();
    }

    private UniversalException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static UniversalException build(GlobalExceptionEnum enums) {
        return new UniversalException(enums);
    }

    public static UniversalException build(Integer code, String message) {
        return new UniversalException(code, message);
    }
}
