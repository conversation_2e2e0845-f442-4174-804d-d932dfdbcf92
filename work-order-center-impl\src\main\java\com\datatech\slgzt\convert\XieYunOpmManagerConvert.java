package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.XieYunNamespaceDTO;
import com.datatech.slgzt.model.dto.XieYunOrgDTO;
import com.datatech.slgzt.model.dto.XieYunProjectDTO;
import com.datatech.slgzt.model.dto.XieYunUserDTO;
import com.datatech.slgzt.model.xieyun.*;
import org.mapstruct.Mapper;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Mapper(componentModel = "spring")
public interface XieYunOpmManagerConvert {

    XieYunUserDTO userOpm2dto(XieYunUserCreateOpm opm);

    XieYunOrgDTO orgOpm2dto(XieyunOrgCreateOpm opm);

    XieYunOrgDTO orgQuotaOpm2dto(XieyunOrgQuotaOpm quotaOpm);

    XieYunProjectDTO projectOpm2dto(XieyunProjectCreateOpm projectCreateOpm);

    XieYunProjectDTO projectQuotaOpm2dto(XieyunProjectQuotaOpm quotaOpm);

    XieYunNamespaceDTO namespaceOpm2dto(XieyunNamespaceCreateOpm namespaceCreateOpm);
}
