package com.datatech.slgzt.controller.container;

import com.datatech.slgzt.convert.ContainerQuotaWebConvert;
import com.datatech.slgzt.manager.ContainerQuotaManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.model.req.container.ContainerQuotaPageReq;
import com.datatech.slgzt.model.req.container.ContainerQuotaReq;
import com.datatech.slgzt.model.vo.container.ContainerQuotaExportVO;
import com.datatech.slgzt.model.vo.container.ContainerQuotaVO;
import com.datatech.slgzt.service.container.ContainerQuotaService;
import com.datatech.slgzt.utils.FileUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 容器配额控制器
 *
 * <AUTHOR>
 * @description 容器配额相关接口
 * @date 2025年05月27日
 */
@Slf4j
@RestController
@RequestMapping("/container/quota")
public class ContainerQuotaController {

    @Resource
    private ContainerQuotaService containerQuotaService;
    @Resource
    private ContainerQuotaManager containerQuotaManager;

    @Resource
    private ContainerQuotaWebConvert convert;

    /**
     * 容器配额分页查询
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<ContainerQuotaVO>> page(@RequestBody ContainerQuotaPageReq req) {
        log.info("分页查询容器配额，查询条件：{}", req);

        // 转换查询条件
        ContainerQuotaQuery query = convert.convert(req);

        // 执行分页查询
        PageResult<ContainerQuotaDTO> page = containerQuotaService.queryPage(query);

        // 转换为VO分页结果
        PageResult<ContainerQuotaVO> result = PageWarppers.box(page, convert::convert);

        log.info("分页查询容器配额完成，总记录数：{}，当前页记录数：{}",
                result.getTotal(), result.getRecords().size());

        return CommonResult.success(result);
    }

    @RequestMapping("/detail")
    public CommonResult<ContainerQuotaVO> detail(@RequestBody ContainerQuotaReq req) {
        ContainerQuotaDTO containerQuotaDTO = containerQuotaManager.getById(req.getId());
        if (ObjNullUtils.isNull(containerQuotaDTO)) {
            return null;
        }
        return CommonResult.success(convert.convert(containerQuotaDTO));
    }

    /**
     * 容器配额导出
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody ContainerQuotaPageReq req, HttpServletResponse response) {
        log.info("导出容器配额，查询条件：{}", req);

        // 转换查询条件
        ContainerQuotaQuery query = convert.convert(req);
        query.setPageSize(10000); // 设置大页面大小以获取所有数据

        // 执行查询
        PageResult<ContainerQuotaDTO> page = containerQuotaService.queryPage(query);

        // 转换为导出VO
        List<ContainerQuotaExportVO> exportList = page.getRecords().stream()
                .map(convert::convertToExportVO)
                .collect(Collectors.toList());

        // 生成文件路径
        String filePath = System.getProperty("user.dir") + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";

        // 导出Excel
        FileUtils.doExport(exportList, ContainerQuotaExportVO.class, filePath);

        // 下载文件
        downloadFile(response, filePath, "容器配额列表.xlsx");

        log.info("容器配额导出完成，导出记录数：{}", exportList.size());
    }

    /**
     * 下载文件
     */
    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();

            // 清空response
            response.reset();

            // 设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());

            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("文件下载失败：", e);
        }
    }
}
