package com.datatech.slgzt.enums;

import org.springframework.util.StringUtils;

public enum ServiceTypeEnum {

    /**
     * 服务类型
     */
    ECS("ecs", "ECS"),
    SYSDISK("sysdisk", "SYSDISK"),
    EVS("evs", "EVS"),
    EIP("eip", "EIP"),
    MYSQL_DISK("mysql_disk", "MYSQL_DISK"),
    MYSQL_ECS("mysql_ecs", "MYSQL_ECS"),
    GCS("gcs", "GCS"),
    OBS("obs", "OBS"),
    SLB("slb", "SLB"),
    NAT("nat", "NAT"),
    VPC("vpc", "VPC"),
    NETWORK("network", "NETWORK"),
    SUBNET("subnet", "SUBNET"),
    OTHER("other", "OTHER"),;

    private final String code;
    private final String desc;

    ServiceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code  编码
     * @return ServiceTypeEnum
     */
    public static ServiceTypeEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (ServiceTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return ServiceTypeEnum.OTHER;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
