package com.cloud.marginal.layoutcenter.controller.temptask;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.service.layoutdb.TempTaskService;
import com.cloud.marginal.model.dto.layout.TempTaskOperationDto;
import com.cloud.marginal.model.dto.layout.TempTaskQueryDto;
import com.cloud.marginal.model.vo.layout.TempTaskDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Since 2023/5/16 16:06
 */
@RestController
@RequestMapping(VersionConstant.V1+"temtask")
public class TempTaskController {

    @Autowired
    private TempTaskService tempTaskService;

    /**
     * 模板任务API关联配置新增
     * @param detail
     * @return
     */
    @PostMapping("/create")
    public CecResult create(@RequestBody @Validated TempTaskOperationDto detail){
        tempTaskService.create(detail);
        return CecResult.success();
    }

    /**
     * 模板任务API关联配置删除
     * @param templateTaskId
     * @param taskApiId
     * @return
     */
    @DeleteMapping("/remove")
    public CecResult remove(@RequestParam("templateTaskId") String templateTaskId,@RequestParam("taskApiId") String taskApiId){
        tempTaskService.remove(templateTaskId,taskApiId);
        return CecResult.success();
    }

    /**
     * 模板任务API关联配置-修改
     * @param detail
     * @return
     */
    @PutMapping("/update")
    public CecResult remove(@RequestBody @Validated TempTaskOperationDto detail){
        if(StrUtil.isEmpty(detail.getTemplateTask().getId())){
            throw new BusinessException("模板任务配置ID不能为空");
        }
        if(StrUtil.isEmpty(detail.getTaskApi().getId())){
            throw new BusinessException("任务API配置ID不能为空");
        }
        tempTaskService.update(detail);
        return CecResult.success();
    }

    /**
     * 模板任务API关联配置-查询
     * @param query
     * @return
     */
    @GetMapping("/page")
    private CecResult<Page<TempTaskDetailVO>> getPage(@ModelAttribute @Validated TempTaskQueryDto query){
        Page<TempTaskDetailVO> page = tempTaskService.getPage(query);
        return CecResult.success(page);
    }

}
