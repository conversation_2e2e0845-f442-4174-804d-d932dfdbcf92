package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 租户报表数据对象
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月30日 14:51:58
 */
@Data
@TableName("WOC_TENANT_REPORT")
public class TenantReportDO {


    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String domainCode;

    private String domainName;

    //虚拟资源池
    private String cmdbRegionName;
    //区域
    private String regionName;

    private Long regionId;

    //部门名称
    private String deptName;

    /**
     * vCPU分配数（单位：核）	内存分配数
     * （单位:GB）	存储分配数（单位：GB）	vCPU均值利用率	内存均值利用率	存储均值利用率	vCPU峰值利用率	内存峰值利用率	存储峰值利用率	GPU数量	公网带宽
     * （Mb）	对象存储
     * （G）	业务系统创建时间	业务系统到期时间	计费号状态	计费号
     */
    //租户id
    private Long tenantId;

    //租户名称
    private String tenantName;

    //业务系统id
    private Long businessSystemId;

    //业务系统名称
    private String businessSystemName;

    //业务模块名称
    private String businessModuleName;

    //负责人
    private String principalName;

    //系统等级
    private String systemLevel;

    //是否有容灾 1表示有容灾，0表示没有容灾
    private String hasDisasterRecovery;

    //项目名称
    private String projectName;

    //vCPU分配数（单位：核）
    private Integer vcpuNum = 0;

    //内存分配数（单位:GB）
    private BigDecimal memory= BigDecimal.ZERO;

    //存储分配数（单位：GB）
    private BigDecimal storage= BigDecimal.ZERO;

    //vCPU均值利用率
    private BigDecimal vcpuUtil= BigDecimal.ZERO;

    //内存均值利用率
    private BigDecimal memoryUtil= BigDecimal.ZERO;

    //存储均值利用率
    private BigDecimal storageUtil= BigDecimal.ZERO;

    //gpu数量
    private Integer gpuNum = 0;

    //公网带宽（Mb）
    private Integer eipBandwidth= 0;

    //对象存储（G）
    private BigDecimal obsStorage= BigDecimal.ZERO;

    //业务系统创建时间
    private LocalDateTime businessSystemCreateTime;

    //业务系统到期时间
    private LocalDateTime businessSystemExpireTime;

    //计费号状态
    private String billIdStatus;

    //计费号
    private String billId;

    //创建时间
    private LocalDateTime createdAt;

    //数据时间
    private String dataTime;

}
