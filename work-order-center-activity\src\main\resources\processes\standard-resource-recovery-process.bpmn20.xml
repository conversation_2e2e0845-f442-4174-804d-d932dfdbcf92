<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:activiti="http://activiti.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:tns="http://www.activiti.org/test"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:yaoqiang="http://bpmn.sourceforge.net"
             exporter="Yaoqiang BPMN Editor" exporterVersion="5.3"
             expressionLanguage="http://www.w3.org/1999/XPath"
             id="m1632821341533" name=""
             targetNamespace="http://www.activiti.org/test"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://bpmn.sourceforge.net/schemas/BPMN20.xsd">
    <process id="standard-resource-recovery-process" isClosed="false" isExecutable="true" name="standard-resource-recovery-process" processType="None">
        <extensionElements>
            <yaoqiang:description/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
        </extensionElements>
        <startEvent id="_2" isInterrupting="true" name="StartEvent" parallelMultiple="false">
            <outgoing>_6</outgoing>
            <outputSet/>
        </startEvent>
        <exclusiveGateway gatewayDirection="Diverging" id="_3" name="ExclusiveGateway">
            <incoming>_6</incoming>
            <outgoing>_8</outgoing>
            <outgoing>_7</outgoing>
        </exclusiveGateway>
        <userTask activiti:assignee="${system}" activiti:exclusive="true" completionQuantity="1" id="_4" implementation="##unspecified" isForCompensation="false" name="system" startQuantity="1">
            <incoming>_8</incoming>
            <outgoing>_9</outgoing>
        </userTask>
        <userTask activiti:assignee="${applyUserId}" activiti:exclusive="true" completionQuantity="1" id="_5" implementation="##unspecified" isForCompensation="false" name="user_task" startQuantity="1">
            <incoming>_7</incoming>
            <incoming>_9</incoming>
            <incoming>_20</incoming>
            <outgoing>_11</outgoing>
            <outgoing>_21</outgoing>
        </userTask>
        <sequenceFlow id="_6" sourceRef="_2" targetRef="_3"/>
        <sequenceFlow id="_7" name="isTenant" sourceRef="_3" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${isTenant==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_8" name="isSystem" sourceRef="_3" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${isTenant==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_9" name="system2User" sourceRef="_4" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <userTask activiti:assignee="${businessLeader2}" activiti:exclusive="true" completionQuantity="1" id="_10" implementation="##unspecified" isForCompensation="false" name="business_depart_leader2" startQuantity="1">
            <incoming>_11</incoming>
            <outgoing>_13</outgoing>
            <outgoing>_20</outgoing>
        </userTask>
        <sequenceFlow id="_11" name="userPass" sourceRef="_5" targetRef="_10">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <exclusiveGateway gatewayDirection="Diverging" id="_12" name="ExclusiveGateway">
            <incoming>_13</incoming>
            <outgoing>_15</outgoing>
            <outgoing>_22</outgoing>
        </exclusiveGateway>
        <sequenceFlow id="_13" name="businessPass" sourceRef="_10" targetRef="_12">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <userTask activiti:assignee="${operationGroupCode}" activiti:exclusive="true" completionQuantity="1" id="_14" implementation="##unspecified" isForCompensation="false" name="operation_group" startQuantity="1">
            <incoming>_15</incoming>
            <outgoing>_17</outgoing>
        </userTask>
        <sequenceFlow id="_15" name="gatewayPass" sourceRef="_12" targetRef="_14">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <userTask activiti:assignee="${tenant}" activiti:exclusive="true" completionQuantity="1" id="_16" implementation="##unspecified" isForCompensation="false" name="tenant_task" startQuantity="1">
            <incoming>_17</incoming>
            <outgoing>_19</outgoing>
        </userTask>
        <sequenceFlow id="_17" name="operationPass" sourceRef="_14" targetRef="_16">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <endEvent id="_18" name="EndEvent">
            <incoming>_19</incoming>
            <incoming>_21</incoming>
            <incoming>_22</incoming>
            <inputSet/>
        </endEvent>
        <sequenceFlow id="_19" sourceRef="_16" targetRef="_18"/>
        <sequenceFlow id="_20" name="businessLeaderBack2UserTask" sourceRef="_10" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_21" name="userTaskCancel" sourceRef="_5" targetRef="_18">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_22" name="businessLeaderCancel" sourceRef="_12" targetRef="_18">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram id="Yaoqiang_Diagram-standard-resource-recovery-process" documentation="background=#3C3F41;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0" name="New Diagram">
        <bpmndi:BPMNPlane bpmnElement="standard-resource-recovery-process">
            <bpmndi:BPMNShape bpmnElement="_2" id="Shape-_2">
                <omgdc:Bounds height="32.0" width="32.0" x="340.0" y="50.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_3" id="Shape-_3" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="340.0" y="150.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_4" id="Shape-_4">
                <omgdc:Bounds height="55.0" width="85.0" x="630.0" y="140.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_5" id="Shape-_5">
                <omgdc:Bounds height="55.0" width="85.0" x="315.0" y="285.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_10" id="Shape-_10">
                <omgdc:Bounds height="55.0" width="85.0" x="315.0" y="455.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_12" id="Shape-_12" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="490.0" y="465.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_14" id="Shape-_14">
                <omgdc:Bounds height="55.0" width="85.0" x="630.0" y="455.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_16" id="Shape-_16">
                <omgdc:Bounds height="55.0" width="85.0" x="630.0" y="640.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_18" id="Shape-_18">
                <omgdc:Bounds height="32.0" width="32.0" x="445.0" y="655.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="_13" id="BPMNEdge__13" sourceElement="_10" targetElement="_12">
                <omgdi:waypoint x="400.0" y="480.0"/>
                <omgdi:waypoint x="460.0" y="480.0"/>
                <omgdi:waypoint x="491.0" y="480.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_15" id="BPMNEdge__15" sourceElement="_12" targetElement="_14">
                <omgdi:waypoint x="521.0" y="480.0"/>
                <omgdi:waypoint x="575.0" y="480.0"/>
                <omgdi:waypoint x="630.0" y="480.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_17" id="BPMNEdge__17" sourceElement="_14" targetElement="_16">
                <omgdi:waypoint x="672.5" y="510.0"/>
                <omgdi:waypoint x="672.5" y="640.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_19" id="BPMNEdge__19" sourceElement="_16" targetElement="_18">
                <omgdi:waypoint x="630.0" y="670.0"/>
                <omgdi:waypoint x="560.0" y="670.0"/>
                <omgdi:waypoint x="476.96871942267126" y="670.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_6" id="BPMNEdge__6" sourceElement="_2" targetElement="_3">
                <omgdi:waypoint x="356.0" y="82.0"/>
                <omgdi:waypoint x="356.0" y="150.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_7" id="BPMNEdge__7" sourceElement="_3" targetElement="_5">
                <omgdi:waypoint x="356.0" y="182.0"/>
                <omgdi:waypoint x="356.0" y="285.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_8" id="BPMNEdge__8" sourceElement="_3" targetElement="_4">
                <omgdi:waypoint x="371.0" y="165.0"/>
                <omgdi:waypoint x="505.0" y="165.0"/>
                <omgdi:waypoint x="630.0" y="165.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_9" id="BPMNEdge__9" sourceElement="_4" targetElement="_5">
                <omgdi:waypoint x="680.0" y="195.0"/>
                <omgdi:waypoint x="680.0" y="220.0"/>
                <omgdi:waypoint x="400.0" y="312.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_20" id="BPMNEdge__20" sourceElement="_10" targetElement="_5">
                <omgdi:waypoint x="490.0" y="481.0"/>
                <omgdi:waypoint x="400.0" y="312.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="-27.0" width="0.0" x="0.0" y="-80.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_11" id="BPMNEdge__11" sourceElement="_5" targetElement="_10">
                <omgdi:waypoint x="357.5" y="340.0"/>
                <omgdi:waypoint x="357.5" y="455.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_22" id="BPMNEdge__22" sourceElement="_12" targetElement="_18">
                <omgdi:waypoint x="505.0" y="496.0"/>
                <omgdi:waypoint x="505.0" y="590.0"/>
                <omgdi:waypoint x="477.0" y="671.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="-32.0" x="1.0" y="-101.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_21" id="BPMNEdge__21" sourceElement="_5" targetElement="_18">
                <omgdi:waypoint x="315.0" y="312.5"/>
                <omgdi:waypoint x="245.0" y="465.0"/>
                <omgdi:waypoint x="445.0" y="671.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="-2.0" width="0.0" x="0.0" y="-45.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>
