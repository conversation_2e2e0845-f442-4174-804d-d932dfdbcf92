package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.SlbListenerServerGroupMapper;
import com.datatech.slgzt.dao.model.SlbListenerServerGroupDO;
import com.datatech.slgzt.model.query.SlbListenerServerGroupQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * SLB监听器服务组DAO
 */
@Repository
public class SlbListenerServerGroupDAO {

    @Resource
    private SlbListenerServerGroupMapper slbListenerServerGroupMapper;
    
    /**
     * 插入
     */
    public void insert(SlbListenerServerGroupDO slbListenerServerGroupDO) {
        slbListenerServerGroupMapper.insert(slbListenerServerGroupDO);
    }
    
    /**
     * 更新
     */
    public void update(SlbListenerServerGroupDO slbListenerServerGroupDO) {
        slbListenerServerGroupMapper.updateById(slbListenerServerGroupDO);
    }
    
    /**
     * 删除
     */
    public void delete(String id) {
        slbListenerServerGroupMapper.deleteById(id);
    }
    
    /**
     * 根据ID查询
     */
    public SlbListenerServerGroupDO getById(String id) {
        return slbListenerServerGroupMapper.selectById(id);
    }

    /**
     * 根据监听器ID查询列表
     */
    public List<SlbListenerServerGroupDO> listByListenerId(String listenerId) {
        return slbListenerServerGroupMapper.selectList(
            Wrappers.<SlbListenerServerGroupDO>lambdaQuery()
                .eq(SlbListenerServerGroupDO::getSlbListenerId, listenerId)
                .orderByDesc(SlbListenerServerGroupDO::getCreateTime)
        );
    }
    
    /**
     * 列表查询
     */
    public List<SlbListenerServerGroupDO> list(SlbListenerServerGroupQuery query) {
        return slbListenerServerGroupMapper.selectList(
            Wrappers.<SlbListenerServerGroupDO>lambdaQuery().like(ObjNullUtils.isNotNull(query.getGroupName()), SlbListenerServerGroupDO::getGroupName, query.getGroupName())
                .eq(ObjNullUtils.isNotNull(query.getSlbListenerId()), SlbListenerServerGroupDO::getSlbListenerId, query.getSlbListenerId())
                .eq(ObjNullUtils.isNotNull(query.getGroupType()), SlbListenerServerGroupDO::getGroupType, query.getGroupType())
                .eq(ObjNullUtils.isNotNull(query.getSlbResourceDetailId()), SlbListenerServerGroupDO::getSlbResourceDetailId, query.getSlbResourceDetailId())
                .orderByDesc(SlbListenerServerGroupDO::getCreateTime)
        );
    }
} 