package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.NonStanderWorkOrderProductMapper;
import com.datatech.slgzt.dao.model.order.NonStanderWorkOrderProductDO;
import com.datatech.slgzt.enums.StatusEnum;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 16:57:42
 */
@Repository
public class NonStanderWorkOrderProductDAO {

    @Resource
    private NonStanderWorkOrderProductMapper mapper;


    /**
     * list
     */
    public List<NonStanderWorkOrderProductDO> list(NonStanderWorkOrderProductQuery query) {
        return mapper.selectList(Wrappers.<NonStanderWorkOrderProductDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getOrderId()), NonStanderWorkOrderProductDO::getWorkOrderId, query.getOrderId())
                .in(ObjNullUtils.isNotNull(query.getIds()), NonStanderWorkOrderProductDO::getId, query.getIds())
                .in(ObjNullUtils.isNotNull(query.getGids()), NonStanderWorkOrderProductDO::getGid, query.getGids())
                .in(ObjNullUtils.isNotNull(query.getSubOrderIds()), NonStanderWorkOrderProductDO::getSubOrderId, query.getSubOrderIds())
                .eq(ObjNullUtils.isNotNull(query.getProductType()), NonStanderWorkOrderProductDO::getProductType, query.getProductType())
                .eq(ObjNullUtils.isNotNull(query.getParentId()), NonStanderWorkOrderProductDO::getParentProductId, query.getParentId())
        );

    }


    public void delByWorkOrderId(String workOrderId) {
        mapper.delete(Wrappers.<NonStanderWorkOrderProductDO>lambdaQuery().eq(NonStanderWorkOrderProductDO::getWorkOrderId, workOrderId));
    }

    /**
     * updateById
     */
    public int updateById(NonStanderWorkOrderProductDO productDO) {
        return mapper.updateById(productDO);
    }

    public void updateByWorkOrderId(NonStanderWorkOrderProductDO productDO) {
        mapper.update(productDO, Wrappers.<NonStanderWorkOrderProductDO>lambdaUpdate()
                .eq(NonStanderWorkOrderProductDO::getWorkOrderId, productDO.getWorkOrderId()));
    }


    public NonStanderWorkOrderProductDO getById(Long id) {
        return mapper.selectById(id);
    }

    /**
     * getByGid
     */
    public NonStanderWorkOrderProductDO getByGid(String gid) {
        return mapper.selectOne(Wrappers.<NonStanderWorkOrderProductDO>lambdaQuery().eq(NonStanderWorkOrderProductDO::getGid, gid));
    }

    public NonStanderWorkOrderProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<NonStanderWorkOrderProductDO>lambdaQuery().eq(NonStanderWorkOrderProductDO::getSubOrderId, subOrderId));
    }

    /**
     * insert
     */
    public void insert(NonStanderWorkOrderProductDO productDO) {
        mapper.insert(productDO);
    }


    public void updateByParentId(NonStanderWorkOrderProductDO product) {
        mapper.update(product, Wrappers.<NonStanderWorkOrderProductDO>lambdaUpdate()
                .eq(NonStanderWorkOrderProductDO::getParentProductId
                        , product.getParentProductId()));
    }

    /**
     * selectById
     */
    public NonStanderWorkOrderProductDO selectById(Long id) {
        return mapper.selectById(id);
    }

    public List<NonStanderWorkOrderProductDO> getByWorkOrderId(String orderId) {
        QueryWrapper<NonStanderWorkOrderProductDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ENABLED", StatusEnum.NORMAL.code());
        queryWrapper.eq("WORK_ORDER_ID", orderId);
        return mapper.selectList(queryWrapper);
    }
}

