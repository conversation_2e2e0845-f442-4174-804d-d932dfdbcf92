package com.datatech.slgzt.impl.device;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.convert.DeviceVirtualInfoConvert;
import com.datatech.slgzt.enums.DeviceMetricEnum;
import com.datatech.slgzt.enums.DeviceMetricSourceEnum;
import com.datatech.slgzt.enums.DeviceModelTFEnum;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.manager.DeviceVirtualInfoManager;
import com.datatech.slgzt.model.BaseMetricInfoModel;
import com.datatech.slgzt.model.BaseVdevice;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.service.device.DeviceGupMetricsService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.ejlchina.data.Mapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class DeviceQdVGupMetricsServiceImpl implements DeviceGupMetricsService {
    @Value("${http.gpuDeviceMetricUrl}")
    private String gpuDeviceMetricUrl;

    @Value("${http.gpuDeviceResourceUrl}")
    private String vgpuDeviceMetricUrl;

    @Resource
    private DeviceVirtualInfoManager deviceVirtualInfoManager;

    @Resource
    private DeviceVirtualInfoConvert deviceVirtualInfoConvert;

    @Resource
    private DeviceGpuInfoManager deviceGpuInfoManager;


    /**
     * 同步虚拟显卡数据
     *
     * @return
     */
    @Override
    public void syncRemoteDeviceDataInfo() {
        log.info("开始同步虚拟显卡数据");
        String baseurl = vgpuDeviceMetricUrl + "/v2/vdevices";
        List<BaseVdevice> baseVdevices = queryVdeviceList(baseurl);
        //按照设备ID对baseVdevices进行去重
        baseVdevices = new ArrayList<>(baseVdevices.stream()
                                                   .collect(Collectors.toMap(BaseVdevice::getUuid, device -> device, (oldValue, newValue) -> oldValue))
                                                   .values());
        List<DeviceVirtualInfoDTO> remoteDeviceInfoList = StreamUtils.mapArray(baseVdevices, deviceVirtualInfoConvert::baseDevice2Dto);
        List<DeviceVirtualInfoDTO> localDeviceVirtualInfoList = deviceVirtualInfoManager.selectDeviceVirtualInfoList(new DeviceInfoQuery());
        List<String> deviceIdList = localDeviceVirtualInfoList.stream()
                                                              .map(DeviceVirtualInfoDTO::getDeviceId)
                                                              .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(remoteDeviceInfoList)) {
            for (DeviceVirtualInfoDTO remoteDevice : remoteDeviceInfoList) {
                if (!deviceIdList.contains(remoteDevice.getDeviceId())) {
                    //拿到物理显卡ID 去找物理卡信息
                    DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGpuInfoManager.getByDeviceId(remoteDevice.getPhysicalDeviceId());
                    if (deviceGpuInfoDTO == null) {
                        log.warn("物理显卡信息不存在，物理显卡ID：{}", remoteDevice.getPhysicalDeviceId());
                        continue;
                    }
                    remoteDevice.setDeviceType("VR");
                    remoteDevice.setAreaCode(deviceGpuInfoDTO.getAreaCode());
                    remoteDevice.setModelName(deviceGpuInfoDTO.getModelName());
                    remoteDevice.setSourceType(DeviceMetricSourceEnum.QD_VIRTUAL_SOURCE.getCode());
                    remoteDevice.setRegionName(deviceGpuInfoDTO.getRegionName());
                    remoteDevice.setCatalogueDomainName(deviceGpuInfoDTO.getCatalogueDomainName());
                    remoteDevice.setBusinessSystemName(deviceGpuInfoDTO.getBusinessSystemName());
                    remoteDevice.setMemory(Objects.isNull(remoteDevice.getMemory()) ? 0 : remoteDevice.getMemory() / 1024);
                    deviceVirtualInfoManager.create(remoteDevice);

                }
            }
        }
        //删除设备不能存在的虚拟显卡
        List<String> remoteDeviceIds = remoteDeviceInfoList.stream()
                                                           .map(DeviceVirtualInfoDTO::getDeviceId)
                                                           .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(localDeviceVirtualInfoList)) {
            List<DeviceVirtualInfoDTO> removeLocalDevice = localDeviceVirtualInfoList.stream()
                                                                                     .filter(device -> !remoteDeviceIds.contains(device.getDeviceId()))
                                                                                     .collect(Collectors.toList());
            log.info("删除设备不存在的虚拟显卡数据：{}", removeLocalDevice);
            List<Long> removeDeviceId = removeLocalDevice.stream()
                                                  .map(DeviceVirtualInfoDTO::getId)
                                                  .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(removeDeviceId)) {
                deviceVirtualInfoManager.deleteBatch(removeDeviceId);
            }
        }


    }


    @Override
    public List<DeviceCardMetricsDTO> calculateGpuDeviceMetric() {

        //获取所有的虚拟卡信息
        List<DeviceVirtualInfoDTO> deviceVirtualInfoList = deviceVirtualInfoManager.selectDeviceVirtualInfoList(new DeviceInfoQuery());
        //过滤掉设备id为空的数据
        deviceVirtualInfoList = deviceVirtualInfoList.stream()
                                                 .filter(item -> StringUtils.isNotBlank(item.getDeviceId()))
                                                 .collect(Collectors.toList());
        /**
         *vGPU的实时显存使用大小，按照deivceId 分组
         */
        Map<String, List<BaseMetricInfoModel>> vgpuMemoryUsage = queryBaseRemoteMetricInfo(DeviceMetricEnum.ORIONX_VGPU_MEMORY_USAGE);
        /**
         *vGPU的实时算力利用率 按照deivceId 分组
         */
        Map<String, List<BaseMetricInfoModel>> orionxVgpuUtilization = queryBaseRemoteMetricInfo(DeviceMetricEnum.ORIONX_VGPU_UTILIZATION);
        /**
         *  vGPU的实时显存使用大小
         */
        Map<String, List<BaseMetricInfoModel>> memoryUsagedGroupByDeviceId = queryBaseRemoteMetricInfo(DeviceMetricEnum.ORIONX_VGPU_MEMORY_USAGE);
        /**
         *vGPU分配的显存大小
         */
        Map<String, List<BaseMetricInfoModel>> memoryTotalGroupByDeviceId = queryBaseRemoteMetricInfo(DeviceMetricEnum.ORIONX_VGPU_MEMORY_TOTAL);
        List<BaseMetricInfoModel> baseMetricInfoModels = Lists.newArrayList();
        memoryTotalGroupByDeviceId.forEach((key, list) -> {
            List<BaseMetricInfoModel> baseMetricInfoValue = memoryTotalGroupByDeviceId.get(key);
            baseMetricInfoModels.addAll(baseMetricInfoValue);
        });
        //计算任务数
        String baseurl = vgpuDeviceMetricUrl + "/v2/vdevices";
        List<BaseVdevice> baseVdevices = queryVdeviceList(baseurl).stream().distinct().collect(Collectors.toList());
        Map<String, List<BaseVdevice>> deviceVirtualsMap = baseVdevices.stream()
                                                                       .collect(Collectors.groupingBy(BaseVdevice::getUuid));
        List<DeviceCardMetricsDTO> deviceCardMetricList = new ArrayList<>();
        //创建监控指标
        deviceVirtualInfoList.forEach(virtualInfoDTO -> {
            //当前物理显卡 所有虚拟显卡显存大小
            List<BaseMetricInfoModel> vgpuMemoryUsageList = Optional.ofNullable(vgpuMemoryUsage.get(virtualInfoDTO.getPhysicalDeviceId()))
                                                                    .orElse(Lists.newArrayList());
            //当前物理显卡 所有虚拟显卡算力利用率
            List<BaseMetricInfoModel> vgpuUtilizationRatioList = Optional.ofNullable(orionxVgpuUtilization.get(virtualInfoDTO.getPhysicalDeviceId()))
                                                                         .orElse(Lists.newArrayList());
            /**
             * 当前物理显卡 所有虚拟显卡显存大小 按照vindex 分组
             */
            Map<String, BaseMetricInfoModel> vgpuMemoryUsageIndex = vgpuMemoryUsageList.stream()
                                                                                       .collect(Collectors.toMap(BaseMetricInfoModel::getVindex, metricInfo -> metricInfo, (oldValue, newValue) -> oldValue));
            /**
             * 当前物理显卡 所有虚拟显卡算力利用率 按照vindex 分组
             */
            Map<String, BaseMetricInfoModel> vgpuUtilizationRatioIndex = vgpuUtilizationRatioList.stream()
                                                                                                 .collect(Collectors.toMap(BaseMetricInfoModel::getVindex, metricInfo -> metricInfo, (oldValue, newValue) -> oldValue));

            List<BaseMetricInfoModel> memoryUsagedList = memoryUsagedGroupByDeviceId.get(virtualInfoDTO.getPhysicalDeviceId());
            List<BaseMetricInfoModel> memoryTotalList = memoryTotalGroupByDeviceId.get(virtualInfoDTO.getPhysicalDeviceId());
            Map<String, String> orionxVgpuMemoryutilizationMap = orionxVgpuMemoryutilization(memoryUsagedList, memoryTotalList);
            DeviceCardMetricsDTO deviceMetricsDTO = new DeviceCardMetricsDTO();
            deviceMetricsDTO.setDeviceId(virtualInfoDTO.getDeviceId());
            //存储指标来源
            deviceMetricsDTO.setMetricSource(DeviceMetricSourceEnum.QD_VIRTUAL_SOURCE.getCode());
            deviceMetricsDTO.setGpuTime(DateUtils.currentHalf(DateUtils.DATE_TIME_YYYY_MM_DD_HH));
            try {
                //按照vdeviceindex
                Integer vindex = virtualInfoDTO.getDeviceVirtualIndex();
                BaseMetricInfoModel vgpuMemoryUsageVindex = Optional.ofNullable(vgpuMemoryUsageIndex.get(String.valueOf(vindex)))
                                                                    .orElse(new BaseMetricInfoModel());
                //使用GB
                String usageMemory = vgpuMemoryUsageVindex.getValue();
                BigDecimal usedMemory = new BigDecimal(StringUtils.isNotBlank(usageMemory) ? Integer.parseInt(usageMemory) : 0);
                BigDecimal bigDecimal = new BigDecimal(1024);
                deviceMetricsDTO.setMemoryUsage(usedMemory.divide(bigDecimal));
                BaseMetricInfoModel baseMetricInfoModel = vgpuUtilizationRatioIndex.get(String.valueOf(vindex));
                if (baseMetricInfoModel != null && StringUtils.isNotBlank(baseMetricInfoModel.getValue())) {
                    deviceMetricsDTO.setGpuUtilPercent(Double.valueOf(baseMetricInfoModel.getValue()));
                } else {
                    deviceMetricsDTO.setGpuUtilPercent(0.0);
                }
                //显存利用率
                if (StringUtils.isNotBlank(orionxVgpuMemoryutilizationMap.get(virtualInfoDTO.getPhysicalDeviceId() + vindex))) {
                    String formatted = String.format("%.2f", Double.parseDouble(orionxVgpuMemoryutilizationMap.get(virtualInfoDTO.getPhysicalDeviceId() + vindex)));
                    Double vgpuMemory = Double.valueOf(formatted);
                    deviceMetricsDTO.setMemUtilpercent(vgpuMemory);
                } else {
                    deviceMetricsDTO.setMemUtilpercent(0.0);
                }
                //设置modelName deviceType 类型待定
                deviceMetricsDTO.setModelName(DeviceModelTFEnum.getByModelName(vgpuMemoryUsageVindex.getPgpuName())
                                                               .getModelName());
                deviceMetricsDTO.setDeviceType(virtualInfoDTO.getDeviceType());
                deviceMetricsDTO.setAreaCode(virtualInfoDTO.getAreaCode());
                //任务数
                Optional.ofNullable(deviceVirtualsMap.get(virtualInfoDTO.getDeviceId())).map(List::size)
                        .ifPresent(deviceMetricsDTO::setAllocationCount);
                deviceCardMetricList.add(deviceMetricsDTO);
            } catch (Exception e) {
                log.error("插入虚拟显卡指标数据出现异常：" + e);
                e.printStackTrace();
            }
        });
        return deviceCardMetricList;
    }


    /**
     * 获取虚拟显卡指标信息
     *
     * @param deviceMetricEnum
     * @return
     */
    public Map<String, List<BaseMetricInfoModel>> queryBaseRemoteMetricInfo(DeviceMetricEnum deviceMetricEnum) {
        String url = gpuDeviceMetricUrl + "/api/v1/query";
        Mapper responseMapper = remoteHttpClientMetric(url, deviceMetricEnum);
        if (responseMapper != null) {
            String successStr = responseMapper.getString("status");
            Precondition.checkArgument("success".equals(successStr), "获取" + deviceMetricEnum.getDesc() + "失败: " + responseMapper.getString("message"));
            return parseMetricInfo2Map(responseMapper.getString("data"));
        }
        return Collections.emptyMap();
    }


    /**
     * 计算内存利用率
     *
     * @param memoryUsagedList
     * @param memoryTotalList
     * @return
     */
    public Map<String, String> orionxVgpuMemoryutilization(List<BaseMetricInfoModel> memoryUsagedList, List<BaseMetricInfoModel> memoryTotalList) {

        Map<String, String> vgpuMemoryutilizationMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(memoryUsagedList)) {
            memoryUsagedList.forEach(memoryUsaged -> {
                String usagedValue = memoryUsaged.getValue();
                String vindex = memoryUsaged.getVindex();
                BaseMetricInfoModel memoryTotalInfo = memoryTotalList.stream().filter(j -> j.getVindex().equals(vindex))
                                                                     .findFirst().orElse(null);
                if (memoryTotalInfo != null) {
                    double used = Double.parseDouble(usagedValue);
                    double total = Double.parseDouble(memoryTotalInfo.getValue());
                    double ratio = used / total * 100;
                    String key = memoryTotalInfo.getPgpuId() + vindex;
                    vgpuMemoryutilizationMap.put(key, String.valueOf(ratio));
                }
            });
        }
        return vgpuMemoryutilizationMap;
    }

}