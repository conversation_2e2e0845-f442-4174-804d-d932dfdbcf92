package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.model.BaseReconveryProductModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: 裸金属回收模型
 * @author: LK
 * @create: 2025-06-30 15:02
 **/
@Data
public class RecoveryFlinkModel extends BaseReconveryProductModel {

    private String flinkName;

    /**
     * cpu核数
     */
    @JsonProperty("vCpus")
    private String vCpus;

    /**
     * 内存大小
     */
    private String ram;


    /**
     * 申请时长
     */
    private String applyTime;
}
