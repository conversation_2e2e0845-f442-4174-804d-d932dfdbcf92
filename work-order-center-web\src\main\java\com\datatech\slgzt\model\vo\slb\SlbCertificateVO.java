package com.datatech.slgzt.model.vo.slb;

import com.datatech.slgzt.enums.CertificateTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * SLB证书VO
 */
@Data
@Accessors(chain = true)
public class SlbCertificateVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 公钥内容
     */
    private String publicKeyContent;

    /**
     * 私钥内容
     */
    private String privateKeyContent;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 域名编码
     */
    private String domainCode;

    /**
     * 域名名称
     */
    private String domainName;

    /**
     * 关联的监听器关系JSON
     */
    private Map<String,String> slbListenerRel;

    private List<String> slbListeners;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 证书类型名称
     */
    private String certificateTypeName;


    /**
     * 监听器名称
     */
    private String slbListenerName;


    /**
     * 证书资源id
     */
    private String resourceId;



} 