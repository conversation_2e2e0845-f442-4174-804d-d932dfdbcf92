package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.util.Date;

/**
 * flink导出vo
 * <AUTHOR>
 * @date 2025/6/24
 **/
@Data
public class RdsMysqlExportVO {
    @ExcelExportHeader(value = "数据库名称")
    private String deviceName;

    @ExcelExportHeader(value = "规格")
    private String spec;

    //ip
    @ExcelExportHeader(value = "IP")
    private String ip;

    @ExcelExportHeader(value = "租户名称")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String cloudPlatform;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private Date resourceApplyTime;

    @ExcelExportHeader(value = "计费号")
    private String billId;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;



}
