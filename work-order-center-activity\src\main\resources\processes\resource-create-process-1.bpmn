<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:yaoqiang="http://bpmn.sourceforge.net" expressionLanguage="http://www.w3.org/1999/XPath" id="m1632821341533" name="" targetNamespace="http://www.activiti.org/test" typeLanguage="http://www.w3.org/2001/XMLSchema">
    <process id="resource-create-process-1" isClosed="false" isExecutable="true" name="resource-create-process-1" processType="None">
        <extensionElements>
            <yaoqiang:description/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
        </extensionElements>
        <startEvent id="_2" isInterrupting="true" name="StartEvent" parallelMultiple="false"/>
        <userTask activiti:assignee="${userId}" activiti:exclusive="true" completionQuantity="1" id="_3" implementation="##unspecified" isForCompensation="false" name="user_task" startQuantity="1"/>
        <userTask activiti:assignee="${schema}" activiti:exclusive="true" completionQuantity="1" id="_4" implementation="##unspecified" isForCompensation="false" name="schema_administrator" startQuantity="1"/>
        <userTask activiti:assignee="${userId}" activiti:exclusive="true" completionQuantity="1" id="_27" implementation="##unspecified" isForCompensation="false" name="tenant_task" startQuantity="1"/>
        <userTask activiti:assignee="${business2}" activiti:exclusive="true" completionQuantity="1" id="_5" implementation="##unspecified" isForCompensation="false" name="business2_depart_leader" startQuantity="1"/>
        <userTask activiti:assignee="${business}" activiti:exclusive="true" completionQuantity="1" id="_17" implementation="##unspecified" isForCompensation="false" name="business_depart_leader" startQuantity="1"/>
        <userTask activiti:assignee="${cloud}" activiti:exclusive="true" completionQuantity="1" id="_6" implementation="##unspecified" isForCompensation="false" name="cloud_leader" startQuantity="1"/>
        <userTask activiti:assignee="${cloud_2}" activiti:exclusive="true" completionQuantity="1" id="_55" implementation="##unspecified" isForCompensation="false" name="cloud_leader_2" startQuantity="1"/>
        <userTask activiti:assignee="${network}" activiti:exclusive="true" completionQuantity="1" id="_9" implementation="##unspecified" isForCompensation="false" name="network_provisioning" startQuantity="1"/>
        <userTask activiti:assignee="${resource}" activiti:exclusive="true" completionQuantity="1" id="_21" implementation="##unspecified" isForCompensation="false" name="resource_creation" startQuantity="1"/>
        <sequenceFlow id="_25" sourceRef="_9" targetRef="_21"/>
        <sequenceFlow id="_12" sourceRef="_2" targetRef="_3"/>
        <sequenceFlow id="_14" sourceRef="_4" targetRef="_8"/>
        <sequenceFlow id="_15" name="reject" sourceRef="_8" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_16" name="adopt" sourceRef="_8" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_38" name="reject" sourceRef="_36" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_39" name="isSkipBusinessNode=1" sourceRef="_36" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_18" sourceRef="_5" targetRef="_10"/>
        <sequenceFlow id="_19" name="business2 reject" sourceRef="_10" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_20" name="business2 adopt" sourceRef="_10" targetRef="_17">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_31" name="business adopt" sourceRef="_29" targetRef="_6">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_32" name="business reject" sourceRef="_29" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_35" name="reject" sourceRef="_36" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_22" sourceRef="_6" targetRef="_11"/>
        <sequenceFlow id="_24" name="cloud reject" sourceRef="_11" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_60" name="cloud 2 reject" sourceRef="_56" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_61" name="cloud 2 reject" sourceRef="_56" targetRef="_17">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_62" name="cloud 2 reject" sourceRef="_56" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_63" name="cloud 2 reject" sourceRef="_56" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_64" name="cloud 2 reject" sourceRef="_56" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_65" name="cloud 2 reject" sourceRef="_56" targetRef="_6">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_13" sourceRef="_3" targetRef="_4"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_10" name="business de leader examine"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_11" name="cloud leader examine"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_56" name="cloud leader 2 examine"/>
        <sequenceFlow id="_23" name="cloud adopt" sourceRef="_11" targetRef="_55">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_57" name="cloud adopt 2" sourceRef="_56" targetRef="_9">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_48" name="cloud adopt resource" sourceRef="_56" targetRef="_21">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_49" name="userTaskClose" sourceRef="_3" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_50" name="schemaAdministratorClose" sourceRef="_8" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_51" name="tenantTaskClose" sourceRef="_36" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_58" sourceRef="_55" targetRef="_56"/>
        <sequenceFlow id="_52" name="business2Close" sourceRef="_10" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_53" name="businessLeaderClose" sourceRef="_29" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_54" name="cloudLeaderClose" sourceRef="_11" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_59" name="cloudLeader2Close" sourceRef="_56" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_37" sourceRef="_27" targetRef="_36"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_36" name="tenant examine"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_8" name="administrator examine"/>
        <sequenceFlow id="_66" name="isSkipBusinessNode=0" sourceRef="_36" targetRef="_6">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_40" sourceRef="_21" targetRef="_7"/>
        <endEvent id="_7" name="EndEvent"/>
        <sequenceFlow id="_30" sourceRef="_17" targetRef="_29"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_29" name="business de leader examine"/>
        <sequenceFlow id="_33" name="business2 reject" sourceRef="_10" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_34" name="business2 reject" sourceRef="_10" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_41" name="business reject" sourceRef="_29" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_42" name="business reject" sourceRef="_29" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_43" name="business reject" sourceRef="_29" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_44" name="cloud reject" sourceRef="_11" targetRef="_17">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_45" name="cloud reject" sourceRef="_11" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_46" name="cloud reject" sourceRef="_11" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_47" name="cloud reject" sourceRef="_11" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[
                ]]></conditionExpression>
        </sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram documentation="background=#3C3F41;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0" id="Diagram-_1" name="New Diagram">
        <bpmndi:BPMNPlane bpmnElement="resource-create-process-1">
            <bpmndi:BPMNShape bpmnElement="_2" id="Shape-_2">
                <omgdc:Bounds height="32.0" width="32.0" x="240.0" y="65.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_3" id="Shape-_3">
                <omgdc:Bounds height="55.0" width="85.0" x="215.0" y="135.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_4" id="Shape-_4">
                <omgdc:Bounds height="55.0" width="85.0" x="145.0" y="265.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_5" id="Shape-_5">
                <omgdc:Bounds height="55.0" width="85.0" x="130.0" y="440.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_6" id="Shape-_6">
                <omgdc:Bounds height="55.0" width="85.0" x="80.0" y="610.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_10" id="Shape-_10" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="237.0" y="550.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_11" id="Shape-_11" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="237.0" y="726.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_9" id="Shape-_9">
                <omgdc:Bounds height="55.0" width="85.0" x="385.46328920030635" y="795.4046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_21" id="Shape-_21">
                <omgdc:Bounds height="55.0" width="85.0" x="570.2889328973346" y="820.2030577822226"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_27" id="Shape-_27">
                <omgdc:Bounds height="55.0" width="85.0" x="394.83333333333337" y="310.16666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_36" id="Shape-_36" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="450.1666666666667" y="425.16666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_8" id="Shape-_8" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="155.0" y="370.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_7" id="Shape-_7">
                <omgdc:Bounds height="32.0" width="32.0" x="828.0" y="730.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_17" id="Shape-_17">
                <omgdc:Bounds height="55.0" width="85.0" x="684.5616024187452" y="495.2067271352985"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_29" id="Shape-_29" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="444.54598135550515" y="644.7847064751826"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_55" id="Shape-_55">
                <omgdc:Bounds height="55.0" width="85.0" x="90.0" y="770.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_56" id="Shape-_56" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="315.0" y="765.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="_35" id="BPMNEdge__35" sourceElement="_36" targetElement="_3">
                <omgdi:waypoint x="450.16666666666663" y="441.16666666666663"/>
                <omgdi:waypoint x="300.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_34" id="BPMNEdge__34" sourceElement="_10" targetElement="_4">
                <omgdi:waypoint x="237.0" y="566.0"/>
                <omgdi:waypoint x="230.0" y="292.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_37" id="BPMNEdge__37" sourceElement="_27" targetElement="_36">
                <omgdi:waypoint x="465.0" y="365.0"/>
                <omgdi:waypoint x="465.0" y="426.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="425.17" y="435.92"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_39" id="BPMNEdge__39" sourceElement="_36" targetElement="_5">
                <omgdi:waypoint x="450.16666666666663" y="441.16666666666663"/>
                <omgdi:waypoint x="215.0" y="467.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="338.58" y="479.45"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_38" id="BPMNEdge__38" sourceElement="_36" targetElement="_4">
                <omgdi:waypoint x="481.83333333333337" y="441.16666666666663"/>
                <omgdi:waypoint x="501.0" y="393.0"/>
                <omgdi:waypoint x="230.0" y="292.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="484.5" y="315.96"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_40" id="BPMNEdge__40" sourceElement="_21" targetElement="_7">
                <omgdi:waypoint x="655.0" y="847.7030577822226"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="638.04" y="736.76"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_42" id="BPMNEdge__42" sourceElement="_29" targetElement="_27">
                <omgdi:waypoint x="460.5459813555051" y="645.4540186444949"/>
                <omgdi:waypoint x="460.5459813555051" y="365.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_41" id="BPMNEdge__41" sourceElement="_29" targetElement="_5">
                <omgdi:waypoint x="445.2152935248174" y="660.7847064751826"/>
                <omgdi:waypoint x="215.0" y="467.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_44" id="BPMNEdge__44" sourceElement="_11" targetElement="_17">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="685.0" y="522.7067271352985"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_43" id="BPMNEdge__43" sourceElement="_29" targetElement="_4">
                <omgdi:waypoint x="445.2152935248174" y="660.7847064751826"/>
                <omgdi:waypoint x="230.0" y="292.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_46" id="BPMNEdge__46" sourceElement="_11" targetElement="_27">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="395.0" y="337.66666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_45" id="BPMNEdge__45" sourceElement="_11" targetElement="_5">
                <omgdi:waypoint x="237.0" y="742.0"/>
                <omgdi:waypoint x="215.0" y="467.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_48" id="BPMNEdge__48" sourceElement="_56" targetElement="_21">
                <omgdi:waypoint x="347.0" y="781.0"/>
                <omgdi:waypoint x="570.0" y="847.7030577822226"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_47" id="BPMNEdge__47" sourceElement="_11" targetElement="_4">
                <omgdi:waypoint x="237.0" y="742.0"/>
                <omgdi:waypoint x="230.0" y="292.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_49" id="BPMNEdge__49" sourceElement="_3" targetElement="_7">
                <omgdi:waypoint x="300.0" y="162.5"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_51" id="BPMNEdge__51" sourceElement="_36" targetElement="_7">
                <omgdi:waypoint x="481.83333333333337" y="441.16666666666663"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_50" id="BPMNEdge__50" sourceElement="_8" targetElement="_7">
                <omgdi:waypoint x="187.0" y="386.0"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_53" id="BPMNEdge__53" sourceElement="_29" targetElement="_7">
                <omgdi:waypoint x="476.7847064751826" y="660.7847064751826"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_52" id="BPMNEdge__52" sourceElement="_10" targetElement="_7">
                <omgdi:waypoint x="269.0" y="566.0"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_54" id="BPMNEdge__54" sourceElement="_11" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_13" id="BPMNEdge__13" sourceElement="_3" targetElement="_4">
                <omgdi:waypoint x="222.5" y="190.0"/>
                <omgdi:waypoint x="222.5" y="265.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="259.0" y="222.84"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_57" id="BPMNEdge__57" sourceElement="_56" targetElement="_9">
                <omgdi:waypoint x="347.0" y="781.0"/>
                <omgdi:waypoint x="385.0" y="822.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_12" id="BPMNEdge__12" sourceElement="_2" targetElement="_3">
                <omgdi:waypoint x="256.0" y="97.0"/>
                <omgdi:waypoint x="256.0" y="135.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="253.0" y="106.34"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_15" id="BPMNEdge__15" sourceElement="_8" targetElement="_3">
                <omgdi:waypoint x="187.0" y="386.0"/>
                <omgdi:waypoint x="195.0" y="270.0"/>
                <omgdi:waypoint x="215.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="178.5" y="283.46"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_59" id="BPMNEdge__59" sourceElement="_56" targetElement="_7">
                <omgdi:waypoint x="347.0" y="781.0"/>
                <omgdi:waypoint x="828.0" y="746.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_14" id="BPMNEdge__14" sourceElement="_4" targetElement="_8">
                <omgdi:waypoint x="171.0" y="320.0"/>
                <omgdi:waypoint x="171.0" y="370.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="262.0" y="342.34"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_58" id="BPMNEdge__58" sourceElement="_55" targetElement="_56">
                <omgdi:waypoint x="175.0" y="797.5"/>
                <omgdi:waypoint x="315.0" y="781.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_16" id="BPMNEdge__16" sourceElement="_8" targetElement="_27">
                <omgdi:waypoint x="187.0" y="386.0"/>
                <omgdi:waypoint x="395.0" y="337.66666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="323.92" y="385.37"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_19" id="BPMNEdge__19" sourceElement="_10" targetElement="_3">
                <omgdi:waypoint x="237.0" y="566.0"/>
                <omgdi:waypoint x="110.0" y="370.0"/>
                <omgdi:waypoint x="215.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="86.0" x="67.0" y="367.96"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_18" id="BPMNEdge__18" sourceElement="_5" targetElement="_10">
                <omgdi:waypoint x="215.0" y="467.5"/>
                <omgdi:waypoint x="237.0" y="566.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="255.0" y="523.84"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_60" id="BPMNEdge__60" sourceElement="_56" targetElement="_3">
                <omgdi:waypoint x="315.0" y="781.0"/>
                <omgdi:waypoint x="300.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_62" id="BPMNEdge__62" sourceElement="_56" targetElement="_5">
                <omgdi:waypoint x="315.0" y="781.0"/>
                <omgdi:waypoint x="215.0" y="467.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_61" id="BPMNEdge__61" sourceElement="_56" targetElement="_17">
                <omgdi:waypoint x="347.0" y="781.0"/>
                <omgdi:waypoint x="685.0" y="522.7067271352985"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_20" id="BPMNEdge__20" sourceElement="_10" targetElement="_17">
                <omgdi:waypoint x="269.0" y="566.0"/>
                <omgdi:waypoint x="685.0" y="522.7067271352985"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="86.0" x="294.78" y="560.84"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_64" id="BPMNEdge__64" sourceElement="_56" targetElement="_4">
                <omgdi:waypoint x="315.0" y="781.0"/>
                <omgdi:waypoint x="230.0" y="292.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_63" id="BPMNEdge__63" sourceElement="_56" targetElement="_27">
                <omgdi:waypoint x="347.0" y="781.0"/>
                <omgdi:waypoint x="395.0" y="337.66666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_66" id="BPMNEdge__66" sourceElement="_36" targetElement="_6">
                <omgdi:waypoint x="450.16666666666663" y="441.16666666666663"/>
                <omgdi:waypoint x="165.0" y="637.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="338.58" y="479.45"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_22" id="BPMNEdge__22" sourceElement="_6" targetElement="_11">
                <omgdi:waypoint x="165.0" y="659.0"/>
                <omgdi:waypoint x="257.0" y="659.0"/>
                <omgdi:waypoint x="257.0" y="730.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="259.5" y="700.59"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_65" id="BPMNEdge__65" sourceElement="_56" targetElement="_6">
                <omgdi:waypoint x="315.0" y="781.0"/>
                <omgdi:waypoint x="165.0" y="637.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_24" id="BPMNEdge__24" sourceElement="_11" targetElement="_3">
                <omgdi:waypoint x="237.0" y="742.0"/>
                <omgdi:waypoint x="80.0" y="405.0"/>
                <omgdi:waypoint x="215.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="49.0" y="455.96"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_23" id="BPMNEdge__23" sourceElement="_11" targetElement="_55">
                <omgdi:waypoint x="237.0" y="742.0"/>
                <omgdi:waypoint x="175.0" y="797.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_25" id="BPMNEdge__25" sourceElement="_9" targetElement="_21">
                <omgdi:waypoint x="470.0" y="822.9046978667511"/>
                <omgdi:waypoint x="570.0" y="847.7030577822226"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="485.6" y="736.99"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_31" id="BPMNEdge__31" sourceElement="_29" targetElement="_6">
                <omgdi:waypoint x="445.2152935248174" y="660.7847064751826"/>
                <omgdi:waypoint x="165.0" y="637.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="80.0" x="319.38" y="651.87"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_30" id="BPMNEdge__30" sourceElement="_17" targetElement="_29">
                <omgdi:waypoint x="685.0" y="522.7067271352985"/>
                <omgdi:waypoint x="476.7847064751826" y="660.7847064751826"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="431.55" y="610.06"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_33" id="BPMNEdge__33" sourceElement="_10" targetElement="_27">
                <omgdi:waypoint x="269.0" y="566.0"/>
                <omgdi:waypoint x="395.0" y="337.66666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_32" id="BPMNEdge__32" sourceElement="_29" targetElement="_3">
                <omgdi:waypoint x="476.7847064751826" y="660.7847064751826"/>
                <omgdi:waypoint x="567.0" y="417.0"/>
                <omgdi:waypoint x="300.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="80.0" x="527.4" y="324.46"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>
