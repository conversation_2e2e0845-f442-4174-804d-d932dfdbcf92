package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.model.dto.layout.LayoutTaskDto;
import com.cloud.marginal.model.entity.layout.FollowTaskDef;
import com.cloud.marginal.model.entity.layout.LayoutTask;
import com.cloud.marginal.model.entity.layout.LayoutTaskDef;
import com.cloud.marginal.model.entity.layout.LayoutTaskNode;
import com.cloud.marginal.model.vo.layout.LayoutTaskDefVo;

import java.util.List;

/**
 * <p>
 * 编排任务配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface LayoutTaskDefService extends IService<LayoutTaskDef> {

    /**
     * 根据模板编号和任务编号生成任务节点
     * @param templateCode 模板编号
     * @param taskCode 任务编号
     */
    LayoutTaskNode generateTaskNodeByTemplateCodeAndTaskCode(String templateCode, String taskCode);

    /**
     * 根据模板编号生成主任务
     * @param templateCode 模板编号
     */
    LayoutTask generateMainTaskByTemplateCode(String templateCode);

    /**
     * 根据模板编号获取附加任务定义集合
     * @param templateCode 模板编号
     */
    List<FollowTaskDef> getFollowTaskDefByTemplateCode(String templateCode);

    void create(LayoutTaskDef layoutTaskDef);


    void update(LayoutTaskDef layoutTaskDef);

    CecPage<LayoutTaskDefVo> getPage(LayoutTaskDto layoutTaskDto);

    List<LayoutTaskDefVo> getList(String name,String templateId);

    void  remove(String id);

    boolean isMasterTask(String taskId);

    LayoutTaskDef getCode(String code);


    // 默认
    /**
     * 根据模板编号和任务编号生成任务节点
     * @param templateCode 模板编号
     */
    List<LayoutTaskNode> generateTaskNodeByDefault(String templateCode);

}
