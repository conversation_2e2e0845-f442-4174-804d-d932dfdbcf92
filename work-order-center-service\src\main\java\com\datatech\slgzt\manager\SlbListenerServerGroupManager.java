package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import com.datatech.slgzt.model.query.SlbListenerServerGroupQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * SLB监听器服务组Manager接口
 */
public interface SlbListenerServerGroupManager {
    
    /**
     * 创建服务组
     */
    void create(SlbListenerServerGroupDTO dto);
    
    /**
     * 更新服务组
     */
    void update(SlbListenerServerGroupDTO dto);
    
    /**
     * 删除服务组
     */
    void delete(String id);
    
    /**
     * 根据ID查询
     */
    SlbListenerServerGroupDTO getById(String id);
    
    /**
     * 根据监听器ID查询列表
     */
    List<SlbListenerServerGroupDTO> listByListenerId(String listenerId);
    
    /**
     * 分页查询
     */
    PageResult<SlbListenerServerGroupDTO> page(SlbListenerServerGroupQuery query);
} 