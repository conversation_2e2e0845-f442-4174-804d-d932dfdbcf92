package com.cloud.marginal.layoutcenter.controller.opration;

import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface.VirtualMachineMgAdapter;
import com.cloud.marginal.model.dto.edge.OperateVirtualMachineDTO;
import com.cloud.marginal.model.dto.edge.ResetVmPwdDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.Executor;

/**
 * @program: taskcenterproject
 * @description: 虚拟机操作controller
 * @author: LK
 * @create: 2025-02-19 09:49
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping(VersionConstant.V1)
public class VirtualMachineController {

    private final VirtualMachineMgAdapter adapter;
    @Autowired
    @Qualifier(value = "asyncServiceExecutor")
    private Executor asyncServiceExecutor;

    /**
     * 操作云主机：START，STOP，RESTART，SUSPEND，RECOVERY
     * @param dto
     * @return
     */
    @PostMapping("/vm/operate")
    public CecResult operateVm(@RequestBody OperateVirtualMachineDTO dto){
        asyncServiceExecutor.execute(() -> adapter.operateVirtualMachine(dto));
        return CecResult.success("云主机操作");
    }

    /**
     * 修改云主机密码
     * @param dto
     * @return
     */
    @PostMapping("/vm/resetPwd")
    public CecResult operateVm(@RequestBody ResetVmPwdDTO dto){
        asyncServiceExecutor.execute(() -> adapter.resetPwd(dto));
        return CecResult.success("修改云主机密码");
    }

}
