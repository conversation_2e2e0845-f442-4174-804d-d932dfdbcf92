package com.datatech.slgzt.enums;

import lombok.Getter;

@Getter
public enum GlobalExceptionEnum {
    PARAM_EXCEPTION(400, "参数异常"),
    TOKEN_NOT_GIVE(401, "暂未登录或token已经过期"),
    ACTIVITY_HAS_END(100024, "流程实例已结束"),
    ACTIVITY_DEFINITION_IS_NOT_FOUND(100025, "流程定义不存在"),
    ACTIVITY_AUDIT_ERROR(100026, "当前任务未在该用户下"),
    ACTIVITY_PERMISSION_DENIAL(100027, "该用户无权取消/撤回流程"),
    ACTIVITY_PARAM_SET_ERROR(100028, "工作流程中属性设置，属性不能为空"),
    AUDIT_PERMISSION_DENIAL(100029, "当前用户没有审核权限"),
    CALL_USER_CENTER_FAILURE(100030, "调用用户中心获取数据失败"),
    FILE_OPERATOR_ERROR(100031, "文件操作失败"),
    ;

    private final Integer code;

    private final String message;

    GlobalExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
