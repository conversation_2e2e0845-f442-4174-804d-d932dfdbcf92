package com.datatech.slgzt.impl.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.component.FtpConfig;
import com.datatech.slgzt.convert.TenantReportServiceConvert;
import com.datatech.slgzt.dao.TenantReportDAO;
import com.datatech.slgzt.dao.model.TenantReportDO;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.ExportTaskManager;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.dto.RegionDTO;
import com.datatech.slgzt.model.dto.VropsRegionDTO;
import com.datatech.slgzt.model.opm.ExportTaskOpm;
import com.datatech.slgzt.model.query.RegionQuery;
import com.datatech.slgzt.model.query.TenantReportQuery;
import com.datatech.slgzt.model.report.RegionTenantExcelDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.ExportTaskService;
import com.datatech.slgzt.utils.FTPUtil;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.ArrayListMultimap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 租户报表导出实现
 * @date 2025年 07月01日 16:57:54
 */
@Slf4j
@Service
public class ExportTaskTenantServiceImpl implements ExportTaskService {

    ExecutorService executor = Executors.newFixedThreadPool(2);

    @Resource
    private ExportTaskManager exportTaskManager;

    @Resource
    private RegionManager regionManager;

    @Resource
    private TenantReportDAO tenantReportDAO;

    @Resource
    private TenantReportServiceConvert tenantReportServiceConvert;

    @Resource
    private FtpConfig ftpConfig;

    private static final String EXPORT_PATH = "export/tenant/";
    private static final String BUSINESS_FILE_NAME = "TENANT_REPORT";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Override
    public void export(ExportTaskOpm opm) {
        opm.setRegionIds(getRegionIds(opm));
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        // 创建导出任务记录
        ExportTaskDTO taskDTO = new ExportTaskDTO();
        taskDTO.setReportName(opm.getReportName());
        taskDTO.setBusinessType(getReportType());
        taskDTO.setStatType(opm.getStatType());
        taskDTO.setStartTime(opm.getStartTime());
        taskDTO.setEndTime(opm.getEndTime());
        taskDTO.setCreator(currentUser.getUserName());
        taskDTO.setQueryCondition(opm.getQueryCondition());
        taskDTO.setStatus(0); // 0-生成中
        taskDTO.setCreateTime(LocalDateTime.now());
        taskDTO.setExportFields(JSON.toJSONString(opm.getExportFields())); // 保存导出字段列表
        String taskId = exportTaskManager.createTask(taskDTO);
        taskDTO.setId(taskId);
        
        executor.execute(() -> {
            // 生成Excel文件
            String fileName = generateExcelFileName();
            String filePath = EXPORT_PATH + fileName;
            // 设置任务的文件名
            taskDTO.setFileName(fileName);
            // 确保导出目录存在
            File exportDir = new File(EXPORT_PATH);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            
            try {
                List<Long> regionIds = opm.getRegionIds();
                // 使用RegionTenantExcelDTO进行租户报表导出
                ExcelWriter excelWriter = EasyExcel.write(filePath, RegionTenantExcelDTO.class)
                                                   .includeColumnFiledNames(opm.getExportFields())
                                                   .build();
                WriteSheet writeSheet = EasyExcel.writerSheet("租户资源使用情况").build();
                
                List<RegionTenantExcelDTO> allExportData = new ArrayList<>();
                
                regionIds.forEach(regionId -> {
                    List<TenantReportDO> reportDOList = tenantReportDAO.list(new TenantReportQuery()
                            .setRegionId(regionId)
                            .setDomainCodeList(opm.getDomainCodes())
                            .setStartTime(opm.getStartTime())
                            .setEndTime(opm.getEndTime())
                    );
                    
                    if (ObjNullUtils.isNotNull(reportDOList)) {
                        // 按照租户ID分组
                        ArrayListMultimap<Long, TenantReportDO> tenantId2Data =
                                StreamUtils.toArrayListMultimap(reportDOList, TenantReportDO::getTenantId);
                        
                        tenantId2Data.keySet().forEach(tenantId -> {
                            List<TenantReportDO> tenantDataList = tenantId2Data.get(tenantId);
                            // 按照DATA_TIME分组聚合数据
                            Map<String, List<TenantReportDO>> groupedByTime = groupDataByTime(tenantDataList, opm.getStatType());
                            // 计算聚合后的数据
                            List<RegionTenantExcelDTO> aggregatedData = calculateAggregatedData(groupedByTime, opm.getStatType());
                            allExportData.addAll(aggregatedData);
                        });
                    }
                });
                
                // 按照数据时间倒序排列
                allExportData.sort((a, b) -> b.getDataTime().compareTo(a.getDataTime()));
                // 写入Excel
                excelWriter.write(allExportData, writeSheet);
                excelWriter.finish();
                
                // FTP上传
                uploadToFtp(filePath, taskDTO);
                
                // 更新任务状态为完成
                taskDTO.setStatus(1); // 1-完成
                exportTaskManager.updateTask(taskDTO);
                log.info("Export completed successfully. File path: {}", filePath);
                
            } catch (Exception e) {
                log.error("Export failed", e);
                // 更新任务状态为失败
                taskDTO.setStatus(2); // 2-失败
                exportTaskManager.updateTask(taskDTO);
            }
        });
    }

    /**
     * 按时间分组数据
     */
    private Map<String, List<TenantReportDO>> groupDataByTime(List<TenantReportDO> dataList, String statType) {
        return dataList.stream()
                .collect(Collectors.groupingBy(dto -> getTimeKey(dto.getDataTime(), statType)));
    }

    /**
     * 计算聚合后的数据
     */
    private List<RegionTenantExcelDTO> calculateAggregatedData(Map<String, List<TenantReportDO>> groupedData, String statType) {
        List<RegionTenantExcelDTO> result = new ArrayList<>();
        
        for (Map.Entry<String, List<TenantReportDO>> entry : groupedData.entrySet()) {
            String timeKey = entry.getKey();
            List<TenantReportDO> dataList = entry.getValue();
            
            if (ObjNullUtils.isNull(dataList)) {
                continue;
            }
            
            // 取一条记录作为基础数据（名称等字段）
            TenantReportDO baseData = dataList.get(0);
            RegionTenantExcelDTO excelDTO = tenantReportServiceConvert.convert(baseData);
            
            // 设置时间键值
            excelDTO.setDataTime(timeKey);
            
            // 计算数量字段的最大值
            excelDTO.setVcpuNum(dataList.stream().mapToInt(TenantReportDO::getVcpuNum).max().orElse(0));
            excelDTO.setGpuNum(dataList.stream().mapToInt(TenantReportDO::getGpuNum).max().orElse(0));
            excelDTO.setEipBandwidth(dataList.stream().mapToInt(TenantReportDO::getEipBandwidth).max().orElse(0));
            
            // 计算BigDecimal字段的最大值
            excelDTO.setMemory(dataList.stream().map(TenantReportDO::getMemory)
                    .filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            excelDTO.setStorage(dataList.stream().map(TenantReportDO::getStorage)
                    .filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            excelDTO.setObsStorage(dataList.stream().map(TenantReportDO::getObsStorage)
                    .filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            
            // 计算利用率的均值
            List<BigDecimal> vcpuUtilList = dataList.stream().map(TenantReportDO::getVcpuUtil)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (ObjNullUtils.isNotNull(vcpuUtilList)) {
                BigDecimal vcpuUtilAvg = vcpuUtilList.stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(vcpuUtilList.size()), 4, RoundingMode.HALF_UP);
                excelDTO.setVcpuUtil(vcpuUtilAvg);
            }
            
            List<BigDecimal> memoryUtilList = dataList.stream().map(TenantReportDO::getMemoryUtil)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (ObjNullUtils.isNotNull(memoryUtilList)) {
                BigDecimal memoryUtilAvg = memoryUtilList.stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(memoryUtilList.size()), 4, RoundingMode.HALF_UP);
                excelDTO.setMemoryUtil(memoryUtilAvg);
            }
            
            List<BigDecimal> storageUtilList = dataList.stream().map(TenantReportDO::getStorageUtil)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (ObjNullUtils.isNotNull(storageUtilList)) {
                BigDecimal storageUtilAvg = storageUtilList.stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(storageUtilList.size()), 4, RoundingMode.HALF_UP);
                excelDTO.setStorageUtil(storageUtilAvg);
            }
            
            // 计算利用率的峰值
            BigDecimal vcpuUtilPeak = vcpuUtilList.stream()
                    .max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            excelDTO.setVcpuPeakUtil(vcpuUtilPeak);
            
            BigDecimal memoryUtilPeak = memoryUtilList.stream()
                    .max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            excelDTO.setMemoryPeakUtil(memoryUtilPeak);
            
            BigDecimal storageUtilPeak = storageUtilList.stream()
                    .max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            excelDTO.setStoragePeakUtil(storageUtilPeak);
            
            result.add(excelDTO);
        }
        
        return result;
    }

    /**
     * 获取时间键值
     */
    private String getTimeKey(String dataTime, String statType) {
        if (ObjNullUtils.isNull(dataTime)) {
            return "unknown";
        }
        
        // dataTime格式是yyyyMMddHH，根据statType截取不同长度
        switch (statType.toUpperCase()) {
            case "HOUR":
                return dataTime.length() >= 10 ? dataTime.substring(0, 10) : dataTime;
            case "DAY":
                return dataTime.length() >= 8 ? dataTime.substring(0, 8) : dataTime;
            case "MONTH":
                return dataTime.length() >= 6 ? dataTime.substring(0, 6) : dataTime;
            default:
                return dataTime;
        }
    }

    /**
     * 上传文件到FTP
     */
    private void uploadToFtp(String filePath, ExportTaskDTO taskDTO) {
        try {
            // 创建FTP工具类实例
            FTPUtil ftpUtil = new FTPUtil(ftpConfig.getIp(), ftpConfig.getPort(), ftpConfig.getUser(), ftpConfig.getPass());
            // 构建远程FTP路径
            String remotePath = ftpConfig.getBasePath() + new SimpleDateFormat("yyyy-MM-dd/").format(new Date());
            // 上传文件到FTP
            File localFile = new File(filePath);
            boolean uploadResult = ftpUtil.uploadFile(remotePath, localFile);
            
            if (uploadResult) {
                // 上传成功,返回FTP上的完整路径
                String ftpFilePath = remotePath + localFile.getName();
                // 更新任务记录中的文件路径和文件名
                taskDTO.setFilePath(ftpFilePath);
                taskDTO.setFileName(localFile.getName());
                exportTaskManager.updateTask(taskDTO);
                log.info("File uploaded to FTP successfully: {}", ftpFilePath);
            } else {
                log.error("Failed to upload file to FTP server");
                throw new RuntimeException("Failed to upload file to FTP server");
            }
        } catch (IOException e) {
            log.error("Error uploading file to FTP: ", e);
            throw new RuntimeException("Error uploading file to FTP: " + e.getMessage());
        }
    }

    private String generateExcelFileName() {
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        return String.format("%s_%s.xlsx", BUSINESS_FILE_NAME, timestamp);
    }

    private List<Long> getRegionIds(ExportTaskOpm opm) {
        // 如果regionIds包含-1，表示查询所有区域
        if (opm.getRegionIds().contains(-1L)) {
            List<RegionDTO> list = regionManager.list(new RegionQuery().setDomainCodes(opm.getDomainCodes()));
//            if (ObjNullUtils.isNotNull(opm.getDomainCodes())&&opm.getDomainCodes().contains(CatalogueDomain.VMWARE.getCode())){
//                List<VropsRegionDTO> vropsRegionDTOS = vropsRegionManager.listAll();
//                vropsRegionDTOS.forEach(vropsRegionDTO -> {
//                    RegionDTO regionVO = new RegionDTO();
//                    regionVO.setId(vropsRegionDTO.getId());
//                    regionVO.setName(vropsRegionDTO.getName());
//                    list.add(regionVO);
//                });
//            }
            return StreamUtils.mapArray(list, RegionDTO::getId);
        }
        // 否则返回指定的regionIds
        return opm.getRegionIds();
    }

    @Override
    public String getReportType() {
        return "TENANT";
    }
}
