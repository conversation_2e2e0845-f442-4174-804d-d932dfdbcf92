<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="http://www.activiti.org/testm1741406565888" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:yaoqiang="http://bpmn.sourceforge.net" id="m1741406565888" name="" targetNamespace="http://www.activiti.org/testm1741406565888" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://bpmn.sourceforge.net/schemas/BPMN20.xsd">
  <process id="non-standard-resource-process" name="non-standard-resource-process" processType="None" isClosed="false" isExecutable="true">
    <extensionElements>
      <yaoqiang:description />
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724" />
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1" />
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724" />
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1" />
    </extensionElements>
    <startEvent id="_1" name="StartEvent" activiti:isInterrupting="true">
      <outgoing>_4</outgoing>
      <outputSet />
    </startEvent>
    <userTask id="_2" name="user_task" implementation="##unspecified" activiti:assignee="${userId}">
      <incoming>_4</incoming>
      <incoming>_45</incoming>
      <incoming>_48</incoming>
      <incoming>_49</incoming>
      <incoming>Flow_0amdyh7</incoming>
      <incoming>Flow_0m7h8yf</incoming>
      <outgoing>_6</outgoing>
      <outgoing>_38</outgoing>
    </userTask>
    <sequenceFlow id="_4" sourceRef="_1" targetRef="_2" />
    <userTask id="_3" name="response_scheme_manager" implementation="##unspecified" activiti:assignee="${responseScheme}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>_6</incoming>
      <incoming>_50</incoming>
      <incoming>_54</incoming>
      <incoming>_56</incoming>
      <incoming>Flow_12ow05y</incoming>
      <outgoing>Flow_0p0ntvp</outgoing>
      <outgoing>Flow_1g3olhc</outgoing>
      <outgoing>Flow_0m7h8yf</outgoing>
    </userTask>
    <sequenceFlow id="_6" sourceRef="_2" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="_7" name="CostGateway">
      <incoming>Flow_0p0ntvp</incoming>
      <outgoing>_11</outgoing>
      <outgoing>_12</outgoing>
    </exclusiveGateway>
    <userTask id="_5" name="province_gov_admin" implementation="##unspecified" activiti:assignee="${govAdmin}">
      <incoming>_11</incoming>
      <outgoing>_18</outgoing>
      <outgoing>_40</outgoing>
      <outgoing>_45</outgoing>
      <outgoing>_50</outgoing>
    </userTask>
    <userTask id="_8" name="branch_leader" implementation="##unspecified" activiti:assignee="${business_leader_2}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>_12</incoming>
      <incoming>_59</incoming>
      <incoming>_61</incoming>
      <outgoing>_21</outgoing>
      <outgoing>_41</outgoing>
      <outgoing>_48</outgoing>
      <outgoing>_54</outgoing>
    </userTask>
    <sequenceFlow id="_11" name="CostBig" sourceRef="_7" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">${cost==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_12" name="CostSmall" sourceRef="_7" targetRef="_8">
      <conditionExpression xsi:type="tFormalExpression">${cost==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_18" sourceRef="_5" targetRef="_23">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <userTask id="_9" name="province_gov_leader" implementation="##unspecified" activiti:assignee="${govLeader}">
      <incoming>_21</incoming>
      <incoming>_16</incoming>
      <outgoing>_22</outgoing>
      <outgoing>_42</outgoing>
      <outgoing>_49</outgoing>
      <outgoing>_56</outgoing>
      <outgoing>_59</outgoing>
    </userTask>
    <userTask id="_10" name="cloud_resource_leader" implementation="##unspecified" activiti:assignee="${cloud_leader_2}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>_22</incoming>
      <outgoing>_25</outgoing>
      <outgoing>_16</outgoing>
      <outgoing>_61</outgoing>
      <outgoing>Flow_12ow05y</outgoing>
      <outgoing>Flow_0amdyh7</outgoing>
      <outgoing>Flow_04ebcxm</outgoing>
    </userTask>
    <sequenceFlow id="_21" sourceRef="_8" targetRef="_9">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_22" sourceRef="_9" targetRef="_10">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="_23" name="CloudGateway">
      <incoming>_18</incoming>
      <outgoing>_28</outgoing>
      <outgoing>_31</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="_25" sourceRef="_10" targetRef="Gateway_070fvlb">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <userTask id="_13" name="offline_open_h" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>_28</incoming>
      <outgoing>_29</outgoing>
    </userTask>
    <userTask id="_15" name="information_archive_h" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>_29</incoming>
      <outgoing>_37</outgoing>
    </userTask>
    <sequenceFlow id="_28" sourceRef="_23" targetRef="_13">
      <conditionExpression xsi:type="tFormalExpression">${openWay==0}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_29" sourceRef="_13" targetRef="_15" />
    <userTask id="_17" name="network_provisioning_h" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>_31</incoming>
      <outgoing>_33</outgoing>
    </userTask>
    <sequenceFlow id="_31" sourceRef="_23" targetRef="_17">
      <conditionExpression xsi:type="tFormalExpression">${openWay==1}</conditionExpression>
    </sequenceFlow>
    <userTask id="_19" name="resource_creation_h" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>_33</incoming>
      <outgoing>_36</outgoing>
    </userTask>
    <sequenceFlow id="_33" sourceRef="_17" targetRef="_19" />
    <endEvent id="_30" name="EndEvent">
      <incoming>_36</incoming>
      <incoming>_37</incoming>
      <incoming>_38</incoming>
      <incoming>_40</incoming>
      <incoming>_41</incoming>
      <incoming>_42</incoming>
      <incoming>Flow_1g3olhc</incoming>
      <incoming>Flow_04ebcxm</incoming>
      <incoming>Flow_0k7795z</incoming>
      <incoming>Flow_0t60vjo</incoming>
      <inputSet />
    </endEvent>
    <sequenceFlow id="_36" name="end" sourceRef="_19" targetRef="_30" />
    <sequenceFlow id="_37" name="end" sourceRef="_15" targetRef="_30" />
    <sequenceFlow id="_38" name="userCancel" sourceRef="_2" targetRef="_30">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_40" name="govAdminCancel" sourceRef="_5" targetRef="_30">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_41" name="branchLeaderCancel" sourceRef="_8" targetRef="_30">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_42" name="govLeaderCancel" sourceRef="_9" targetRef="_30">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_45" name="govAdminBackUser" sourceRef="_5" targetRef="_2">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_48" name="branchLeaderBackUser" sourceRef="_8" targetRef="_2">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_49" name="govLeaderBackUser" sourceRef="_9" targetRef="_2">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_50" name="govAdminBackScheme" sourceRef="_5" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='response_scheme_manager'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_54" name="branchLeaderBackScheme" sourceRef="_8" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='response_scheme_manager'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_56" name="govLeaderBackScheme" sourceRef="_9" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='response_scheme_manager'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_59" name="govLeaderBackBranchLeader" sourceRef="_9" targetRef="_8">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='branch_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_61" name="cloudBackBranchLeader" sourceRef="_10" targetRef="_8">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='branch_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_16" name="cloudBackGovLeader" sourceRef="_10" targetRef="_9">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='province_gov_leader'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0p0ntvp" sourceRef="_3" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression">${message==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_12ow05y" sourceRef="_10" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='response_scheme_manager'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0amdyh7" sourceRef="_10" targetRef="_2">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_1g3olhc" sourceRef="_3" targetRef="_30">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0m7h8yf" sourceRef="_3" targetRef="_2">
      <conditionExpression xsi:type="tFormalExpression">${message==0 &amp;&amp; nodeCode=='user_task'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_04ebcxm" sourceRef="_10" targetRef="_30">
      <conditionExpression xsi:type="tFormalExpression">${message==2}</conditionExpression>
    </sequenceFlow>
    <userTask id="_26" name="network_provisioning_l" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:assigneeType>static</activiti:assigneeType>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>Flow_149xhdy</incoming>
      <outgoing>Flow_0o1x627</outgoing>
    </userTask>
    <userTask id="_27" name="resource_creation_l" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:assigneeType>static</activiti:assigneeType>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>Flow_0o1x627</incoming>
      <outgoing>Flow_0t60vjo</outgoing>
    </userTask>
    <userTask id="_20" name="offline_open_l" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:assigneeType>static</activiti:assigneeType>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>Flow_0dj0bpl</incoming>
      <outgoing>Flow_061l2f1</outgoing>
    </userTask>
    <userTask id="_24" name="information_archive_l" implementation="##unspecified" activiti:assignee="${xiaoyun}">
      <extensionElements>
        <activiti:assigneeType>static</activiti:assigneeType>
        <activiti:staticAssigneeVariables>[]</activiti:staticAssigneeVariables>
      </extensionElements>
      <incoming>Flow_061l2f1</incoming>
      <outgoing>Flow_0k7795z</outgoing>
    </userTask>
    <exclusiveGateway id="Gateway_070fvlb" name="CloudGateway">
      <incoming>_25</incoming>
      <outgoing>Flow_149xhdy</outgoing>
      <outgoing>Flow_0dj0bpl</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_149xhdy" sourceRef="Gateway_070fvlb" targetRef="_26">
      <conditionExpression xsi:type="tFormalExpression">${openWay==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0dj0bpl" sourceRef="Gateway_070fvlb" targetRef="_20">
      <conditionExpression xsi:type="tFormalExpression">${openWay==0}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_061l2f1" sourceRef="_20" targetRef="_24" />
    <sequenceFlow id="Flow_0o1x627" sourceRef="_26" targetRef="_27" />
    <sequenceFlow id="Flow_0k7795z" sourceRef="_24" targetRef="_30" />
    <sequenceFlow id="Flow_0t60vjo" sourceRef="_27" targetRef="_30" />
  </process>
  <bpmndi:BPMNDiagram id="Yaoqiang_Diagram-non-standard-resource-process" name="New Diagram" documentation="background=#3C3F41;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0">
    <bpmndi:BPMNPlane bpmnElement="non-standard-resource-process">
      <bpmndi:BPMNShape id="Shape-_2" bpmnElement="_1">
        <dc:Bounds x="-316" y="194" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-327" y="193" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_3" bpmnElement="_2">
        <dc:Bounds x="-343" y="250" width="85" height="55" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_5" bpmnElement="_3">
        <dc:Bounds x="-343" y="342" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-323" y="112" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_7" bpmnElement="_7" isMarkerVisible="false">
        <dc:Bounds x="-316" y="434" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-334" y="439" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_9" bpmnElement="_5">
        <dc:Bounds x="-473" y="492" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-808" y="327" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_10" bpmnElement="_8">
        <dc:Bounds x="-213" y="492" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-553" y="162" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_19" bpmnElement="_9">
        <dc:Bounds x="-213" y="582" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-753" y="252" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_20" bpmnElement="_10">
        <dc:Bounds x="-213" y="672" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-753" y="212" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_23" bpmnElement="_23" isMarkerVisible="false">
        <dc:Bounds x="-446" y="764" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-529.5" y="773" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_26" bpmnElement="_13">
        <dc:Bounds x="-533" y="832" width="85" height="55" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_27" bpmnElement="_15">
        <dc:Bounds x="-533" y="923" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-1553" y="753" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_30" bpmnElement="_17">
        <dc:Bounds x="-415" y="832" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-1260" y="517" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_32" bpmnElement="_19">
        <dc:Bounds x="-415" y="923" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-1440" y="608" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Shape-_34" bpmnElement="_30">
        <dc:Bounds x="-316" y="1024" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-375" y="1033" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0uewzvy" bpmnElement="_26">
        <dc:Bounds x="-153" y="832" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-998" y="517" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0oc2mut" bpmnElement="_27">
        <dc:Bounds x="-153" y="923" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-1178" y="608" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0f8ixkk" bpmnElement="_20">
        <dc:Bounds x="-273" y="832" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-1118" y="662" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xt712p" bpmnElement="_24">
        <dc:Bounds x="-273" y="923" width="85" height="55" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-1293" y="753" width="85" height="55" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rvwxcm" bpmnElement="Gateway_070fvlb" isMarkerVisible="false">
        <dc:Bounds x="-186" y="764" width="32" height="32" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-206.5" y="806" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge__4" bpmnElement="_4" sourceElement="_1" targetElement="_2">
        <di:waypoint x="-302" y="226" />
        <di:waypoint x="-302" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__6" bpmnElement="_6" sourceElement="_2" targetElement="_3">
        <di:waypoint x="-300" y="305" />
        <di:waypoint x="-300" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__11" bpmnElement="_11" sourceElement="_7" targetElement="_5">
        <di:waypoint x="-308" y="458" />
        <di:waypoint x="-410" y="458" />
        <di:waypoint x="-427" y="492" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-366" y="460.0000000000001" width="39" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__12" bpmnElement="_12" sourceElement="Shape-_7" targetElement="Shape-_10">
        <di:waypoint x="-286" y="452" />
        <di:waypoint x="-170" y="471" />
        <di:waypoint x="-170" y="492" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-261" y="466" width="51" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__18" bpmnElement="_18" sourceElement="Shape-_9" targetElement="Shape-_23">
        <di:waypoint x="-430" y="547" />
        <di:waypoint x="-430" y="764" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__21" bpmnElement="_21" sourceElement="_8" targetElement="_9">
        <di:waypoint x="-170" y="547" />
        <di:waypoint x="-170" y="582" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__22" bpmnElement="_22" sourceElement="_9" targetElement="_10">
        <di:waypoint x="-170" y="637" />
        <di:waypoint x="-170" y="672" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__25" bpmnElement="_25" sourceElement="Shape-_20" targetElement="BPMNShape_1rvwxcm">
        <di:waypoint x="-170" y="727" />
        <di:waypoint x="-170" y="764" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__28" bpmnElement="_28" sourceElement="Shape-_23" targetElement="Shape-_26">
        <di:waypoint x="-435" y="791" />
        <di:waypoint x="-490" y="800" />
        <di:waypoint x="-490" y="830" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__29" bpmnElement="_29" sourceElement="_13" targetElement="_15">
        <di:waypoint x="-490" y="887" />
        <di:waypoint x="-490" y="923" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__31" bpmnElement="_31" sourceElement="Shape-_23" targetElement="Shape-_30">
        <di:waypoint x="-425" y="791" />
        <di:waypoint x="-372" y="800" />
        <di:waypoint x="-372" y="830" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__33" bpmnElement="_33" sourceElement="_17" targetElement="_19">
        <di:waypoint x="-372" y="887" />
        <di:waypoint x="-372" y="923" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="0" y="0" width="0" height="0" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__36" bpmnElement="_36" sourceElement="Shape-_32" targetElement="Shape-_34">
        <di:waypoint x="-372" y="981" />
        <di:waypoint x="-372" y="1001" />
        <di:waypoint x="-300" y="1001" />
        <di:waypoint x="-300" y="1024" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-392.9687194226708" y="982" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__37" bpmnElement="_37" sourceElement="Shape-_27" targetElement="Shape-_34">
        <di:waypoint x="-490" y="981" />
        <di:waypoint x="-490" y="1000" />
        <di:waypoint x="-300" y="1000" />
        <di:waypoint x="-300" y="1024" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-508.9999999999998" y="982" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__38" bpmnElement="_38" sourceElement="Shape-_3" targetElement="Shape-_34">
        <di:waypoint x="-258" y="270" />
        <di:waypoint x="100" y="270" />
        <di:waypoint x="100" y="1080" />
        <di:waypoint x="-300" y="1080" />
        <di:waypoint x="-300" y="1056" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="22.99999999999983" y="253.5" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__40" bpmnElement="_40" sourceElement="Shape-_9" targetElement="Shape-_34">
        <di:waypoint x="-473" y="520" />
        <di:waypoint x="-600" y="520" />
        <di:waypoint x="-600" y="1080" />
        <di:waypoint x="-300" y="1080" />
        <di:waypoint x="-300" y="1056" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-609.9999999999998" y="503.5000000000001" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__41" bpmnElement="_41" sourceElement="_8" targetElement="_30">
        <di:waypoint x="-128" y="510" />
        <di:waypoint x="100" y="510" />
        <di:waypoint x="100" y="1080" />
        <di:waypoint x="-301" y="1080" />
        <di:waypoint x="-301" y="1056" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-7" y="496" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__42" bpmnElement="_42" sourceElement="Shape-_19" targetElement="Shape-_34">
        <di:waypoint x="-128" y="600" />
        <di:waypoint x="100" y="600" />
        <di:waypoint x="100" y="1080" />
        <di:waypoint x="-300" y="1080" />
        <di:waypoint x="-300" y="1056" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7" y="583" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__45" bpmnElement="_45" sourceElement="Shape-_9" targetElement="Shape-_3">
        <di:waypoint x="-472" y="500" />
        <di:waypoint x="-500" y="500" />
        <di:waypoint x="-500" y="290" />
        <di:waypoint x="-343" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-517.0000000000002" y="432.0000000000002" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__48" bpmnElement="_48" sourceElement="Shape-_10" targetElement="Shape-_3">
        <di:waypoint x="-129" y="500" />
        <di:waypoint x="-100" y="500" />
        <di:waypoint x="-100" y="290" />
        <di:waypoint x="-258" y="289" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-115" y="441" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__49" bpmnElement="_49" sourceElement="Shape-_19" targetElement="Shape-_3">
        <di:waypoint x="-129" y="590" />
        <di:waypoint x="-50" y="590" />
        <di:waypoint x="-50" y="290" />
        <di:waypoint x="-258" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-63" y="302" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__50" bpmnElement="_50" sourceElement="Shape-_9" targetElement="Shape-_5">
        <di:waypoint x="-472" y="500" />
        <di:waypoint x="-490" y="500" />
        <di:waypoint x="-490" y="380" />
        <di:waypoint x="-343" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-491" y="417" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__54" bpmnElement="_54" sourceElement="Shape-_10" targetElement="Shape-_5">
        <di:waypoint x="-129" y="500" />
        <di:waypoint x="-110" y="500" />
        <di:waypoint x="-110" y="380" />
        <di:waypoint x="-258" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-111.00000000000011" y="439" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__56" bpmnElement="_56" sourceElement="Shape-_19" targetElement="Shape-_5">
        <di:waypoint x="-129" y="590" />
        <di:waypoint x="-60" y="590" />
        <di:waypoint x="-60" y="380" />
        <di:waypoint x="-258" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-209.00000000000034" y="386" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__59" bpmnElement="_59" sourceElement="Shape-_19" targetElement="Shape-_10">
        <di:waypoint x="-129" y="590" />
        <di:waypoint x="-70" y="590" />
        <di:waypoint x="-70" y="530" />
        <di:waypoint x="-128" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-151.500000000039" y="546.0000000000155" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__61" bpmnElement="_61" sourceElement="Shape-_20" targetElement="Shape-_10">
        <di:waypoint x="-128" y="700" />
        <di:waypoint x="-10" y="700" />
        <di:waypoint x="-10" y="530" />
        <di:waypoint x="-128" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-22.99999999999966" y="530" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge__16" bpmnElement="_16" sourceElement="Shape-_20" targetElement="Shape-_19">
        <di:waypoint x="-128" y="700" />
        <di:waypoint x="-20" y="700" />
        <di:waypoint x="-20" y="620" />
        <di:waypoint x="-128" y="620" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="-111.49999999999824" y="626.999999999999" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p0ntvp_di" bpmnElement="Flow_0p0ntvp">
        <di:waypoint x="-300" y="397" />
        <di:waypoint x="-300" y="434" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12ow05y_di" bpmnElement="Flow_12ow05y">
        <di:waypoint x="-128" y="700" />
        <di:waypoint x="0" y="700" />
        <di:waypoint x="0" y="380" />
        <di:waypoint x="-258" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0amdyh7_di" bpmnElement="Flow_0amdyh7">
        <di:waypoint x="-128" y="700" />
        <di:waypoint x="10" y="700" />
        <di:waypoint x="10" y="290" />
        <di:waypoint x="-258" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g3olhc_di" bpmnElement="Flow_1g3olhc">
        <di:waypoint x="-258" y="370" />
        <di:waypoint x="100" y="370" />
        <di:waypoint x="100" y="1080" />
        <di:waypoint x="-300" y="1080" />
        <di:waypoint x="-300" y="1056" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m7h8yf_di" bpmnElement="Flow_0m7h8yf">
        <di:waypoint x="-342" y="350" />
        <di:waypoint x="-370" y="350" />
        <di:waypoint x="-370" y="290" />
        <di:waypoint x="-343" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04ebcxm_di" bpmnElement="Flow_04ebcxm">
        <di:waypoint x="-128" y="710" />
        <di:waypoint x="100" y="710" />
        <di:waypoint x="100" y="1080" />
        <di:waypoint x="-300" y="1080" />
        <di:waypoint x="-300" y="1056" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_149xhdy_di" bpmnElement="Flow_149xhdy">
        <di:waypoint x="-158" y="784" />
        <di:waypoint x="-100" y="800" />
        <di:waypoint x="-100" y="832" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dj0bpl_di" bpmnElement="Flow_0dj0bpl">
        <di:waypoint x="-182" y="784" />
        <di:waypoint x="-230" y="800" />
        <di:waypoint x="-230" y="832" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_061l2f1_di" bpmnElement="Flow_061l2f1">
        <di:waypoint x="-230" y="887" />
        <di:waypoint x="-230" y="923" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o1x627_di" bpmnElement="Flow_0o1x627">
        <di:waypoint x="-110" y="887" />
        <di:waypoint x="-110" y="923" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k7795z_di" bpmnElement="Flow_0k7795z">
        <di:waypoint x="-230" y="978" />
        <di:waypoint x="-230" y="1001" />
        <di:waypoint x="-300" y="1001" />
        <di:waypoint x="-300" y="1024" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t60vjo_di" bpmnElement="Flow_0t60vjo">
        <di:waypoint x="-110" y="978" />
        <di:waypoint x="-110" y="1000" />
        <di:waypoint x="-300" y="1000" />
        <di:waypoint x="-300" y="1024" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
