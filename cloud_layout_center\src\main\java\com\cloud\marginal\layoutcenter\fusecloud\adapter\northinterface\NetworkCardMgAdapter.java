package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EcsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.PortParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.dto.edge.CreatePortDTO;
import com.cloud.marginal.model.dto.edge.PortOperationDTO;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 网卡管理适配
 */
@Component
@Slf4j
public class NetworkCardMgAdapter extends BaseNorthInterfaceAdapter {

    /**
     * 创建网卡
     */
    public TaskVO createNetworkCard(String taskId, Integer taskSource){
        log.info("createPort start");
        CreatePortDTO portCreateDTO = generateCreatePortDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateNetworkCard(),
                null,
                portCreateDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVOResult,"create port");
        return tasksVOResult.getEntity();
    }

    private CreatePortDTO generateCreatePortDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        //PORT_CREATE 产品中心未定义产品 故而特殊处理
        ProductOrderParam productOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.ECS_CREATE.getCode());
        EcsParam ecsParam = JSONObject.parseObject(productOrder.getAttrs(), EcsParam.class);

        CreatePortDTO createPortDTO = new CreatePortDTO();
        createPortDTO.setBillId(layoutOrderParam.getAccount());
        createPortDTO.setGroupId(layoutOrderParam.getCustomId());
        createPortDTO.setRegionCode(layoutOrderParam.getRegionCode());
        createPortDTO.setName(ecsParam.getVmName()+"-port");
        List<CreatePortDTO.FixedIp>  fixedIps = Lists.newArrayList();
        for (EcsParam.Nic nic: ecsParam.getNics()) {
            CreatePortDTO.FixedIp ip = new CreatePortDTO.FixedIp();
            ip.setSubnetId(nic.getSubnetId());
            ip.setIpAddress(nic.getIpAddress());
            fixedIps.add(ip);
        }
        createPortDTO.setFixedIps(fixedIps);
        createPortDTO.setOrderId(productOrder.getSubOrderId());
        return createPortDTO;
    }

    /**
     * 将网卡挂载至指定主机
     * @param mainTaskId
     */
    public TaskVO attachNetworkCard(String mainTaskId, Integer taskSource){
        log.info("attachNetworkCard start");
        PortOperationDTO netWorkCardAttachDTO = new PortOperationDTO();
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getAttachNetworkCard(),
                null,
                netWorkCardAttachDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVOResult,"attach network card");
        return tasksVOResult.getEntity();
    }
}
