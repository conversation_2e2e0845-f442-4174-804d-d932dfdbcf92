package com.datatech.slgzt.enums;

import org.springframework.util.StringUtils;

public enum CmdbModelTypeEnum {
    /**
     * 云产品类型
     */
    VM("VIRTUAL_MACHINE", "云主机"),
    OBS("BUCKET", "对象存储"),
    I3_IP("I3_IP", "三级IP地址"),
    I3_DETAIL_IP("I3_DETAIL_IP", "三级IP地址明细表"),
    TENANT("TENANT", "租户"),
    BUSINESS_SYSTEM("BUSINESS_SYSTEM", "业务系统");


    private  String code;
    private  String desc;

    CmdbModelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static CmdbModelTypeEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (CmdbModelTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}