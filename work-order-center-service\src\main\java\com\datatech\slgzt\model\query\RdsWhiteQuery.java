package com.datatech.slgzt.model.query;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单查询对象
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Data
public class RdsWhiteQuery {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID列表
     */
    private List<Long> idList;

    /**
     * 数据库主键ID
     */
    private String rdsId;

    /**
     * 白名单名称
     */
    private String whiteName;

    /**
     * 白名单IP
     */
    private String ips;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 1有效 0删除
     */
    private Integer enabled;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;
} 