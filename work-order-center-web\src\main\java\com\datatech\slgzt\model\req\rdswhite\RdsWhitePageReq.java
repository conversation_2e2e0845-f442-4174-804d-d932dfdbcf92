package com.datatech.slgzt.model.req.rdswhite;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单分页查询请求
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Data
public class RdsWhitePageReq {

    /**
     * 数据库主键ID
     */
    private String rdsId;

    /**
     * 白名单名称
     */
    private String whiteName;

    /**
     * 白名单IP
     */
    private String ips;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 1有效 0删除
     */
    private Integer enabled;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页显示条数
     */
    private Integer pageSize = 10;
} 