package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.NetworkParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.SubnetParam;
import com.cloud.marginal.model.dto.edge.CreateSubnetDTO;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 子网适配管理
 */
@Component
@Slf4j
public class SubnetMgAdapter extends BaseNorthInterfaceAdapter{

    /**
     * 创建子网
     */
    public TaskVO createSubnet(String taskId, Integer taskSource){
        log.info("createSubnet start");
        CreateSubnetDTO createSubnetDTO = generateCreateSubnetDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateSubnet(),
                null,
                createSubnetDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult,"create Subnet");
        return tasksVOResult.getEntity();
    }

    private CreateSubnetDTO generateCreateSubnetDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam subnetOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.SUBNET_CREATE.getCode());
        SubnetParam subnetParam = JSONObject.parseObject(subnetOrder.getAttrs(), SubnetParam.class);
        CreateSubnetDTO createSubnetDTO = new CreateSubnetDTO();
        if(StringUtils.isEmpty(subnetParam.getNetworkId())){
            ProductOrderParam networkOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NETWORK_CREATE.getCode());
            NetworkParam networkParam = JSONObject.parseObject(networkOrder.getAttrs(), NetworkParam.class);
            subnetParam.setNetworkId(networkParam.getId());
        }
        BeanUtils.copyProperties(subnetParam,createSubnetDTO);
        createSubnetDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        return createSubnetDTO;
    }
}
