package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 资源中心任务管理接口适配
 */
@Component
@Slf4j
public class TaskMgAdapter extends BaseNorthInterfaceAdapter {


    /**
     * 任务id查询
     */
    public TaskVO getTaskById(String cloudTaskId){
        log.info("getTaskById start");
        Map<String,Object> pathParam = new HashMap<>();
        pathParam.put("taskId",cloudTaskId);
        CecResult<TaskVO> result = northInterfaceHttpRequestUtil.get(northInterfaceAddress.getTaskDetailById()+"?taskId={taskId}",
                null,
                new TypeReference<CecResult<TaskVO>>() {
                },
                pathParam);
        return result.getEntity();

    }

     /**
     * 订单id查询底层任务
     */
    public TaskVO getTaskByOrderId(String orderId){
        log.info("getTaskByOrderId start");
        Map<String,Object> pathParam = new HashMap<>();
        pathParam.put("orderId",orderId);
        CecResult<TaskVO> result = northInterfaceHttpRequestUtil.get(northInterfaceAddress.getTaskDetailByOrderId()+"?orderId={orderId}",
                null,
                new TypeReference<CecResult<TaskVO>>() {
                },
                pathParam);
        return result.getEntity();

    }
}
