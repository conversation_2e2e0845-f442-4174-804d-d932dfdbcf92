package com.datatech.slgzt.model.req.vip;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 虚拟IP分页查询请求
 */
@Data
public class VirtualIpPageReq {
    /** 虚拟IP名称 */
    private String vipName;
    /** 云平台编码 */
    private String domainCode;
    /** 业务系统id */
    private String businessSystemId;

    private String vpcName;

    private String resourcePoolId;
    /** 资源池编码 */
    private String regionCode;
    /** 可用区ID */
    private String azId;

    private LocalDateTime createdTimeEnd;

    private LocalDateTime createdTimeStart;

    //来源
    private String sourceType;
    /** 当前页码 */
    private Integer pageNum = 1;
    /** 每页显示条数 */
    private Integer pageSize = 10;
} 