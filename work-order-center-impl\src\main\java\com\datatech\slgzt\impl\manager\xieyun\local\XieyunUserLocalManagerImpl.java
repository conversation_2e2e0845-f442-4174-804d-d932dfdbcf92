package com.datatech.slgzt.impl.manager.xieyun.local;

import com.datatech.slgzt.convert.XieYunBeanManagerConvert;
import com.datatech.slgzt.dao.container.XieYunUserDAO;
import com.datatech.slgzt.dao.model.container.XieYunUserDO;
import com.datatech.slgzt.enums.DeleteEnum;
import com.datatech.slgzt.manager.xieyun.local.XieyunUserLocalManager;
import com.datatech.slgzt.model.dto.XieYunUserDTO;
import com.datatech.slgzt.model.query.container.XieYunUserQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 谐云用户本地处理
 *
 * @Author: liupeihan
 * @Date: 2025/4/15
 */

@Service
public class XieyunUserLocalManagerImpl implements XieyunUserLocalManager {

    @Resource
    private XieYunUserDAO userDAO;

    @Resource
    private XieYunBeanManagerConvert convert;

    @Override
    public List<XieYunUserDTO> list(XieYunUserQuery query) {
        return convert.userDOS2DTOS(userDAO.list(query));
    }

    @Override
    public void insert(XieYunUserDTO userDTO) {
        XieYunUserDO userDO = convert.userDTO2DO(userDTO);
        userDAO.insert(userDO);
    }

    @Override
    public void updateDeleteByUserId(String userId, Integer delete) {
        XieYunUserDO userDO = new XieYunUserDO();
        userDO.setDeleted(DeleteEnum.DELETE.code().equals(delete));
        userDO.setUpdatedTime(LocalDateTime.now());
        userDAO.updateDeleteByUserId(userDO, userId);
    }
}

