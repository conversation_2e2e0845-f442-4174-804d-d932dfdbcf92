package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.base.BaseService;
import com.cloud.marginal.layoutcenter.factory.LaoutServiceFactory;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EipParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.NatParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import com.cloud.resource.api.nat.dto.CreateNatRcDto;
import com.cloud.resource.api.nat.dto.DeleteNatRcDto;
import com.cloud.resource.api.nat.dto.NatBindEipRcDto;
import com.cloud.resource.api.nat.dto.NatUnBindEipRcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * nat适配管理
 */
@Component
@Slf4j
public class NatMgAdapter extends BaseNorthInterfaceAdapter implements BaseService {

    @Resource
    private LaoutServiceFactory laoutServiceFactory;

    @Override
    public void afterPropertiesSet() throws Exception {
        laoutServiceFactory.register("NAT", this);
    }

    @Override
    public TaskVO handler(LayoutTaskVO layoutTaskVO) {
        String id = layoutTaskVO.getId();
        Integer taskSource = layoutTaskVO.getTaskSource();
        switch (layoutTaskVO.getTaskCode()) {
            case "NAT_CREATE":
                return this.createNat(id, taskSource);
            case "NAT_DELETE":
                return this.deleteNat(id, taskSource);
            case "NAT_BIND_EIP":
                return this.bindNatEip(id, taskSource);
            case "NAT_UNBIND_EIP":
                return this.unbindNatEip(id, taskSource);
            default:
                throw new BusinessException("产品类型错误");
        }
    }

    /**
     * 创建nat
     */
    public TaskVO createNat(String taskId, Integer taskSource) {
        log.info("createNat start");
        CreateNatRcDto natDTO = generateCreateNatDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateNat(),
                null,
                natDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("createNat url is:{}", northInterfaceAddress.getCreateNat());
        log.info("createNat params is:{}", JSONObject.toJSON(natDTO));

        checkResultThrowExceptionIfFail(tasksVoResult, "create nat");
        return tasksVoResult.getEntity();
    }

    /**
     * nat绑定Eip
     */
    public TaskVO bindNatEip(String taskId, Integer taskSource) {
        log.info("bindNatEip start");
        NatBindEipRcDto natDTO = generateBindNatEipDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getBindNatEip(),
                null,
                natDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("bindNatEip url is:{}", northInterfaceAddress.getBindNatEip());
        log.info("bindNatEip params is:{}", JSONObject.toJSON(natDTO));
        checkResultThrowExceptionIfFail(tasksVoResult, "nat bind eip");
        return tasksVoResult.getEntity();
    }

    /**
     * nat解绑Eip
     */
    public TaskVO unbindNatEip(String taskId, Integer taskSource) {
        log.info("unbindNatEip start");
        NatUnBindEipRcDto natDTO = generateUnbindNatEipDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getUnbindNatEip(),
                null,
                natDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("unbindNatEip url is:{}", northInterfaceAddress.getUnbindNatEip());
        log.info("unbindNatEip params is:{}", JSONObject.toJSON(natDTO));
        checkResultThrowExceptionIfFail(tasksVoResult, "nat unbind eip");
        return tasksVoResult.getEntity();
    }


    /**
     * nat删除
     */
    public TaskVO deleteNat(String taskId, Integer taskSource) {
        log.info("deleteNat start");
        DeleteNatRcDto natDTO = generateDeleteRdsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteNat(),
                null,
                natDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("deleteNat url is:{}", northInterfaceAddress.getDeleteNat());
        log.info("deleteNat params is:{}", JSONObject.toJSON(natDTO));
        checkResultThrowExceptionIfFail(tasksVoResult, "delete nat");
        return tasksVoResult.getEntity();
    }


    private CreateNatRcDto generateCreateNatDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam natOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NAT_CREATE.getCode());
        NatParam natParam = JSONObject.parseObject(natOrder.getAttrs(), NatParam.class);
        CreateNatRcDto natDTO = new CreateNatRcDto();
        BeanUtils.copyProperties(natParam, natDTO);
        natDTO.setOrderId(layoutOrderParam.getSubOrderId());
        natDTO.setRegionCode(layoutOrderParam.getRegionCode());
        natDTO.setBillId(layoutOrderParam.getAccount());
        natDTO.setGroupId(layoutOrderParam.getCustomId());
        natDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());

        natDTO.setGlobalId(natParam.getGId());
        return natDTO;
    }


    private NatBindEipRcDto generateBindNatEipDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam natOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NAT_CREATE.getCode());
        ProductOrderParam eipOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_CREATE.getCode());
        NatParam natParam = JSONObject.parseObject(natOrder.getAttrs(), NatParam.class);
        EipParam eipParam = JSONObject.parseObject(eipOrder.getAttrs(), EipParam.class);

        NatBindEipRcDto natDTO = new NatBindEipRcDto();
        natDTO.setBillId(layoutOrderParam.getAccount());
        natDTO.setGroupId(layoutOrderParam.getCustomId());
        natDTO.setRegionCode(layoutOrderParam.getRegionCode());
        natDTO.setInstanceId(natParam.getGId());
        natDTO.setEipId(eipParam.getgId());
        return natDTO;
    }


    private NatUnBindEipRcDto generateUnbindNatEipDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam natOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NAT_DELETE.getCode());
        ProductOrderParam eipOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_DELETE.getCode());
        NatParam natParam = JSONObject.parseObject(natOrder.getAttrs(), NatParam.class);
        EipParam eipParam = JSONObject.parseObject(eipOrder.getAttrs(), EipParam.class);
        NatUnBindEipRcDto natDTO = new NatUnBindEipRcDto();
        natDTO.setBillId(layoutOrderParam.getAccount());
        natDTO.setGroupId(layoutOrderParam.getCustomId());
        natDTO.setRegionCode(layoutOrderParam.getRegionCode());
        natDTO.setInstanceId(natParam.getGId());
        natDTO.setEipId(eipParam.getgId());
        return natDTO;
    }

    private DeleteNatRcDto generateDeleteRdsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam natOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.NAT_DELETE.getCode());
        NatParam natParam = JSONObject.parseObject(natOrder.getAttrs(), NatParam.class);
        DeleteNatRcDto natDTO = new DeleteNatRcDto();
        natDTO.setBillId(layoutOrderParam.getAccount());
        natDTO.setGroupId(layoutOrderParam.getCustomId());
        natDTO.setRegionCode(layoutOrderParam.getRegionCode());
        natDTO.setInstanceId(natParam.getGId());
        return natDTO;
    }
}
