package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.SlbListenerDTO;
import com.datatech.slgzt.model.query.SlbListenerQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * SLB监听器Manager接口
 */
public interface SlbListenerManager {
    
    /**
     * 新增
     *
     * @return
     */
    String create(SlbListenerDTO dto);
    
    /**
     * 修改
     */
    void update(SlbListenerDTO dto);
    
    /**
     * 删除
     */
    void delete(String id);
    
    /**
     * 根据ID查询
     */
    SlbListenerDTO getById(String id);

    List<SlbListenerDTO> listBySlbId(Long slbId);

    List<SlbListenerDTO> listBySlbIds(List<Long> slbIds);

    List<SlbListenerDTO> listByCreateTaskId(String createTaskId);

    List<SlbListenerDTO> listByUpdateTaskId(String updateTaskId);

    List<SlbListenerDTO> listByDeleteTaskId(String deleteTaskId);

    
    /**
     * 分页查询
     */
    PageResult<SlbListenerDTO> page(SlbListenerQuery query);
} 