package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.model.dto.layout.SaveApiDef;
import com.cloud.marginal.model.dto.layout.ApiDefDto;
import com.cloud.marginal.model.entity.layout.LayoutApiDef;
import com.cloud.marginal.model.vo.layout.ApiDefVO;

import java.util.List;

/**
 * <p>
 * 编排API配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface LayoutApiDefService extends IService<LayoutApiDef> {

    CecResult createApiDef(SaveApiDef layoutApiDef);

    CecResult updateApiDef(SaveApiDef layoutApiDef);

    CecResult deleteApiDef(String apiDefId);

    List<ApiDefVO> listApiDef(String apiName);

    CecPage<ApiDefVO> pageApiDef(ApiDefDto apiDefDto);
}
