package com.datatech.slgzt.annotation;

import java.lang.annotation.*;

/**
 * Kafka消息幂等注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface KafkaIdempotent {

    /**
     * 幂等键的超时时间（秒）
     */
    long timeout() default 3600;
    
    /**
     * 幂等键的前缀
     */
    String prefix() default "kafka:idempotent:";
    
    /**
     * 是否使用方法参数作为幂等键的一部分
     */
    boolean useArgs() default true;
} 