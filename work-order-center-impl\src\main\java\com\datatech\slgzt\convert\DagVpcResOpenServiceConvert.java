package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.vpc.VpcOrderDTO;
import com.datatech.slgzt.model.dto.vpc.VpcSubnetDTO;
import com.datatech.slgzt.model.nostander.VpcSubnetUnTaskModel;
import com.datatech.slgzt.model.nostander.VpcUnTaskModel;
import org.mapstruct.Mapper;

/**
 * 编排VPC资源转换器
 */
@Mapper(componentModel = "spring")
public interface DagVpcResOpenServiceConvert {

    VpcUnTaskModel vpcDto2Model(VpcOrderDTO dto);

    VpcOrderDTO vpcModel2dto(VpcUnTaskModel model);

    VpcSubnetUnTaskModel subnetDto2Model(VpcSubnetDTO dto);

    VpcSubnetDTO subnetModel2dto(VpcSubnetUnTaskModel model);
}