package com.datatech.slgzt.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.convert.RdsUserManagerConvert;
import com.datatech.slgzt.dao.RdsUserDAO;
import com.datatech.slgzt.dao.model.RdsUserDO;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.RdsUserManager;
import com.datatech.slgzt.model.dto.RdsUserDTO;
import com.datatech.slgzt.model.dto.RdsUserOperateDTO;
import com.datatech.slgzt.model.query.RdsUserQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.UuidUtil;
import com.datatech.slgzt.warpper.PageWarppers;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: workordercenterproject
 * @description: 数据库用户Manager实现类
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@Service
@Slf4j
public class RdsUserManagerImpl implements RdsUserManager {

    @Resource
    private RdsUserDAO rdsUserDAO;

    @Resource
    private RdsUserManagerConvert rdsUserManagerConvert;

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterUrl;

    private String createUserUrl = "/v1/cloud/resourcecenter/rds/user/create";

    private String deleteUserUrl = "/v1/cloud/resourcecenter/rds/user/delete";

    private String resetPwdUrl = "/v1/cloud/resourcecenter/rds/user/resetPwd";

    @Override
    public PageResult<RdsUserDTO> page(RdsUserQuery query) {
        // 1. start page
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        
        // 2. execute query
        List<RdsUserDO> list = rdsUserDAO.list(query);
        
        // 3. convert and return result
        return PageWarppers.box(new PageInfo<>(list), rdsUserManagerConvert::do2dto);
    }

    @Override
    public List<RdsUserDTO> list(RdsUserQuery query) {
        List<RdsUserDO> list = rdsUserDAO.list(query);
        return rdsUserManagerConvert.do2dto(list);
    }

    @Override
    @Transactional
    public void add(RdsUserDTO rdsUserDTO) {
        rdsUserDTO.setId(IdUtil.getSnowflake().nextId());
        String gid = UuidUtil.getGid(ProductTypeEnum.RDS_MYSQL.getCode());
        rdsUserDTO.setGid(gid);
        // 设置创建时间
        rdsUserDTO.setCreateTime(LocalDateTime.now());
        rdsUserDAO.insert(rdsUserManagerConvert.dto2do(rdsUserDTO));
        //调资源中心创建用户
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("rdsId", rdsUserDTO.getRdsId());
        paramMap.put("userName", rdsUserDTO.getUserName());
        paramMap.put("password", rdsUserDTO.getPassword());
        paramMap.put("gId", rdsUserDTO.getGid());
        Mapper dataMapper = OkHttps.sync(resourceCenterUrl + createUserUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(paramMap))
                .post()
                .getBody()
                .toMapper();
        log.info("创建数据库用户响应: {}", dataMapper.toString());
        String successStr = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "创建数据库用户失败: " + dataMapper.getString("message"));
    }

    @Override
    public void update(RdsUserOperateDTO rdsUserOperateDTO) {
        Long id = rdsUserOperateDTO.getId();
        //根据id获取对象
        RdsUserDTO rdsUserDTO = this.getById(id);
        // 设置修改时间
        rdsUserDTO.setModifyTime(LocalDateTime.now());
        rdsUserDTO.setPassword(rdsUserOperateDTO.getNewPassword());
        rdsUserDAO.update(rdsUserManagerConvert.dto2do(rdsUserDTO));
        //调用资源中心更新用户
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rdsId", rdsUserDTO.getRdsId());
        paramMap.put("rdsUserId", rdsUserDTO.getGid());
        paramMap.put("password", rdsUserOperateDTO.getOldPassword());
        paramMap.put("newPassword", rdsUserOperateDTO.getNewPassword());
        Mapper dataMapper = OkHttps.sync(resourceCenterUrl + resetPwdUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(paramMap))
                .post()
                .getBody()
                .toMapper();
        log.info("修改数据库用户密码响应: {}", dataMapper.toString());
        String successStr = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "修改数据库用户密码失败: " + dataMapper.getString("message"));
    }

    @Override
    @Transactional
    public void delete(Long id) {
        RdsUserDTO rdsUserDTO = this.getById(id);
        rdsUserDAO.delete(id);
        //调用资源中心删除用户
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("rdsId", rdsUserDTO.getRdsId());
        paramMap.put("rdsUserId", rdsUserDTO.getGid());
        Mapper dataMapper = OkHttps.sync(resourceCenterUrl + deleteUserUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(paramMap))
                .post()
                .getBody()
                .toMapper();
        log.info("删除数据库用户响应: {}", dataMapper.toString());
        String successStr = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "删除数据库用户失败: " + dataMapper.getString("message"));
    }

    @Override
    public RdsUserDTO getById(Long id) {
        RdsUserQuery query = new RdsUserQuery();
        query.setId(id);
        List<RdsUserDO> list = rdsUserDAO.list(query);
        if (list.isEmpty()) {
            return null;
        }
        return rdsUserManagerConvert.do2dto(list.get(0));
    }
} 