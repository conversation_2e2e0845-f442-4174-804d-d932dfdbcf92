package com.datatech.slgzt.enums;


import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import org.apache.commons.lang3.StringUtils;

public enum OrderStatusEnum {

    /**
     * 订单状态-一类
     */
    EXAMINING("EXAMINING", "进行中"), //审核中
    REJECT("REJECT", "驳回确认"), //驳回的话，工单也是结束，但是可以重新提交
    CLOSE("CLOSE", "工单关闭"), //工单关单(用户关单)，工单为结束，不可重新提交
    END("END", "工单完结"), //工单结束
    UNKNOWN("unknown", "未知订单操作类型"),
    ;

    private final String code;
    private final String desc;

    OrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static OrderStatusEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (OrderStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return OrderStatusEnum.UNKNOWN;
    }

    public static OrderStatusEnum findDescByAuditStatus(ActivityEnum.ActivityStatusEnum auditStatus) {
        switch (auditStatus) {
            case REJECT:
                return OrderStatusEnum.REJECT;
            case CLOSE:
                return OrderStatusEnum.CLOSE;
        }
        return OrderStatusEnum.UNKNOWN;
    }

}
