package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: vpn回收参数
 * @author: LK
 * @create: 2025-06-10 17:21
 **/
@Data
public class RecoveryVpnModel extends BaseReconveryProductModel {

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * vpn名称
     */
    private String name;

    /**
     * 最大连接数
     */
    private String maxConnection;

    /**
     * 带宽
     */
    private Integer bandwidth;

    /**
     * vpc名称
     */
    private String vpcName;

    /**
     * 子网名称
     */
    private String subnetName;

    /**
     * 自定义setter方法处理带宽字符串
     */
    public void setBandwidth(String bandwidthStr) {
        if (bandwidthStr != null) {
            this.bandwidth = Integer.parseInt(bandwidthStr.replaceAll("[^0-9]", ""));
        }
    }

}
