package com.datatech.slgzt.model.req.corporate;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

@Data
public class CorporateOrderPriceCalculateReq {
    

    private String productType;

    /**
     * Ecs申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private CloudEcsResourceModel ecsModel;

    /**
     * mysql申请资源列表的json
     *
     * @see CloudEcsResourceModel //ecs资源模型
     */
    private EcsModel mysqlModel;

    private EcsModel redisModel;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private CpuEcsResourceModel gcsModel;

    /**
     * evs申请资源列表的json
     */
    private EvsModel evsModel;

    /**
     * eip申请资源列表的json
     */
    private EipModel eipModel;

    /**
     * nat资源申请json
     */
    private NatGatwayModel natModel;

    /**
     * slb资源申请json
     */
    private SlbModel slbModel;

    /**
     * obs资源申请json
     */
    private ObsModel obsModel;

    /**
     * 容器资源配额申请json
     */
    private CQModel cqModel;

    /**
     * 备份策略申请json
     */
    private BackupModel backupModel;

    /**
     * 云端口
     */
    private CloudPortModel cloudPortModel;

    private VpnModel vpnModel;
} 