package com.datatech.slgzt.enums.bpmn;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

public class ActivityEnum {

    @Getter
    public enum ActivityProcessEnum {
        /**
         * 工作流流程节点定义
         */
        RESOURCE_PROCESS("resource-create-process-1", "资源开通"),
        RESOURCE_RECOVERY_PROCESS("standard-resource-recovery-process", "回收配置流程"),
        //非标工单创建流程
        NON_STANDARD_PROCESS("non-standard-resource-process", "非标工单创建流程"),
        RESOURCE_CHANGE_PROCESS("resource-change-process", "资源变更流程"),
        ;

        private final String code;

        private final String message;

        ActivityProcessEnum(String code, String message) {
            this.code = code;
            this.message = message;
        }

        /**
         * 通过code获取enum
         *
         * @param code
         * @return
         */
        public static ActivityProcessEnum getByCode(String code) {
            if (!StringUtils.isEmpty(code)) {
                for (ActivityProcessEnum value : values()) {
                    if (value.getCode().equals(code)) {
                        return value;
                    }
                }
            }
            return null;
        }
    }

    /**
     * 审批状态
     */
    @Getter
    public enum ActivityStatusEnum {
        PASS(1, "通过"),
        REJECT(0, "驳回"),//驳回可重新提交
        CLOSE(2, "关单");//关单

        private final Integer code;
        private final String message;

        ActivityStatusEnum(Integer code, String message) {
            this.code = code;
            this.message = message;
        }

        /**
         * 通过code获取enum
         *
         * @param code 工单状态
         * @return 工单枚举
         */
        public static ActivityStatusEnum getByCode(Integer code) {
            if (code == null) {
                return PASS;
            }
            if (!StringUtils.isEmpty(String.valueOf(code))) {
                for (ActivityStatusEnum value : values()) {
                    if (value.getCode().equals(code)) {
                        return value;
                    }
                }
            }
            return PASS;
        }
    }
}
