package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class ResourcePriceCalculateDTO {
    /**
     * 计费类型：day-按天, month-按月, year-按年
     */
    private String billType;

    /**
     * 资源类型：VM-云主机, VOL-云硬盘, OBS-对象存储, SLB-负载均衡, NAT-NAT网关, VPN-VPN
     */
    private String resourceType;

    /**
     * 云主机信息
     */
    private VmPriceInfo vmInfo;

    /**
     * 云硬盘信息
     */
    private List<VolumePriceInfo> volInfo;

    /**
     * 对象存储信息
     */
    private List<ObsPriceInfo> obsInfo;

    /**
     * 负载均衡信息
     */
    private List<SlbPriceInfo> slbInfo;

    /**
     * NAT网关信息
     */
    private List<NatPriceInfo> natInfo;

    /**
     * VPN信息
     */
    private List<VpnPriceInfo> vpnInfo;

    @Data
    public static class VmPriceInfo {
        private Integer num;
        private String flavorId;
        private String sysVolType;
        private Integer sysVolSize;
        private Integer bandwidth;
        private List<VolumePriceInfo> dataVolList;
    }

    @Data
    public static class VolumePriceInfo {
        private String volType;
        private Integer volSize;
    }

    @Data
    public static class ObsPriceInfo {
        private Integer bucketSize;
        private Integer bucketNum;
    }

    @Data
    public static class SlbPriceInfo {
        private String flavorId;
        private Integer bandwidth;
    }

    @Data
    public static class NatPriceInfo {
        private String flavorId;
        private Integer bandwidth;
    }

    @Data
    public static class VpnPriceInfo {
        private Integer num;
        private Integer bandwidth;
    }
} 