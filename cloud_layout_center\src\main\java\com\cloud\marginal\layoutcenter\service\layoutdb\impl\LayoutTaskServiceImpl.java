package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.marginal.enums.layout.StateEnum;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutTaskService;
import com.cloud.marginal.mapper.layout.LayoutTaskMapper;
import com.cloud.marginal.model.entity.layout.LayoutTask;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 编排主任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Service
public class LayoutTaskServiceImpl extends ServiceImpl<LayoutTaskMapper, LayoutTask> implements LayoutTaskService {

    @Resource
    private LayoutTaskMapper layoutTaskMapper;

    @Override
    @Transactional
    public void retry(String id) {
        LayoutTask layoutTask = layoutTaskMapper.selectById(id);
        if(layoutTask != null && StateEnum.TIMEOUT.toString().equals(layoutTask.getState().toString())){
            layoutTask.setState(StateEnum.PROCESSING);
            layoutTask.setRetryCount(3);
            layoutTaskMapper.updateById(layoutTask);
        }
    }
}
