package com.datatech.slgzt.dao.model.cloudPort;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-31
 */
@Getter
@Setter
@ToString
@TableName("WOC_CLOUD_PORT")
public class WocCloudPortDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ID_WORKER)
    private String id;

    @TableField("CLOUD_PORT_NAME")
    private String cloudPortName;



    @TableField("VPC_NAME")
    private String vpcName;

    @TableField("VLAN_ID")
    private String vlanId;

    @TableField("PLAT_FORM_CODE")
    private String platformCode;

    @TableField("VPC_ID")
    private String vpcId;

    @TableField("PEER_IP")
    private String peerIp;

    @TableField("SRC_IP")
    private String srcIp;

    @TableField("PEER_PASS_WORD")
    private String peerPassword;

    @TableField("STATUS")
    @TableLogic(delval = "0", value = "1")
    private String status;

    @TableField("TASK_STATUS_EXT")
    private String taskStatusExt;

    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("RESOURCE_ID")
    private String resourceId;




    @TableField("PORT_ID")
    private String portId;

    @TableField("AZ_NAME")
    private String azName;

    /**
     * azCode
     */
    @TableField("AZ_CODE")
    private String azCode;

    /**
     * 业务系统ID
     */
    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;


    @TableField("BUSINESS_SYSTEM_ID")
    private String businessSystemId;
    /**
     * 云平台类型
     */
    @TableField("CATALOGUE_DOMAIN_NAME")
    private String catalogueDomainName;



    /**
     * 云平台类型CODE
     */
    @TableField("CATALOGUE_DOMAIN_CODE")
    private String catalogueDomainCode;
    /**
     * 云平台名称
     */
    @TableField("PLAT_FORM_NAME")
    private String platformName;


    /**
     * 云平台名称
     */
    @TableField("REGION_NAME")
    private String regionName;



    /**
     * 云平台名称
     */
    @TableField("TENANT_ID")
    private String tenantId;



}
