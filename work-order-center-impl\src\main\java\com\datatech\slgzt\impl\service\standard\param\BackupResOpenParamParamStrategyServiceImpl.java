package com.datatech.slgzt.impl.service.standard.param;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.BackupModel;
import com.datatech.slgzt.model.nostander.NatGatwayModel;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.standard.ResOpenParamStrategyService;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.utils.UuidUtil;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 云备份参数封装
 * @author: LK
 * @create: 2025-06-04 15:08
 **/
@Service
public class BackupResOpenParamParamStrategyServiceImpl implements ResOpenParamStrategyService {

    @Override
    public List<ResOpenReqModel.ProductOrder> assembleParam(ResOpenOpm opm) {
        ArrayList<ResOpenReqModel.ProductOrder> productOrders = Lists.newArrayList();
        List<BackupModel> backupModelList = opm.getBackupModelList();
        for(BackupModel backupModel : backupModelList) {
            ResOpenReqModel.ProductOrder backupProductOrder = new ResOpenReqModel.ProductOrder();
            backupProductOrder.setProductOrderId(backupModel.getId().toString());
            String productOrderType = "";
            String backupType = backupModel.getBackupType();
            if ("ECS".equals(backupType)) {
                productOrderType = "BACKUP_ECS_CREATE";
            } else if ("EVS".equals(backupType)) {
                productOrderType = "BACKUP_EVS_CREATE";
            }
            backupProductOrder.setProductOrderType(productOrderType);
            backupProductOrder.setProductType("backup_" + backupType.toLowerCase());
            backupProductOrder.setSubOrderId(opm.getSubOrderId());
            backupProductOrder.setGId(opm.getGId());
            ResOpenReqModel.Attrs backupAttrs = new ResOpenReqModel.Attrs();
            backupAttrs.setGId(opm.getGId());
            backupAttrs.setGroupId(opm.getCustomId());
            backupAttrs.setRegionCode(backupModel.getRegionCode());
            backupAttrs.setSystemSource("OAC");
            backupAttrs.setJobName(backupModel.getJobName());
            backupAttrs.setBackupType(backupModel.getBackupType());
            backupAttrs.setFrequency(backupModel.getFrequency());
            backupAttrs.setDaysOfWeek(backupModel.getDaysOfWeek());
            backupAttrs.setObjectIdList(backupModel.getObjectIdList());
            backupProductOrder.setAttrs(backupAttrs);
            productOrders.add(backupProductOrder);
        }
        return productOrders;
    }

    @Override
    public ProductTypeEnum register() {
        return ProductTypeEnum.BACKUP;
    }
}
