package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

public interface DeviceVirtualInfoManager {
    /**
     * 新增虚拟显卡基本信息
     */
    void create(DeviceVirtualInfoDTO dto);

    void updateLastByDeviceId(DeviceVirtualInfoDTO dto);

    /**
     * 更新虚拟显卡基本信息
     */
    void update(DeviceVirtualInfoDTO dto);

    /**
     * 删除显卡基本信息
     */
    void delete(Long id);

    void deleteBatch(List<Long> ids);

    /**
     * 分页虚拟卡基本信息
     */
    PageResult<DeviceVirtualInfoDTO> queryVirtualDeviceInfoPage(DeviceInfoQuery query);


    /**
     * 获取虚拟卡基本信息
     */
    List<DeviceVirtualInfoDTO> selectDeviceVirtualInfoList(DeviceInfoQuery query);
}
