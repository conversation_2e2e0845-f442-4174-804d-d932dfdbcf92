package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;
import lombok.ToString;


@Data
@ToString
public class SlbParam {

    /**
     * 子网id
     * */
    private String subnetId;

    /**
     * 负载均衡gId,修改和删除时需要
     * */
    private String gId;

    private String addressType;

    /**
     * VPC的ID
     * */
    private String vpcId;

    /**
     * 负载均衡名称
     * */
    private String name;

    /**
     * 负载均衡规格编码
     * */
    private String flavorCode;

    private String flavorId;

    private PublicInfo publicInfo;

    private String azCode;

    private String vip;
    private String slbName;

    @Data
    public class PublicInfo {
        private String publicId;
    }
}
