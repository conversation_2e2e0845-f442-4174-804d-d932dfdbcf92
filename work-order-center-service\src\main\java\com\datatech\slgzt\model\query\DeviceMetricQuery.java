package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 9:47
 * @description 获取指标查询条件
 */
@Data
@Accessors(chain = true)
public class DeviceMetricQuery {

    /**
     *区域编码
     */
    private String areaCode;

    /**
     * GPU/NPU卡序列号/uuid
     */
    private String deviceId;

    /**
     * GPU/NPU卡序列号/uuid
     */
    private List<String> deviceIds;

    /**
     * 显卡型号
     */
    private String modelName;

    /**
     * 指定时间的指标
     */
    private LocalDateTime startTime;

    /**
     * 指定时间的指标
     */
    private LocalDateTime  endTime;


    private String  gpuTime;


    /**
     * 显卡类型 gpu、npu
     */
    private String  deviceType;


    /**
     * 指标来源
     */
    private List<String> metricSources;

    /**
     * 统计类型：HOUR-按小时, DAY-按天, MONTH-按月
     */
    private String statType;

}