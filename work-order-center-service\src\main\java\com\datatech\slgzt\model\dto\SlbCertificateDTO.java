package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * SLB证书DTO
 */
@Data
@Accessors(chain = true)
public class SlbCertificateDTO {
    
    /**
     * 主键ID
     */
    private String id;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 公钥内容
     */
    private String publicKeyContent;

    /**
     * 私钥内容
     */
    private String privateKeyContent;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 域名编码
     */
    private String domainCode;


    /**
     * 资源池编码
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 业务系统id
     */
    private String  businessSystemId;


    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 域名名称
     */
    private String domainName;

    /**
     * 关联的监听器关系JSON
     */
    private Map<String,String> slbListenerRel;

    /**
     * 证书资源id
     */
    private String resourceId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 任务状态
     */
    private String taskStatusExt;

    /**
     * 云类型
     */
    private String catalogueDomainCode;

    //来源
    private String sourceType;




} 