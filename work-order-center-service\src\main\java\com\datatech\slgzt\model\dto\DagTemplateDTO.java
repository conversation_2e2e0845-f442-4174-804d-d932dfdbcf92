package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月03日 10:35:26
 */
@Data
public class DagTemplateDTO {


    //模板ID
    private String id;

    //模板名称
    private String name;

    //模板描述
    private String description;

    /**
     * 前端保存的DAG画布数据
     */
    private String dagCanvas;



    private List<VpcUnTaskModel> vpcModelList;


    private List<NetworkUnTaskModel> networkModelList;

    /**
     * Ecs申请资源列表的json
     *
     */
    private List<CloudEcsResourceModel> ecsModelList;

    /**
     * mysql申请资源列表的json
     *
     */
    private List<EcsModel> mysqlModelList;

    private List<EcsModel> redisModelList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> gcsModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    private List<CloudPortModel> cloudPortModelList;


    private List<String> domainCodeList;

    private List<String> catalogueDomainCodeList;

    private List<String> reginCodeList;
    //模版创建人
    private String creator;

    //模版创建用户ID
    private String creatorId;

    //模版创建时间
    private LocalDateTime createTime;
}
