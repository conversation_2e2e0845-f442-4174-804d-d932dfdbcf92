package com.datatech.slgzt.impl;

import cn.hutool.core.collection.ListUtil;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.VMResourcePerformanceManager;
import com.datatech.slgzt.model.CustomCountDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.query.CorporateOrderQuery;
import com.datatech.slgzt.model.query.CustomQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.service.ManagerViewService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-01 16:03
 **/
@Service
public class ManagerViewServiceImpl implements ManagerViewService {

    @Resource
    private VmMapper vmMapper;

    @Resource
    private CorporateOrderManager corporateOrderManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VMResourcePerformanceManager vmResourcePerformanceManager;

    /**
     * 客户经理统计
     * @param userId 用户id
     * @return
     */
    @Override
    public CustomCountDTO customCount(Long userId) {
        CustomCountDTO dto = new CustomCountDTO();
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        if (CollectionUtils.isEmpty(customTenants)) {
            return null;
        }

        // 计算客户总数
        dto.setTotalCustom(customTenants.stream()
                .map(CustomTenantDTO::getCustomId)
                .distinct()
                .count());

        List<Long> tenantIds = customTenants.stream()
                .map(CustomTenantDTO::getTenantId)
                .collect(Collectors.toList());

        // 处理订单数据
        List<CorporateOrderDTO> orders = corporateOrderManager.list(
                new CorporateOrderQuery().setTenantIds(tenantIds));
        dto.setTotalOrder(CollectionUtils.isEmpty(orders) ? 0 : orders.size());
        dto.setMonthTotalOrder(CollectionUtils.isEmpty(orders) ? 0L :
                orders.stream()
                        .filter(o -> o.getCreateTime() != null)
                        .filter(o -> YearMonth.from(o.getCreateTime()).equals(YearMonth.now()))
                        .count());

        // 处理产品数据
        List<ResourceDetailDTO> products = resourceDetailManager.list(
                new ResourceDetailQuery().setTenantList(tenantIds).setSourceTypeList(ListUtil.toList("DG", "FB")));
        dto.setTotalProduct(CollectionUtils.isEmpty(products) ? 0 : products.size());
        dto.setMonthTotalProduct(CollectionUtils.isEmpty(products) ? 0L :
                products.stream()
                        .filter(p -> p.getCreateTime() != null)
                        .filter(p -> YearMonth.from(p.getCreateTime()).equals(YearMonth.now()))
                        .count());

        return dto;
    }

    /**
     * 客户集团列表
     * @param userId
     * @return
     */
    @Override
    public List<CustomTenantDTO> customDetail(Long userId) {
        // 1. 获取所有租户信息（一次性查询）
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        if (CollectionUtils.isEmpty(customTenants)) {
            return Collections.emptyList();
        }

        // 2. 提取所有租户ID（去重）
        Set<Long> allTenantIds = customTenants.stream()
                .map(CustomTenantDTO::getTenantId)
                .collect(Collectors.toSet());

        // 3. 批量查询订单和产品数据（减少DB交互）
        Map<Long, List<CorporateOrderDTO>> ordersByTenant = getOrdersByTenants(allTenantIds);
        Map<Long, List<ResourceDetailDTO>> productsByTenant = getProductsByTenants(allTenantIds);

        // 4. 并行处理统计逻辑
        return customTenants.stream()
                .collect(Collectors.groupingBy(CustomTenantDTO::getCustomId))
                .values()
                .parallelStream()  // 并行流加速处理
                .map(group -> {
                    CustomTenantDTO dto = group.get(0);
                    List<Long> groupTenantIds = group.stream()
                            .map(CustomTenantDTO::getTenantId)
                            .collect(Collectors.toList());

                    // 合并统计订单数据
                    dto.setTotalOrder(groupTenantIds.stream()
                            .mapToInt(tenantId -> ordersByTenant.getOrDefault(tenantId, Collections.emptyList()).size())
                            .sum());
                    dto.setMonthTotalOrder(groupTenantIds.stream()
                            .flatMap(tenantId -> ordersByTenant.getOrDefault(tenantId, Collections.emptyList()).stream())
                            .filter(order -> order.getCreateTime() != null)
                            .filter(order -> YearMonth.from(order.getCreateTime()).equals(YearMonth.now()))
                            .count());

                    // 合并统计产品数据
                    dto.setTotalProduct(groupTenantIds.stream()
                            .mapToInt(tenantId -> productsByTenant.getOrDefault(tenantId, Collections.emptyList()).size())
                            .sum());

                    return dto;
                })
                .sorted(Comparator.comparingInt(CustomTenantDTO::getTotalOrder).reversed())
                .limit(2)
                .collect(Collectors.toList());
    }

    // 批量查询订单（减少N+1问题）
    private Map<Long, List<CorporateOrderDTO>> getOrdersByTenants(Set<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return Collections.emptyMap();
        }
        List<CorporateOrderDTO> orders = corporateOrderManager.list(
                new CorporateOrderQuery().setTenantIds(new ArrayList<>(tenantIds)));
        return orders.stream().collect(Collectors.groupingBy(CorporateOrderDTO::getTenantId));
    }

    // 批量查询产品（带过滤条件）
    private Map<Long, List<ResourceDetailDTO>> getProductsByTenants(Set<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return Collections.emptyMap();
        }
        List<ResourceDetailDTO> products = resourceDetailManager.list(
                new ResourceDetailQuery()
                        .setTenantList(new ArrayList<>(tenantIds))
                        .setSourceTypeList(ListUtil.toList("DG", "FB")));
        return products.stream().collect(Collectors.groupingBy(ResourceDetailDTO::getTenantId));
    }

    @Override
    public List<VMResourcePerformanceDTO> selectTop5vCPUGroupByCustom(Long userId) {
        // 1. 获取基础数据并校验
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        if (CollectionUtils.isEmpty(customTenants)) {
            return null;
        }
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        // 2. 获取性能数据
        return vmResourcePerformanceManager.selectTop5vCPUGroupByCustom(ListUtil.toList(map.keySet()));
    }

    @Override
    public List<CustomTenantDTO> totalProductTop5(Long userId) {
        // 1. 获取基础数据并校验
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        if (CollectionUtils.isEmpty(customTenants)) {
            return Collections.emptyList();
        }

        // 2. 提取所有租户ID（批量处理）
        Set<Long> allTenantIds = customTenants.stream()
                .map(CustomTenantDTO::getTenantId)
                .collect(Collectors.toSet());

        // 3. 批量查询订单和产品数据（减少数据库交互）
        Map<Long, List<ResourceDetailDTO>> productsByTenant = getProductsByTenants(allTenantIds);

        // 4. 处理统计逻辑
        return customTenants.stream()
                .collect(Collectors.groupingBy(CustomTenantDTO::getCustomId))
                .values()
                .stream()
                .map(group -> {
                    CustomTenantDTO dto = group.get(0);
                    List<Long> groupTenantIds = group.stream()
                            .map(CustomTenantDTO::getTenantId)
                            .collect(Collectors.toList());

                    // 合并统计产品数据
                    dto.setTotalProduct(groupTenantIds.stream()
                            .mapToInt(tenantId -> productsByTenant.getOrDefault(tenantId, Collections.emptyList()).size())
                            .sum());

                    return dto;
                })
                .sorted(Comparator.comparingInt(CustomTenantDTO::getTotalProduct).reversed())
                .limit(5)
                .collect(Collectors.toList());
    }

    @Override
    public List<VMResourcePerformanceDTO> selectTop5MemGroupByCustom(Long userId) {
        // 1. 获取基础数据并校验
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        if (CollectionUtils.isEmpty(customTenants)) {
            return null;
        }
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        // 2. 获取性能数据
        return vmResourcePerformanceManager.selectTop5MemGroupByCustom(ListUtil.toList(map.keySet()));
    }

    @Override
    public List<VMResourcePerformanceDTO> top5Resource(Long userId, String orderType) {
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        if (CollectionUtils.isEmpty(customTenants)) {
            return null;
        }
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        return vmResourcePerformanceManager.selectTop5Resource(ListUtil.toList(map.keySet()), orderType);
    }

    @Override
    public List<VMResourcePerformanceDTO> top5ResourceOfCustom(String customId, String orderType) {
        return vmResourcePerformanceManager.selectTop5Resource(ListUtil.toList(customId), orderType);
    }

    @Override
    public CustomDTO getCustomDetail(String customId) {
        // 1. 获取客户基础信息及关联租户
        CustomDTO customDTO = vmMapper.getCustomById(customId);
        Precondition.checkArgument(customDTO, "客户不存在");

        // 2. 处理租户ID（从逗号分隔字符串转为List）
        List<Long> tenantIds = customDTO.getTenantId() == null ?
                Collections.emptyList() :
                Arrays.stream(customDTO.getTenantId().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toList());

        // 3. 查询并处理订单数据
        List<CorporateOrderDTO> orders = CollectionUtils.isEmpty(tenantIds) ?
                Collections.emptyList() :
                corporateOrderManager.list(new CorporateOrderQuery().setTenantIds(tenantIds));

        customDTO.setTotalOrder(orders.size());
        customDTO.setMonthTotalOrder(
                orders.stream()
                        .filter(order -> order.getCreateTime() != null)
                        .filter(order -> YearMonth.from(order.getCreateTime()).equals(YearMonth.now()))
                        .count()
        );

        // 4. 查询并处理产品数据
        List<ResourceDetailDTO> products = CollectionUtils.isEmpty(tenantIds) ?
                Collections.emptyList() :
                resourceDetailManager.list(new ResourceDetailQuery().setTenantList(tenantIds));

        customDTO.setTotalProduct(products.size());
        customDTO.setMonthTotalProduct(
                products.stream()
                        .filter(product -> product.getCreateTime() != null)
                        .filter(product -> YearMonth.from(product.getCreateTime()).equals(YearMonth.now()))
                        .count()
        );

        return customDTO;
    }

    @Override
    public PageResult<CustomDTO> page(CustomQuery customQuery) {
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(customQuery.getCurrentUserId());
        //Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");
        if (CollectionUtils.isEmpty(customTenants)) {
            return null;
        }
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        customQuery.setCustomIds(ListUtil.toList(map.keySet()));
        PageHelper.startPage(customQuery.getPageNum(), customQuery.getPageSize());
        List<CustomDTO> customList = vmMapper.getCustomList(customQuery);
        return PageWarppers.box(new PageInfo<>(customList));
    }
}
