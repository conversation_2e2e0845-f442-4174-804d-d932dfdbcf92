package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.RegionCmdbMapper;
import com.datatech.slgzt.dao.model.RegionCmdbDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月27日 15:03:05
 */
@Repository
public class RegionCmdbDAO {

    @Resource
    private RegionCmdbMapper regionCmdbMapper;


    public List<RegionCmdbDO> listByCodeList(List<String> codeList) {
        return regionCmdbMapper.selectList(Wrappers.<RegionCmdbDO>lambdaQuery()
                                                   .in(RegionCmdbDO::getCode, codeList));
    }
}
