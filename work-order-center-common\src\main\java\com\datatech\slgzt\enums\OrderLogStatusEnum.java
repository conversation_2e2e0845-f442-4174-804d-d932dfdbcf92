package com.datatech.slgzt.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum OrderLogStatusEnum {
    /**
     * 订单状态枚举
     */
    CREATE("CREATE", "创建"),
    EXAMINING("EXAMINING", "审核中"),
    PASS("PASS", "审核通过"),
    REJECT("REJECT", "审批被驳回"),
    CLOSE("CLOSE", "工单关单"),
    END("END", "工单完结"),
    RESUBMIT("RESUBMIT","重新提交"),
    GOING("GOING", "进行中"),
    SUCCESS("SUCCESS", "完成");


    private final String code;
    private final String desc;

    OrderLogStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static OrderLogStatusEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for(OrderLogStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }


    /**
     * transfer code to enum
     * PASS(1, "通过"),
     * REJECT(0, "驳回"),//驳回可重新提交
     * CLOSE(2, "关单");//关单
     *
     * @param code
     * @return
     */
    public static String getByCode(Integer code) {
        if (code == 1) {
            return OrderLogStatusEnum.PASS.getCode();
        } else if (code == 0) {
            return OrderLogStatusEnum.REJECT.getCode();
        } else if (code == 2) {
            return OrderLogStatusEnum.CLOSE.getCode();
        } else {
            return null;
        }
    }

}
