package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.tem.*;
import lombok.Data;

import java.util.List;

/**
 * DAG模版创建服务参数
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 15:43:44
 */
@Data
public class DagProductOpm {

    private String configId;

    //全局参数模型
    private TemGlobalParamModel temGlobalParamModel;

    //Ecs参数模型
    private List<EcsModel> dagEcsModelList;

    //gcs参数模型
    private List<EcsModel> dagGcsModelList;

    //evs参数模型
    private List<EvsModel> dagEvsModelList;

    //eip参数模型
    private List<EipModel> dagEipModelList;

    //slb参数模型
    private List<SlbModel> dagSlbModelList;

    //vpc参数模型
    private List<DagVpcModel> dagVpcModelList;

    //nat参数模型
    private List<NatGatwayModel> dagNatModelList;

    //obs参数模型
    private List<ObsModel> dagObsModelList;

}
