package com.datatech.slgzt.helps;

import com.datatech.slgzt.config.XieyunProperties;
import com.google.common.base.Joiner;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 批量关系处理帮助类
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月06日 09:25:53
 */
@Component
public class BatchRelationHelper  implements InitializingBean {

    public static BatchRelationHelper INSTANCE;


    @Resource
    private RedissonClient redissonClient;


    private static final String DELIMITER = ":";

    private static final long DEFAULT_EXPIRE_TIME = 3600; // 默认缓存时间为1小时




    /**
     * 暂存数据到Redis缓存
     * @param params 需要缓存的数据Map
     */
    public void dagCacheData(Map<String, Object> params,String... keyIndexs) {
        if (keyIndexs == null) {
            return;
        }
        // 动态拼接key
        String cacheKey ="dag_cache:"+Jo<PERSON>.on(DELIMITER).join(keyIndexs);
        // 获取缓存Map
        RMapCache<String, Object> cacheMap = redissonClient.getMapCache(cacheKey);
        // 将数据存入缓存
        params.forEach((key, value) -> cacheMap.put(key, value, DEFAULT_EXPIRE_TIME, TimeUnit.SECONDS));
    }

    /**
     * 获取缓存的数据
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 缓存的数据Map
     */
    public Map<String, Object> getDagCachedData(String... keyIndexs) {
        if (keyIndexs == null) {
            return null;
        }
        // 动态拼接key
        String cacheKey = "dag_cache:" + Joiner.on(DELIMITER).join(keyIndexs);
        // 获取缓存Map
        RMapCache<String, Object> cacheMap = redissonClient.getMapCache(cacheKey);
        // 返回缓存的数据
        return cacheMap.readAllMap();
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        INSTANCE = this;
    }
}
