package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.model.entity.layout.LayoutTaskNode;

/**
 * <p>
 * 编排任务节点表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface LayoutTaskNodeService extends IService<LayoutTaskNode> {

    /**
     * 重试
     * @param taskId
     */
     void retry(String taskId);

    /**
     * 通过任务ID查询
     * @param taskId
     * @return
     */
    LayoutTaskNode getByTaskId(String taskId);

}
