package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

import java.util.List;

@Data
public class VpcParam {

    private String id;

    private Long tenantId;

    private String vpcName;

    private String regionCode;

    private String description;

    private String outInstanceId;

    private String cidr;

    private String ipv6Cidr;

    private List<Subnet> subnets;

    //资源中心id
    private String resourceId;

    private String gId;public String getgId() {    return gId;}public void setgId(String gId) {    this.gId = gId;};


    @Data
    public static class Subnet{

        private String cidr;

        private String ipv6Cidr;

        private String subnetName;
        /**
         * 子网ip
         */
        private String gatewayIp;

        /**
         * 是否创建IPv6子网
         * false（默认）：不创建
         * true：创建
         */
        private Boolean ipv6Enable = false;

        /**
         * 子网是否开启dhcp功能false（默认）不开启 true开启
         */
        private Boolean dhcpEnable = false;

        /**
         * 子网描述  非必传
         */
        private String description;

        /**
         * 子网dns服务器地址1
         */
        private String primaryDns;

        /**
         * 子网dns服务器地址2
         */
        private String secondaryDns;

        private String azCode;
    }
}
