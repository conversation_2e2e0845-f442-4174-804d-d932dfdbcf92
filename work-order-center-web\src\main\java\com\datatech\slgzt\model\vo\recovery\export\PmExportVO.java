package com.datatech.slgzt.model.vo.recovery.export;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: pm导出
 * @author: LK
 * @create: 2025-07-07 09:31
 **/
@Data
public class PmExportVO {

    @ExcelExportHeader("裸金属名称")
    private String pmName;

    /**
     * cpu核数
     */
    @ExcelExportHeader("cpu核数")
    private String vCpus;

    /**
     * 内存大小
     */
    @ExcelExportHeader("内存")
    private String ram;

    /**
     * 硬盘大小
     */
    @ExcelExportHeader("硬盘")
    private String diskSize;

    /**
     * GPU型号
     */
    @ExcelExportHeader("显卡类型")
    private String gpuType;

    /**
     * GPU数量
     */
    @ExcelExportHeader("显卡数量")
    private String gpuNum;

    /**
     * gpu显卡类型 NPU or GPU
     */
    @ExcelExportHeader("显卡型号")
    private String gpuCardType;

    /**
     * 申请时长
     */
    @ExcelExportHeader("申请时长")
    private String applyTimeCn;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;

}
