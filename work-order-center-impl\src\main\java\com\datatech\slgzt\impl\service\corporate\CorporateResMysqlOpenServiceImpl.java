package com.datatech.slgzt.impl.service.corporate;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.standard.StandardEcsCombinationResOpenStrategyServiceProvider;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.model.dto.CorporateOrderDTO;
import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.CloudEcsResourceModel;
import com.datatech.slgzt.model.nostander.EipModel;
import com.datatech.slgzt.model.nostander.EvsModel;
import com.datatech.slgzt.model.nostander.MysqlV2Model;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.corporate.CorporateResOpenService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 18:50:05
 */
@Slf4j
@Service
public class CorporateResMysqlOpenServiceImpl implements CorporateResOpenService {


    @Resource
    private CorporateOrderProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private CorporateOrderManager corporateOrderManager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public Object openResource(CorporateOrderProductDTO productDTO) {
        //不是只有ESC
        String productType = productDTO.getProductType();
        //开通资源类型要是GPS 或者是ESC
        Precondition.checkArgument(ProductTypeEnum.RDS_MYSQL.getCode().equals(productType),
                "算力编排不支持该类型开通");
        //把开通的网络资源挂载到对应的附加字段里去
        CorporateOrderDTO orderDTO = corporateOrderManager.getById(productDTO.getOrderId());
        MysqlV2Model mysqlModel = JSON.parseObject(productDTO.getPropertySnapshot(), MysqlV2Model.class);
        Long tenantId = platformService.getOrCreateTenantId(mysqlModel.getBillId(), mysqlModel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        ResOpenReqModel resOpenReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        resOpenReqModel.setAccount(mysqlModel.getBillId());
        //设置业务code;
        resOpenReqModel.setSourceExtType(OrderTypeEnum.CORPORATE.getCode());
        //设置业务code
        resOpenReqModel.setBusinessCode("RDS_CREATE");
        //设置业务系统code
        resOpenReqModel.setBusinessSystemCode(String.valueOf(mysqlModel.getBusinessSystemId()));
        //设置客户id
        resOpenReqModel.setCustomId(mysqlModel.getCustomNo());
        //设置区域编码
        resOpenReqModel.setRegionCode(mysqlModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resOpenReqModel.setSubOrderId(productDTO.getSubOrderId());
        //设置租户id 可能是需要传入底层租户id 应该要查询下 目前不知道查询的方式
        resOpenReqModel.setTenantId(tenantId);
        //设置userId
        resOpenReqModel.setUserId(orderDTO.getCreateBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resOpenReqModel.setTaskSource(6);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList = Lists.newArrayList();
        //------------------mysql部分------------------------------------------
        List<ResOpenReqModel.ProductOrder> escProduct =
                StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.RDS_MYSQL).assembleParam(
                        new ResOpenOpm()
                                .setGId(productDTO.getGid())
                                .setTenantId(tenantId)
                                .setSubOrderId(productDTO.getSubOrderId().toString())
                                .setMysqlModel(mysqlModel));
        reqProductList.addAll(escProduct);
        //------------------如果还有其他产品要开通在这里补充------------------------------------------
        resOpenReqModel.setProductOrders(reqProductList);
        //------------------产品参数设置结束-------------------------------------------------------
        //把对应的产品都改成开通中状态
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl);
        String jsonString = JSON.toJSONString(resOpenReqModel);
        log.info(jsonString);
        Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(jsonString)
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败, " + dataMapper.getString("message"));
        log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
        return null;
    }

    /**
     * 注册
     */
    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.RDS_MYSQL;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }

}
