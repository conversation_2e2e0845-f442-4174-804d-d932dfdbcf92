package com.datatech.slgzt.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseMetricInfoModel implements Serializable {
    /**
     * DCGM 版本
     */
    @J<PERSON><PERSON>ield(name="DCGM_FI_DRIVER_VERSION")
    private String version;
    @JSONField(name="Hostname")
    private String hostname;
    @JSONField(name="UUID")
    private String uuid;
    @J<PERSON>NField(name="__name__")
    private String metricName;
    @JSONField(name="controller_name")
    private String controllerName;
    @JSONField(name="device")
    private String device;
    @J<PERSON>NField(name="gpu")
    private String gpu;
    @J<PERSON><PERSON>ield(name="instance")
    private String instance;
    @J<PERSON>NField(name = "job")
    private String job;
    @JSONField(name="modelName")
    private String modelName;
    @J<PERSON><PERSON>ield(name="name")
    private String name;
    @JSONField(name="namespace")
    private String namespace;


    //======vgpu======

    @J<PERSON>NField(name="allocation_id")
    private String allocationId;
    @J<PERSON><PERSON><PERSON>(name="client_id")
    private String clientId;
    @J<PERSON><PERSON><PERSON>(name="client_pod_id")
    private String clientPodId;

    @JSONField(name="client_pod_name")
    private String clientPodName;
    @JSONField(name="client_pod_ns")
    private String clientPodNs;


    @JSONField(name="group_id")
    private String groupId;
    @JSONField(name="host_ip")
    private String hostIp;
    @JSONField(name="pgpu_name")
    private String pgpuName;

    @JSONField(name="pindex")
    private String pindex;

    @JSONField(name="task_name")
    private String taskName;

    @JSONField(name="vindex")
    private String vindex;

    @JSONField(name="pgpu_id")
    private String pgpuId;

    private String value;

//==========Npu 指标================


    @JSONField(name="pcie_bus_info")
    private String pcieBusInfo;

    @JSONField(name="vdie_id")
    private String vdieId;

    @JSONField(name="process_id")
    private String processId;

}

