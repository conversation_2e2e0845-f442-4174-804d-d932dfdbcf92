package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 物理机模型
 * <AUTHOR>
 * @date 2025/6/23
 */
@Data
public class PhysicalMachineModel extends BaseProductModel {


    private String pmName;

    /**
     * 开通数量
     */
    private Integer openNum = 1;

    /**
     * cpu核数
     */
    @JsonProperty("vCpus")
    private Integer vCpus;

    /**
     * 内存大小
     */
    private Integer ram;

    /**
     * 硬盘大小
     */
    private Integer diskSize;

    /**
     * GPU型号
     */
    private String gpuType;

    /**
     * GPU数量
     */
    private Integer gpuNum;

    /**
     * gpu显卡类型 NPU or GPU
     */
    private String gpuCardType;


    /**
     * 申请时长
     */
    private String applyTime;

}
