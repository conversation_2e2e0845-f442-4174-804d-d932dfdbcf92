package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

@Data
public class VpnParam {
    /**
     * 订单id不能为空
     */
    private String orderId;

    /**
     * 云区域编码不能为空
     */
    private String regionCode;
    /**
     * 计费号
     */
    private String billId;
    /**
     * 集团客户编码
     */
    private String groupId;
    private String projectId;
    private String azCode;

    /**
     * vpn名称
     * */
    private String name;
    private Integer maxConnection;

    /**
     * VPC编码
     * */
    private String vpcId;

    /**
     * 子网 ID
     * */
    private String subnetId;
    private String addressType;
//    private String publicIpId;
    private EipInfo publicIpInfo;

    /**
     * VPN网关标识
     * */
    private String localId;

    private String gId;

    @Data
    public static class EipInfo {
        private String publicIp;
        private String bandwidth;
    }
}
