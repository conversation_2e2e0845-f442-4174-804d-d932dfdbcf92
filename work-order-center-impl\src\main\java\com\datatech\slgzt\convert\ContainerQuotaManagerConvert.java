package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.container.ContainerQuotaDO;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.nostander.CQModel;

import org.mapstruct.Mapper;

/**
 * 容器配额管理器转换器
 * <AUTHOR>
 * @description 容器配额DO和DTO之间的转换
 * @date 2025年05月27日
 */
@Mapper(componentModel = "spring")
public interface ContainerQuotaManagerConvert {

    /**
     * DO转DTO
     * @param containerQuotaDO DO对象
     * @return DTO对象
     */
    ContainerQuotaDTO do2DTO(ContainerQuotaDO containerQuotaDO);

    /**
     * DTO转DO
     * @param containerQuotaDTO DTO对象
     * @return DO对象
     */
    ContainerQuotaDO dto2DO(ContainerQuotaDTO containerQuotaDTO);

    /**
     * CQModel转DO
     * @param cqModel CQModel对象
     * @return DO对象
     */
    ContainerQuotaDO cqModel2DO(CQModel cqModel);
}
