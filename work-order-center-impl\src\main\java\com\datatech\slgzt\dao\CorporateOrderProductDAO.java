package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.CorporateOrderProductMapper;
import com.datatech.slgzt.dao.model.CorporateOrderProductDO;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class CorporateOrderProductDAO {

    @Resource
    private CorporateOrderProductMapper mapper;

    public List<CorporateOrderProductDO> list(CorporateOrderProductQuery query) {
        return mapper.selectList(
            Wrappers.<CorporateOrderProductDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getOrderId()), CorporateOrderProductDO::getOrderId, query.getOrderId())
                .in(ObjNullUtils.isNotNull(query.getIds()), CorporateOrderProductDO::getId, query.getIds())
                .eq(ObjNullUtils.isNotNull(query.getProductType()), CorporateOrderProductDO::getProductType, query.getProductType())
                .eq(ObjNullUtils.isNotNull(query.getParentId()), CorporateOrderProductDO::getParentProductId, query.getParentId())
                .in(ObjNullUtils.isNotNull(query.getGids()), CorporateOrderProductDO::getGid, query.getGids())
                .in(ObjNullUtils.isNotNull(query.getSubOrderIds()), CorporateOrderProductDO::getSubOrderId, query.getSubOrderIds())
                .orderByDesc(CorporateOrderProductDO::getCreateTime)
        );
    }

    public void insert(CorporateOrderProductDO corporateOrderProductDO) {
        mapper.insert(corporateOrderProductDO);
    }

    public void updateById(CorporateOrderProductDO corporateOrderProductDO) {
        mapper.updateById(corporateOrderProductDO);
    }

    public void delete(Long id) {
        mapper.deleteById(id);
    }

    public void deleteByWorkOrderId(String workOrderId) {
        mapper.delete(Wrappers.<CorporateOrderProductDO>lambdaQuery()
            .eq(CorporateOrderProductDO::getOrderId, workOrderId));
    }

    public CorporateOrderProductDO getById(Long id) {
        return mapper.selectById(id);
    }

    public CorporateOrderProductDO getByGid(String gid) {
        return mapper.selectOne(Wrappers.<CorporateOrderProductDO>lambdaQuery()
            .eq(CorporateOrderProductDO::getGid, gid));
    }

    public CorporateOrderProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<CorporateOrderProductDO>lambdaQuery()
            .eq(CorporateOrderProductDO::getSubOrderId, subOrderId));
    }

    public void updateByParentId(CorporateOrderProductDO corporateOrderProductDO){
        mapper.update(corporateOrderProductDO, Wrappers.<CorporateOrderProductDO>lambdaUpdate()
            .eq(CorporateOrderProductDO::getParentProductId, corporateOrderProductDO.getParentProductId()));
    }

} 