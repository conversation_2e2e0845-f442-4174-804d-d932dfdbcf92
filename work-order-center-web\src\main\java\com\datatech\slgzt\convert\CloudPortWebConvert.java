package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.cloudPort.CloudPortDTO;
import com.datatech.slgzt.model.query.WocCloudPortQuery;
import com.datatech.slgzt.model.req.couldPort.CloudPortCreateReq;
import com.datatech.slgzt.model.req.couldPort.ClouldPortPageReq;
import com.datatech.slgzt.model.vo.cloudPort.CloudPortVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CloudPortWebConvert {



    /**
     * 分页请求转Query
     */
    WocCloudPortQuery convert(ClouldPortPageReq req);


    /**
     * 新增请求转DTO
     */
    CloudPortDTO convert(CloudPortCreateReq req);


    /**
     * DTO请求转VO
     */
    CloudPortVO convert(CloudPortDTO dto);

}
