package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.DeviceCardMetricsDAO;
import com.datatech.slgzt.dao.model.DeviceCardMetricsDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.time.LocalDateTime;
import java.util.List;


public interface DeviceCardMetricsMapper extends BaseMapper<DeviceCardMetricsDO> {


    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "avgBuildQuery")
    List<DeviceCardMetricsDO> queryAvgDeviceMetrics(
            @Param("areaCode") String areaCode,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 按时间聚合查询GPU指标数据
     */
    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "buildGpuAggregateQuery")
    List<DeviceCardMetricsDO> queryGpuMetricsAggregated(
            @Param("deviceIds") List<String> deviceIds,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("statType") String statType
    );

    // 动态 SQL 构建方法
    static String avgBuildQuery(@Param("areaCode") String areaCode,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime) {
        return new org.apache.ibatis.jdbc.SQL() {{
            SELECT("AREA_CODE AS areaCode");
            SELECT("GPU_TIME AS gpuTime");
            SELECT("AVG(GPU_UTIL_PERCENT) AS GPU_UTIL_PERCENT");
            SELECT("AVG(MEM_UTIL_PERCENT) AS MEM_UTIL_PERCENT");

            FROM("woc_gpu_device_metrics_distributed");

            if (areaCode != null && !areaCode.isEmpty()) {
                WHERE("AREA_CODE = #{areaCode}");
            }
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }

            GROUP_BY("AREA_CODE, GPU_TIME");
            ORDER_BY("AREA_CODE, GPU_TIME");
        }}.toString();
    }

    /**
     * 构建GPU聚合查询SQL
     */
    static String buildGpuAggregateQuery(@Param("deviceIds") List<String> deviceIds,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime,
                                       @Param("statType") String statType) {
        return new org.apache.ibatis.jdbc.SQL() {{
            SELECT("DEVICE_ID AS deviceId");
            SELECT("MODEL_NAME AS modelName");
            SELECT("DEVICE_TYPE AS deviceType");
            SELECT("AREA_CODE AS areaCode");
            
            // 根据聚合类型选择时间分组字段
            if ("HOUR".equalsIgnoreCase(statType)) {
                SELECT("GPU_TIME AS gpuTime");
            } else if ("DAY".equalsIgnoreCase(statType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 8) AS gpuTime");
            } else if ("MONTH".equalsIgnoreCase(statType)) {
                SELECT("SUBSTRING(GPU_TIME, 1, 6) AS gpuTime");
            } else {
                SELECT("GPU_TIME AS gpuTime");
            }
            
            // 聚合指标数据
            SELECT("AVG(GPU_UTIL_PERCENT) AS gpuUtilPercent");
            SELECT("AVG(MEM_UTIL_PERCENT) AS memUtilpercent");
            SELECT("AVG(MEMORY_USAGE) AS memoryUsage");
            SELECT("AVG(DEV_POWER_USAGE) AS devPowerUsage");
            SELECT("AVG(DEV_GPU_TEMP) AS devGpuTemp");
            SELECT("MAX(ALLOCATION_COUNT) AS allocationCount");

            FROM("woc_gpu_device_metrics_distributed");

            // 设备ID过滤
            if (deviceIds != null && !deviceIds.isEmpty()) {
                WHERE("DEVICE_ID IN (" + 
                    String.join(",", deviceIds.stream().map(id -> "'" + id + "'").toArray(String[]::new)) + ")");
            }
            
            // 时间范围过滤
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }

            // 根据聚合类型分组
            if ("HOUR".equalsIgnoreCase(statType)) {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, GPU_TIME");
            } else if ("DAY".equalsIgnoreCase(statType)) {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, SUBSTRING(GPU_TIME, 1, 8)");
            } else if ("MONTH".equalsIgnoreCase(statType)) {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, SUBSTRING(GPU_TIME, 1, 6)");
            } else {
                GROUP_BY("DEVICE_ID, MODEL_NAME, DEVICE_TYPE, AREA_CODE, GPU_TIME");
            }

            ORDER_BY("gpuTime DESC, DEVICE_ID");
        }}.toString();
    }
}
