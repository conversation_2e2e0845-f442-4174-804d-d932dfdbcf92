package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.FlavorCorporateDO;
import com.datatech.slgzt.dao.model.FlavorDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface FlavorCorporateMapper extends BaseMapper<FlavorCorporateDO> {

    /**
     * select FLAVOR_ID, f.FLAVOR_NAME
     * from MC_VM_T vm
     * left join MC_FLAVOR_T f on vm.FLAVOR_ID = f.ID
     * where vm.ID ='';
     *
     * @param deviceId
     * @return
     */
    @Select("select f.FLAVOR_NAME from MC_VM_T vm " +
            "left join MC_FLAVOR_T f on vm.FLAVOR_ID = f.ID " +
            "where vm.DELETED=1 and f.DELETED=1" +
            "and vm.ID =#{deviceId}")
    String getFlavorNameByDeviceId(@Param("deviceId") String deviceId);
}
