package com.datatech.slgzt.manager.xieyun.local;

import com.datatech.slgzt.model.dto.XieYunUserDTO;
import com.datatech.slgzt.model.query.container.XieYunUserQuery;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/15
 */
public interface XieyunUserLocalManager {

    List<XieYunUserDTO> list(XieYunUserQuery query);

    void insert(XieYunUserDTO userDTO);

    void updateDeleteByUserId(String userId, Integer delete);
}
