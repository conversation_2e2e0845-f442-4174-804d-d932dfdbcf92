package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 模版产品创建的表
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 14:25:58
 */
@Data
@TableName("WOC_DAG_PRODUCT")
public class DagProductDO{

    @TableField(value = "ID")
    private String id;

    @TableField(value = "DAG_ID")
    private String dagId;

    @TableField(value = "ORDER_ID")
    private String orderId;

    //产品类型
    @TableField(value = "PRODUCT_TYPE")
    private String productType;

    //产品创建状态
    @TableField(value = "OPEN_STATUS")
    private String openStatus;

    //消息
    @TableField(value = "MESSAGE")
    private String message;


    //属性快照
    @TableField("PROPERTY_SNAPSHOT")
    private String propertySnapshot;

    //父类产品id 可以为空
    @TableField("PARENT_PRODUCT_ID")
    private Long parentProductId;

    /**
     * gid
     */
    @TableField("GID")
    private String gid;

    /**
     * gid
     */
    @TableField("SUB_ORDER_ID")
    private Long subOrderId;

    @TableLogic(value = "1", delval = "0")
    @TableField("ENABLED")
    private Boolean enabled;

    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("MODIFY_TIME")
    private LocalDateTime modifyTime;
}
