package com.datatech.slgzt.model.business.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 业务系统查询
 *
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/13
 */

@Data
@Accessors(chain = true)
public class BusinessQueryRequest {

    /**
     * 0：租户视图查询，1：下拉列表查询
     */
    private String type;

    /**
     * 登录用户id
     */
    private Long userId;

    /**
     * 业务系统编号列表
     */
    private List<Long> busiSystemIds;

    /**
     * 第几页
     */
    private Integer pageNum = 1;
    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    private String sourceType;

}

