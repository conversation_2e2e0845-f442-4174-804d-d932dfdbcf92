package com.datatech.slgzt.impl.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.DeviceMetricEnum;
import com.datatech.slgzt.enums.DeviceMetricSourceEnum;
import com.datatech.slgzt.enums.DeviceModelTFEnum;
import com.datatech.slgzt.model.BaseMetricInfoModel;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.service.device.DeviceGupInfoService;
import com.datatech.slgzt.service.device.DeviceGupMetricsService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/13 18:06
 * @description TODO
 */
@Slf4j
@Service
public class DeviceNpuMertircServcieImpl implements DeviceGupMetricsService {

    @Value("${http.npuDeviceMetricUrl}")
    private String npuDeviceMetricUrl;

    @Resource
    private DeviceGupInfoService deviceGupInfoService;

    @Override
    public List<DeviceCardMetricsDTO> calculateGpuDeviceMetric() {
        //获取Npu所有的设备
        String url = npuDeviceMetricUrl + "/api/v1/query";
        //获取所有NPU 信息
        List<DeviceGpuInfoDTO> localDeviceInfoList = deviceGupInfoService.queryLocalDeviceInfos(new DeviceInfoQuery().setSourceType(DeviceMetricSourceEnum.NPU_CHIP_SOURCE.getCode()));
        //过滤掉设备id为空的数据
        localDeviceInfoList = localDeviceInfoList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceId()))
                .collect(Collectors.toList());
        //算力利用率
        Map<String, BaseMetricInfoModel> gupMetricNpuUtilization = queryRemoteDeviceMetric(url, DeviceMetricEnum.NPU_CHIP_INFO_UTILIZATION);

        //获取NPU的温度
        Map<String, BaseMetricInfoModel> memoryMetricNpuTemperature = queryRemoteDeviceMetric(url, DeviceMetricEnum.NPU_CHIP_INFO_TEMPERATURE);
        //使用内存显存
        Map<String, BaseMetricInfoModel> memoryMetricHbmNpuUsedMemory = queryRemoteDeviceMetric(url, DeviceMetricEnum.NPU_CHIP_INFO_HBM_USED_MEMORY);
        Map<String, BaseMetricInfoModel> memoryMetricHbmNpuTotalMemory = queryRemoteDeviceMetric(url, DeviceMetricEnum.NPU_CHIP_INFO_HBM_TOTAL_MEMORY);

        Map<String, BaseMetricInfoModel> memoryMetricNpuTotalMemory = queryRemoteDeviceMetric(url, DeviceMetricEnum.NPU_CHIP_INFO_TOTAL_MEMORY);
        Map<String, BaseMetricInfoModel> memoryMetricNpuUsedMemory = queryRemoteDeviceMetric(url, DeviceMetricEnum.NPU_CHIP_INFO_USED_MEMORY);

        Map<String, BaseMetricInfoModel> metricMetricNpuPower = queryRemoteDeviceMetric(url, DeviceMetricEnum.NPU_CHIP_INFO_POWER);

        Map<String, List<BaseMetricInfoModel>> metricProcessNums = queryDeviceProcessCount(url, DeviceMetricEnum.MACHINE_NPU_NUMS);
        log.info("获取NPU算力利用率：{}", JSON.toJSON(gupMetricNpuUtilization));
        log.info("获取NPU的温度：{}", JSON.toJSON(memoryMetricNpuTemperature));
        log.info("获取NPU使用显存：{}", JSON.toJSON(memoryMetricNpuUsedMemory));
        log.info("获取NPU总内存：{}", JSON.toJSON(memoryMetricNpuTotalMemory));
        log.info("获取NPU总内存：{}", JSON.toJSON(metricMetricNpuPower));
        List<DeviceCardMetricsDTO> deviceCardMetrics = new ArrayList<>();
        //创建一个监控指标
        for (DeviceGpuInfoDTO deviceGupInfoDTO : localDeviceInfoList) {
            String id = deviceGupInfoDTO.getDeviceId();
            //设备id存在以逗号分隔的情况,处理下循环
            List<String> deviceIdList= Splitter.on(",").splitToList(id);
            deviceIdList.forEach(deviceId -> {
                DeviceCardMetricsDTO deviceMetricsDTO = new DeviceCardMetricsDTO();
                deviceMetricsDTO.setDeviceId(deviceId);
                deviceMetricsDTO.setModelName(DeviceModelTFEnum.getByModelName(deviceGupInfoDTO.getModelName())
                                                               .getModelName());
                deviceMetricsDTO.setAreaCode(deviceGupInfoDTO.getAreaCode());
                deviceMetricsDTO.setDeviceType("NPU");
                deviceMetricsDTO.setBusinessSystemName(deviceGupInfoDTO.getBusinessSystemName());
                //当前时间记录到小时
                deviceMetricsDTO.setGpuTime(DateUtils.currentHalf("yyyyMMddHH"));
                //算力利用率
                BaseMetricInfoModel metricInfo = gupMetricNpuUtilization.get(deviceId);
                if (ObjNullUtils.isNotNull(metricInfo)&&StringUtils.isNotBlank(metricInfo.getValue())) {
                    deviceMetricsDTO.setGpuUtilPercent(Double.valueOf(metricInfo.getValue()));
                }
                //显存
                BaseMetricInfoModel metricNpuUsedMemory;
                if(deviceGupInfoDTO.getModelName().equals("910B")){
                     metricNpuUsedMemory=memoryMetricHbmNpuUsedMemory.get(deviceId);
                }else {
                     metricNpuUsedMemory = memoryMetricNpuUsedMemory.get(deviceId);
                }
                if (metricNpuUsedMemory != null && StringUtils.isNotBlank(metricNpuUsedMemory.getValue())) {
                    BigDecimal usedMemory = new BigDecimal(metricNpuUsedMemory.getValue());
                    BigDecimal bigDecimal = new BigDecimal(1024);
                    deviceMetricsDTO.setMemoryUsage(NumberUtil.div(usedMemory,bigDecimal));
                }
                //温度
                BaseMetricInfoModel metricNpuTemperature = memoryMetricNpuTemperature.get(deviceId);
                if (metricNpuTemperature != null && StringUtils.isNotBlank(metricNpuTemperature.getValue())) {
                    deviceMetricsDTO.setDevGpuTemp(Double.valueOf(metricNpuTemperature.getValue()));
                }
                //计算显存利用率
                BaseMetricInfoModel metricNpuTotalMemory;
                if(deviceGupInfoDTO.getModelName().equals("910B")){
                    metricNpuTotalMemory=memoryMetricHbmNpuTotalMemory.get(deviceId);
                }else {
                    metricNpuTotalMemory = memoryMetricNpuTotalMemory.get(deviceId);
                }
                deviceMetricsDTO.setMemUtilpercent(computeMemoryUtilization(metricNpuUsedMemory, metricNpuTotalMemory));
                //功耗
                BaseMetricInfoModel metricNpuNpuPower = metricMetricNpuPower.get(deviceId);
                if (metricNpuNpuPower != null && StringUtils.isNotBlank(metricNpuNpuPower.getValue())) {
                    deviceMetricsDTO.setDevPowerUsage(Double.valueOf(metricNpuNpuPower.getValue()));
                }

                //处理器任务数
                List<BaseMetricInfoModel> baseMetricInfoModels = metricProcessNums.get(deviceId);
                if (CollectionUtil.isNotEmpty(baseMetricInfoModels)) {
                    deviceMetricsDTO.setAllocationCount(baseMetricInfoModels.size());
                }

                deviceMetricsDTO.setMetricSource(DeviceMetricSourceEnum.NPU_CHIP_SOURCE.getCode());
                deviceCardMetrics.add(deviceMetricsDTO);
            });

        }
        return deviceCardMetrics;
    }



    /**
     * 获取NPU 显卡数据
     *
     * @return
     */
    @Override
    public void syncRemoteDeviceDataInfo() {

    }


    /**
     * 计算内存利用率
     *
     * @param metricNpuUsedMemory
     * @param metricNpuTotalMemory
     * @return
     */
    public Double computeMemoryUtilization(BaseMetricInfoModel metricNpuUsedMemory, BaseMetricInfoModel metricNpuTotalMemory) {
        if (metricNpuUsedMemory == null || metricNpuTotalMemory == null) {
            return 0.0;
        }
        BigDecimal npuUsedMemory = new BigDecimal(metricNpuUsedMemory.getValue());
        BigDecimal totalMemory = new BigDecimal(metricNpuTotalMemory.getValue());
        //如果totalMemory为0，直接返回0
        if (totalMemory.compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }
        BigDecimal divved = NumberUtil.div(npuUsedMemory, totalMemory);
        return NumberUtil.mul(divved,new BigDecimal("100")).doubleValue();

    }


    /**
     * 根据指标枚举获取具体指标信息
     *
     * @param deviceMetricEnum
     * @return
     */
    @Override
    public Map<String, BaseMetricInfoModel> queryRemoteDeviceMetric(String url, DeviceMetricEnum deviceMetricEnum) {
        Mapper responseMapper = remoteHttpClientMetric(url, deviceMetricEnum);
        log.info("获取指标信息地址url：{} 具体指标：{},获取指标结果：{}", url, deviceMetricEnum.getCode(), JSON.toJSON(responseMapper));
        if (responseMapper != null) {
            String successStr = responseMapper.getString("status");
            Precondition.checkArgument("success".equals(successStr), deviceMetricEnum.getDesc() + "失败: "
                    + responseMapper.getString("message"));
            return parseNpuMetricInfo(responseMapper.getString("data"));
        }
        return Collections.emptyMap();
    }


    /**
     * 解析出指标相对应的值
     *
     * @param metricJson
     * @return
     */
    public Map<String, BaseMetricInfoModel> parseNpuMetricInfo(String metricJson) {
        List<JSONObject> listObjs = Optional.ofNullable(metricJson)
                                            .map(JSONObject::parseObject)
                                            .map(obj -> obj.getString("result"))
                                            .map(JSONArray::parseArray)
                                            .map(arr -> arr.toJavaList(JSONObject.class))
                                            .orElse(Collections.emptyList());

        List<BaseMetricInfoModel> baseMetricInfos = new ArrayList<>();
        listObjs.forEach(itemMetric -> {
            BaseMetricInfoModel deviceMetric = JSONObject.parseObject(itemMetric.getString("metric"), BaseMetricInfoModel.class);
            Optional.ofNullable(itemMetric.getString("value"))
                    .map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(String.class))
                    .filter(list -> list.size() > 1)
                    .map(list -> list.get(1))
                    .ifPresent(deviceMetric::setValue);

            baseMetricInfos.add(deviceMetric);
        });
        //构建指标
        return baseMetricInfos.stream()
                              .collect(Collectors.toMap(BaseMetricInfoModel::getVdieId, meticInfo -> meticInfo, (oldValue, newValue) -> oldValue));
    }

    /**
     * 获取任务数量
     *
     * @param url
     * @param deviceMetricEnum
     * @return
     */
    public Map<String, List<BaseMetricInfoModel>> queryDeviceProcessCount(String url, DeviceMetricEnum deviceMetricEnum) {
        Mapper responseMapper = remoteHttpClientMetric(url, deviceMetricEnum);
        log.info("获取指标信息地址url：{} 具体指标：{},获取指标结果：{}", url, deviceMetricEnum.getCode(), JSON.toJSON(responseMapper));
        if (responseMapper != null) {
            String successStr = responseMapper.getString("status");
            Precondition.checkArgument("success".equals(successStr), deviceMetricEnum.getDesc() + "失败: "
                    + responseMapper.getString("message"));
            return parseNpuProcessCountMetric(responseMapper.getString("data"));
        }
        return Collections.emptyMap();
    }


    /**
     * 解析出指标相对应的值
     *
     * @param metricJson
     * @return
     */
    public Map<String, List<BaseMetricInfoModel>> parseNpuProcessCountMetric(String metricJson) {
        List<JSONObject> listObjs = Optional.ofNullable(metricJson)
                                            .map(JSONObject::parseObject)
                                            .map(obj -> obj.getString("result"))
                                            .map(JSONArray::parseArray)
                                            .map(arr -> arr.toJavaList(JSONObject.class))
                                            .orElse(Collections.emptyList());

        List<BaseMetricInfoModel> baseMetricInfos = new ArrayList<>();
        listObjs.forEach(itemMetric -> {
            BaseMetricInfoModel deviceMetric = JSONObject.parseObject(itemMetric.getString("metric"), BaseMetricInfoModel.class);
            Optional.ofNullable(itemMetric.getString("value"))
                    .map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(String.class))
                    .filter(list -> list.size() > 1)
                    .map(list -> list.get(1))
                    .ifPresent(deviceMetric::setValue);

            baseMetricInfos.add(deviceMetric);
        });
        return baseMetricInfos.stream().collect(Collectors.groupingBy(BaseMetricInfoModel::getVdieId));
    }

}