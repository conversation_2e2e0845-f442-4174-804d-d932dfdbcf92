-- 为WOC_STANDARD_WORK_ORDER_PRODUCT表添加JOB_EXECUTION_ID字段
-- 用于保存协云环境创建Job的执行ID，仅对CQ产品有效

-- 添加字段
ALTER TABLE SLGZT.WOC_STANDARD_WORK_ORDER_PRODUCT 
ADD JOB_EXECUTION_ID VARCHAR2(50) NULL;

-- 添加字段注释
COMMENT ON COLUMN SLGZT.WOC_STANDARD_WORK_ORDER_PRODUCT.JOB_EXECUTION_ID IS 'Job执行ID，仅对CQ产品有效，用于协云环境创建任务的跟踪和重试';

-- 创建索引（可选，如果需要根据JOB_EXECUTION_ID查询）
-- CREATE INDEX IDX_WOC_SWOP_JOB_EXEC_ID ON SLGZT.WOC_STANDARD_WORK_ORDER_PRODUCT(JOB_EXECUTION_ID);
