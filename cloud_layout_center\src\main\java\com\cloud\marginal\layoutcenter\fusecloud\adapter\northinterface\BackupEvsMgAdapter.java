package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.BackupParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.resource.api.backup.dto.CreateBackupRcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * evs备份适配管理
 */
@Component
@Slf4j
public class BackupEvsMgAdapter extends BaseNorthInterfaceAdapter {
    /**
     * 开通evs
     */
    public TaskVO createBackupEvs(String taskId, Integer taskSource){
        log.info("createBackupEvs start");
        CreateBackupRcDto evsDTO = generateCreateEvsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateBackupEvs(),
                null,
                evsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("createBackupEvs url is:{}",northInterfaceAddress.getCreateBackupEvs());
        log.info("createBackupEvs params is:{}",JSONObject.toJSON(evsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"create backup evs");
        return tasksVOResult.getEntity();
    }





    private CreateBackupRcDto generateCreateEvsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam evsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.BACKUP_EVS_CREATE.getCode());
        BackupParam evsParam = JSONObject.parseObject(evsOrder.getAttrs(), BackupParam.class);
        CreateBackupRcDto evsDTO = new CreateBackupRcDto();
        BeanUtils.copyProperties(evsParam,evsDTO);
        evsDTO.setOrderId(layoutOrderParam.getSubOrderId());
        evsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        evsDTO.setBillId(layoutOrderParam.getAccount());
        evsDTO.setGroupId(layoutOrderParam.getCustomId());
        evsDTO.setgId(evsParam.getGId());
        evsDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());

        CreateBackupRcDto.Job job = new CreateBackupRcDto.Job();
        BeanUtils.copyProperties(evsParam.getJob(),job);
        List<Integer> daysOfWeek = new ArrayList<>();
        if(!CollectionUtils.isEmpty(evsParam.getJob().getDaysOfWeek())){
            evsParam.getJob().getDaysOfWeek().forEach(item->{
                daysOfWeek.add(item.getDay());
            });
            Integer[] integers = ArrayUtil.toArray(daysOfWeek,Integer.class);
            job.setDaysOfWeek(integers);
        }
        evsDTO.setJob(job);


        if(ObjectUtil.isNotEmpty(evsParam.getExtendparam())){
            CreateBackupRcDto.Extendparam extendparam = new CreateBackupRcDto.Extendparam();
            BeanUtils.copyProperties(evsParam.getExtendparam(),extendparam);
            evsDTO.setExtendparam(extendparam);
        }

        if(ObjectUtil.isNotEmpty(evsParam.getExtendparam())) {
            CreateBackupRcDto.Retention retention = new CreateBackupRcDto.Retention();
            BeanUtils.copyProperties(evsParam.getRetention(),retention);
            evsDTO.setRetention(retention);
        }
        return evsDTO;
    }


}
