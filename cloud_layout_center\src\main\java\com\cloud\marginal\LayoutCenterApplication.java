package com.cloud.marginal;

import com.cloud.marginal.layoutcenter.config.UniqueNameGenerator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {"com.cloud.*","com.ccmp.*"}, nameGenerator = UniqueNameGenerator.class)
@EnableDiscoveryClient
public class LayoutCenterApplication {
    public static void main(String[] args) {
        SpringApplication.run(LayoutCenterApplication.class,args);
    }
}
