package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/2/17
 */

@Getter
@AllArgsConstructor
public enum RecoveryStatusEnum {
    // 回收状态 0 工单未发起回收 1 资源待回收 2 回收中 3 回收完成 4 回收失败 5 网络回收单独参数 6 取消
    ORDER_TO_BE_RECOVERED(0, "-"),
    RESOURCE_TO_BE_RECOVERED(1, "待回收"),
    RECOVERING(2, "回收中"),
    RECOVERY_COMPLETE(3, "回收完成"),
    RECLAIM_FAILURE(4, "回收失败"),
    CANCELED(6, "取消"),
    // 网络回收状态区分，其他资源勿用
    NETWORK_RECOVERING(5, "回收中"),
    DEFAULT_EMPTY_VALUE(null, "未回收"),
    ;

    private final Integer type;

    private final String remark;

    public static String getRemarkByType(Integer type) {
        if (type == null) {
            return DEFAULT_EMPTY_VALUE.remark;
        }

        RecoveryStatusEnum[] values = RecoveryStatusEnum.values();
        for (RecoveryStatusEnum value : values) {
            if (value.type.equals(type)) {
                return value.remark;
            }
        }

        return DEFAULT_EMPTY_VALUE.remark;
    }


    /**
     * 判断是否可以回收
     */
    public static boolean canRecovery(Integer type) {
        //如果type == null,或者状态是回收失败的状态，都可以回收 或者是0
        if (type == null || RECLAIM_FAILURE.type.equals(type)||ORDER_TO_BE_RECOVERED.type.equals(type)) {
            return true;
        }
        return false;
    }

    /**
     * 适配任务中心结果
     * 1是成功
     * 0是失败
     */
    public static RecoveryStatusEnum adaptTaskCenterResult(Integer result) {
        if (result.equals(1)) {
            return RECOVERY_COMPLETE;
        } else {
            return RECLAIM_FAILURE;
        }
    }
}
