package com.datatech.slgzt.dao.container;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.container.XieYunNamespaceMapper;
import com.datatech.slgzt.dao.model.container.XieYunNamespaceDO;
import com.datatech.slgzt.dao.model.container.XieYunOrgDO;
import com.datatech.slgzt.model.query.container.XieYunNamespaceQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: liupeihan
 * @Date: 2025/4/14
 */

@Repository
public class XieYunNamespaceDAO {

    @Resource
    private XieYunNamespaceMapper mapper;

    public void insert(XieYunNamespaceDO namespaceDO) {
        mapper.insert(namespaceDO);
    }

    public XieYunNamespaceDO getById(String id) {
        return mapper.selectById(id);
    }

    public void update(XieYunNamespaceDO namespaceDO) {
        mapper.updateById(namespaceDO);
    }

    public List<XieYunNamespaceDO> list(XieYunNamespaceQuery query) {
        return mapper.selectList(Wrappers.<XieYunNamespaceDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getNamespaceName()), XieYunNamespaceDO::getNamespaceName, query.getNamespaceName())
                .likeLeft(ObjNullUtils.isNotNull(query.getNamespaceDesc()), XieYunNamespaceDO::getNamespaceDesc, query.getNamespaceDesc())
        );
    }

    public void updateByNamespaceId(XieYunNamespaceDO namespaceDO) {
        UpdateWrapper<XieYunNamespaceDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("XIE_YUN_NAMESPACE_ID", namespaceDO.getXieYunNamespaceId());
        mapper.update(namespaceDO, updateWrapper);
    }
}

