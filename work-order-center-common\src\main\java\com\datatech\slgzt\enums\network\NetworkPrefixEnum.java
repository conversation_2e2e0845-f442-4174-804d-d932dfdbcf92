package com.datatech.slgzt.enums.network;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 * @Date: 2025/2/17
 */

@Getter
@AllArgsConstructor
public enum NetworkPrefixEnum {
    // 回收状态 0 工单未发起回收 1 资源待回收 2 回收中 3 回收完成 4 回收失败
    VPC("vpc", "VPC"),
    NET("net", "网络"),
    SUB("sub", "子网"),
    VPC_SUB("vsub", "子网"),
    ;

    private final String type;

    private final String remark;

    public static String getRemarkByType(String type) {
        if (type == null) {
            return null;
        }

        NetworkPrefixEnum[] values = NetworkPrefixEnum.values();
        for (NetworkPrefixEnum value : values) {
            if (value.type.equals(type)) {
                return value.remark;
            }
        }

        return null;
    }
}
