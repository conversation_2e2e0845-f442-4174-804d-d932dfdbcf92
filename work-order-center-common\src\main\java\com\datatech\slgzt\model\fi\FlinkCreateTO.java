package com.datatech.slgzt.model.fi;


import lombok.Data;

import java.util.List;
import java.util.Map;

//{
//    "name": "test_1205",
//    "id": "",
//    "description": "测试",
//    "childrenTenant": [],
//    "hasChildren": false,
//    "leafTenant": true,
//    "parentId": "",
//    "resourceModel": {
//        "calcResource": {
//            "capacitySchedulerProperties": {},
//            "resourceType": "CALC_RESOURCE",
//            "relationType": "SHARE",
//            "configMode": "SUPER",
//            "serviceName": "Yarn",
//            "superSchedulerProperties": {
//                "resourceAllocations": [
//                    {
//                        "resourceAllocation": {
//                            "resourceWeight": 20,
//                            "maxCpuVCores": "50",
//                            "maxResourcePercent": "0",
//                            "maxResourceValue": "1000",
//                            "minCpuVCores": "20",
//                            "minResourcePercent": "0",
//                            "minResourceValue": "100",
//                            "reservedCpuVCores": 0,
//                            "reservedResourcePercent": "0",
//                            "reservedResourceValue": 0
//                        }
//                    }
//                ]
//            }
//        },
//        "serviceResources": [],
//        "storageResource": []
//    }
//}
@Data
public class FlinkCreateTO {
    private String name;
    private String id;
    private String description;
    private List<Object> childrenTenant;
    private Boolean hasChildren;
    private Boolean leafTenant;
    private String parentId;
    private ResourceModel resourceModel;

    @Data
    public static class ResourceModel {
        private CalcResource calcResource;
        private List<Object> serviceResources;
        private List<Object> storageResource;
    }

    @Data
    public static class CalcResource {
        private Map<String, String> capacitySchedulerProperties;
        private String resourceType;
        private String relationType;
        private String configMode;
        private String serviceName;
        private SuperSchedulerProperties superSchedulerProperties;
    }

    @Data
    public static class SuperSchedulerProperties {
        private List<ResourceAllocations> resourceAllocations;
    }

    @Data
    public static class ResourceAllocations {
        private ResourceAllocation resourceAllocation;
    }

    @Data
    public static class ResourceAllocation {
        private Integer resourceWeight;
        private Integer maxCpuVCores;
        private String maxResourcePercent;
        private Integer maxResourceValue;
        private Integer minCpuVCores;
        private String minResourcePercent;
        private Integer minResourceValue;
        private Integer reservedCpuVCores;
        private String reservedResourcePercent;
        private Integer reservedResourceValue;

    }
}
