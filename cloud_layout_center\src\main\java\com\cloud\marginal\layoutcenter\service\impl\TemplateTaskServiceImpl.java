package com.cloud.marginal.layoutcenter.service.impl;

import com.cloud.marginal.layoutcenter.fusecloud.utils.AdapterMethodCallUtil;
import com.cloud.marginal.layoutcenter.service.TemplateTaskService;
import com.cloud.marginal.mapper.layout.LayoutApiMapper;
import com.cloud.marginal.mapper.layout.LayoutTaskNodeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class TemplateTaskServiceImpl implements TemplateTaskService {

    @Resource
    private LayoutTaskNodeMapper layoutTaskNodeMapper;

    @Resource
    private LayoutApiMapper layoutApiMapper;

    @Autowired
    private AdapterMethodCallUtil adapterMethodCallUtil;

    @Override
    public void taskExec(String mainTaskId) {

    }

}
