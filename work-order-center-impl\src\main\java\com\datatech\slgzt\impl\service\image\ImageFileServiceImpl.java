package com.datatech.slgzt.impl.service.image;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.manager.ImageFileManager;
import com.datatech.slgzt.model.dto.ImageFileDTO;
import com.datatech.slgzt.model.query.ImageFileQuery;
import com.datatech.slgzt.service.file.FileService;
import com.datatech.slgzt.service.image.ImageFileService;
import com.datatech.slgzt.utils.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-05-07 11:24
 **/
@Service
public class ImageFileServiceImpl implements ImageFileService {

    @Resource
    private ImageFileManager manager;

    @Resource
    private FileService fileService;

    @Override
    public Long insertImageFile(ImageFileDTO dto) {
        dto.setId(IdUtil.getSnowflake().nextId());
        manager.insert(dto);
        return dto.getId();
    }

    @Override
    public PageResult<ImageFileDTO> page(ImageFileQuery query) {
        PageResult<ImageFileDTO> page = manager.page(query);
        List<ImageFileDTO> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (ImageFileDTO record : records) {
                if (record.getUploadCompleted()) {
                    record.setProgress(100);
                } else {
                    List<Integer> uploadedChunks = fileService.getUploadedChunks(record.getMd5());
                    record.setProgress((int) Math.round((double) uploadedChunks.size() / record.getTotalParts() * 100));
                    record.setUploadCompleted(uploadedChunks.size() == record.getTotalParts());
                }

            }
        }
        return page;
    }

    @Override
    public void updateImageFile(ImageFileDTO dto) {
        manager.updateById(dto);
    }

    @Override
    public void deleteImageFile(Long id) {
        //删除镜像文件表数据
        manager.deleteById(id);
    }
}
