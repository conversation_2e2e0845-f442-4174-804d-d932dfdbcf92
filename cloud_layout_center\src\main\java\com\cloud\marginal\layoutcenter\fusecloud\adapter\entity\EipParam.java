package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

@Data
public class EipParam {

    private String id;

    private String regionCode;

    private String eipName;

    private Long tenantId;

    private String norm;

    private Long bandwidth;

    private String publicIp;
    //eip创建绑定已有云主机
    private String ecsResourceId;
    //eip资源中心id删除时使用
    private String resourceId;

    private String outInstanceId;

    private String type;

    private String gId;public String getgId() {    return gId;}public void setgId(String gId) {    this.gId = gId;};

}
