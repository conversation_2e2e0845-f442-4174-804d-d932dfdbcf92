package com.datatech.slgzt.model.vo.recovery.export;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: es
 * @author: LK
 * @create: 2025-06-30 15:02
 **/
@Data
public class EsExportVO {

    /**
     * 名称
     */
    @ExcelExportHeader("索引模板名称")
    private String name;

    /**
     * 日均增量数据
     */
    @ExcelExportHeader("日均增量数据")
    private String averageDailyIncrementData;

    /**
     * 保留时间（天）
     */
    @ExcelExportHeader("数据保留时间")
    private String retainTime;

    /**
     * 副本数量
     */
    @ExcelExportHeader("索引副本")
    private String numberOfReplicas;

    /**
     * 磁盘大小
     */
    @ExcelExportHeader("磁盘大小")
    private String diskSize;

    /**
     * 索引模板
     */
    @ExcelExportHeader("索引模板")
    private JSONObject indexTemplate;

    /**
     * 申请时长
     */
    @ExcelExportHeader("申请时长")
    private String applyTimeCn;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;
}
