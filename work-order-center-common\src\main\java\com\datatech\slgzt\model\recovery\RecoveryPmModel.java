package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.model.BaseReconveryProductModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: 裸金属回收模型
 * @author: LK
 * @create: 2025-06-30 15:02
 **/
@Data
public class RecoveryPmModel extends BaseReconveryProductModel {

    private String pmName;

    /**
     * cpu核数
     */
    private String vCpus;

    /**
     * 内存大小
     */
    private String ram;

    /**
     * 硬盘大小
     */
    private String diskSize;

    /**
     * GPU型号
     */
    private String gpuType;

    /**
     * GPU数量
     */
    private Integer gpuNum;

    /**
     * gpu显卡类型 NPU or GPU
     */
    private String gpuCardType;


    /**
     * 申请时长
     */
    private String applyTime;
}
