package com.datatech.slgzt.model.vo.corporate;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资源开通工单基础信息
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/13
 */

@Data
public class DgRecoveryOrderVO {
    private String id;
    /**
     * 订单编号
     */
    @ExcelExportHeader(value = "订单编号")
    private String orderCode;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 创建者id
     */
    @JsonProperty("createdBy")
    private Long creatorId;

    /**
     * 创建人名称
     */
    @ExcelExportHeader(value = "申请人名称")
    @JsonProperty("createdUserName")
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}

