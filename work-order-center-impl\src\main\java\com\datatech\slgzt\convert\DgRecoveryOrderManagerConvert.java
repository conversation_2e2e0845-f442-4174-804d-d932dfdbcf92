package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.DgRecoveryOrderDO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface DgRecoveryOrderManagerConvert {


    DgRecoveryOrderDTO do2dto(DgRecoveryOrderDO dgRecoveryOrderDO);

    DgRecoveryOrderDO dto2do(DgRecoveryOrderDTO dgRecoveryOrderDTO);

}
