package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

@Data
@Accessors(chain = true)
public class CorporateOrderProductQuery {

    private String orderId;

    private List<Long> ids;

    private String productType;

    private Long parentId;

    private List<String> gids;

    private Collection<Long> subOrderIds;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
} 