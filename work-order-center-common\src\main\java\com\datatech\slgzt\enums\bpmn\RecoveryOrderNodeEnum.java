package com.datatech.slgzt.enums.bpmn;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 回收节点-枚举
 */
@Getter
public enum RecoveryOrderNodeEnum {

    /**
     * 订单状态-一类
     */
    EXAMINING("EXAMINING", "进行中"), //审核中
    REJECT("REJECT", "驳回确认"), //驳回的话，工单也是结束，但是可以重新提交
    CLOSE("CLOSE", "工单关闭"), //工单关单(用户关单)，工单为结束，不可重新提交
    END("END", "工单完结"), //工单结束

    /**
     * 回收资源-一类
     */
    USER_TASK("user_task", "租户确认"),
    SCHEMA_ADMINISTRATOR("schema_administrator", "统一架构负责人审核"),
//    PROFESSIONAL_GROUP("professional_group", "主机专业组审核"),
    RETREAT_DIMENSION("retreat_dimension", "交维清退"),
    RESOURCE_RECOVERY("resource_recovery", "资源回收"),
    NETWORK_RECOVERY("network_recovery", "网络回收"),
    BUSINESS_DEPART_LEADER("business_depart_leader", "业务部门领导审核"),
    BUSINESS_DEPART_LEADER2("business_depart_leader2", "业务部门领导审核"),
    CLOUD_LEADER("cloud_leader", "云资源部领导审核"),
    CROSS_DIMENSION("cross_dimension", "资源回收"),
    UNKNOWN("unknown", "未知订单回收操作类型"),
    ;

    private final String code;
    private final String desc;

    RecoveryOrderNodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     */
    public static RecoveryOrderNodeEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (RecoveryOrderNodeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return RecoveryOrderNodeEnum.UNKNOWN;
    }

    public static RecoveryOrderNodeEnum findDescByAuditStatus(ActivityEnum.ActivityStatusEnum auditStatus) {
        switch (auditStatus) {
            case REJECT:
                return RecoveryOrderNodeEnum.REJECT;
            case CLOSE:
                return RecoveryOrderNodeEnum.CLOSE;
        }
        return RecoveryOrderNodeEnum.UNKNOWN;
    }
}
