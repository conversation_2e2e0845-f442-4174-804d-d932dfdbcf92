package com.datatech.slgzt.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 虚拟机性能数据表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VMResourcePerformanceDTO {

    /**
     * resouce_detail表的id，主键
     */
    private Long resourceDetailId;

    /**
     * resouce_detail表的设备id
     */
    private String deviceId;
    /**
     * resouce_detail表的设备名称
     */
    private String deviceName;

    /**
     * resouce_detail表的设备类型
     */
    private String resourceDetailType;

    /**
     * resouce_detail表的云平台名称
     */
    private String domainName;

    /**
     * resouce_detail表的云平台编码
     */
    private String domainCode;

    /**
     * resouce_detail表的可用区名称
     */
    private String azName;

    /**
     * resouce_detail表的可用区编码
     */
    private String azCode;

    /**
     * resouce_detail表的资源池id
     */
    private Long resourcePoolId;

    /**
     * resouce_detail表的资源池名称
     */
    private String resourcePoolName;

    /**
     * resouce_detail表的资源池编码
     */
    private String resourcePoolCode;

    /**
     * CMP_TENANT中租户id
     */
    private Long tenantId;

    /**
     * CMP_TENANT中的租户名称
     */
    private String tenantName;

    /**
     * CMP_TENANT中的STATUS，1-未删除
     */
    private Integer tenantStatus;

    /**
     * CMP_TENANT中的TYPE
     */
    private Integer tenantType;

    /**
     * CMP_TENANT中的客户id
     */
    private String customId;

    /**
     * CCMP_CUSTOM中客户名称
     */
    private String customName;

    /**
     * CCMP_CUSTOM中客户创建人
     */
    private String customCreatedBy;

    /**
     * CCMP_CUSTOM中的status列，1-未删除
     */
    private Integer customStatus;

    /**
     * MC_VM_T中INSTANCE_UUID
     */
    private String vmInstanceUuid;

    /**
     * MC_VM_T中的deleted字段，1-未删除
     */
    private Integer vmDeleted;

    /**
     * CK中云主机名称
     */
    private String ckHostName;

    /**
     * CK中云主机ip
     */
    private String ckIp;

    /**
     * CK中云主机cpu利用率
     */
    private BigDecimal ckCpuUtil;

    /**
     * CK中云主机内存利用率
     */
    private BigDecimal ckMemUtil;

    /**
     * CK中云主机磁盘读IOPS
     */
    private BigDecimal ckDiskReadIops;

    /**
     * CK中云主机磁盘写IOPS
     */
    private BigDecimal ckDiskWriteIops;

    /**
     * CK中云主机容量
     */
    private BigDecimal ckCapacity;

    /**
     * CK中云主机已用容量
     */
    private BigDecimal ckCapacityUsed;

    /**
     * CK中云主机容量利用率
     */
    private BigDecimal ckCapacityUtil;

    /**
     * CK中性能数据的最后更新时间
     */
    private LocalDateTime ckLastedTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态 1.正常，其他-删除
     */
    private Integer status;

    private BigDecimal topPercent;
} 