package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.DeviceVirtualInfoConvert;
import com.datatech.slgzt.dao.DeviceVirtualInfoDAO;
import com.datatech.slgzt.dao.model.DeviceVirtualInfoDO;
import com.datatech.slgzt.manager.DeviceVirtualInfoManager;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


@Service
public class DeviceVirtualCardInfoManagerImpl implements DeviceVirtualInfoManager {

    @Resource
    private DeviceVirtualInfoDAO deviceVirtualInfoDAO;
    @Resource
    private DeviceVirtualInfoConvert deviceVirtualInfoConvert;


    @Override
    public void create(DeviceVirtualInfoDTO dto) {
        DeviceVirtualInfoDO deviceVirtualInfoDO = deviceVirtualInfoConvert.dto2do(dto);
        deviceVirtualInfoDO.setCreatedAt(LocalDateTime.now());
        deviceVirtualInfoDAO.insert(deviceVirtualInfoDO);
    }


    @Override
    public void updateLastByDeviceId(DeviceVirtualInfoDTO dto) {
        deviceVirtualInfoDAO.updateLastByDeviceId(deviceVirtualInfoConvert.dto2do(dto));
    }


    @Override
    public void update(DeviceVirtualInfoDTO dto) {
        DeviceVirtualInfoDO deviceVirtualInfoDO = deviceVirtualInfoConvert.dto2do(dto);
        deviceVirtualInfoDAO.updateById(deviceVirtualInfoDO);
    }



    @Override
    public void delete(Long id) {
        deviceVirtualInfoDAO.deleteById(id);
    }

    @Override
    public void deleteBatch(List<Long> ids) {
         deviceVirtualInfoDAO.deleteByIds(ids);
    }

    @Override
    public PageResult<DeviceVirtualInfoDTO> queryVirtualDeviceInfoPage(DeviceInfoQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<DeviceVirtualInfoDO> list = deviceVirtualInfoDAO.selectList(query);
        return PageWarppers.box(new PageInfo<>(list), deviceVirtualInfoConvert::do2Dto);
    }

    @Override
    public List<DeviceVirtualInfoDTO> selectDeviceVirtualInfoList(DeviceInfoQuery query) {
        return StreamUtils.mapArray(deviceVirtualInfoDAO.selectList(query), deviceVirtualInfoConvert::do2Dto);
    }
}
