package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.RouterInterfaceParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 路由适配管理
 */
@Component
@Slf4j
public class RouteInterfaceMgAdapter extends BaseNorthInterfaceAdapter{

    /**
     * 创建路由
     */
    public TaskVO createRouteInterface(String taskId, Integer taskSource){
        log.info("create route interface start");
        RouterInterfaceParam param = generateCreateRouteDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateRouteInterface(),
                null,
                param,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult,"create route interface");
        return tasksVOResult.getEntity();
    }

    private RouterInterfaceParam generateCreateRouteDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam router = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.ROUTE_CREATE.getCode());
        ProductOrderParam subnet = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.SUBNET_CREATE.getCode());
        RouterInterfaceParam param = new RouterInterfaceParam();
        param.setTenantId(layoutOrderParam.getTenantId());
        param.setRegionCode(layoutOrderParam.getRegionCode());
        param.setIfChangeType("ADD");
        JSONObject routerParam = JSON.parseObject(router.getAttrs());
        JSONObject subnetParam = JSON.parseObject(subnet.getAttrs());
        param.setRouterId(routerParam.getString("id"));
        param.setSubnetId(subnetParam.getString("id"));
        return param;
    }
}
