package com.datatech.slgzt.enums;

import com.datatech.slgzt.utils.ObjNullUtils;

/**
 * 云类型目录枚举类
 * <AUTHOR>
 */

public enum CatalogueDomainCodeEnum {

    CLOUDST_GROUP_MOC("cloudst_group_moc", "移动云"),
    CLOUDST_GROUP_NWC("cloudst_group_nwc", "网络云"),
    CLOUDST_GROUP_PLF("cloudst_group_plf", "平台云"),
    CLOUDST_GROUP_ITC("cloudst_group_itc", "IT云"),
    UNKNOWN("unknown", "-"),
    ;

    private final String code;
    private final String desc;

    CatalogueDomainCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static CatalogueDomainCodeEnum getByCode(String code) {
        if (ObjNullUtils.isNotNull(code)) {
            for (CatalogueDomainCodeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return CatalogueDomainCodeEnum.UNKNOWN;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}