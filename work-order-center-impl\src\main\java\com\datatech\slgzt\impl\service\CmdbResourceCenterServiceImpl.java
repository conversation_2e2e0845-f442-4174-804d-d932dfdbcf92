package com.datatech.slgzt.impl.service;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.mapper.DcnNetworkSegmentMapper;
import com.datatech.slgzt.dao.mapper.VirtualIpMapper;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.RegionDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.service.CmdbResourceCenterService;
import com.datatech.slgzt.utils.IpUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月21日 14:06:36
 */
@Slf4j
@Service
public class CmdbResourceCenterServiceImpl implements CmdbResourceCenterService {


    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;

    @Resource
    private VirtualIpMapper virtualIpMapper;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private RegionManager regionManager;

    @Resource
    private DcnNetworkSegmentMapper dcnNetworkSegmentMapper;

    /**
     * regionCode	1	String	云区域编码
     * appId	1	Long	业务系统主键ID
     * appModuleId	1	Long	业务系统模块ID
     * prefix	1	String	子网前缀
     * mask	1	String	掩码长度
     *
     * @param appDTO
     * @param orderDTO
     */

    @Override
    public void createLevel3IpBindBusSys(CmpAppDTO appDTO, StandardWorkOrderDTO orderDTO) {
        List<ResourceDetailDTO> list = resourceDetailManager.list(new ResourceDetailQuery()
                .setOrderId(orderDTO.getId())
                .setTypeList(Lists.newArrayList("ecs", "gcs","mysql", "redis")));
        //过滤出云主机类型的资源
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/cmdb/createCmdbIpAndAppRel";
        //循环
        for (ResourceDetailDTO resourceDetailDTO : list) {
            String manageIp = virtualIpMapper.getIpListByDeviceId(resourceDetailDTO.getDeviceId());
            //如果为空,则跳过
            if (ObjNullUtils.isNull(manageIp)) {
                continue;
            }
            String resourcePoolId = resourceDetailDTO.getResourcePoolId();
            //通过资源池ID获取区域编码
            RegionDTO regionDTO = regionManager.getById(Long.valueOf(resourcePoolId));
            //匹配是否在网段内
            List<String> cidr = dcnNetworkSegmentMapper.getByRegionId(resourcePoolId);
            if (ObjNullUtils.isNull(cidr)) {
                continue;
            }
            //有个在网段内的IP就可以了
            boolean isInCidr = cidr.stream().anyMatch(c -> IpUtils.isIpInCidr(manageIp, c));
            if (!isInCidr) {
                return;
            }
            HashMap<String, String> params = new HashMap<>();
            params.put("regionCode", regionDTO.getCode());
            params.put("appId", resourceDetailDTO.getBusinessSysId().toString());
            params.put("appModuleId", resourceDetailDTO.getModuleId().toString());
            params.put("prefix", manageIp);
            params.put("mask", "32");
            log.info("调用3级IP创建参数: {}", JSON.toJSONString(params));
            Mapper responseMapper = OkHttpsUtils.http().sync(url)
                                                .bodyType(OkHttps.JSON)
                                                .setBodyPara(JSON.toJSONString(params))
                                                .post()
                                                .getBody()
                                                .toMapper();
            log.info("调用3级IP创建: {}", responseMapper.toString());
            String successStr = responseMapper.getString("success");
            Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                    "调用3级IP创建失败: " + responseMapper.getString("message"));

        }

    }


    @Override
    public void delLevel3IpBindBusSys(Long detailId) {
        log.info("deleteInstance start detailId : {}", detailId);
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.selectByIdNoStatus(detailId);
        if (!ProductTypeEnum.ECS.getCode().equals(resourceDetailDTO.getType())
                || !ProductTypeEnum.GCS.getCode().equals(resourceDetailDTO.getType())
                || !ProductTypeEnum.MYSQL.getCode().equals(resourceDetailDTO.getType())
                || !ProductTypeEnum.REDIS.getCode().equals(resourceDetailDTO.getType())) {
            return;
        }
        //过滤出云主机类型的资源
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/cmdb/deleteCmdbIpAndAppRel";
        //循环

        String manageIp = virtualIpMapper.getIpListByDeviceIdAll(resourceDetailDTO.getDeviceId());
        //如果为空,则跳过
        if (ObjNullUtils.isNull(manageIp)) {
            return;
        }
        String resourcePoolId = resourceDetailDTO.getResourcePoolId();
        //通过资源池ID获取区域编码
        RegionDTO regionDTO = regionManager.getById(Long.valueOf(resourcePoolId));
        //匹配是否在网段内
        List<String> cidr = dcnNetworkSegmentMapper.getByRegionId(resourcePoolId);
        if (ObjNullUtils.isNull(cidr)) {
            return;
        }
        //有个在网段内的IP就可以了
        boolean isInCidr = cidr.stream().anyMatch(c -> IpUtils.isIpInCidr(manageIp, c));
        if (!isInCidr) {
            return;
        }
        HashMap<String, String> params = new HashMap<>();
        params.put("regionCode", regionDTO.getCode());
        params.put("appId", resourceDetailDTO.getBusinessSysId().toString());
        params.put("appModuleId", resourceDetailDTO.getModuleId().toString());
        params.put("prefix", manageIp);
        params.put("mask", "32");
        log.info("调用3级IP删除参数: {}", JSON.toJSONString(params));
        Mapper responseMapper = OkHttpsUtils.http().sync(url)
                                            .bodyType(OkHttps.JSON)
                                            .setBodyPara(JSON.toJSONString(params))
                                            .post()
                                            .getBody()
                                            .toMapper();
        log.info("调用3级IP删除: {}", responseMapper.toString());
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr),
                "调用3级IP删除失败: " + responseMapper.getString("message"));


    }
}
