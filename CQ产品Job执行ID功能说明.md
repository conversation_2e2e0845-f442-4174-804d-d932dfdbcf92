# CQ产品Job执行ID功能说明

## 功能概述

为WOC_STANDARD_WORK_ORDER_PRODUCT表增加JOB_EXECUTION_ID字段，用于保存协云环境创建Job的执行ID，仅对容器配额(CQ)产品有效。

## 实现方案

### 1. 数据库变更

#### 新增字段
- **表名**: `WOC_STANDARD_WORK_ORDER_PRODUCT`
- **字段名**: `JOB_EXECUTION_ID`
- **类型**: `VARCHAR2(50)`
- **允许空值**: `YES`
- **用途**: 保存Spring Batch Job执行ID，仅对CQ产品有效

#### 迁移脚本
```sql
-- 添加字段
ALTER TABLE SLGZT.WOC_STANDARD_WORK_ORDER_PRODUCT 
ADD JOB_EXECUTION_ID VARCHAR2(50) NULL;

-- 添加字段注释
COMMENT ON COLUMN SLGZT.WOC_STANDARD_WORK_ORDER_PRODUCT.JOB_EXECUTION_ID IS 'Job执行ID，仅对CQ产品有效，用于协云环境创建任务的跟踪和重试';
```

### 2. 代码变更

#### 2.1 DO对象更新
- **文件**: `StandardWorkOrderProductDO.java`
- **新增字段**: `jobExecutionId`
- **注解**: `@TableField("JOB_EXECUTION_ID")`

#### 2.2 DTO对象更新
- **文件**: `StandardWorkOrderProductDTO.java`
- **新增字段**: `jobExecutionId`

#### 2.3 Manager层更新
- **接口**: `StandardWorkOrderProductManager.java`
- **新增方法**: `updateJobExecutionIdBySubOrderId(Long subOrderId, String jobExecutionId)`

#### 2.4 DAO层更新
- **文件**: `StandardWorkOrderProductDAO.java`
- **新增方法**: `updateJobExecutionIdBySubOrderId(StandardWorkOrderProductDO product)`

### 3. 核心逻辑实现

#### 3.1 Job执行前保存ExecutionId
**位置**: `XieyunEnvironmentCreateJobListener.beforeJob()`

**逻辑**:
1. 获取JobExecution的ID和SubOrderId
2. 根据SubOrderId查询产品信息
3. 验证产品类型是否为CQ
4. 如果是CQ产品，更新JOB_EXECUTION_ID字段
5. 异常处理，不影响Job正常执行

```java
@Override
public void beforeJob(JobExecution jobExecution) {
    Long jobExecutionId = jobExecution.getId();
    Long subOrderId = jobParameters.getLong("subOrderId");
    
    if (subOrderId != null) {
        StandardWorkOrderProductDTO productDTO = productManager.getBySubOrderId(subOrderId);
        if (productDTO != null && "CQ".equals(productDTO.getProductType())) {
            productManager.updateJobExecutionIdBySubOrderId(subOrderId, jobExecutionId.toString());
        }
    }
}
```

#### 3.2 开通时支持重试
**位置**: `StandardResCQOpenServiceImpl.openStandardResource()`

**逻辑**:
1. 检查产品是否已有JobExecutionId
2. 如果有，解析为Long类型作为重试参数
3. 调用异步服务时传入executionId参数

```java
// 获取已有的Job执行ID（用于重试场景）
Long existingExecutionId = null;
if (productDTO.getJobExecutionId() != null && !productDTO.getJobExecutionId().trim().isEmpty()) {
    existingExecutionId = Long.parseLong(productDTO.getJobExecutionId());
}

// 调用协云平台创建基础环境（异步）
xieyunEnvironmentService.createBaseEnvironment(productDTO.getSubOrderId(), existingExecutionId, cqModel)
```

## 使用场景

### 1. 首次开通
1. 用户提交CQ产品开通申请
2. 系统调用`createBaseEnvironment`方法，executionId为null
3. Spring Batch创建新的Job执行
4. `beforeJob`监听器保存JobExecutionId到数据库
5. Job正常执行

### 2. 重试开通
1. 用户或系统触发CQ产品重新开通
2. 系统从数据库读取已有的JobExecutionId
3. 调用`createBaseEnvironment`方法，传入已有的executionId
4. Spring Batch根据executionId状态决定重启或继续执行
5. 如果需要创建新Job，`beforeJob`监听器会更新JobExecutionId

## 技术特点

### 1. 仅对CQ产品有效
- 通过产品类型判断：`"CQ".equals(productDTO.getProductType())`
- 其他产品类型不会保存JobExecutionId

### 2. 异常安全
- `beforeJob`方法中的异常不会影响Job执行
- 使用try-catch包装，只记录日志

### 3. 支持重试机制
- 利用Spring Batch的重启功能
- 根据JobExecution状态智能处理

### 4. 数据一致性
- 使用数据库事务保证数据一致性
- 通过SubOrderId唯一标识产品

## 测试验证

### 1. 单元测试
- **文件**: `XieyunJobExecutionIdTest.java`
- **覆盖场景**:
  - CQ产品正常更新JobExecutionId
  - 非CQ产品不更新JobExecutionId
  - 产品不存在的处理
  - 异常情况的处理

### 2. 集成测试建议
1. 创建CQ产品工单
2. 触发开通流程
3. 验证数据库中JobExecutionId字段已保存
4. 模拟失败场景，触发重试
5. 验证重试时使用已有的JobExecutionId

## 注意事项

### 1. 数据库兼容性
- 新字段允许为空，不影响现有数据
- 建议在生产环境部署前先在测试环境验证

### 2. 性能影响
- 新增字段对查询性能影响微小
- 如需频繁根据JobExecutionId查询，可考虑添加索引

### 3. 监控建议
- 监控Job执行状态和重试情况
- 关注JobExecutionId更新失败的日志

## 扩展性

该设计具有良好的扩展性：
1. 可以为其他产品类型添加类似功能
2. 可以扩展为保存更多Job相关信息
3. 可以基于JobExecutionId实现更复杂的任务管理功能
