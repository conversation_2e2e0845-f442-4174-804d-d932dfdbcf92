package com.datatech.slgzt.enums;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @description 退维状态枚举
 * @date 2025年02月19日 16:02:43
 */
public enum DisDimensionStatusEnum {
    SUCCESS("success", "退维成功"),
    RUNNING("running", "退维中"),
    //待退维
    WAIT("wait", "待退维"),
    FAIL("fail", "退维失败"),
    UNKNOWN("unknown", "-"),
    ;

    private final String code;
    private final String desc;

    DisDimensionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static String getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (DisDimensionStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value.desc;
                }
            }
        }
        return DisDimensionStatusEnum.UNKNOWN.desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
