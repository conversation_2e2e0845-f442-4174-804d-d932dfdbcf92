package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;

import java.util.List;

/**
 * 虚拟机性能数据表Manager接口
 */
public interface VMResourcePerformanceManager {
    void insert(VMResourcePerformanceDTO dto);

    void update(VMResourcePerformanceDTO dto);

    void delete(Long id);

    VMResourcePerformanceDTO getById(Long resourceDetailId);

    List<VMResourcePerformanceDTO> selectTop5vCPUGroupByCustom(List<String> customIds);

    List<VMResourcePerformanceDTO> selectTop5MemGroupByCustom(List<String> customIds);

    List<VMResourcePerformanceDTO> selectTop5IORateGroupByCustom(List<String> customIds);

    List<VMResourcePerformanceDTO> selectTop5Resource(List<String> customIds, String orderType);
}