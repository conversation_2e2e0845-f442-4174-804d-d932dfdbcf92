package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.CorporateOrderProductDO;
import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CorporateOrderProductManagerConvert {

    CorporateOrderProductDTO do2dto(CorporateOrderProductDO corporateOrderProduct);

    CorporateOrderProductDO dto2do(CorporateOrderProductDTO corporateOrderProductDTO);

    List<CorporateOrderProductDTO> dos2DTOs(List<CorporateOrderProductDO> productDOS);

    List<CorporateOrderProductDO> dtoList2DOs(List<CorporateOrderProductDTO> productDTOS);
} 