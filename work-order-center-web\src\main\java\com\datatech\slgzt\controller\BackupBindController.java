package com.datatech.slgzt.controller;

import cn.hutool.core.collection.ListUtil;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.BackupOperateOpm;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.req.backup.BackupOperateReq;
import com.datatech.slgzt.service.BackupOperateService;
import com.datatech.slgzt.utils.Precondition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: 云备份策略绑定
 * @author: LK
 * @create: 2025-06-06 09:19
 **/
@Slf4j
@RestController
@RequestMapping("/backup")
public class BackupBindController {

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private BackupOperateService backupOperateService;

    @PostMapping("/operate")
    public CommonResult<Void> operateBackup(@RequestBody BackupOperateReq req) {
        ResourceDetailQuery resourceDetailQuery = new ResourceDetailQuery();
        resourceDetailQuery.setIds(req.getDetailId());
        List<ResourceDetailDTO> deviceList = resourceDetailManager.list(resourceDetailQuery);
        List<String> deviceIds = deviceList.stream()
                .map(ResourceDetailDTO::getDeviceId)
                .collect(Collectors.toList());
        List<String> deviceNames = deviceList.stream()
                .map(ResourceDetailDTO::getDeviceName)
                .collect(Collectors.toList());
        ResourceDetailDTO backupDTO = resourceDetailManager.getById(req.getBackupDetailId());
        if ("BIND".equals(req.getType())) {
            deviceList.forEach(device -> Precondition.checkArgument(Objects.isNull(device.getBackupId()), "设备只能绑定一个备份策略"));
        }
        deviceList.forEach(device -> Precondition.checkArgument(device.getResourcePoolId().equals(backupDTO.getResourcePoolId()), device.getDeviceName() + "不在同资源池,不能操作"));
        deviceList.forEach(device -> Precondition.checkArgument(device.getType().equals(backupDTO.getBackupType().toLowerCase()), device.getDeviceName() + "不同资源类型,不能操作"));
        BackupOperateOpm opm = new BackupOperateOpm();
        opm.setBackupJobId(backupDTO.getDeviceId());
        opm.setBackupType(backupDTO.getBackupType());
        if ("BIND".equals(req.getType())) {
            opm.setBindResList(deviceIds);
        } else if ("UNBIND".equals(req.getType())) {
            opm.setUnbindResList(deviceIds);
        }
        //待用服务进行绑定、解绑
        backupOperateService.operateBackup(opm);
        //更新资源绑定关系
        if ("BIND".equals(req.getType())) {
            //资源更新
            deviceList.forEach(device -> {
                device.setBackupId(backupDTO.getDeviceId());
                resourceDetailManager.updateById(device);
            });
            //云备份策略资源更新
            backupDTO.setRelatedDeviceId(convertRelatedDeviceId(backupDTO.getRelatedDeviceId(), deviceIds, 1));
            backupDTO.setRelatedDeviceName(convertRelatedDeviceName(backupDTO.getRelatedDeviceName(), deviceNames, 1));
            backupDTO.setRelatedDeviceType(backupDTO.getBackupType().toLowerCase());
            resourceDetailManager.updateById(backupDTO);
        } else if ("UNBIND".equals(req.getType())) {
            //资源更新
            deviceList.forEach(device -> resourceDetailManager.clearBackupInfoById(device.getId()));
            //云备份策略资源更新
            backupDTO.setRelatedDeviceId(convertRelatedDeviceId(backupDTO.getRelatedDeviceId(), deviceIds, 2));
            backupDTO.setRelatedDeviceName(convertRelatedDeviceName(backupDTO.getRelatedDeviceName(), deviceNames, 2));
            if (StringUtils.isEmpty(backupDTO.getRelatedDeviceId())) {
                backupDTO.setRelatedDeviceType(null);
                resourceDetailManager.clearRelatedInfoById(backupDTO.getId());
            } else {
                resourceDetailManager.updateById(backupDTO);
            }
        }

        return CommonResult.success(null);
    }

    private String convertRelatedDeviceId(String oldRelatedDeviceIds,
                                           List<String> newRelatedDeviceIds,
                                           Integer type) {
        String relatedDeviceId = null;
        if (1 == type) {
            if (StringUtils.isNotBlank(oldRelatedDeviceIds)) {
                List<String> list = ListUtil.toList(oldRelatedDeviceIds.split(","));
                list.addAll(newRelatedDeviceIds);
                list = list.stream().distinct().collect(Collectors.toList());
                relatedDeviceId = String.join(",", list);
            } else {
                relatedDeviceId = String.join(",", newRelatedDeviceIds);
            }
        } else if (2 == type) {
            List<String> oldList = ListUtil.toList(oldRelatedDeviceIds.split(","));
            oldList.removeIf(newRelatedDeviceIds::contains);
            relatedDeviceId = oldList.isEmpty() ? null : String.join(",", oldList);
        }
        return relatedDeviceId;
    }

    private String convertRelatedDeviceName(String oldRelatedDeviceName,
                                           List<String> newRelatedDeviceName,
                                           Integer type) {
        String relatedDeviceName = null;
        if (1 == type) {
            if (StringUtils.isNotBlank(oldRelatedDeviceName)) {
                List<String> list = ListUtil.toList(oldRelatedDeviceName.split(","));
                list.addAll(newRelatedDeviceName);
                list = list.stream().distinct().collect(Collectors.toList());
                relatedDeviceName = String.join(",", list);
            } else {
                relatedDeviceName = String.join(",", newRelatedDeviceName);
            }
        } else if (2 == type) {
            List<String> oldList = ListUtil.toList(oldRelatedDeviceName.split(","));
            oldList.removeIf(newRelatedDeviceName::contains);
            relatedDeviceName = oldList.isEmpty() ? null : String.join(",", oldList);
        }
        return relatedDeviceName;
    }
}
