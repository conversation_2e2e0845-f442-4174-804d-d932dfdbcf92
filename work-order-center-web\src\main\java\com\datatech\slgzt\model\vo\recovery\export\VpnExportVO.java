package com.datatech.slgzt.model.vo.recovery.export;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: vpn导出
 * @author: LK
 * @create: 2025-06-12 09:40
 **/
@Data
public class VpnExportVO {

    @ExcelExportHeader(value = "vpn名称")
    private String name;

    @ExcelExportHeader(value = "允许同时连接的最大客户端数量")
    private Integer maxConnection;

    @ExcelExportHeader(value = "带宽")
    private String bandwidth;

    @ExcelExportHeader(value = "申请时长")
    private String applyTimeCn;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;
}
