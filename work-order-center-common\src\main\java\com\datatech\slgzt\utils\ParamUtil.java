package com.datatech.slgzt.utils;

import net.sourceforge.pinyin4j.PinyinHelper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-17 16:14
 **/
public class ParamUtil {

    /**
     * 判断是否为中文字符（包括扩展区 A/B、标点等）
     */
    private static boolean isChinese(char c) {
        Character.UnicodeBlock block = Character.UnicodeBlock.of(c);
        return block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                block == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS ||
                block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ||
                block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ||
                block == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION ||
                block == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS;
    }

    /**
     * 获取字符串的首字母简拼（中文取拼音首字母，英文按单词取首字母，统一小写）
     */
    public static String getFirstLetters(String input) {
        StringBuilder sb = new StringBuilder();
        char[] chars = input.toCharArray();
        int i = 0;

        while (i < chars.length) {
            char c = chars[i];

            // 处理中文字符
            if (isChinese(c)) {
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        String pinyin = pinyinArray[0].replaceAll("\\d", "");
                        sb.append(Character.toLowerCase(pinyin.charAt(0)));
                    }
                } catch (Exception ignore) {
                }
                i++;
            }
            // 处理英文字符（连续视为一个单词，仅取第一个字母）
            else if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) {
                sb.append(Character.toLowerCase(c)); // 只取首字母并转小写
                i++;
                while (i < chars.length && ((chars[i] >= 'A' && chars[i] <= 'Z') || (chars[i] >= 'a' && chars[i] <= 'z'))) {
                    i++;
                }
            }
            // 处理数字字符
            else if (c >= '0' && c <= '9') {
                sb.append(c); // 直接保留数字
                i++;
            }
            // 忽略其他字符（如符号、空格等）
            else {
                i++;
            }
        }

        return sb.toString();
    }

    /**
     * 生成格式：首字母简拼 + 时间戳（毫秒）
     */
    public static String generateKey(String input) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        return getFirstLetters(input) + sdf.format(new Date());
    }


    /**
     * 生成随机密码
     *
     * @param length 密码长度
     * @return 随机生成的密码
     */
    public static String generatePassword(int length) {
        // 定义密码字符集（大小写字母、数字、特殊字符）
        String upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowerCaseLetters = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "0123456789";
        String specialCharacters = "!@#$%^&*()-_=+[]{}|;:,.<>?";

        // 组合所有字符集
        String allCharacters = upperCaseLetters + lowerCaseLetters + numbers + specialCharacters;

        Random random = new Random();
        StringBuilder password = new StringBuilder(length);

        // 确保至少包含一个大写字母、小写字母、数字和特殊字符
        password.append(upperCaseLetters.charAt(random.nextInt(upperCaseLetters.length())));
        password.append(lowerCaseLetters.charAt(random.nextInt(lowerCaseLetters.length())));
        password.append(numbers.charAt(random.nextInt(numbers.length())));
        password.append(specialCharacters.charAt(random.nextInt(specialCharacters.length())));

        // 剩余部分随机填充
        for (int i = 4; i < length; i++) {
            password.append(allCharacters.charAt(random.nextInt(allCharacters.length())));
        }

        // 打乱顺序以确保随机性
        return shuffleString(password.toString());
    }

    /**
     * 打乱字符串顺序
     *
     * @param s 输入字符串
     * @return 打乱后的字符串
     */
    private static String shuffleString(String s) {
        List<Character> characters = new ArrayList<>();
        for (char c : s.toCharArray()) {
            characters.add(c);
        }
        StringBuilder shuffled = new StringBuilder(s.length());
        Random random = new Random();
        while (!characters.isEmpty()) {
            int index = random.nextInt(characters.size());
            shuffled.append(characters.remove(index));
        }
        return shuffled.toString();
    }

    /**
     * 测试入口
     */
    public static void main(String[] args) {
        String input = "算力工作台测试0618-vmware测试专用";
        String key = generateKey(input);
        System.out.println("输入：" + input);
        System.out.println("生成结果：" + key);
        System.out.println("生成密码：" + generatePassword(14));
    }

}
