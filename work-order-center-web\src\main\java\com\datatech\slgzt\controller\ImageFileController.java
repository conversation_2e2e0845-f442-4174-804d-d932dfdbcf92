package com.datatech.slgzt.controller;

import com.datatech.slgzt.convert.ImageFileWebConvert;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ImageFileDTO;
import com.datatech.slgzt.model.query.ImageFileQuery;
import com.datatech.slgzt.model.req.image.ImageFileCreateReq;
import com.datatech.slgzt.model.req.image.ImageFilePageReq;
import com.datatech.slgzt.model.vo.image.ImageFileVO;
import com.datatech.slgzt.service.image.ImageFileService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @program: workordercenterproject
 * @description: 镜像文件controller
 * @author: LK
 * @create: 2025-05-07 11:25
 **/
@RestController
@RequestMapping("/imageFile")
public class ImageFileController {

    @Resource
    private ImageFileService imageFileService;

    @Resource
    private ImageFileWebConvert convert;

    @PostMapping("/insert")
    public CommonResult<Long> insert(@RequestBody ImageFileCreateReq req) {
        Precondition.checkArgument(req.getFileName(), "文件名称不能为空");
        Precondition.checkArgument(req.getMd5(), "md5值不能为空");
        Precondition.checkArgument(req.getTotalChunksCount(), "文件总块数不能为空");
        ImageFileDTO imageFileDTO = convert.convert(req);
        return CommonResult.success(imageFileService.insertImageFile(imageFileDTO));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<ImageFileVO>> page(@RequestBody ImageFilePageReq req) {
        ImageFileQuery imageFileQuery = convert.convert(req);
        PageResult<ImageFileDTO> page = imageFileService.page(imageFileQuery);
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }

    @PostMapping("/update")
    public CommonResult<String> update(@RequestBody ImageFileCreateReq req) {
        ImageFileDTO imageFileDTO = convert.convert(req);
        imageFileService.updateImageFile(imageFileDTO);
        return CommonResult.success("更新成功");
    }

    @GetMapping("/delete")
    public CommonResult<String> delete(Long id) {
        imageFileService.deleteImageFile(id);
        return CommonResult.success("删除成功");
    }

}
