package com.cloud.marginal.layoutcenter.fusecloud.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

@Component
public class MethodChainCallUtil {

    @Autowired
    AdapterMethodCallUtil methodCall;

    /**
     * 执行方法，如果方法有下一个方法就递归调用，直到没有后续的方法
     * @param node 方法节点
     * @return
     * @throws ClassNotFoundException
     * @throws NoSuchMethodException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    public Map call(MethodNode node) throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        Map methodCallResult = node.getMethodParam() == null ?methodCall.adapterMethodCall(node.getFullTypeName(), node.getMethodName()) : methodCall.adapterMethodCall(node.getFullTypeName(), node.getMethodName(), node.getMethodParam());

        if(node.getNextNode()==null){
            return methodCallResult;
        }
        //参数映射转换
        if(node.getResultToNextNodeParamMapping()!=null){
            node.getResultToNextNodeParamMapping().forEach((curNodeResultKey, nextNodeParamKey)->{
                if(node.getNextNode().getMethodParam() == null) node.getNextNode().setMethodParam(new HashMap<>());
                node.getNextNode().getMethodParam().put(nextNodeParamKey,methodCallResult.get(curNodeResultKey));
            });
        }
        return call(node.getNextNode());
    }
}
