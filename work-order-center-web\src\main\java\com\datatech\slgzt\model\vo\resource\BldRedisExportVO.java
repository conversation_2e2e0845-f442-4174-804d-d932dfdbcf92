package com.datatech.slgzt.model.vo.resource;

import com.alibaba.fastjson.annotation.JSONField;
import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.util.Date;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-12 16:35
 **/
@Data
public class BldRedisExportVO {

    /**
     * redis实例名称
     */
    @ExcelExportHeader("实例名称")
    private String deviceName;

    /**
     * redis实例ip
     */
    @ExcelExportHeader("实例ip")
    private String ip;

    /**
     * CPU架构
     */
    @ExcelExportHeader("CPU架构")
    private String cpuArchitecture;

    /**
     * 业务系统名称
     */
    @ExcelExportHeader("业务系统名称")
    private String businessSystemName;

    /**
     * 用户名
     */
    @ExcelExportHeader("用户名")
    private String username;

    /**
     * 密码
     */
    @ExcelExportHeader("密码")
    private String password;

    @ExcelExportHeader(value = "申请时长")
    private String applyTime;

    @ExcelExportHeader(value = "租户名称")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String cloudPlatform;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private Date effectiveTime;

    @ExcelExportHeader(value = "到期时间")
    private Date expireTime;

    @ExcelExportHeader(value = "计费号")
    private String billId;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;

}
