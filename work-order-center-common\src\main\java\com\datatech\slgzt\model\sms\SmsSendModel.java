package com.datatech.slgzt.model.sms;

import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 短信发送模型
 * 用于封装发送短信所需的各种参数
 *
 * <AUTHOR>
 * @date 2025年 03月17日 19:46:49
 */
@Data
@Accessors(chain = true)
public class SmsSendModel {


    //用户id
    private Long userId;

    //角色列表
    private List<String> roles;

    //订单类型
    private String orderType;

    //订单code
    private String orderCode;

    //订单标题
    private String orderTitle;

    //产品类型
    private String productType;

    /**
     * 直接通过手机号发送短信
     */
    private String phone;

    /**
     * 订单类型中文
     */
    private String orderTypeCn;

    //动作类型
    private ActivityEnum.ActivityStatusEnum activityStatus;

    //当前节点
    private String currentNode;

    //当前节点
    private String currentNodeName;

    // 创建工单用户id,当工单结束后需要给创建者发送消息
    private Long createdOrderUserId;

    private String expireTime;

    //宝兰德redis
    private String username;

    private String password;

    private String instancePassword;

    private Long tenantId;

}
