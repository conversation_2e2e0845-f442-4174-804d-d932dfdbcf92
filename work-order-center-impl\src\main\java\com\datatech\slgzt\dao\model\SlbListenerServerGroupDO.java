package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datatech.slgzt.model.TaskStatusExt;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SLB监听器服务组DO
 */
@Data
@TableName("WOC_SLB_LISTENER_SERVER_GROUP")
public class SlbListenerServerGroupDO implements Serializable {

    private static final long serialVersionUID = 1L;

    //主键ID
    @TableId(value = "ID", type = IdType.ID_WORKER)
    private String id;

    //分组名称
    @TableField("GROUP_NAME")
    private String groupName;

    //分组Type
    @TableField("GROUP_TYPE")
    private String groupType;

    //Slb资源ID
    @TableField("SLB_RESOURCE_DETAIL_ID")
    private Long slbResourceDetailId;

    //slb监听器ID
    @TableField("SLB_LISTENER_ID")
    private String slbListenerId;

    //slb监听器名称
    @TableField("SLB_LISTENER_NAME")
    private String slbListenerName;

    //服务器信息JSON
    @TableField("SERVER_INFO")
    private String serverInfoModelList;

    @TableField("TASK_STATUS_EXT")
    private String taskStatusExt;
    //创建时间
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;
} 