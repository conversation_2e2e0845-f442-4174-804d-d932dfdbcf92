package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.CatalogueDomainConfigManagerConvert;
import com.datatech.slgzt.dao.CatalogueDomainConfigDAO;
import com.datatech.slgzt.dao.model.CatalogueDomainConfigDO;
import com.datatech.slgzt.manager.CatalogueDomainConfigManager;
import com.datatech.slgzt.model.dto.CatalogueDomainConfigDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 目录域配置Manager实现
 */
@Service
public class CatalogueDomainConfigManagerImpl implements CatalogueDomainConfigManager {

    @Resource
    private CatalogueDomainConfigDAO catalogueDomainConfigDAO;

    @Resource
    private CatalogueDomainConfigManagerConvert convert;

    @Override
    public List<CatalogueDomainConfigDTO> listByBusinessCode(String businessCode) {
        List<CatalogueDomainConfigDO> configDOS = catalogueDomainConfigDAO.listByBusinessCode(businessCode);
        return convert.doList2dtoList(configDOS);
    }



} 