<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:yaoqiang="http://bpmn.sourceforge.net"
             exporter="Yaoqiang BPMN Editor" exporterVersion="5.3" expressionLanguage="http://www.w3.org/1999/XPath"
             id="m1632821341533" name="" targetNamespace="http://www.activiti.org/test"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://bpmn.sourceforge.net/schemas/BPMN20.xsd">
    <process id="resource-create-process-1" isClosed="false" isExecutable="true" name="resource-create-process-1"
             processType="None">
        <extensionElements>
            <yaoqiang:description/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276"
                                 imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0"
                                 width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
            <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276"
                                 imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0"
                                 width="598.1102362204724"/>
            <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
        </extensionElements>
        <startEvent id="_2" isInterrupting="true" name="StartEvent" parallelMultiple="false">
            <outgoing>_12</outgoing>
            <outputSet/>
        </startEvent>
        <userTask activiti:assignee="${userId}" activiti:exclusive="true" completionQuantity="1" id="_3"
                  implementation="##unspecified" isForCompensation="false" name="user_task" startQuantity="1">
            <incoming>_12</incoming>
            <incoming>_15</incoming>
            <incoming>_19</incoming>
            <incoming>_24</incoming>
            <incoming>_32</incoming>
            <incoming>_35</incoming>
            <incoming>_60</incoming>
            <outgoing>_13</outgoing>
            <outgoing>_49</outgoing>
        </userTask>
        <userTask activiti:assignee="${schema}" activiti:exclusive="true" completionQuantity="1" id="_4"
                  implementation="##unspecified" isForCompensation="false" name="schema_administrator"
                  startQuantity="1">
            <incoming>_13</incoming>
            <incoming>_34</incoming>
            <incoming>_38</incoming>
            <incoming>_43</incoming>
            <incoming>_47</incoming>
            <incoming>_64</incoming>
            <outgoing>_14</outgoing>
<!--            <outgoing>_50</outgoing>-->
        </userTask>
        <userTask activiti:assignee="${userId}" activiti:exclusive="true" completionQuantity="1" id="_27"
                  implementation="##unspecified" isForCompensation="false" name="tenant_task" startQuantity="1">
            <incoming>_16</incoming>
            <incoming>_33</incoming>
            <incoming>_42</incoming>
            <incoming>_46</incoming>
            <incoming>_63</incoming>
            <outgoing>_36</outgoing>
<!--            <outgoing>_51</outgoing>-->
        </userTask>
        <userTask activiti:assignee="${business2}" activiti:exclusive="true" completionQuantity="1" id="_5"
                  implementation="##unspecified" isForCompensation="false" name="business2_depart_leader"
                  startQuantity="1">
            <incoming>_39</incoming>
            <incoming>_41</incoming>
            <incoming>_45</incoming>
            <incoming>_62</incoming>
            <outgoing>_18</outgoing>
        </userTask>
        <userTask activiti:assignee="${business}" activiti:exclusive="true" completionQuantity="1" id="_17"
                  implementation="##unspecified" isForCompensation="false" name="business_depart_leader"
                  startQuantity="1">
            <incoming>_20</incoming>
            <incoming>_44</incoming>
            <incoming>_61</incoming>
            <outgoing>_30</outgoing>
        </userTask>
        <userTask activiti:assignee="${cloud}" activiti:exclusive="true" completionQuantity="1" id="_6"
                  implementation="##unspecified" isForCompensation="false" name="cloud_leader" startQuantity="1">
            <incoming>_31</incoming>
            <incoming>_65</incoming>
            <incoming>_66</incoming>
            <outgoing>_22</outgoing>
        </userTask>
        <userTask activiti:assignee="${cloud_2}" activiti:exclusive="true" completionQuantity="1" id="_55"
                  implementation="##unspecified" isForCompensation="false" name="cloud_leader_2" startQuantity="1">
            <incoming>_23</incoming>
            <outgoing>_58</outgoing>
        </userTask>
        <userTask activiti:assignee="${network}" activiti:exclusive="true" completionQuantity="1" id="_9"
                  implementation="##unspecified" isForCompensation="false" name="network_provisioning"
                  startQuantity="1">
            <incoming>_57</incoming>
            <outgoing>_25</outgoing>
        </userTask>
        <userTask activiti:assignee="${resource}" activiti:exclusive="true" completionQuantity="1" id="_21"
                  implementation="##unspecified" isForCompensation="false" name="resource_creation" startQuantity="1">
            <incoming>_25</incoming>
            <incoming>_48</incoming>
            <outgoing>_40</outgoing>
        </userTask>
        <sequenceFlow id="_25" sourceRef="_9" targetRef="_21"/>
        <sequenceFlow id="_12" sourceRef="_2" targetRef="_3"/>
        <sequenceFlow id="_14" sourceRef="_4" targetRef="_8"/>
        <sequenceFlow id="_15" name="reject" sourceRef="_8" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='user_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_16" name="adopt" sourceRef="_8" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_38" name="reject" sourceRef="_36" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='schema_administrator'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_39" name="isSkipBusinessNode=1" sourceRef="_36" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1 && isSkipBusinessNode==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_18" sourceRef="_5" targetRef="_10"/>
        <sequenceFlow id="_19" name="business2 reject" sourceRef="_10" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='user_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_20" name="business2 adopt" sourceRef="_10" targetRef="_17">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_31" name="business adopt" sourceRef="_29" targetRef="_6">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_32" name="business reject" sourceRef="_29" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='user_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_35" name="reject" sourceRef="_36" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='user_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_22" sourceRef="_6" targetRef="_11"/>
        <sequenceFlow id="_24" name="cloud reject" sourceRef="_11" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='user_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_60" name="cloud 2 reject" sourceRef="_56" targetRef="_3">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='user_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_61" name="cloud 2 reject" sourceRef="_56" targetRef="_17">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='business_depart_leader'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_62" name="cloud 2 reject" sourceRef="_56" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='business2_depart_leader'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_63" name="cloud 2 reject" sourceRef="_56" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='tenant_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_64" name="cloud 2 reject" sourceRef="_56" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='schema_administrator'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_65" name="cloud 2 reject" sourceRef="_56" targetRef="_6">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='cloud_leader'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_13" sourceRef="_3" targetRef="_4"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_10" name="business de leader examine">
            <incoming>_18</incoming>
            <outgoing>_19</outgoing>
            <outgoing>_20</outgoing>
            <outgoing>_52</outgoing>
        </exclusiveGateway>
        <exclusiveGateway gatewayDirection="Diverging" id="_11" name="cloud leader examine">
            <incoming>_22</incoming>
            <outgoing>_23</outgoing>
            <outgoing>_24</outgoing>
            <outgoing>_44</outgoing>
            <outgoing>_45</outgoing>
            <outgoing>_46</outgoing>
            <outgoing>_47</outgoing>
            <outgoing>_54</outgoing>
        </exclusiveGateway>
        <exclusiveGateway gatewayDirection="Diverging" id="_56" name="cloud leader 2 examine">
            <incoming>_58</incoming>
            <outgoing>_48</outgoing>
            <outgoing>_57</outgoing>
            <outgoing>_59</outgoing>
            <outgoing>_60</outgoing>
            <outgoing>_61</outgoing>
            <outgoing>_62</outgoing>
            <outgoing>_63</outgoing>
            <outgoing>_64</outgoing>
            <outgoing>_65</outgoing>
        </exclusiveGateway>
        <sequenceFlow id="_23" name="cloud adopt" sourceRef="_11" targetRef="_55">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_57" name="cloud adopt 2" sourceRef="_56" targetRef="_9">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1 && isNetOpen==0}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_48" name="cloud adopt resource" sourceRef="_56" targetRef="_21">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1 && isNetOpen==1}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_49" name="userTaskClose" sourceRef="_3" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_50" name="schemaAdministratorClose" sourceRef="_8" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_51" name="tenantTaskClose" sourceRef="_36" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_58" sourceRef="_55" targetRef="_56"/>
        <sequenceFlow id="_52" name="business2Close" sourceRef="_10" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_53" name="businessLeaderClose" sourceRef="_29" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_54" name="cloudLeaderClose" sourceRef="_11" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_59" name="cloudLeader2Close" sourceRef="_56" targetRef="_7">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==2}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_37" sourceRef="_27" targetRef="_36"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_36" name="tenant examine">
            <incoming>_37</incoming>
            <outgoing>_38</outgoing>
            <outgoing>_39</outgoing>
            <outgoing>_35</outgoing>
            <outgoing>_51</outgoing>
            <outgoing>_66</outgoing>
        </exclusiveGateway>
        <exclusiveGateway gatewayDirection="Diverging" id="_8" name="administrator examine">
            <incoming>_14</incoming>
            <outgoing>_15</outgoing>
            <outgoing>_16</outgoing>
            <outgoing>_50</outgoing>
        </exclusiveGateway>
        <sequenceFlow id="_66" name="isSkipBusinessNode=0" sourceRef="_36" targetRef="_6">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==1 && isSkipBusinessNode==0}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_40" sourceRef="_21" targetRef="_7"/>
        <endEvent id="_7" name="EndEvent">
            <incoming>_40</incoming>
            <incoming>_49</incoming>
            <incoming>_50</incoming>
            <incoming>_51</incoming>
            <incoming>_52</incoming>
            <incoming>_53</incoming>
            <incoming>_54</incoming>
            <incoming>_59</incoming>
            <inputSet/>
        </endEvent>
        <sequenceFlow id="_30" sourceRef="_17" targetRef="_29"/>
        <exclusiveGateway gatewayDirection="Diverging" id="_29" name="business de leader examine">
            <incoming>_30</incoming>
            <outgoing>_31</outgoing>
            <outgoing>_32</outgoing>
            <outgoing>_53</outgoing>
        </exclusiveGateway>
        <sequenceFlow id="_33" name="business2 reject" sourceRef="_10" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='tenant_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_34" name="business2 reject" sourceRef="_10" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='schema_administrator'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_41" name="business reject" sourceRef="_29" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='business2_depart_leader'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_42" name="business reject" sourceRef="_29" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='tenant_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_43" name="business reject" sourceRef="_29" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='schema_administrator'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_44" name="cloud reject" sourceRef="_11" targetRef="_17">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='business_depart_leader'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_45" name="cloud reject" sourceRef="_11" targetRef="_5">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='business2_depart_leader'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_46" name="cloud reject" sourceRef="_11" targetRef="_27">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='tenant_task'}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="_47" name="cloud reject" sourceRef="_11" targetRef="_4">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[${message==0 && nodeCode=='schema_administrator'}]]>
            </conditionExpression>
        </sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram id="Yaoqiang_Diagram-resource-create-process-1" name="New Diagram" resolution="96.0">
        <bpmndi:BPMNPlane bpmnElement="resource-create-process-1">
            <bpmndi:BPMNShape bpmnElement="_2" id="Shape-_2">
                <omgdc:Bounds height="32.0" width="32.0" x="240.0" y="65.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_3" id="Shape-_3">
                <omgdc:Bounds height="55.0" width="85.0" x="215.0" y="135.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_4" id="Shape-_4">
                <omgdc:Bounds height="55.0" width="85.0" x="224.0" y="275.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_5" id="Shape-_5">
                <omgdc:Bounds height="55.0" width="85.0" x="218.0" y="462.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_6" id="Shape-_6">
                <omgdc:Bounds height="55.0" width="85.0" x="220.0" y="635.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_10" id="Shape-_10" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="237.0" y="550.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_11" id="Shape-_11" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="237.0" y="726.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_9" id="Shape-_9">
                <omgdc:Bounds height="55.0" width="85.0" x="368.46328920030635" y="719.4046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_21" id="Shape-_21">
                <omgdc:Bounds height="55.0" width="85.0" x="524.2889328973346" y="719.2030577822226"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_27" id="Shape-_27">
                <omgdc:Bounds height="55.0" width="85.0" x="394.83333333333337" y="368.16666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_36" id="Shape-_36" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="407.1666666666667" y="468.16666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_8" id="Shape-_8" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="244.0" y="374.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_7" id="Shape-_7">
                <omgdc:Bounds height="32.0" width="32.0" x="828.0" y="730.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_17" id="Shape-_17">
                <omgdc:Bounds height="55.0" width="85.0" x="396.5616024187452" y="543.2067271352985"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_29" id="Shape-_29" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="413.54598135550515" y="640.7847064751826"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="_13" id="BPMNEdge__13" sourceElement="_3" targetElement="_4">
                <omgdi:waypoint x="262.0" y="190.0"/>
                <omgdi:waypoint x="262.0" y="275.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="259.0" y="222.84"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_35" id="BPMNEdge__35" sourceElement="_36" targetElement="_3">
                <omgdi:waypoint x="407.16666666666663" y="484.16666666666663"/>
                <omgdi:waypoint x="300.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_12" id="BPMNEdge__12" sourceElement="_2" targetElement="_3">
                <omgdi:waypoint x="256.0" y="97.0"/>
                <omgdi:waypoint x="256.0" y="135.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="253.0" y="106.34"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_34" id="BPMNEdge__34" sourceElement="_10" targetElement="_4">
                <omgdi:waypoint x="253.0" y="550.0"/>
                <omgdi:waypoint x="253.0" y="330.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_15" id="BPMNEdge__15" sourceElement="_8" targetElement="_3">
                <omgdi:waypoint x="244.0" y="390.0"/>
                <omgdi:waypoint x="195.0" y="270.0"/>
                <omgdi:waypoint x="215.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="178.5" y="283.46"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_37" id="BPMNEdge__37" sourceElement="_27" targetElement="_36">
                <omgdi:waypoint x="423.1666666666667" y="423.0"/>
                <omgdi:waypoint x="423.1666666666667" y="468.16666666666674"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="425.17" y="435.92"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_14" id="BPMNEdge__14" sourceElement="_4" targetElement="_8">
                <omgdi:waypoint x="260.0" y="330.0"/>
                <omgdi:waypoint x="260.0" y="374.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="262.0" y="342.34"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_39" id="BPMNEdge__39" sourceElement="_36" targetElement="_5">
                <omgdi:waypoint x="407.16666666666663" y="484.16666666666663"/>
                <omgdi:waypoint x="303.0" y="489.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="338.58" y="479.45"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_66" id="BPMNEdge__66" sourceElement="_36" targetElement="_6">
                <omgdi:waypoint x="407.16666666666663" y="484.16666666666663"/>
                <omgdi:waypoint x="303.0" y="489.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="338.58" y="479.45"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_16" id="BPMNEdge__16" sourceElement="_8" targetElement="_27">
                <omgdi:waypoint x="276.0" y="390.0"/>
                <omgdi:waypoint x="395.0" y="395.66666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="323.92" y="385.37"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_38" id="BPMNEdge__38" sourceElement="_36" targetElement="_4">
                <omgdi:waypoint x="438.83333333333337" y="484.16666666666663"/>
                <omgdi:waypoint x="501.0" y="393.0"/>
                <omgdi:waypoint x="309.0" y="302.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="33.0" x="484.5" y="315.96"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_19" id="BPMNEdge__19" sourceElement="_10" targetElement="_3">
                <omgdi:waypoint x="237.0" y="566.0"/>
                <omgdi:waypoint x="110.0" y="370.0"/>
                <omgdi:waypoint x="215.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="86.0" x="67.0" y="367.96"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_18" id="BPMNEdge__18" sourceElement="_5" targetElement="_10">
                <omgdi:waypoint x="253.0" y="517.0"/>
                <omgdi:waypoint x="253.0" y="550.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="255.0" y="523.84"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_40" id="BPMNEdge__40" sourceElement="_21" targetElement="_7">
                <omgdi:waypoint x="609.0" y="746.7030577822226"/>
                <omgdi:waypoint x="673.0" y="746.5749921788414"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="638.04" y="736.76"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_20" id="BPMNEdge__20" sourceElement="_10" targetElement="_17">
                <omgdi:waypoint x="269.0" y="566.0"/>
                <omgdi:waypoint x="397.0" y="570.7067271352985"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="86.0" x="294.78" y="560.84"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_42" id="BPMNEdge__42" sourceElement="_29" targetElement="_27">
                <omgdi:waypoint x="429.54598135550515" y="641.4540186444949"/>
                <omgdi:waypoint x="429.54598135550515" y="423.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_41" id="BPMNEdge__41" sourceElement="_29" targetElement="_5">
                <omgdi:waypoint x="414.2152935248174" y="656.7847064751826"/>
                <omgdi:waypoint x="303.0" y="489.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_22" id="BPMNEdge__22" sourceElement="_6" targetElement="_11">
                <omgdi:waypoint x="257.0" y="635.4545454545455"/>
                <omgdi:waypoint x="257.0" y="659.0"/>
                <omgdi:waypoint x="257.0" y="730.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="259.5" y="700.59"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_44" id="BPMNEdge__44" sourceElement="_11" targetElement="_17">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="397.0" y="570.7067271352985"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_43" id="BPMNEdge__43" sourceElement="_29" targetElement="_4">
                <omgdi:waypoint x="414.2152935248174" y="656.7847064751826"/>
                <omgdi:waypoint x="309.0" y="302.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_24" id="BPMNEdge__24" sourceElement="_11" targetElement="_3">
                <omgdi:waypoint x="237.0" y="742.0"/>
                <omgdi:waypoint x="80.0" y="405.0"/>
                <omgdi:waypoint x="215.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="49.0" y="455.96"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_46" id="BPMNEdge__46" sourceElement="_11" targetElement="_27">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="395.0" y="395.66666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_23" id="BPMNEdge__23" sourceElement="_11" targetElement="_55">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape bpmnElement="_55" id="Shape-_55">
                <omgdc:Bounds height="55.0" width="85.0" x="220.0" y="635.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="_56" id="Shape-_56" isMarkerVisible="false">
                <omgdc:Bounds height="32.0" width="32.0" x="237.0" y="726.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="_57" id="BPMNEdge__57" sourceElement="_56" targetElement="_9">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_58" id="BPMNEdge__58" sourceElement="_55" targetElement="_56">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_59" id="BPMNEdge__59" sourceElement="_56" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_60" id="BPMNEdge__60" sourceElement="_56" targetElement="_3">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_61" id="BPMNEdge__61" sourceElement="_56" targetElement="_17">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_62" id="BPMNEdge__62" sourceElement="_56" targetElement="_5">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_63" id="BPMNEdge__63" sourceElement="_56" targetElement="_27">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_64" id="BPMNEdge__64" sourceElement="_56" targetElement="_4">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_65" id="BPMNEdge__65" sourceElement="_56" targetElement="_6">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_48" id="BPMNEdge__48" sourceElement="_56" targetElement="_21">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_49" id="BPMNEdge__49" sourceElement="_3" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_50" id="BPMNEdge__50" sourceElement="_8" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_51" id="BPMNEdge__51" sourceElement="_36" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_52" id="BPMNEdge__52" sourceElement="_10" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_53" id="BPMNEdge__53" sourceElement="_29" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_54" id="BPMNEdge__54" sourceElement="_11" targetElement="_7">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="368.0" y="746.9046978667511"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="_45" id="BPMNEdge__45" sourceElement="_11" targetElement="_5">
                <omgdi:waypoint x="253.0" y="726.0"/>
                <omgdi:waypoint x="253.0" y="517.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_25" id="BPMNEdge__25" sourceElement="_9" targetElement="_21">
                <omgdi:waypoint x="453.0" y="746.9046978667511"/>
                <omgdi:waypoint x="524.0" y="746.7030577822226"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="485.6" y="736.99"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_47" id="BPMNEdge__47" sourceElement="_11" targetElement="_4">
                <omgdi:waypoint x="253.0" y="726.0"/>
                <omgdi:waypoint x="253.0" y="330.0"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_31" id="BPMNEdge__31" sourceElement="_29" targetElement="_6">
                <omgdi:waypoint x="414.2152935248174" y="656.7847064751826"/>
                <omgdi:waypoint x="305.0" y="662.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="80.0" x="319.38" y="651.87"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_30" id="BPMNEdge__30" sourceElement="_17" targetElement="_29">
                <omgdi:waypoint x="429.54598135550515" y="598.0"/>
                <omgdi:waypoint x="429.54598135550515" y="641.4540186444949"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="6.0" x="431.55" y="610.06"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_33" id="BPMNEdge__33" sourceElement="_10" targetElement="_27">
                <omgdi:waypoint x="269.0" y="742.0"/>
                <omgdi:waypoint x="395.0" y="395.66666666666663"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="_32" id="BPMNEdge__32" sourceElement="_29" targetElement="_3">
                <omgdi:waypoint x="445.7847064751826" y="656.7847064751826"/>
                <omgdi:waypoint x="567.0" y="417.0"/>
                <omgdi:waypoint x="300.0" y="162.5"/>
                <bpmndi:BPMNLabel>
                    <omgdc:Bounds height="19.84" width="80.0" x="527.4" y="324.46"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>
