package com.datatech.slgzt.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

/**
 * IPv6功能测试类
 */
public class IpUtilsIPv6Test {

    @Test
    public void testIsIPv6() {
        // 测试有效的IPv6地址
        assertTrue(IpUtils.isIPv6("2001:db8::1"));
        assertTrue(IpUtils.isIPv6("::1"));
        assertTrue(IpUtils.isIPv6("2001:db8:85a3::8a2e:370:7334"));
        assertTrue(IpUtils.isIPv6("::"));
        assertTrue(IpUtils.isIPv6("2001:db8:85a3:0:0:8a2e:370:7334"));
        
        // 测试无效的IPv6地址
        assertFalse(IpUtils.isIPv6("***********"));
        assertFalse(IpUtils.isIPv6("invalid"));
        assertFalse(IpUtils.isIPv6(null));
        assertFalse(IpUtils.isIPv6(""));
    }

    @Test
    public void testIsIPv6Cidr() {
        // 测试有效的IPv6 CIDR
        assertTrue(IpUtils.isIPv6Cidr("2001:db8::/32"));
        assertTrue(IpUtils.isIPv6Cidr("::1/128"));
        assertTrue(IpUtils.isIPv6Cidr("2001:db8:85a3::/48"));
        
        // 测试无效的IPv6 CIDR
        assertFalse(IpUtils.isIPv6Cidr("***********/24"));
        assertFalse(IpUtils.isIPv6Cidr("2001:db8::/129")); // 前缀长度超出范围
        assertFalse(IpUtils.isIPv6Cidr("invalid/32"));
        assertFalse(IpUtils.isIPv6Cidr(null));
    }

    @Test
    public void testIpv6ToBigInteger() {
        // 测试IPv6地址转换为BigInteger
        BigInteger result = IpUtils.ipv6ToBigInteger("::1");
        assertEquals(BigInteger.ONE, result);
        
        // 测试无效地址
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.ipv6ToBigInteger("***********");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.ipv6ToBigInteger("invalid");
        });
    }

    @Test
    public void testBigIntegerToIpv6() {
        // 测试BigInteger转换为IPv6地址
        String result = IpUtils.bigIntegerToIpv6(BigInteger.ONE);
        assertEquals("::1", result);
        
        // 测试无效数值
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.bigIntegerToIpv6(BigInteger.valueOf(-1));
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.bigIntegerToIpv6(null);
        });
    }

    @Test
    public void testParseIpv6Cidr() {
        // 测试解析IPv6 CIDR
        String[] result = IpUtils.parseIpv6Cidr("2001:db8::/32");
        assertArrayEquals(new String[]{"2001:db8::", "32"}, result);
        
        // 测试无效CIDR
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.parseIpv6Cidr("***********/24");
        });
    }

    @Test
    public void testGetIpv6NetworkAddress() {
        // 测试获取IPv6网络地址
        String result = IpUtils.getIpv6NetworkAddress("2001:db8:85a3::8a2e:370:7334/48");
        assertEquals("2001:db8:85a3::", result);
        
        String result2 = IpUtils.getIpv6NetworkAddress("2001:db8::1/64");
        assertEquals("2001:db8::", result2);
    }

    @Test
    public void testIsIpv6InCidr() {
        // 测试IPv6地址是否在CIDR范围内
        assertTrue(IpUtils.isIpv6InCidr("2001:db8::1", "2001:db8::/32"));
        assertTrue(IpUtils.isIpv6InCidr("2001:db8:85a3::1", "2001:db8::/16"));
        assertFalse(IpUtils.isIpv6InCidr("2002:db8::1", "2001:db8::/32"));
        
        // 测试无效输入
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.isIpv6InCidr("***********", "2001:db8::/32");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.isIpv6InCidr("2001:db8::1", "***********/24");
        });
    }

    @Test
    public void testGenerateAvailableIpv6s() {
        // 测试生成可用IPv6地址
        List<String> result = IpUtils.generateAvailableIpv6s("2001:db8::/126", null, null, 3);
        assertEquals(3, result.size());
        
        // 验证生成的IP都在网段内
        for (String ip : result) {
            assertTrue(IpUtils.isIpv6InCidr(ip, "2001:db8::/126"));
        }
        
        // 测试带过滤IP的情况
        List<String> filterIps = Arrays.asList("2001:db8::1");
        List<String> result2 = IpUtils.generateAvailableIpv6s("2001:db8::/126", null, filterIps, 2);
        assertEquals(2, result2.size());
        assertFalse(result2.contains("2001:db8::1"));
    }

    @Test
    public void testGetIpVersion() {
        // 测试IP版本检测
        assertEquals(4, IpUtils.getIpVersion("***********"));
        assertEquals(6, IpUtils.getIpVersion("2001:db8::1"));
        assertEquals(0, IpUtils.getIpVersion("invalid"));
        assertEquals(0, IpUtils.getIpVersion(null));
    }

    @Test
    public void testGetCidrVersion() {
        // 测试CIDR版本检测
        assertEquals(4, IpUtils.getCidrVersion("***********/24"));
        assertEquals(6, IpUtils.getCidrVersion("2001:db8::/32"));
        assertEquals(0, IpUtils.getCidrVersion("invalid/24"));
        assertEquals(0, IpUtils.getCidrVersion(null));
    }

    @Test
    public void testIsIpInCidrAuto() {
        // 测试自动检测IP版本的CIDR判断
        assertTrue(IpUtils.isIpInCidrAuto("*************", "***********/24"));
        assertTrue(IpUtils.isIpInCidrAuto("2001:db8::1", "2001:db8::/32"));
        
        // 测试版本不匹配的情况
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.isIpInCidrAuto("***********", "2001:db8::/32");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.isIpInCidrAuto("2001:db8::1", "***********/24");
        });
    }

    @Test
    public void testGenerateAvailableIpsAuto() {
        // 测试自动检测CIDR版本的IP生成
        List<String> ipv4Result = IpUtils.generateAvailableIpsAuto("***********/30", null, null, 2);
        assertEquals(2, ipv4Result.size());
        
        List<String> ipv6Result = IpUtils.generateAvailableIpsAuto("2001:db8::/126", null, null, 2);
        assertEquals(2, ipv6Result.size());
        
        // 测试无效CIDR
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.generateAvailableIpsAuto("invalid/24", null, null, 2);
        });
    }

    @Test
    public void testGetNetworkAddressAuto() {
        // 测试自动检测CIDR版本的网络地址获取
        assertEquals("***********", IpUtils.getNetworkAddressAuto("*************/24"));
        assertEquals("2001:db8::", IpUtils.getNetworkAddressAuto("2001:db8::1/32"));
        
        // 测试无效CIDR
        assertThrows(IllegalArgumentException.class, () -> {
            IpUtils.getNetworkAddressAuto("invalid/24");
        });
    }
}
