package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

/**
 * 挂载数据盘的模型
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月05日 19:51:48
 */
@Data
public class EvsModel extends BaseProductModel {


    /**
     * 是否挂载数据盘
     */
    private Boolean mountDataDisk;

    /**
     * 如果mountDataDisk为true，则需要填写挂载数据盘的列表
     * 挂载数据盘的列表
     */
    private Integer sysDiskSize;

    private String sysDiskType;

    private String sysDiskName;

    //开通数量
    private Integer openNum;

    private String productType;

    /**
     * 申请时长
     */
    private String applyTime;


    /**
     * 挂载云主机id
     */
    private String vmId;

    /**
     * vmName
     */
    private String vmName;


    /**
     * 云类型
     */
    private String catalogueDomainCode;


    /**
     * 云平台id
     */
    private String domainCode;

}
