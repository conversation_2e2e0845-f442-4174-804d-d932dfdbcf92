package com.datatech.slgzt.dao.container;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.container.XieYunUserMapper;
import com.datatech.slgzt.dao.model.container.XieYunUserDO;
import com.datatech.slgzt.enums.DeleteEnum;
import com.datatech.slgzt.model.query.container.XieYunUserQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: liupeihan
 * @Date: 2025/4/14
 */

@Repository
public class XieYunUserDAO {

    @Resource
    private XieYunUserMapper mapper;

    public void insert(XieYunUserDO userDO) {
        mapper.insert(userDO);
    }

    public XieYunUserDO getById(String userId) {
        return mapper.selectById(userId);
    }

    public void update(XieYunUserDO userDO) {
        mapper.updateById(userDO);
    }

    public void updateDeleteByUserId(XieYunUserDO userDO, String userId) {
        UpdateWrapper<XieYunUserDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("XIE_YUN_USER_ID", userId);
        mapper.update(userDO, updateWrapper);
    }

    public List<XieYunUserDO> list(XieYunUserQuery query) {
        return mapper.selectList(Wrappers.<XieYunUserDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getName()), XieYunUserDO::getName, query.getName())
                .eq(ObjNullUtils.isNotNull(query.getUsername()), XieYunUserDO::getUsername, query.getUsername())
                .eq(ObjNullUtils.isNotNull(query.getMobile()), XieYunUserDO::getMobile, query.getMobile())
                .eq(ObjNullUtils.isNotNull(query.getEmail()), XieYunUserDO::getEmail, query.getEmail())
        );
    }
}

