package com.datatech.slgzt.model.recovery;

import com.alibaba.fastjson.annotation.JSONField;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-12 16:13
 **/
@Data
public class RecoveryBldRedisModel extends BaseReconveryProductModel {

    /**
     * redis实例名称
     */
    private String name;

    /**
     * redis实例ip
     */
    private String ip;

    /**
     * CPU架构
     */
    private String cpuArchitecture;

    /**
     * 业务系统名称
     */
    private String businessSysName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;
}
