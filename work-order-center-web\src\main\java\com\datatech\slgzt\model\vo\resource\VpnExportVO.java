package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: vpn导出
 * @author: LK
 * @create: 2025-06-12 09:40
 **/
@Data
public class VpnExportVO {

    @ExcelExportHeader(value = "VPN名称")
    private String deviceName;

    @ExcelExportHeader(value = "最大客户端数")
    private String spec;

    @ExcelExportHeader(value = "vpc")
    private String vpcName;

    @ExcelExportHeader(value = "子网")
    private String subnetName;

    @ExcelExportHeader(value = "公网IP")
    private String eip;

    @ExcelExportHeader(value = "带宽")
    private String bandWidth;

    @ExcelExportHeader(value = "申请时长")
    private String applyTime;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private String resourceApplyTime;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;

    @ExcelExportHeader(value = "计费号")
    private String billId;

    @ExcelExportHeader(value = "状态")
    private String deviceStatus;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;
}
