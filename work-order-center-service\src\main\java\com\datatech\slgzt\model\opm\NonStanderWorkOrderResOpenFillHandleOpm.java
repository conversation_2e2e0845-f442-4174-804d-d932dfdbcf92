package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 17:28:23
 */
@Data
public class NonStanderWorkOrderResOpenFillHandleOpm {


   private NonStanderWorkOrderProductDTO dto;

   private NonStanderWorkOrderDTO orderDTO;
    /**
     * 绑定的多平面模型
     */
    private List<PlaneNetworkModel> planeNetworkModelList;


    /**
     * azCode
     */
    private String azCode;

    /**
     * azId
     */
    private Long azId;

    /**
     * azName
     */
    private String azName;

    //模版编号 用于规格 可能会空
    private String templateCode;

}
