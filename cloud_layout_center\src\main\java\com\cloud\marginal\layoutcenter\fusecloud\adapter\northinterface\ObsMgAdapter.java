package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ObsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.mapper.layout.ResourceDetailMapper;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.DeleteObsVo;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.utils.UuidUtil;
import com.cloud.resource.api.obs.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * obs适配管理
 */
@Component
@Slf4j
public class ObsMgAdapter extends BaseNorthInterfaceAdapter {

    @Resource
    private ResourceDetailMapper detailMapper;

    /**
     * 创建obs
     */
    public TaskVO createObs(String taskId, Integer taskSource){
        log.info("createObs start");
        CreateObsRcDto obsDTO = generateCreateObsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateObs(),
                null,
                obsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("createObs url is:{}",northInterfaceAddress.getCreateObs());
        log.info("createObs params is:{}",JSONObject.toJSON(obsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"create obs");
        return tasksVOResult.getEntity();
    }

    /**
     * obs配额变更
     */
    public TaskVO modifyObsQuota(String taskId, Integer taskSource){
        log.info("modifyObsQuota start");
        ModifyObsQuotaRcDto obsDTO = generateModifyObsQuotaDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getModifyObsQuota(),
                null,
                obsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("modifyObsQuota url is:{}",northInterfaceAddress.getModifyObsQuota());
        log.info("modifyObsQuota params is:{}",JSONObject.toJSON(obsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"modify obs quota");
        return tasksVOResult.getEntity();
    }

    /**
     * obs删除
     */
    public TaskVO deleteObs(String taskId, Integer taskSource){
        log.info("deleteObs start");
        DeleteObsRcDto obsDTO = generateDeleteRdsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteObs(),
                null,
                obsDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("deleteObs url is:{}",northInterfaceAddress.getDeleteObs());
        log.info("deleteObs params is:{}",JSONObject.toJSON(obsDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"delete obs");
        return tasksVOResult.getEntity();
    }

    /**
     * obs删除（自己调用）
     */
    @Async
    public TaskVO deleteObsV2(String obsId){
        log.info("deleteObs start");
        DeleteObsVo obsVo = detailMapper.selectObsIdByObsId(obsId);
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteObs(),
                null,
                obsVo,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("deleteObs url is:{}",northInterfaceAddress.getDeleteObs());
        log.info("deleteObs params is:{}",JSONObject.toJSON(obsVo));
        checkResultThrowExceptionIfFail(tasksVOResult,"delete obs");
        return tasksVOResult.getEntity();
    }

    /**
     * 删除桶
     */
    public TaskVO deleteBucket(String taskId, Integer taskSource) {
        log.info("deleteBucket start");
        DeleteObsBucketRcDto bucketRcDto = generateDeleteBucketDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteBucket(),
                null,
                bucketRcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("deleteBucket url is:{}",northInterfaceAddress.getDeleteBucket());
        log.info("deleteBucket params is:{}",JSONObject.toJSON(bucketRcDto));
        checkResultThrowExceptionIfFail(tasksVOResult,"delete obs");
        return tasksVOResult.getEntity();
    }


    private CreateObsRcDto generateCreateObsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam obsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.OBS_CREATE.getCode());
        ObsParam obsParam = JSONObject.parseObject(obsOrder.getAttrs(), ObsParam.class);
        ObsParam.ObsBucket createBucket = obsParam.getCreateBucket();
        CreateObsRcDto obsDTO = new CreateObsRcDto();
        BeanUtils.copyProperties(obsParam,obsDTO);
        obsDTO.setOrderId(layoutOrderParam.getSubOrderId());
        obsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        obsDTO.setBillId(layoutOrderParam.getAccount());
        obsDTO.setGroupId(layoutOrderParam.getCustomId());
        obsDTO.setGlobalId(obsParam.getGId());
        obsDTO.setBillingPlan(obsParam.getBillingPlan());

        ObsBucketDto obsBucketDto = new ObsBucketDto();
        if(createBucket != null){
            BeanUtils.copyProperties(createBucket,obsBucketDto);
            obsDTO.setCreateBucket(obsBucketDto);
        }

        return obsDTO;
    }


    private ModifyObsQuotaRcDto generateModifyObsQuotaDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam obsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.OBS_MODIFY_QUOTA.getCode());
        ObsParam obsParam = JSONObject.parseObject(obsOrder.getAttrs(), ObsParam.class);
        ModifyObsQuotaRcDto obsDTO = new ModifyObsQuotaRcDto();
        obsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        obsDTO.setBillId(layoutOrderParam.getAccount());
        obsDTO.setGroupId(layoutOrderParam.getCustomId());
        obsDTO.setOrderId(layoutOrderParam.getSubOrderId());
        obsDTO.setInstanceId(obsParam.getGId());
        obsDTO.setQuota(obsParam.getQuota());
        return obsDTO;
    }
    private DeleteObsRcDto generateDeleteRdsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam obsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.OBS_DELETE.getCode());
        ObsParam obsParam = JSONObject.parseObject(obsOrder.getAttrs(), ObsParam.class);
        DeleteObsRcDto obsDTO = new DeleteObsRcDto();
        obsDTO.setRegionCode(layoutOrderParam.getRegionCode());
        obsDTO.setBillId(layoutOrderParam.getAccount());
        obsDTO.setGroupId(layoutOrderParam.getCustomId());
        obsDTO.setInstanceId(obsParam.getGId());
        return obsDTO;
    }

    private DeleteObsBucketRcDto generateDeleteBucketDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam obsOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.BUCKET_DELETE.getCode());
        JSONObject jsonObject = JSONObject.parseObject(obsOrder.getAttrs());
        DeleteObsBucketRcDto bucketRcDto = new DeleteObsBucketRcDto();
        bucketRcDto.setRegionCode(layoutOrderParam.getRegionCode());
        bucketRcDto.setBillId(layoutOrderParam.getAccount());
        bucketRcDto.setGroupId(layoutOrderParam.getCustomId());
        bucketRcDto.setOptUuid(UuidUtil.generateId());
        bucketRcDto.setInstanceId(jsonObject.getString("resourceId"));
        return bucketRcDto;
    }
}
