package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.xieyun.XieyunOrgQuotaOpm;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/4/14
 */

@Data
@Accessors(chain = true)
public class XieYunOrgDTO {

    private String xieYunOrgId;

    private String code;

    private String name;

    private String clusterName;

    private String nodePoolName;

    private String description;

    private String xieYunUserId;

    /**
     * 谐云仓库id，修改时插入
     */
    private String xieYunRepoId;

    /**
     * 谐云制品id，修改时插入
     */
    private String xieYunRegistryId;

    private List<XieyunOrgQuotaOpm> xieYunOrgQuotaOpmList;

    /**
     * cpu值
     */
    private String cpuValue;

    /**
     * 内存值
     */
    private String memoryValue;

}

