package com.datatech.slgzt.enums.network;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: suxin
 * @Date: 2024/11/18
 * @Description: vpc参数校验枚举
 */

@Getter
@AllArgsConstructor
public enum VpcVerifyEnum {

    NAME_VERIFY("vpc名称"),
    NET_VERIFY("网段选择"),
    SUB_NET_VERIFY("子网掩码"),
    ;

    private final String type;

    public String setType() {
        return type;
    }
}
