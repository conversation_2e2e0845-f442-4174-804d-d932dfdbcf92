package com.datatech.slgzt.model.req.corporate;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CorporateOrderProductUpdateReq {

    @NotNull(message = "ID不能为空")
    private Long id;

    @NotBlank(message = "产品类型不能为空")
    private String productType;

    @NotBlank(message = "属性快照不能为空")
    private String propertySnapshot;

    private Long parentProductId;

    private String gid;

    private String ext;

    private Long subOrderId;

    private Long jobExecutionId;

    private String openStatus;

    private String message;
} 