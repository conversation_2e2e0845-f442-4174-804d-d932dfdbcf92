package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datatech.slgzt.dao.mapper.TenantReportMapper;
import com.datatech.slgzt.dao.model.TenantReportDO;
import com.datatech.slgzt.model.query.TenantReportQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月30日 15:20:20
 */
@Repository
public class TenantReportDAO extends ServiceImpl<TenantReportMapper, TenantReportDO> {



    //按照dateTime删除数据
    public void deleteByDataTime(String dateTime) {
        this.baseMapper.delete(Wrappers.<TenantReportDO>lambdaQuery()
                                       .eq(TenantReportDO::getDataTime, dateTime));
    }

    public List<TenantReportDO> list(TenantReportQuery query){
         return this.baseMapper.selectList(Wrappers.<TenantReportDO>lambdaQuery()
                .eq(TenantReportDO::getRegionId, query.getRegionId())
                .in(ObjNullUtils.isNotNull(query.getDomainCodeList()),TenantReportDO::getDomainCode, query.getDomainCodeList())
                .ge(TenantReportDO::getCreatedAt, query.getStartTime())
                .le(TenantReportDO::getCreatedAt, query.getEndTime()));
    }

}
