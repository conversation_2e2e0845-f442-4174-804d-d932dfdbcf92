package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: 宝兰德redis模型
 * @author: LK
 * @create: 2025-07-08 16:08
 **/
@Data
public class BldRedisModel extends BaseProductModel {

    private Integer openNum = 1;

    /**
     * redis实例名称
     */
    private String name;

    /**
     * redis实例ip
     */
    private String ip;

    /**
     * CPU架构
     */
    private String cpuArchitecture;

    /**
     * 业务系统名称
     */
    private String businessSysName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 下发实例的密码，随机生成的14位字符串
     */
    private String instancePassword;
}
