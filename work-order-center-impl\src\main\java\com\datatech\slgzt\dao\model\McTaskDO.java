package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月14日 17:15:23
 */
@Data
@TableName("MC_TASK_T")
public class McTaskDO {

    @TableField("ID")
    @TableId(value = "ID", type = IdType.ID_WORKER)
    private String id;

    @TableField("STATUS")
    private String status;

    @TableField("MESSAGE")
    private String message;

}
