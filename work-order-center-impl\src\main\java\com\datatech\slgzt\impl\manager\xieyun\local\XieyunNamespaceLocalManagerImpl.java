package com.datatech.slgzt.impl.manager.xieyun.local;

import com.datatech.slgzt.convert.XieYunBeanManagerConvert;
import com.datatech.slgzt.dao.container.XieYunNamespaceDAO;
import com.datatech.slgzt.dao.model.container.XieYunNamespaceDO;
import com.datatech.slgzt.manager.xieyun.local.XieyunNamespaceLocalManager;
import com.datatech.slgzt.model.dto.XieYunNamespaceDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 谐云本地命名空间处理
 *
 * @Author: liupeihan
 * @Date: 2025/4/15
 */

@Service
public class XieyunNamespaceLocalManagerImpl implements XieyunNamespaceLocalManager {

    @Resource
    private XieYunNamespaceDAO namespaceDAO;

    @Resource
    private XieYunBeanManagerConvert convert;

    @Override
    public void insert(XieYunNamespaceDTO namespaceDTO) {
        XieYunNamespaceDO namespaceDO = convert.namespaceDTO2DO(namespaceDTO);
        namespaceDO.setCreatedTime(LocalDateTime.now());
        namespaceDAO.insert(namespaceDO);
    }

    @Override
    public void updateByNamespaceId(XieYunNamespaceDTO namespaceDTO) {
        XieYunNamespaceDO namespaceDO = convert.namespaceDTO2DO(namespaceDTO);
        namespaceDO.setUpdatedTime(LocalDateTime.now());
        namespaceDAO.updateByNamespaceId(namespaceDO);
    }
}

