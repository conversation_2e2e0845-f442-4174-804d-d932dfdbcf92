package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.datatech.slgzt.model.req.corporate.CorporateOrderProductPageReq;
import com.datatech.slgzt.model.req.corporate.CorporateOrderProductCreateReq;
import com.datatech.slgzt.model.req.corporate.CorporateOrderProductUpdateReq;
import com.datatech.slgzt.model.vo.corporate.CorporateOrderProductVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CorporateOrderProductWebConvert {

    CorporateOrderProductQuery convert(CorporateOrderProductPageReq req);

    CorporateOrderProductDTO convert(CorporateOrderProductCreateReq req);

    CorporateOrderProductDTO convert(CorporateOrderProductUpdateReq req);

    CorporateOrderProductVO convert(CorporateOrderProductDTO dto);
} 