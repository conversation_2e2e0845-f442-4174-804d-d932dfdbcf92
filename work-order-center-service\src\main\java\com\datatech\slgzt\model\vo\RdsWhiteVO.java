package com.datatech.slgzt.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单VO
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Data
public class RdsWhiteVO {

    private Long id;

    /**
     * 数据库主键ID
     */
    private String rdsId;

    /**
     * 白名单名称
     */
    private String whiteName;

    /**
     * 白名单IP
     */
    private String ips;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 1有效 0删除
     */
    private Integer enabled;
} 