package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.VpnParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.resource.api.vpn.dto.CreateVpnRcDto;
import com.cloud.resource.api.vpn.dto.DeleteVpnRcDto;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * vpn适配管理
 */
@Component
@Slf4j
public class VpnMgAdapter extends BaseNorthInterfaceAdapter {
    /**
     * 创建vpn
     */
    public TaskVO createVpn(String taskId, Integer taskSource){
        log.info("createVpn start");
        CreateVpnRcDto vpnDTO = generateCreateVpnDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateVpn(),
                null,
                vpnDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("createVpn url is:{}",northInterfaceAddress.getCreateVpn());
        log.info("createVpn params is:{}",JSONObject.toJSON(vpnDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"create vpn");
        return tasksVOResult.getEntity();
    }

    /**
     * vpn删除
     */
    public TaskVO deleteVpn(String taskId, Integer taskSource){
        log.info("deleteVpn start");
        DeleteVpnRcDto vpnDTO = generateDeleteRdsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteVpn(),
                null,
                vpnDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("deleteVpn url is:{}",northInterfaceAddress.getDeleteVpn());
        log.info("deleteVpn params is:{}",JSONObject.toJSON(vpnDTO));
        checkResultThrowExceptionIfFail(tasksVOResult,"delete vpn");
        return tasksVOResult.getEntity();
    }




    private CreateVpnRcDto generateCreateVpnDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam vpnOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.VPN_CREATE.getCode());
        VpnParam vpnParam = JSONObject.parseObject(vpnOrder.getAttrs(), VpnParam.class);
        CreateVpnRcDto vpnDTO = new CreateVpnRcDto();
        BeanUtils.copyProperties(vpnParam,vpnDTO);
        vpnDTO.setOrderId(layoutOrderParam.getSubOrderId());
        vpnDTO.setRegionCode(layoutOrderParam.getRegionCode());
        vpnDTO.setBillId(layoutOrderParam.getAccount());
        vpnDTO.setGroupId(layoutOrderParam.getCustomId());
        vpnDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());

        CreateVpnRcDto.EipInfo publicIpInfo = new CreateVpnRcDto.EipInfo();
        BeanUtils.copyProperties(vpnParam.getPublicIpInfo(),publicIpInfo);
        vpnDTO.setPublicIpInfo(publicIpInfo);
        return vpnDTO;
    }

    private DeleteVpnRcDto generateDeleteRdsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam vpnOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.VPN_DELETE.getCode());
        VpnParam vpnParam = JSONObject.parseObject(vpnOrder.getAttrs(), VpnParam.class);
        DeleteVpnRcDto vpnDTO = new DeleteVpnRcDto();
        vpnDTO.setRegionCode(layoutOrderParam.getRegionCode());
        vpnDTO.setBillId(layoutOrderParam.getAccount());
        vpnDTO.setGroupId(layoutOrderParam.getCustomId());
        vpnDTO.setVpnId(vpnParam.getGId());
        return vpnDTO;
    }
}
