package com.datatech.slgzt.model.vo.recovery.export;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-06-06 16:53
 **/
@Data
public class BackupExportVO {

    @ExcelExportHeader(value = "策略名称")
    private String jobName;

    @ExcelExportHeader(value = "备份类型")
    private String backupType;

    @ExcelExportHeader(value = "备份频率")
    private String frequency;

    @ExcelExportHeader(value = "周数")
    private Integer daysOfWeek;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

}
