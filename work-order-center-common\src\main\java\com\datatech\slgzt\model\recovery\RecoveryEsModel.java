package com.datatech.slgzt.model.recovery;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: 裸金属回收模型
 * @author: LK
 * @create: 2025-06-30 15:02
 **/
@Data
public class RecoveryEsModel extends BaseReconveryProductModel {

    /**
     * 名称
     */
    private String name;

    /**
     * 日均增量数据
     */
    private String averageDailyIncrementData;

    /**
     * 保留时间（天）
     */
    private String retainTime;

    /**
     * 副本数量
     */
    private String numberOfReplicas;

    /**
     * 磁盘大小
     */
    private String diskSize;

    /**
     * 索引模板
     */
    private JSONObject indexTemplate;
}
