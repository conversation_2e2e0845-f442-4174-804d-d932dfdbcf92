package com.datatech.slgzt.controller.dag;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.nostander.BackupModel;
import com.datatech.slgzt.model.opm.DagOrderOpm;
import com.datatech.slgzt.model.query.AzQuery;
import com.datatech.slgzt.model.req.dag.DagOrderOpenReq;
import com.datatech.slgzt.model.req.res.OrderStatusNoticeReq;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.DagOrderService;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.datatech.slgzt.enums.SourceTypeEnum.DAG;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月03日 15:17:26
 */
@Slf4j
@RestController
@RequestMapping("/dagOrderResOpen")
public class DagOrderResOpenController {
    private static final String BATCH_RESTART_TOPIC = "woc_dag_batch_restart_topic_lj";

    @Resource
    private DagTemplateManager dagTemplateManager;

    @Resource
    private DagProductManager dagProductManager;

    @Resource
    private DagOrderService dagOrderService;


    @Resource
    private RegionManager regionManager;

    @Resource
    private AzManager azManager;

    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;
    @Resource
    private DagOrderManager dagOrderManager;
    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;


    @RequestMapping(value = "/open", method = RequestMethod.POST)
//    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> open(@RequestBody DagOrderOpenReq req) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "当前用户未登录");
        Precondition.checkArgument(req.getTemplateId(), "模板ID不能为空");
        Precondition.checkArgument(req.getRegionId(), "资源池ID不能为空");
        DagTemplateDTO dagTemplateDTO = dagTemplateManager.getById(req.getTemplateId());
        Precondition.checkArgument(dagTemplateDTO, "模板不存在");
        RegionDTO regionDTO = regionManager.getById(req.getRegionId());
        Precondition.checkArgument(regionDTO, "资源池不存在");
        List<AzDTO> azList = azManager.list(new AzQuery().setRegionId(regionDTO.getId()));
        Precondition.checkArgument(azList != null && !azList.isEmpty(), "资源池下没有可用的AZ");
        AzDTO azDTO = StreamUtils.findAny(azList);
        CmpAppDTO cmpAppDTO = businessService.getById(req.getBusinessSystemId());
        Precondition.checkArgument(cmpAppDTO, "业务系统不存在");
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Precondition.checkArgument(tenantDTO, "租户不存在");
//        CmpAppModuleDTO cmpAppModuleDTO = businessModuleManager.getById(req.getModuleId());
//        Precondition.checkArgument(cmpAppModuleDTO, "业务模块不存在");
        Precondition.checkArgument(req.getDomainCode(), "域编码不能为空");
        CatalogueDomain catalogueDomain = CatalogueDomain.getByCode(req.getDomainCode());
        Precondition.checkArgument(catalogueDomain != null, "域编码不存在");
        CatalogueDomain parentCode = catalogueDomain.getParent();
        Precondition.checkArgument(parentCode != null, "域编码不存在");
        DagOrderOpm dagOrderOpm = new DagOrderOpm();
        dagOrderOpm.setTemplateId(req.getTemplateId());
        dagOrderOpm.setRegionId(regionDTO.getId().toString());
        dagOrderOpm.setRegionCode(regionDTO.getCode());
        dagOrderOpm.setRegionName(regionDTO.getName());
        dagOrderOpm.setAzId(azDTO.getId().toString());
        dagOrderOpm.setAzCode(azDTO.getCode());
        dagOrderOpm.setAzName(azDTO.getName());
        dagOrderOpm.setBusinessSystemId(String.valueOf(req.getBusinessSystemId()));
        dagOrderOpm.setBusinessSystemName(cmpAppDTO.getSystemName());
        dagOrderOpm.setTenantId(tenantDTO.getId());
        dagOrderOpm.setTenantName(tenantDTO.getName());
        dagOrderOpm.setBillId(tenantDTO.getBillId());
        dagOrderOpm.setCustomNo(tenantDTO.getCustomNo());
        dagOrderOpm.setModuleId(req.getModuleId());
//        dagOrderOpm.setModuleName(cmpAppModuleDTO.getModuleName());
        dagOrderOpm.setDomainCode(req.getDomainCode());
        dagOrderOpm.setDomainName(catalogueDomain.getName());
        dagOrderOpm.setCatalogueDomainCode(parentCode.getCode());
        dagOrderOpm.setCatalogueDomainName(parentCode.getName());

        //---------------------产品对象---------------------------------------------
        dagOrderOpm.setVpcModelList(dagTemplateDTO.getVpcModelList());
        dagOrderOpm.setNetworkModelList(dagTemplateDTO.getNetworkModelList());
        dagOrderOpm.setEcsModelList(dagTemplateDTO.getEcsModelList());
        dagOrderOpm.setGcsModelList(dagTemplateDTO.getGcsModelList());
        dagOrderOpm.setSlbModelList(dagTemplateDTO.getSlbModelList());
        dagOrderOpm.setMysqlModelList(dagTemplateDTO.getMysqlModelList());
        dagOrderOpm.setRedisModelList(dagTemplateDTO.getRedisModelList());
        dagOrderOpm.setNatModelList(dagTemplateDTO.getNatModelList());
        dagOrderOpm.setEvsModelList(dagTemplateDTO.getEvsModelList());
        dagOrderOpm.setEipModelList(dagTemplateDTO.getEipModelList());
        dagOrderOpm.setObsModelList(dagTemplateDTO.getObsModelList());
        dagOrderOpm.setCloudPortModelList(dagTemplateDTO.getCloudPortModelList());
        dagOrderOpm.setCreatedBy(currentUser.getId());
        dagOrderOpm.setCreatedByName(currentUser.getUserName());
        dagOrderService.open(dagOrderOpm);
        return CommonResult.success(null);
    }

    /**
     * 回调接口
     * 工单中心->调用->编排中心
     * 编排中心->调用->工单中心 这一步的回调方法
     * 返回结果 包括所有产品 这边对应PRODUCT表
     * 改变状态和消息即可
     *
     * @param req
     * @return
     */
    @PostMapping("/layoutTaskNotify")
    public CommonResult<Void> layoutTaskNotify(@RequestBody @Validated OrderStatusNoticeReq req) {
        //订购类型
        log.info("编排回调 layoutTaskNotify start req:{}", JSONObject.toJSONString(req));
        String type = req.getType();
        //1是产品主任务的回调  2是子产品的回调
        Integer orderType = req.getOrderType();
        switch (OrderTypeEnum.getByCode(type)) {
            case SUBSCRIBE:
                subscribeLayout(orderType, req);
                break;
        }
        return CommonResult.build(1, 200, "回调成功", null);
    }

    /**
     * 资源开通回调
     */
    private void subscribeLayout(Integer orderType, OrderStatusNoticeReq req) {
        if (orderType == 1) {
            DagProductDTO productDTO = dagProductManager.getBySubOrderId(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(productDTO, "找不到对应产品");
            DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());
            Precondition.checkArgument(orderDTO, "找不到对应工单");
            //更新对应的状态
            productDTO.setMessage(req.getMessage());
            productDTO.setOpenStatus(ResOpenEnum.adaptTaskCenterResult(req.getHandleResult()).getCode());
            dagProductManager.update(productDTO);
//            obsOpenTaskManager.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
            // 如果成功，则重启流程，继续往下执行
            if (req.getHandleResult().equals(1)) {
                kafkaTemplate.send(BATCH_RESTART_TOPIC, orderDTO.getId(), KafkaMessage.of(new BatchRestartModel().setJobExecutionId(orderDTO.getJobExecutionId())
                        // 已更新product，不需要它更新
                        .setRestartOnly(true)));
            }
        }
        if (orderType == 2) {
            DagProductDTO productDTO = dagProductManager.getById(req.getOrderId());
            Precondition.checkArgument(productDTO, "找不到对应产品");
            DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());
            Precondition.checkArgument(orderDTO, "找不到对应工单");
            //备份策略需要取resourceId，不在主产品更新
            if (0 == productDTO.getParentProductId() && !ProductTypeEnum.BACKUP.getCode().equals(productDTO.getProductType())) {
                log.info("该产品是主产品，由主任务来更新状态");
                return;
            }
            //更新对应的状态
            productDTO.setMessage(req.getMessage());
            productDTO.setOpenStatus(ResOpenEnum.adaptTaskCenterResult(req.getHandleResult()).getCode());
            dagProductManager.update(productDTO);
            //如果是备份策略，构造资源入库
            if (ProductTypeEnum.BACKUP.getCode().equals(productDTO.getProductType()) && 1 == req.getHandleResult()) {
                saveBackupResource(productDTO, orderDTO, req.getResourceId());
                // 如果成功，则重启流程，继续往下执行
                if (req.getHandleResult().equals(1)) {
                    kafkaTemplate.send(BATCH_RESTART_TOPIC, orderDTO.getId(), KafkaMessage.of(new BatchRestartModel().setJobExecutionId(orderDTO.getJobExecutionId())
                            // 已更新product，不需要它更新
                            .setRestartOnly(true)));
                }
            }
        }
    }

    /**
     * 备份策略资源入库
     */
    private void saveBackupResource(DagProductDTO productDTO, DagOrderDTO orderDTO, String resourceId) {
        BackupModel backupModel = JSON.parseObject(productDTO.getPropertySnapshot(), BackupModel.class);
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setType(ProductTypeEnum.BACKUP.getCode());
        resourceDetailDTO.setOrderId(orderDTO.getId());
        resourceDetailDTO.setOrderCode(orderDTO.getOrderCode());
        resourceDetailDTO.setBillId(backupModel.getBillId());
        resourceDetailDTO.setDeviceId(resourceId);
        resourceDetailDTO.setDeviceName(backupModel.getJobName());
        resourceDetailDTO.setTenantId(backupModel.getTenantId());
        resourceDetailDTO.setTenantName(orderDTO.getTenantName());
        resourceDetailDTO.setBusinessSysId(backupModel.getBusinessSystemId());
        resourceDetailDTO.setBusinessSysName(backupModel.getBusinessSystemName());
        resourceDetailDTO.setDomainCode(backupModel.getDomainCode());
        resourceDetailDTO.setDomainName(backupModel.getDomainName());
        resourceDetailDTO.setApplyUserName(orderDTO.getCreator());
        resourceDetailDTO.setResourcePoolId(String.valueOf(backupModel.getRegionId()));
        resourceDetailDTO.setResourcePoolCode(backupModel.getRegionCode());
        resourceDetailDTO.setResourcePoolName(backupModel.getRegionName());
        resourceDetailDTO.setBackupType(backupModel.getBackupType());
        resourceDetailDTO.setFrequency(backupModel.getFrequency());
        resourceDetailDTO.setDaysOfWeek(backupModel.getDaysOfWeek());
        resourceDetailDTO.setCreateTime(LocalDateTime.now());
        resourceDetailDTO.setResourceApplyTime(orderDTO.getCreatedTime());
        resourceDetailDTO.setEffectiveTime(LocalDateTime.now());
        resourceDetailDTO.setSourceExtType(DAG.getPrefix());
        resourceDetailManager.batchSaveResourceDetail(ListUtil.toList(resourceDetailDTO));
    }

}
