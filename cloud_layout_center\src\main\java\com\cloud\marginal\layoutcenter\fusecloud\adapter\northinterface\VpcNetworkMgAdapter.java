package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.VpcParam;
import com.cloud.marginal.model.dto.edge.*;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * vpc网络适配管理
 */
@Component
@Slf4j
public class VpcNetworkMgAdapter extends BaseNorthInterfaceAdapter {

    /**
     * 创建vpc网络
     */
    public TaskVO createVpcNetwork(String taskId, Integer taskSource){
        log.info("createVpcNetwork start");
        CreateVpcAndSubnetDTO createVpcAndSubnetDTO = generateCreateVpcDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateVpc(),
                null,
                createVpcAndSubnetDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult,"create vpc");
        return tasksVOResult.getEntity();
    }

    /**
     * 生成北向接口创建VPC的参数
     * @param layoutParam 编排参数
     */
    private CreateVpcAndSubnetDTO generateCreateVpcDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam vpcOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.VPC_CREATE.getCode());
        VpcParam vpcParam = JSONObject.parseObject(vpcOrder.getAttrs(), VpcParam.class);

        CreateVpcAndSubnetDTO createVpcDTO = new CreateVpcAndSubnetDTO();
        createVpcDTO.setRegionCode(layoutOrderParam.getRegionCode());
        createVpcDTO.setTenantId(layoutOrderParam.getTenantId());
        createVpcDTO.setName(vpcParam.getVpcName());
        createVpcDTO.setgId(vpcParam.getgId());
        createVpcDTO.setIpv4Cidr(vpcParam.getCidr());
        createVpcDTO.setIpv6Cidr(vpcParam.getIpv6Cidr());
        createVpcDTO.setOrderId(vpcOrder.getProductOrderId());
        createVpcDTO.setDescription(vpcParam.getDescription());
        createVpcDTO.setOutInstanceId(vpcParam.getOutInstanceId());
        createVpcDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        List<SysCreateSubnetDTO> createSubnetList = new ArrayList<SysCreateSubnetDTO>();
        List<VpcParam.Subnet> subnets = null == vpcParam.getSubnets() ? Lists.newArrayList() : vpcParam.getSubnets();
        for (VpcParam.Subnet subnet : subnets) {
            SysCreateSubnetDTO createSubnetDTO = new SysCreateSubnetDTO();
            createSubnetDTO.setCidr(subnet.getCidr());
            createSubnetDTO.setAzCode(subnet.getAzCode());
            createSubnetDTO.setIpv6Cidr(subnet.getIpv6Cidr());
            //createSubnetDTO.setIpVersion(Long.valueOf(subnet.getIpVersion()));
            createSubnetDTO.setRegionCode(layoutOrderParam.getRegionCode());
            createSubnetDTO.setTenantId(layoutOrderParam.getTenantId());
            createSubnetDTO.setName(subnet.getSubnetName());
            createSubnetDTO.setGatewayIp(subnet.getGatewayIp());
            createSubnetDTO.setIpv6Enable(subnet.getIpv6Enable());
            createSubnetDTO.setEnableDhcp(subnet.getDhcpEnable());
            createSubnetDTO.setPrimaryDns(subnet.getPrimaryDns());
            createSubnetDTO.setSecondaryDns(subnet.getSecondaryDns());
            createSubnetDTO.setOrderId(vpcOrder.getProductOrderId());
            createSubnetDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
            createSubnetList.add(createSubnetDTO);
        }
        createVpcDTO.setSubnetDTOList(createSubnetList);
        return createVpcDTO;
    }

    /**
     * 删除vpc网络
     */
    public TaskVO deleteVpcNetwork(String taskId, Integer taskSource){
        log.info("deleteVpcNetwork start");
        DeleteVpcDTO deleteVpcDTO = generateDeleteVpcDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVOResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteVpc(),
                null,
                deleteVpcDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVOResult,"delete vpc");
        return tasksVOResult.getEntity();
    }

    private DeleteVpcDTO generateDeleteVpcDTO(LayoutParam layoutParam) {

        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam vpcOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.VPC_DELETE.getCode());
        VpcParam vpcParam = JSONObject.parseObject(vpcOrder.getAttrs(), VpcParam.class);

        DeleteVpcDTO deleteVpcDTO = new DeleteVpcDTO();
        deleteVpcDTO.setTenantId(layoutOrderParam.getTenantId());
        deleteVpcDTO.setVpcId(vpcParam.getResourceId());
        return deleteVpcDTO;
    }

    /**
     * vpc对等连接
     */
    public TaskVO vpcPeer(String mainTaskId){
        log.info("vpcPeer start");
        return null;
    }
}
