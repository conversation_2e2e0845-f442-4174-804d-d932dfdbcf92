package com.datatech.slgzt.model.business.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 业务系统模块表
 */
@Data
@Accessors(chain = true)
public class BusinessSystemModuleVO implements Serializable {

    private static final long serialVersionUID = 309821427937174487L;

    /**
     * 模板id
     */
    private Long id;

    /**
     * 系统编码
     */
    private Long systemId;

    /**
     * 系统名称
     */
    private String moduleName;

    /**
     * 业务系统名称
     */
    private String systemName;


}