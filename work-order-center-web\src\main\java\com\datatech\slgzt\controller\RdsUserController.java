package com.datatech.slgzt.controller;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.RdsUserWebConvert;
import com.datatech.slgzt.manager.RdsUserManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.RdsUserDTO;
import com.datatech.slgzt.model.dto.RdsUserOperateDTO;
import com.datatech.slgzt.model.query.RdsUserQuery;
import com.datatech.slgzt.model.req.rdsuser.*;
import com.datatech.slgzt.model.vo.RdsUserVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @program: workordercenterproject
 * @description: 数据库用户控制器
 * @author: LK
 * @create: 2025-06-25 16:17
 **/
@RestController
@RequestMapping("/rdsUser")
public class RdsUserController {

    @Resource
    private RdsUserManager rdsUserManager;

    @Resource
    private RdsUserWebConvert rdsUserWebConvert;

    @PostMapping("/page")
    public CommonResult<PageResult<RdsUserVO>> page(@RequestBody RdsUserPageReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageNum()), "页码不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPageSize()), "每页大小不能为空");
        
        RdsUserQuery query = rdsUserWebConvert.convert(req);
        PageResult<RdsUserDTO> page = rdsUserManager.page(query);
        return CommonResult.success(PageWarppers.box(page, rdsUserWebConvert::convert));
    }

    @PostMapping("/create")
    @OperationLog(description = "新增数据库用户", operationType = "CREATE")
    public CommonResult<Void> create(@RequestBody RdsUserCreateReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getRdsId()), "数据库ID不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getUserName()), "用户名称不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getPassword()), "密码不能为空");
        
        RdsUserDTO dto = rdsUserWebConvert.convert(req);
        rdsUserManager.add(dto);
        return CommonResult.success(null);
    }

    @PostMapping("/update")
    @OperationLog(description = "更新数据库用户", operationType = "UPDATE")
    public CommonResult<Void> update(@RequestBody RdsUserUpdateReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getId()), "ID不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getOldPassword()), "旧密码不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getNewPassword()), "新密码不能为空");

        RdsUserOperateDTO dto = rdsUserWebConvert.convert(req);
        rdsUserManager.update(dto);
        return CommonResult.success(null);
    }

    @PostMapping("/delete")
    @OperationLog(description = "删除数据库用户", operationType = "DELETE")
    public CommonResult<Void> delete(@RequestBody RdsUserDeleteReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getId()), "ID不能为空");
        
        rdsUserManager.delete(req.getId());
        return CommonResult.success(null);
    }

    @PostMapping("/detail")
    @OperationLog(description = "查询数据库用户详情", operationType = "QUERY")
    public CommonResult<RdsUserVO> detail(@RequestBody RdsUserDetailReq req) {
        // 参数校验
        Precondition.checkArgument(ObjNullUtils.isNotNull(req.getId()), "ID不能为空");
        
        RdsUserDTO dto = rdsUserManager.getById(req.getId());
        return CommonResult.success(rdsUserWebConvert.convert(dto));
    }
} 