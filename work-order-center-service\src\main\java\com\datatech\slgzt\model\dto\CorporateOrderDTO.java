package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月09日 10:26:37
 */
@Data
public class CorporateOrderDTO {

    private String id;

    private String orderCode;

    private BigDecimal orderAmount;

    //创建人
    private Long createBy;

    //创建人姓名
    private String createByName;

    //创建时间
    private LocalDateTime createTime;

    private Long jobExecutionId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;
}
