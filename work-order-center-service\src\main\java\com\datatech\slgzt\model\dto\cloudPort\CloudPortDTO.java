package com.datatech.slgzt.model.dto.cloudPort;

import com.datatech.slgzt.model.TaskStatusExt;
import lombok.Data;
import org.springframework.scheduling.support.SimpleTriggerContext;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云端口DTO
 */

@Data
public class CloudPortDTO implements Serializable {

    private String id;

    /**
     * 云区域编码
     */
    private String regionCode;



    /**
     * 云区域id
     */
    private String regionId;


    /**
     * 云平台编码
     */
    private String platformCode;

    /**
     * 云平台名称
     */
    private String platformName;


    /**
     * 云平台类型
     */
    private String catalogueDomainCode;


    /**
     * 云平台类型
     */
    private String catalogueDomainName;


    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 业务系统ID
     */
    private String businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;


    /**
     * 可用区编码
     */
    private String azCode;


    /**
     * 可用区 名称
     */
    private String azName;


    private String portId;

    /**
     * VPC ID
     */
    private String vpcId;
    /**
     * VPC名称
     */
    private String vpcName;

    /**
     * CM2端地址
     */
    private String peerIp;

    private String cloudPortName;

    private String resourceId;



    /**
     * 本端地址
     */
    private String srcIp;

    private String peerPassword;


    private String vlanId;

    private String status;

    private String peerAsNumber;

    private String remoteCidr;

    private String taskStatusExt;

    private LocalDateTime createTime;

}
