package com.datatech.slgzt.model.resourcce;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @program: workordercenterproject
 * @description: vpn概览
 * @author: LK
 * @create: 2025-06-10 15:37
 **/
@Data
@Accessors(chain = true)
public class ExtendVpnResource {

    /**
     * 资源开通数量
     */
    private Integer resourceNumbers = 0;

    /**
     * 带宽大小
     */
    private String bandWidthNumbers;

    /**
     * 最大连接数
     */
    private Integer maxConnectionNumbers = 0;

    private Integer bandWidthNumbersTemp = 0;


}
