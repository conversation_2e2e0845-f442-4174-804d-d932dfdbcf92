package com.datatech.slgzt.enums;

/**
 * 状态枚举值
 *
 * <AUTHOR>
 * @Date: 2024/11/18 15:40
 * @Description:
 */
public enum StatusEnum {

    NORMAL(1, "生效"),
    DELETE(0, "失效")
    ;

    private final Integer code;
    private final String message;

    StatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer code() {
        return code;
    }

    public String message() {
        return message;
    }
}
