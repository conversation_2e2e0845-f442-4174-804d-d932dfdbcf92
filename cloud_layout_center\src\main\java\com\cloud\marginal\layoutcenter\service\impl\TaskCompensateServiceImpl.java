package com.cloud.marginal.layoutcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cloud.marginal.enums.layout.StateEnum;
import com.cloud.marginal.enums.layout.StatusEnum;
import com.cloud.marginal.enums.layout.TaskTypeEnum;
import com.cloud.marginal.layoutcenter.service.LayoutService;
import com.cloud.marginal.layoutcenter.service.TaskCompensateService;
import com.cloud.marginal.mapper.layout.LayoutTaskMapper;
import com.cloud.marginal.mapper.layout.LayoutTaskNodeMapper;
import com.cloud.marginal.model.entity.layout.LayoutTask;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.util.ShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;

@Service
@Slf4j
public class TaskCompensateServiceImpl implements TaskCompensateService {

    @Resource
    private LayoutService layoutServiceImpl;

    @Resource
    private LayoutTaskMapper layoutTaskMapper;

    @Resource
    private LayoutTaskNodeMapper layoutTaskNodeMapper;

    @Autowired
    @Qualifier(value = "asyncServiceExecutor")
    private Executor asyncServiceExecutor;


    @Override
    public ReturnT<String> mainTaskCompensate() {
        log.info("master task layout compensate job start");
        // 定时任务分片参数
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        int currentNodeIndex = shardingVo.getIndex();
        int countOfNode = shardingVo.getTotal();
        // 获取所有未处理，或失败但重试次数>0的主任务
        List<LayoutTask> needCompensateMainTaskIdList = this.getNeedCompensateMainTaskIdList();
        if (!CollectionUtils.isEmpty(needCompensateMainTaskIdList)) {
            needCompensateMainTaskIdList.parallelStream().forEach(mainTask -> {
                if ((uuidToInt(mainTask.getId()) % countOfNode) == currentNodeIndex) {
                    asyncServiceExecutor.execute(() -> {
                        layoutServiceImpl.layoutTaskExecut(mainTask.getId());
                    });

                }
            });
        }
        log.info("master task layout compensate job end");
        return ReturnT.SUCCESS;
    }

    @Override
    public ReturnT<String> processingTaskCompensate() {
        log.info("processing task layout compensate job start");
        // 定时任务分片参数
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        int currentNodeIndex = shardingVo.getIndex();
        int countOfNode = shardingVo.getTotal();
        // 获取所有处理中，且5分钟以上未更新状态的任务
        List<LayoutTaskVO> needCompensateTaskList = layoutTaskMapper.getNeedCompensateTaskOfStateProcessing();
        log.info("processing task layout compensate job size" + needCompensateTaskList.size());
        if (!CollectionUtils.isEmpty(needCompensateTaskList)) {
            needCompensateTaskList.forEach(task -> {
                if ((uuidToInt(task.getId()) % countOfNode) == currentNodeIndex) {
                    modifyTaskUpdateTime(task);
                    StateEnum state = task.getState();
                    if (StateEnum.UNPROCESSED.getName().equals(state.toString())) {
                        layoutServiceImpl.layoutTaskExecut(task.getMasterTaskId());
                    } else {
                        layoutServiceImpl.processingTaskExecute(task);
                    }
                }
            });
        }
        log.info("processing task layout compensate job end");
        return ReturnT.SUCCESS;
    }

    @Override
    public ReturnT<String> executingTaskHandle() {
        log.info("executing task layout job start");
        // 定时任务分片参数
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        int currentNodeIndex = shardingVo.getIndex();
        int countOfNode = shardingVo.getTotal();
        // 获取所有执行中的子任务
        List<LayoutTaskVO> layoutTaskVOList = layoutTaskNodeMapper.getTasksOfStateExecuting(1);
        layoutTaskVOList.parallelStream().forEach(task -> {
            if ((uuidToInt(task.getId()) % countOfNode) == currentNodeIndex) {
                asyncServiceExecutor.execute(() -> {
                    layoutServiceImpl.taskMonitor(task);
                });
            }
        });
        log.info("executing task layout job end");
        return ReturnT.SUCCESS;
    }

    /**
     * 修改任务的修改时间，防止下次定时任务的超时判定不准确
     */
    private boolean modifyTaskUpdateTime(LayoutTaskVO layoutTaskVO) {
        Date date = new Date();
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("UPDATED_TIME", date);
        wrapper.set("REVISION", layoutTaskVO.getRevision() + 1);
        wrapper.eq("REVISION", layoutTaskVO.getRevision());
        wrapper.eq("ID", layoutTaskVO.getId());
        int updateResult = 0;
        if (layoutTaskVO.getTaskType().equals(TaskTypeEnum.MASTER.toString())) {
            updateResult = layoutTaskMapper.update(null, wrapper);
        } else {
            updateResult = layoutTaskNodeMapper.update(null, wrapper);
        }
        if (updateResult == 1) {
            layoutTaskVO.setRevision(layoutTaskVO.getRevision() + 1);
            layoutTaskVO.setUpdatedTime(date);
            return true;
        }
        return false;
    }

    /**
     * 将uuid转成int
     *
     * @param id uuid
     */
    private int uuidToInt(String id) {
        char[] chars = id.toCharArray();
        int intVal = 0;
        for (int i = 0; i < chars.length; i++) {
            intVal += chars[i];
        }
        return intVal ^ (intVal >>> 16);
    }

    /**
     * 获取需要补偿的主任务id集合
     *
     * @return 实体类只包含id
     */
    private List<LayoutTask> getNeedCompensateMainTaskIdList() {
        LambdaQueryWrapper<LayoutTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(LayoutTask::getId);
        wrapper.eq(LayoutTask::getStatus, StatusEnum.VALID.getCode())
                .and(w1 ->
                        w1.eq(LayoutTask::getState, StateEnum.UNPROCESSED)
                                .or(w2 ->
                                        w2.eq(LayoutTask::getState, StateEnum.ERROR)
                                                .gt(LayoutTask::getRetryCount, 0)
                                )

                );
        return layoutTaskMapper.selectList(wrapper);
    }

}
