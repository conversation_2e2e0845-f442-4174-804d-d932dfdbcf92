package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * flink模型
 * <AUTHOR>
 * @date 2025/6/23
 */
@Data
public class FlinkModel extends BaseProductModel {

    private String flinkName;

    /**
     * 开通数量
     */
    private Integer openNum = 1;

    /**
     * cpu核数
     */
    @JsonProperty("vCpus")
    private Integer vCpus;

    /**
     * 内存大小
     */
    private Integer ram;


    /**
     * 申请时长
     */
    private String applyTime;

}
