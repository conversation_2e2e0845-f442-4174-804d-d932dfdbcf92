package com.datatech.slgzt.dao.model.container;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@TableName("XIE_YUN_CLUSTER")
public class XieYunClusterDO {

    @TableField("ID")
    private String id;

    @TableField("CLUSTER_NAME")
    private String clusterName;

    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;

    /**
     * 1：删除,0：正常
     */
    @TableField("DELETED")
    private Boolean deleted;

}

