package com.datatech.slgzt.listener;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ChangeTypeEnum;
import com.datatech.slgzt.enums.ChangeTypeProductStatusEnum;
import com.datatech.slgzt.enums.ChangeTypeResourceDetailStatusEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.ChangeWorkOrderManager;
import com.datatech.slgzt.manager.ChangeWorkOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.change.ChangeBaseModel;
import com.datatech.slgzt.model.dto.ChangeWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.service.change.ChangeResourceService;
import com.datatech.slgzt.service.change.ChangeWorkOrderService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.Precondition;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("resourceChangeCreateListener")
public class ResourceChangeCreateListener implements TaskListener, InitializingBean {

    @Resource
    private ChangeWorkOrderService changeWorkOrderService;

    @Resource
    private ChangeWorkOrderManager changeWorkOrderManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private ChangeWorkOrderProductManager changeWorkOrderProductManager;

    @Autowired
    private List<ChangeResourceService> changeResourceServices;
    private final Map<String, ChangeResourceService> recoveryResourceMap = Maps.newHashMap();

    @Override
    public void notify(DelegateTask delegateTask) {
        String changeWorkOrderId = (String) delegateTask.getVariable("changeWorkOrderId");
        log.info("change start, changeWorkOrderId:{}", changeWorkOrderId);
        Precondition.checkArgument(changeWorkOrderId, "工单id不能为空");
        ChangeWorkOrderDTO recoveryWorkOrderDTO = changeWorkOrderManager.getById(changeWorkOrderId);
        Precondition.checkArgument(recoveryWorkOrderDTO, "工单不存在");
        //查询工单下的产品
        List<ChangeWorkOrderProductDTO> changeWorkOrderProductDTOS = changeWorkOrderProductManager.listByWorkOrderId(changeWorkOrderId);
        //找到对应的资源id
        changeWorkOrderProductDTOS.forEach(changeWorkOrderProductDTO -> {
            if (changeWorkOrderProductDTO.getParentProductId() > 0) {
                return;
            }
            try {
                change(changeWorkOrderProductDTO, recoveryWorkOrderDTO);
            } catch (Exception e) {
                ChangeWorkOrderProductDTO updateDto = new ChangeWorkOrderProductDTO();
                updateDto.setId(changeWorkOrderProductDTO.getId());
                updateDto.setChangeStatus(ChangeTypeProductStatusEnum.CHANGE_FAIL.getCode());
                updateDto.setMessage(e.getMessage());
                changeWorkOrderProductManager.update(updateDto);
                log.warn("任务失败, change product:{}, msg:{}", changeWorkOrderProductDTO, ExceptionUtils.getStackTrace(e));

            }
        });
    }

    public void change(ChangeWorkOrderProductDTO changeWorkOrderProductDTO, ChangeWorkOrderDTO recoveryWorkOrderDTO) {
        String resourceDetailId = changeWorkOrderProductDTO.getResourceDetailId();
        String propertySnapshot = changeWorkOrderProductDTO.getPropertySnapshot();
        ChangeBaseModel changeBaseModel = JSON.parseObject(propertySnapshot, ChangeBaseModel.class);
        ResourceDetailDTO detailDTO = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        if (CollectionUtils.isEmpty(changeWorkOrderProductDTO.getChangeType())) {
            log.warn("dirty data, change type is null or empty, change product:{}", changeWorkOrderProductDTO);
            return;
        }

        boolean timeChange = changeWorkOrderProductDTO.getChangeType().contains(ChangeTypeEnum.DELAY.getCode());
        //不是 只更新时间，就必然有属性更新
        boolean propertyChange = !(timeChange && changeWorkOrderProductDTO.getChangeType().size() == 1);

        if (propertyChange) {
            ChangeResourceService changeResourceService = recoveryResourceMap.get(detailDTO.getType());
            Precondition.checkArgument(changeResourceService, "找不到对应资源回收服务处理器");
            changeResourceService.changeResource(recoveryWorkOrderDTO, Collections.singletonList(changeWorkOrderProductDTO));
        }
        if (timeChange) {
            //按照时间更新
            ResourceDetailDTO updateDTO = new ResourceDetailDTO();
            updateDTO.setId(Long.valueOf(resourceDetailId));
            updateDTO.setExpireTime(DateUtils.processGoodsExpireTime(detailDTO.getExpireTime(), changeBaseModel.getChangeTime()));
            String eipId = detailDTO.getEipId();
            if (eipId != null && !detailDTO.getType().equals(ProductTypeEnum.EIP.getCode())) {
                ResourceDetailDTO eipDetail = resourceDetailManager.getByDeviceId(eipId);
                if (eipDetail != null) {
                    ResourceDetailDTO eipUpdateDTO = new ResourceDetailDTO();
                    eipUpdateDTO.setId(eipDetail.getId());
                    eipUpdateDTO.setExpireTime(DateUtils.processGoodsExpireTime(eipDetail.getExpireTime(), changeBaseModel.getChangeTime()));
                    resourceDetailManager.updateById(eipUpdateDTO);
                }
            }
            String volumeIds = detailDTO.getVolumeId();
            if (volumeIds != null && !detailDTO.getType().equals(ProductTypeEnum.EVS.getCode())) {
                for (String volumeId : detailDTO.getVolumeId().split(",")) {
                    ResourceDetailDTO evsDetail = resourceDetailManager.getByDeviceId(volumeId);
                    if (evsDetail != null) {
                        ResourceDetailDTO evsUpdateDTO = new ResourceDetailDTO();
                        evsUpdateDTO.setId(evsDetail.getId());
                        evsUpdateDTO.setExpireTime(DateUtils.processGoodsExpireTime(evsDetail.getExpireTime(), changeBaseModel.getChangeTime()));
                        resourceDetailManager.updateById(evsUpdateDTO);
                    }
                }
            }

            if (!propertyChange) {
//                updateDTO.setChangeStatus(ChangeTypeResourceDetailStatusEnum.UN_CHANGE.getType());
                updateDTO.setHisChangeOrderIds(StringUtils.isBlank(detailDTO.getHisChangeOrderIds()) ?
                        recoveryWorkOrderDTO.getId() :
                        String.format("%s,%s", detailDTO.getHisChangeOrderIds(), recoveryWorkOrderDTO.getId()));
                updateDTO.setHisChangeOrderCodes(StringUtils.isBlank(detailDTO.getHisChangeOrderCodes()) ?
                        recoveryWorkOrderDTO.getOrderCode() :
                        String.format("%s,%s", detailDTO.getHisChangeOrderCodes(), recoveryWorkOrderDTO.getOrderCode()));
            }
            resourceDetailManager.updateById(updateDTO);
        }
        // 改变任务状态
        if (timeChange && !propertyChange) {
            changeWorkOrderProductManager.updateStatusByIds(Collections.singletonList(changeWorkOrderProductDTO.getId()),
                    ChangeTypeProductStatusEnum.CHANGE_SUCCESS.getCode());
            if (ProductTypeEnum.ECS.getCode().equals(detailDTO.getType())
                    || ProductTypeEnum.GCS.getCode().equals(detailDTO.getType())
                    || ProductTypeEnum.MYSQL.getCode().equals(detailDTO.getType())
                    || ProductTypeEnum.REDIS.getCode().equals(detailDTO.getType())) {
                changeWorkOrderService.tryStartEcs(detailDTO);
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (ChangeResourceService resourceService : changeResourceServices) {
            recoveryResourceMap.put(resourceService.registerOpenService().getCode(), resourceService);
        }
    }
}