package com.datatech.slgzt.config;

import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.configuration.support.JobRegistryBeanPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月11日 10:51:10
 */
@Configuration
public class BatchJobRegistryConfig {

    @Resource
    private JobRegistry jobRegistry; // 注入Batch的注册中心

    @Bean
    public JobRegistryBeanPostProcessor jobRegister() {
        JobRegistryBeanPostProcessor processor = new JobRegistryBeanPostProcessor();
        processor.setJobRegistry(jobRegistry);
        return processor;
    }

}
