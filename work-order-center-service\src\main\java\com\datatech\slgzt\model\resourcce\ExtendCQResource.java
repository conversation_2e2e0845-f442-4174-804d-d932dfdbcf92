package com.datatech.slgzt.model.resourcce;

import lombok.Data;

/**
 * 容器资源配额扩展信息
 * <AUTHOR>
 * @description 容器资源配额概览显示
 * @date 2025年 01月01日 00:00:00
 */
@Data
public class ExtendCQResource {

    /**
     * 资源开通数量
     */
    private Integer resourceNumbers = 0;

    /**
     * CPU核心数
     */
    private String cpuNumbers;

    /**
     * 内存大小
     */
    private String memoryNumbers;

    /**
     * GPU显存大小
     */
    private String gpuMemoryNumbers;

    /**
     * 物理GPU卡数量临时计算值
     */
    private String gpuCoreNumbers;

    /**
     * 虚拟GPU卡数量临时计算值
     */
    private String gpuVirtualCoreNumbers;

    /**
     * CPU核心数临时计算值
     */
    private transient Integer cpuNumbersTmp = 0;

    /**
     * 内存大小临时计算值
     */
    private transient Integer memoryNumbersTmp = 0;

    /**
     * GPU显存大小临时计算值
     */
    private transient Integer gpuMemoryNumbersTmp = 0;

    /**
     * GPU算力大小临时计算值
     */
    private transient Integer gpuRatioNumbersTmp = 0;

    /**
     * 物理GPU卡数量临时计算值
     */
    private transient Integer gpuCoreNumbersTmp = 0;

    /**
     * 虚拟GPU卡数量临时计算值
     */
    private transient Integer gpuVirtualCoreNumbersTmp = 0;
}
