# IpUtils IPv6 支持文档

## 概述

IpUtils 工具类现在已经完全支持 IPv6 地址处理，包括地址验证、转换、CIDR 解析、网络计算和可用 IP 生成等功能。

## 新增功能

### 1. IPv6 地址检测

```java
// 检测是否为 IPv6 地址
boolean isIPv6 = IpUtils.isIPv6("2001:db8::1"); // true
boolean isIPv4 = IpUtils.isIPv6("***********"); // false

// 检测是否为 IPv6 CIDR
boolean isIPv6Cidr = IpUtils.isIPv6Cidr("2001:db8::/32"); // true
boolean isIPv4Cidr = IpUtils.isIPv6Cidr("***********/24"); // false
```

### 2. IPv6 地址转换

```java
// IPv6 地址转换为 BigInteger
BigInteger bigInt = IpUtils.ipv6ToBigInteger("2001:db8::1");

// BigInteger 转换为 IPv6 地址
String ipv6 = IpUtils.bigIntegerToIpv6(bigInt);
```

### 3. IPv6 CIDR 解析

```java
// 解析 IPv6 CIDR
String[] parts = IpUtils.parseIpv6Cidr("2001:db8::/32");
// parts[0] = "2001:db8::", parts[1] = "32"

// 获取 IPv6 网络地址
String networkAddr = IpUtils.getIpv6NetworkAddress("2001:db8:85a3::1/48");
// 结果: "2001:db8:85a3::"

// 获取 IPv6 最大地址
String maxAddr = IpUtils.getIpv6MaxAddress("2001:db8::/32");
// 结果: "2001:db8:ffff:ffff:ffff:ffff:ffff:ffff"
```

### 4. IPv6 地址范围检测

```java
// 检测 IPv6 地址是否在 CIDR 范围内
boolean inRange = IpUtils.isIpv6InCidr("2001:db8::1", "2001:db8::/32"); // true
```

### 5. IPv6 可用 IP 生成

```java
// 生成可用的 IPv6 地址
List<String> availableIps = IpUtils.generateAvailableIpv6s(
    "2001:db8::/126", 
    null,           // 已使用的 IP 列表
    null,           // 需要过滤的 IP 列表
    3               // 需要生成的数量
);
```

### 6. 通用方法（自动检测 IPv4/IPv6）

```java
// 自动检测 IP 版本
int version = IpUtils.getIpVersion("2001:db8::1"); // 返回 6
int version = IpUtils.getIpVersion("***********"); // 返回 4

// 自动检测 CIDR 版本
int cidrVersion = IpUtils.getCidrVersion("2001:db8::/32"); // 返回 6

// 自动检测并判断 IP 是否在 CIDR 范围内
boolean inRange = IpUtils.isIpInCidrAuto("2001:db8::1", "2001:db8::/32");

// 自动检测并生成可用 IP（支持 IPv4 和 IPv6）
List<String> ips = IpUtils.generateAvailableIpsAuto("2001:db8::/126", null, null, 2);

// 自动检测并获取网络地址
String networkAddr = IpUtils.getNetworkAddressAuto("2001:db8::1/32");
```

## 更新的现有方法

### generateAvailableIps 方法

现在 `generateAvailableIps` 方法已经支持自动检测 IPv4 和 IPv6：

```java
// 自动检测 IPv4
List<String> ipv4Ips = IpUtils.generateAvailableIps("***********/24", null, null, 5);

// 自动检测 IPv6
List<String> ipv6Ips = IpUtils.generateAvailableIps("2001:db8::/64", null, null, 5);
```

## 使用示例

### 完整的 IPv6 使用示例

```java
public class IPv6Example {
    public static void main(String[] args) {
        String ipv6Cidr = "2001:db8::/64";
        
        // 1. 验证 CIDR 格式
        if (IpUtils.isIPv6Cidr(ipv6Cidr)) {
            System.out.println("有效的 IPv6 CIDR: " + ipv6Cidr);
            
            // 2. 获取网络信息
            String networkAddr = IpUtils.getIpv6NetworkAddress(ipv6Cidr);
            String maxAddr = IpUtils.getIpv6MaxAddress(ipv6Cidr);
            System.out.println("网络地址: " + networkAddr);
            System.out.println("最大地址: " + maxAddr);
            
            // 3. 生成可用 IP
            List<String> availableIps = IpUtils.generateAvailableIps(ipv6Cidr, null, null, 5);
            System.out.println("可用 IP 地址:");
            availableIps.forEach(ip -> System.out.println("  " + ip));
            
            // 4. 检测 IP 是否在范围内
            String testIp = "2001:db8::1";
            boolean inRange = IpUtils.isIpInCidrAuto(testIp, ipv6Cidr);
            System.out.println(testIp + " 在范围内: " + inRange);
        }
    }
}
```

## 注意事项

1. **性能考虑**: IPv6 网段通常非常大，生成大量 IP 时请注意性能影响
2. **内存使用**: IPv6 地址使用 BigInteger 存储，比 IPv4 的 long 类型占用更多内存
3. **兼容性**: 所有新方法都向后兼容，现有的 IPv4 功能不受影响
4. **错误处理**: 所有方法都包含适当的参数验证和错误处理

## 支持的 IPv6 格式

- 完整格式: `2001:0db8:85a3:0000:0000:8a2e:0370:7334`
- 压缩格式: `2001:db8:85a3::8a2e:370:7334`
- 回环地址: `::1`
- 全零地址: `::`
- CIDR 格式: `2001:db8::/32` (前缀长度 0-128)

## 版本信息

- 添加时间: 2024年
- 兼容性: 完全向后兼容
- 依赖: 仅使用 Java 标准库，无额外依赖
