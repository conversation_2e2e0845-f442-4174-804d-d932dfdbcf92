package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.vo.device.DeviceCardMetricsVO;
import com.datatech.slgzt.model.vo.device.DevicePhysicalRunStatusVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Desc 显卡指标转换器
 * <AUTHOR>
 * @DATA 2025-06-12
 */
@Mapper(componentModel = "spring")
public interface DeviceCardMetricInfoWebConvert {

    DeviceCardMetricsVO convert(DeviceCardMetricsDTO req);


    List<DeviceCardMetricsVO> convert(List<DeviceCardMetricsDTO> list);




    List<DevicePhysicalRunStatusVO> convertVO(List<DeviceCardMetricsDTO> list);



}
