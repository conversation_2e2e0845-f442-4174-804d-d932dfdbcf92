package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.ExportTaskMapper;
import com.datatech.slgzt.dao.model.report.ExportTaskDO;
import com.datatech.slgzt.model.query.ExportTaskQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 导出任务DAO
 */
@Repository
public class ExportTaskDAO {

    @Resource
    private ExportTaskMapper exportTaskMapper;
    
    /**
     * 插入导出任务
     */
    public void insert(ExportTaskDO exportTaskDO) {
        exportTaskMapper.insert(exportTaskDO);
    }

    /**
     * 更新导出任务
     */
    public void update(ExportTaskDO exportTaskDO) {
        exportTaskMapper.updateById(exportTaskDO);
    }

    /**
     * 根据ID查询
     */
    public ExportTaskDO getById(String id) {
        return exportTaskMapper.selectById(id);
    }

    /**
     * 根据ID删除
     */
    public void deleteById(String id) {
        exportTaskMapper.deleteById(id);
    }

    /**
     * 列表查询
     */
    public List<ExportTaskDO> list(ExportTaskQuery query) {
        return exportTaskMapper.selectList(Wrappers.<ExportTaskDO>lambdaQuery()
                .like(ObjNullUtils.isNotNull(query.getCreator()), ExportTaskDO::getCreator, query.getCreator())
                .like(ObjNullUtils.isNotNull(query.getReportName()), ExportTaskDO::getReportName, query.getReportName())
                .eq(ObjNullUtils.isNotNull(query.getStatus()), ExportTaskDO::getStatus, query.getStatus())
                .eq(ObjNullUtils.isNotNull(query.getBusinessType()), ExportTaskDO::getBusinessType, query.getBusinessType())
                .ge(ObjNullUtils.isNotNull(query.getStartTime()), ExportTaskDO::getStartTime, query.getStartTime())
                .le(ObjNullUtils.isNotNull(query.getEndTime()), ExportTaskDO::getEndTime, query.getEndTime())
                .orderByDesc(ExportTaskDO::getCreateTime)
        );
    }
}