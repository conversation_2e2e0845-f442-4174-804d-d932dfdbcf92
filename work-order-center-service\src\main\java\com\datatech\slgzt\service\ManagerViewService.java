package com.datatech.slgzt.service;

import com.datatech.slgzt.model.CustomCountDTO;
import com.datatech.slgzt.model.dto.CustomDTO;
import com.datatech.slgzt.model.dto.CustomTenantDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import com.datatech.slgzt.model.query.CustomQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-01 16:03
 **/
public interface ManagerViewService {

    CustomCountDTO customCount(Long userId);

    List<CustomTenantDTO> customDetail(Long userId);

    List<VMResourcePerformanceDTO> selectTop5vCPUGroupByCustom(Long userId);

    List<CustomTenantDTO> totalProductTop5(Long userId);

    List<VMResourcePerformanceDTO> selectTop5MemGroupByCustom(Long userId);

    List<VMResourcePerformanceDTO> top5Resource(Long userId, String orderType);

    List<VMResourcePerformanceDTO> top5ResourceOfCustom(String customId, String orderType);

    CustomDTO getCustomDetail(String customId);

    PageResult<CustomDTO> page(CustomQuery customQuery);
}
