package com.datatech.slgzt.manager;


import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * 设备显卡基本信息
 */
public interface DeviceGpuInfoManager  {

    /**
     * 新增显卡基本信息
     */
    void create(DeviceGpuInfoDTO dto);

    List<String> groupModelName();

    /**
     * 更新显卡基本信息
     */
    void update(DeviceGpuInfoDTO dto);

    /**
     * 删除显卡基本信息
     */
    void delete(Long id);

    List<DeviceGpuInfoDTO> selectDeviceGpuInfoList(DeviceInfoQuery query);

    PageResult<DeviceGpuInfoDTO> queryDeviceGupInfoPage(DeviceInfoQuery query);

    DeviceGpuInfoDTO getByDeviceId(String deviceId);

    void updateLastByDeviceId(DeviceGpuInfoDTO dto);
}
