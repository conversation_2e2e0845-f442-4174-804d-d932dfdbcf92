package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.ExternalNetworkMapper;
import com.datatech.slgzt.dao.model.McExternalNetworkDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部网络DAO
 */
@Repository
public class McExternalNetworkDAO {

    @Resource
    private ExternalNetworkMapper externalNetworkMapper;


    public List<McExternalNetworkDO> list(Long regionId) {
        return externalNetworkMapper.selectList(Wrappers.<McExternalNetworkDO>lambdaQuery()
                                                        .eq(McExternalNetworkDO::getRegionId, regionId));
    }

} 