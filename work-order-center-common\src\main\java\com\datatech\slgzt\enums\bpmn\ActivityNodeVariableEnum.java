package com.datatech.slgzt.enums.bpmn;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/8
 * @Description:
 */

@Getter
@AllArgsConstructor
public enum ActivityNodeVariableEnum {

    NON_STANDARD_ABOVE_COST_NODE("cost", 1L, "高于成本"),
    NON_STANDARD_BELOW_COST_NODE("cost", 2L, "低于成本"),
    NON_STANDARD_OFFLINE_OPEN("openWay", 0L, "线下开通"),
    NON_STANDARD_NETWORK_OPEN("openWay", 1L, "网络开通"),
    ;


    private final String variableKey;

    private final Long variableValue;

    private final String remark;


}
