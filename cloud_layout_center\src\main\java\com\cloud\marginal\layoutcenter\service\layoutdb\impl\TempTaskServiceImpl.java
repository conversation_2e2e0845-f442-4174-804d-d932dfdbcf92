package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.ccmp.exceptioncenter.common.utils.UuidUtil;
import com.cloud.marginal.enums.layout.StatusEnum;
import com.cloud.marginal.enums.layout.TaskRelTypeEnum;
import com.cloud.marginal.layoutcenter.service.layoutdb.*;
import com.cloud.marginal.mapper.layout.TasksRelDefMapper;
import com.cloud.marginal.mapper.layout.TemplateTaskDefMapper;
import com.cloud.marginal.model.dto.layout.TempTaskOperationDto;
import com.cloud.marginal.model.dto.layout.TempTaskQueryDto;
import com.cloud.marginal.model.entity.layout.LayoutTaskDef;
import com.cloud.marginal.model.entity.layout.TaskApiDef;
import com.cloud.marginal.model.entity.layout.TasksRelDef;
import com.cloud.marginal.model.entity.layout.TemplateTaskDef;
import com.cloud.marginal.model.vo.layout.TempTaskDetailVO;
import com.cloud.marginal.utils.AssertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @Since 2023/5/16 16:07
 */
@Service
public class TempTaskServiceImpl implements TempTaskService {

    @Autowired
    private TemplateTaskDefService templateTaskDefService;

    @Autowired
    private LayoutTaskDefService layoutTaskDefService;

    @Resource
    private TemplateTaskDefMapper templateTaskDefMapper;

    @Autowired
    private TaskApiDefService taskApiDefService;

    @Resource
    private TasksRelDefMapper tasksRelDefMapper;

    @Autowired
    private TasksRelDefService tasksRelDefService;

    @Override
    @Transactional
    public void create(TempTaskOperationDto detail) {
        TemplateTaskDef templateTask = detail.getTemplateTask();

        // 校验重复项
        TemplateTaskDef templateTaskDef = templateTaskDefService.getSingle(templateTask.getTemplateId(), templateTask.getTaskId());
        if(templateTaskDef != null){
            throw new BusinessException("当前模板中已存在此任务，请勿重复关联");
        }
        // 插入templateTask
        templateTask.setId(UuidUtil.getUUID());
        templateTask.setRelRule("MULTIPLE");
        templateTask.setCreatedTime(new Date());
        templateTaskDefService.save(templateTask);

        // 插入taskApi
        TaskApiDef taskApi = detail.getTaskApi();
        taskApi.setId(UuidUtil.getUUID());
        taskApi.setCreatedTime(new Date());
        if(ObjectUtils.isEmpty(taskApi.getApiId())){
            taskApi.setApiId("default");
        }
        taskApiDefService.save(taskApi);

        // 插入主任务通知依赖
        insertNoticeRel(templateTask);

    }



    @Override
    @Transactional
    public void remove(String templateTaskId, String taskApiId) {
        TemplateTaskDef templateTask = templateTaskDefService.getById(templateTaskId);
        if(templateTask == null){
            throw new BusinessException("关联关系不存在！");
        }

        // 删除templateTask
        templateTaskDefService.delete(templateTaskId);

        // 删除taskApi
        taskApiDefService.delete(taskApiId);

        // 删除主任务通知依赖
        removeNoticeRel(templateTask);
    }

    @Override
    @Transactional
    public void update(TempTaskOperationDto detail) {
        TemplateTaskDef templateTask = detail.getTemplateTask();

        // 校验重复项
        TemplateTaskDef existTemplateTask = templateTaskDefService.getSingle(templateTask.getTemplateId(), templateTask.getTaskId());
        if(existTemplateTask != null && !templateTask.getId().equals(existTemplateTask.getId())){
            throw new BusinessException("当前模板中已存在此任务，请勿重复关联");
        }
        // 查询旧的模板
        TemplateTaskDef dbTemplateTask = templateTaskDefService.getById(templateTask.getId());

        // 更新templateTask
        templateTask.setRelRule("MULTIPLE");
        templateTask.setUpdatedTime(new Date());
        templateTaskDefService.updateById(templateTask);

        // 更新taskApi
        TaskApiDef taskApi = detail.getTaskApi();
        taskApi.setUpdatedTime(new Date());
        taskApiDefService.updateById(taskApi);

        // 删除主任务通知依赖
        updateNoticeRel(dbTemplateTask,templateTask);
    }

    private void updateNoticeRel(TemplateTaskDef dbTemplateTask, TemplateTaskDef templateTask) {
        if(!dbTemplateTask.getTaskId().equals(templateTask.getTaskId())){
            //  删除旧的依赖，添加新的依赖
            removeNoticeRel(dbTemplateTask);
            insertNoticeRel(templateTask);
        }
    }

    @Override
    @Transactional
    public Page<TempTaskDetailVO> getPage(TempTaskQueryDto query) {
        Page<TemplateTaskDef> page = new Page<>(query.getPageNum(), query.getPageSize());
        return templateTaskDefMapper.getPage(page,query.getTemplateId(),query.getTaskName());
    }

    private void insertNoticeRel(TemplateTaskDef templateTask) {
        LayoutTaskDef notice = layoutTaskDefService.getCode("MASK_NOTICE");
        LayoutTaskDef relTask = layoutTaskDefService.getById(templateTask.getTaskId());
        AssertUtil.notNull(relTask,"关联任务不能为空");
        TasksRelDef tasksRel = new TasksRelDef();
        tasksRel.setId(UuidUtil.getUUID());
        tasksRel.setTemplateId(templateTask.getTemplateId());
        tasksRel.setTaskId(notice.getId());
        tasksRel.setRelTaskId(templateTask.getTaskId());
        tasksRel.setDescription(notice.getDescription()+"执行依赖于"+relTask.getDescription()+"执行");
        tasksRel.setRelType(TaskRelTypeEnum.EXECUTE);
        tasksRel.setRelState(24);
        tasksRel.setCreatedTime(new Date());
        tasksRelDefMapper.insert(tasksRel);
    }

    private void removeNoticeRel(TemplateTaskDef temTask) {
        LayoutTaskDef notice = layoutTaskDefService.getCode("MASK_NOTICE");
        LambdaUpdateWrapper<TasksRelDef> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(TasksRelDef::getTemplateId,temTask.getTemplateId());
        wrapper.eq(TasksRelDef::getTaskId,notice.getId());
        wrapper.eq(TasksRelDef::getRelTaskId,temTask.getTaskId());
        wrapper.set(TasksRelDef::getStatus, StatusEnum.INVALID.getCode());
        tasksRelDefService.update(wrapper);
    }


}
