package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 虚拟机性能数据表
 * <AUTHOR>
 */
@Data
@TableName("WOC_VM_RESOURCE_PERFORMANCE")
public class VMResourcePerformanceDO {

    /**
     * resouce_detail表的id，主键
     */
    @TableId(type = IdType.NONE)
    @TableField("RESOURCE_DETAIL_ID")
    private Long resourceDetailId;

    /**
     * resouce_detail表的设备id
     */
    @TableField("DEVICE_ID")
    private String deviceId;
    /**
     * resouce_detail表的设备名称
     */
    @TableField("DEVICE_NAME")
    private String deviceName;

    /**
     * resouce_detail表的设备类型
     */
    @TableField("RESOURCE_DETAIL_TYPE")
    private String resourceDetailType;

    /**
     * resouce_detail表的云平台名称
     */
    @TableField("DOMAIN_NAME")
    private String domainName;

    /**
     * resouce_detail表的云平台编码
     */
    @TableField("DOMAIN_CODE")
    private String domainCode;

    /**
     * resouce_detail表的可用区名称
     */
    @TableField("AZ_NAME")
    private String azName;

    /**
     * resouce_detail表的可用区编码
     */
    @TableField("AZ_CODE")
    private String azCode;


    /**
     * resouce_detail表的资源池id
     */
    @TableField("RESOURCE_POOL_ID")
    private Long resourcePoolId;

    /**
     * resouce_detail表的资源池名称
     */
    @TableField("RESOURCE_POOL_NAME")
    private String resourcePoolName;

    /**
     * resouce_detail表的资源池编码
     */
    @TableField("RESOURCE_POOL_CODE")
    private String resourcePoolCode;

    /**
     * CMP_TENANT中租户id
     */
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * CMP_TENANT中的租户名称
     */
    @TableField("TENANT_NAME")
    private String tenantName;

    /**
     * CMP_TENANT中的STATUS，1-未删除
     */
    @TableField("TENANT_STATUS")
    private Integer tenantStatus;

    /**
     * CMP_TENANT中的TYPE
     */
    @TableField("TENANT_TYPE")
    private Integer tenantType;

    /**
     * CMP_TENANT中的客户id
     */
    @TableField("CUSTOM_ID")
    private String customId;

    /**
     * CCMP_CUSTOM中客户名称
     */
    @TableField("CUSTOM_NAME")
    private String customName;

    /**
     * CCMP_CUSTOM中客户创建人
     */
    @TableField("CUSTOM_CREATED_BY")
    private String customCreatedBy;

    /**
     * CCMP_CUSTOM中的status列，1-未删除
     */
    @TableField("CUSTOM_STATUS")
    private Integer customStatus;

    /**
     * MC_VM_T中INSTANCE_UUID
     */
    @TableField("VM_INSTANCE_UUID")
    private String vmInstanceUuid;

    /**
     * MC_VM_T中的deleted字段，1-未删除
     */
    @TableField("VM_DELETED")
    private Integer vmDeleted;

    /**
     * CK中云主机名称
     */
    @TableField("CK_HOST_NAME")
    private String ckHostName;

    /**
     * CK中云主机ip
     */
    @TableField("CK_IP")
    private String ckIp;

    /**
     * CK中云主机cpu利用率
     */
    @TableField("CK_CPU_UTIL")
    private BigDecimal ckCpuUtil;

    /**
     * CK中云主机内存利用率
     */
    @TableField("CK_MEM_UTIL")
    private BigDecimal ckMemUtil;

    /**
     * CK中云主机磁盘读IOPS
     */
    @TableField("CK_DISK_READ_IOPS")
    private BigDecimal ckDiskReadIops;

    /**
     * CK中云主机磁盘写IOPS
     */
    @TableField("CK_DISK_WRITE_IOPS")
    private BigDecimal ckDiskWriteIops;

    /**
     * CK中云主机容量
     */
    @TableField("CK_CAPACITY")
    private BigDecimal ckCapacity;

    /**
     * CK中云主机已用容量
     */
    @TableField("CK_CAPACITY_USED")
    private BigDecimal ckCapacityUsed;

    /**
     * CK中云主机容量利用率
     */
    @TableField("CK_CAPACITY_UTIL")
    private BigDecimal ckCapacityUtil;

    /**
     * CK中性能数据的最后更新时间
     */
    @TableField("CK_LASTED_TIME")
    private LocalDateTime ckLastedTime;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 状态 1.正常，其他-删除
     */
    @TableField("STATUS")
    @TableLogic(value = "1", delval = "0")
    private Integer status;

    @TableField(exist = false)
    private BigDecimal topPercent;
}
