package com.datatech.slgzt.convert;

import com.datatech.slgzt.enums.ApprovalTimeEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;

import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.model.req.container.ContainerQuotaPageReq;
import com.datatech.slgzt.model.vo.container.ContainerQuotaVO;
import com.datatech.slgzt.model.vo.container.ContainerQuotaExportVO;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface ContainerQuotaWebConvert {
    ContainerQuotaQuery convert(ContainerQuotaPageReq req);

    ContainerQuotaVO convert(ContainerQuotaDTO dto);

    /**
     * DTO转导出VO，需要自定义映射GPU卡数量
     */
    @Mapping(target = "gpuCardCount", expression = "java(calculateGpuCardCount(dto.getGpuCore(), dto.getGpuVirtualCore()))")
    @Mapping(target = "applyTime", source = "applyTime" ,qualifiedByName = "applyTime")
    ContainerQuotaExportVO convertToExportVO(ContainerQuotaDTO dto);

    /**
     * 计算GPU卡数量
     */
    default Integer calculateGpuCardCount(Integer gpuCore, Integer gpuVirtualCore) {
        int core = gpuCore != null ? gpuCore : 0;
        int virtualCore = gpuVirtualCore != null ? gpuVirtualCore : 0;
        return core + virtualCore;
    }

    @Named("applyTime")
    default String applyTime(String applyTime) {
        if (StringUtils.isNotBlank(applyTime) && applyTime.matches("\\d+")) {
            return applyTime + "天";
        }
        ApprovalTimeEnum approvalTimeEnum = ApprovalTimeEnum.getByCode(applyTime);
        if (approvalTimeEnum != null) {
            return approvalTimeEnum.getName();
        }
        return null;
    }
}
