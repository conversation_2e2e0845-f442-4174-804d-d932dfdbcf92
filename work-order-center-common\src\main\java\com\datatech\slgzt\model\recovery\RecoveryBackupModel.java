package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 云备份回收
 * @author: LK
 * @create: 2025-06-05 15:03
 **/
@Data
public class RecoveryBackupModel extends BaseReconveryProductModel {

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 策略名称
     */
    private String jobName;

    /**
     * 备份类型 ECS：云主机 EVS：云硬盘
     */
    private String backupType;

    /**
     * 备份频率 weeks/days
     * 若type=“days”时填写，表示每天都备份执行
     */
    private String frequency;

    /**
     * 周数
     * 若type=“weeks”时填写：每周几执行，1-7分别代表周一到周日
     */
    private Integer daysOfWeek;

    /**
     * 需要备份的云主机/云硬盘id
     */
    private List<String> objectIdList;
}
