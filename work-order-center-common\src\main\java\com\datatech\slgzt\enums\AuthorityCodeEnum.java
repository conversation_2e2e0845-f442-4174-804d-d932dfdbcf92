package com.datatech.slgzt.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date: 2024/11/18 15:32
 * @Description: 权限编码-权限菜单编码-枚举值
 */
public enum AuthorityCodeEnum {

    USER_TASK("user_task", "租户用户"),
    SCHEMA_ADMINISTRATOR("schema_administrator", "统一架构负责人"),
    PROFESSIONAL_GROUP("professional_group", "主机专业组"),
    TENANT_TASK("tenant_task", "租户确认"),
    BUSINESS_DEPART_LEADER("business_depart_leader", "二级业务部门领导"),
    BUSINESS_DEPART_LEADER2("business_depart_leader2", "三级业务部门领导"),
    BUSINESS2_DEPART_LEADER("business2_depart_leader", "三级业务部门领导"),
    CLOUD_LEADER("cloud_leader", "三级云资源部领导"),
    CLOUD_LEADER_2("cloud_leader_2", "二级云资源部领导"),

    ALARM_SUPPRESSION("alarm_suppression", "屏蔽告警"),
    SHUTDOWN("creator_and_shutdown", "云主机关机"),

    NETWORK_PROVISIONING("network_provisioning", "网络开通"),
    RESOURCE_CREATION("resource_creation", "资源开通"),
    ACCESS_TO_4A("access_to_4a", "4A接入"),
    CROSS_DIMENSION("cross_dimension", "交维入网"),
    NETWORK_MANAGER("network_manager", "网络管理"),

    RETREAT_DIMENSION("retreat_dimension", "交维清退"),
    RESOURCE_RECOVERY("resource_recovery", "资源回收"),
    RESOURCE_CHANGE("resource_change", "资源变更"),
    NETWORK_RECOVERY("network_recovery", "网络回收"),

    OPERATION_GROUP("operation_group", "运营组"),
    SPECIAL_GROUP("special_group", "专业组"),
    SYSTEM_ADMIN("system_admin", "系统管理员"),

    TENANT_ADMIN("tenant_admin", "租户管理员"),
    SUPER_ADMIN("super_admin", "超级管理员"),

    FREE_USER("free_user", "游离用户"),
    SYSTEM("system", "系统发起"),

    CUSTOMER_MANAGER("customer_manager", "客户经理"),
    RESPONSE_SCHEME_MANAGER("response_scheme_manager", "响应方案经理"),
    BRANCH_LEADER("branch_leader", "分公司领导"),
    PROVINCE_GOV_ADMIN("province_gov_admin", "省政企管理员"),
    PROVINCE_GOV_LEADER("province_gov_leader", "省政企领导"),
    CLOUD_RESOURCE_LEADER("cloud_resource_leader", "云资源部领导"),
    XIAOYUN("xiaoyun", "移动小芸"),
    ;

    private final String code;
    private final String message;

    AuthorityCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;

    }

    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static AuthorityCodeEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (AuthorityCodeEnum value : values()) {
                if (value.code().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }
}
