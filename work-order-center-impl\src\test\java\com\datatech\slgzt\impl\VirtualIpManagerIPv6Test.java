package com.datatech.slgzt.impl;

import com.datatech.slgzt.dao.VirtualIpDAO;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.model.VirtualIpDO;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.model.dto.network.AvailableIpDTO;
import com.datatech.slgzt.utils.IpUtils;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * VirtualIpManagerImpl IPv6功能完整测试
 * 使用JUnit 5、AssertJ和Mockito进行专业测试
 *
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("VirtualIpManagerImpl IPv6功能测试")
public class VirtualIpManagerIPv6Test {

    @Mock
    private NetworkSubnetOrderMapper networkSubnetOrderMapper;

    @Mock
    private VirtualIpDAO virtualIpDAO;

    @InjectMocks
    private VirtualIpManagerImpl virtualIpManager;

    private NetworkSubnetOrder ipv6Subnet;
    private NetworkSubnetOrder ipv4Subnet;

    @BeforeEach
    void setUp() {
        // 创建IPv6子网模拟对象
        ipv6Subnet = new NetworkSubnetOrder();
        ipv6Subnet.setId("subnet-ipv6-001");
        ipv6Subnet.setCidr("2001:db8::/64");
        ipv6Subnet.setIpVersion("IPv6");
        ipv6Subnet.setGateway("2001:db8::1");
        ipv6Subnet.setSubnetName("测试IPv6子网");

        // 创建IPv4子网模拟对象
        ipv4Subnet = new NetworkSubnetOrder();
        ipv4Subnet.setId("subnet-ipv4-001");
        ipv4Subnet.setCidr("***********/24");
        ipv4Subnet.setIpVersion("IPv4");
        ipv4Subnet.setGateway("***********");
        ipv4Subnet.setSubnetName("测试IPv4子网");
    }

    @Test
    @DisplayName("测试IPv6地址压缩功能")
    void testIPv6Compression() {
        // 测试用例：原始地址 -> 期望压缩结果
        String[][] testCases = {
                {"2001:db8:0:0:0:0:0:1", "2001:db8::1"},
                {"2001:0db8:0000:0000:0000:0000:0000:0001", "2001:db8::1"},
                {"2001:db8:85a3:0:0:8a2e:370:7334", "2001:db8:85a3::8a2e:370:7334"},
                {"0000:0000:0000:0000:0000:0000:0000:0001", "::1"},
                {"0000:0000:0000:0000:0000:0000:0000:0000", "::"},
                {"2001:db8:0:0:1:0:0:1", "2001:db8::1:0:0:1"}
        };

        for (String[] testCase : testCases) {
            String original = testCase[0];
            String expected = testCase[1];
            String compressed = IpUtils.compressIpv6Address(original);

            assertThat(compressed)
                    .as("IPv6地址压缩: %s 应该压缩为 %s", original, expected)
                    .isEqualTo(expected);
        }
    }

    @Test
    @DisplayName("测试IPv6 CIDR格式检测")
    void testIPv6CidrDetection() {
        // 测试用例：CIDR -> [是否为IPv6, 是否为IPv4, 期望版本号]
        Object[][] testCases = {
                {"2001:db8::/32", true, false, 6},
                {"::1/128", true, false, 6},
                {"2001:db8:85a3::/48", true, false, 6},
                {"fe80::/10", true, false, 6},
                {"***********/24", false, true, 4},
                {"10.0.0.0/8", false, true, 4},
                {"invalid/32", false, false, 0},
                {"2001:db8::/129", false, false, 0}, // 无效前缀长度
                {"", false, false, 0}
        };

        for (Object[] testCase : testCases) {
            String cidr = (String) testCase[0];
            boolean expectedIPv6 = (Boolean) testCase[1];
            boolean expectedIPv4 = (Boolean) testCase[2];
            int expectedVersion = (Integer) testCase[3];

            boolean isIPv6Cidr = IpUtils.isIPv6Cidr(cidr);
            boolean isIPv4Cidr = IpUtils.isIPv4Cidr(cidr);
            int version = IpUtils.getCidrVersion(cidr);

            assertThat(isIPv6Cidr)
                    .as("IPv6 CIDR检测失败: %s", cidr)
                    .isEqualTo(expectedIPv6);
            assertThat(isIPv4Cidr)
                    .as("IPv4 CIDR检测失败: %s", cidr)
                    .isEqualTo(expectedIPv4);
            assertThat(version)
                    .as("CIDR版本检测失败: %s", cidr)
                    .isEqualTo(expectedVersion);
        }
    }

    @Test
    @DisplayName("测试VirtualIpManager的IPv6可用IP获取功能")
    void testGetAvailableIpForIPv6() {
        // 准备测试数据 - 使用小网段便于测试
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/126"); // 改为小网段
        List<String> filterIps = Lists.newArrayList("2001:db8::4");
        Integer count = 2; // 减少数量
        List<String> usedIps = Lists.newArrayList("2001:db8::2");
        List<VirtualIpDO> virtualIpDOS = Lists.newArrayList(
                createVirtualIpDO("2001:db8::3")
        );

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(usedIps);
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(virtualIpDOS);

        // 执行测试
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIps, count);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIpVersion()).isEqualTo("IPv6");
        assertThat(result.getAvailableIps()).isNotNull();

        // 验证生成的IP都在网段内
        for (String ip : result.getAvailableIps()) {
            assertThat(IpUtils.isIpv6InCidr(ip, ipv6Subnet.getCidr()))
                    .as("生成的IP %s 应该在网段 %s 内", ip, ipv6Subnet.getCidr())
                    .isTrue();
        }

        // 验证生成的IP不在已使用列表中
        assertThat(result.getAvailableIps())
                .as("生成的IP不应该在已使用列表中")
                .doesNotContainAnyElementsOf(usedIps);

        // 验证生成的IP不在过滤列表中
        assertThat(result.getAvailableIps())
                .as("生成的IP不应该在过滤列表中")
                .doesNotContainAnyElementsOf(filterIps);

        // 验证生成的IP不是网关
        assertThat(result.getAvailableIps())
                .as("生成的IP不应该是网关地址")
                .doesNotContain(ipv6Subnet.getGateway());

        // 验证Mock调用
        verify(networkSubnetOrderMapper).selectById(subnetId);
        verify(virtualIpDAO).getSubnetId(subnetId);
        verify(virtualIpDAO).getIpListBySubnetId("device-001");
        verify(virtualIpDAO).listBySubnetId(subnetId);
    }

    @Test
    @DisplayName("测试IPv4向后兼容性")
    void testGetAvailableIpForIPv4Compatibility() {
        // 准备测试数据
        String subnetId = "subnet-ipv4-001";
        List<String> filterIps = Lists.newArrayList("***********00");
        Integer count = 3;
        List<String> usedIps = Lists.newArrayList("***********", "***********");

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv4Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-002");
        when(virtualIpDAO.getIpListBySubnetId("device-002")).thenReturn(usedIps);
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Collections.emptyList());

        // 执行测试
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIps, count);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIpVersion()).isEqualTo("IPv4");
        assertThat(result.getAvailableIps()).isNotEmpty();
        assertThat(result.getAvailableIps()).hasSize(count);

        // 验证生成的IP都在网段内
        for (String ip : result.getAvailableIps()) {
            assertThat(IpUtils.isIpInCidr(ip, ipv4Subnet.getCidr()))
                    .as("生成的IP %s 应该在网段 %s 内", ip, ipv4Subnet.getCidr())
                    .isTrue();
        }

        // 验证Mock调用
        verify(networkSubnetOrderMapper).selectById(subnetId);
        verify(virtualIpDAO).getSubnetId(subnetId);
        verify(virtualIpDAO).getIpListBySubnetId("device-002");
        verify(virtualIpDAO).listBySubnetId(subnetId);
    }

//    @Test
//    @DisplayName("测试getUnusedIpList的IPv6支持")
//    void testGetUnusedIpListForIPv6() {
//        // 准备测试数据
//        String subnetId = "subnet-ipv6-001";
//        String cidr = "2001:db8::/126"; // 小网段便于测试
//        String keyword = "::1";
//        List<String> usedIps = Lists.newArrayList("2001:db8::2");
//
//        // 配置Mock行为
//        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
//        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(usedIps);
//        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Collections.emptyList());
//
//        // 执行测试
//        List<String> result = virtualIpManager.getUnusedIpList(subnetId, cidr, keyword);
//
//        // 验证结果
//        assertThat(result).isNotNull();
//
//        // 验证包含关键字的IP
//        for (String ip : result) {
//            assertThat(ip).contains(keyword);
//            assertThat(IpUtils.isIpv6InCidr(ip, cidr))
//                .as("IP %s 应该在网段 %s 内", ip, cidr)
//                .isTrue();
//            assertThat(usedIps).doesNotContain(ip);
//        }
//
//        // 验证Mock调用
//        verify(virtualIpDAO).getSubnetId(subnetId);
//        verify(virtualIpDAO).getIpListBySubnetId("device-001");
//        verify(virtualIpDAO).listBySubnetId(subnetId);
//    }

    @Test
    @DisplayName("测试IPv6地址范围验证")
    void testIPv6AddressValidation() {

        // 测试用例：[IP地址, CIDR, 期望结果]
        Object[][] testCases = {
                {"2001:db8::1", "2001:db8::/32", true},
                {"2001:db8:85a3::1", "2001:db8::/16", true},
                {"2002:db8::1", "2001:db8::/32", false},
                {"::1", "::1/128", true},
                {"::2", "::1/128", false},
                {"fe80::1", "fe80::/10", true},
                {"2001:db8::ffff", "2001:db8::/32", true}
        };

        for (Object[] testCase : testCases) {
            String ipAddress = (String) testCase[0];
            String cidr = (String) testCase[1];
            boolean expected = (Boolean) testCase[2];

            try {
                boolean inRange = IpUtils.isIpv6InCidr(ipAddress, cidr);
                System.out.println(String.format("  %s 在 %s 范围内: %s (期望: %s) %s",
                        ipAddress, cidr, inRange, expected,
                        inRange == expected ? "✓" : "✗"));

                if (expected != inRange) {
                    throw new RuntimeException(String.format("IPv6地址范围验证失败: %s 在 %s 中", ipAddress, cidr));
                }
            } catch (Exception e) {
                throw new RuntimeException(String.format("IPv6地址范围验证异常: %s 在 %s 中 - %s",
                        ipAddress, cidr, e.getMessage()), e);
            }
        }
    }

    @Test
    @DisplayName("测试异常场景处理")
    void testExceptionScenarios() {
        // 测试子网不存在的情况
        String nonExistentSubnetId = "non-existent-subnet";
        when(networkSubnetOrderMapper.selectById(nonExistentSubnetId)).thenReturn(null);

        assertThatThrownBy(() -> virtualIpManager.getAvailableIp(nonExistentSubnetId, Lists.newArrayList(), 5))
                .isInstanceOf(Exception.class)
                .hasMessageContaining("子网不存在");
    }

    @Test
    @DisplayName("测试边界条件")
    void testBoundaryConditions() {
        // 测试请求数量为0
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/126");
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(Lists.newArrayList());
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, Lists.newArrayList(), 0);
        assertThat(result.getAvailableIps()).isEmpty();

        // 测试请求数量超过可用IP数量（/126网段只有4个可用IP）
        List<String> usedIps = Lists.newArrayList("2001:db8::1", "2001:db8::2", "2001:db8::3");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(usedIps);

        AvailableIpDTO result2 = virtualIpManager.getAvailableIp(subnetId, Lists.newArrayList(), 10);
        assertThat(result2.getAvailableIps()).hasSizeLessThanOrEqualTo(4); // 最多4个IP
    }

    @Test
    @DisplayName("测试性能保护机制")
    void testPerformanceProtection() {
        // 测试大网段的性能保护（/64网段有大量IP）
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/64"); // 大网段
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(Lists.newArrayList());
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 请求大量IP，应该有性能保护
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, Lists.newArrayList(), 1000);

        // 验证返回的IP数量合理（不会无限生成）
        assertThat(result.getAvailableIps()).hasSizeLessThanOrEqualTo(1000);
        assertThat(result.getAvailableIps()).isNotEmpty();
    }

    @Test
    @DisplayName("测试checkIpAvailable方法的IPv6支持")
    void testCheckIpAvailableForIPv6() {
        // 准备测试数据
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/64");
        List<String> usedIps = Lists.newArrayList("2001:db8::2", "2001:db8::3");

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(usedIps);
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 测试用例：[IP地址, 期望结果, 描述]
        Object[][] testCases = {
                {"2001:db8::100", true, "正常可用的IPv6地址"},
                {"2001:db8::1", false, "网关地址（应该不可用）"},
                {"2001:db8::2", false, "已使用的IP地址"},
                {"2002:db8::1", false, "不在网段范围内的地址"},
                {"***********", false, "IPv4地址（版本不匹配）"},
                {"invalid-ip", false, "无效的IP地址格式"}
        };

        for (Object[] testCase : testCases) {
            String testIp = (String) testCase[0];
            boolean expectedAvailable = (Boolean) testCase[1];
            String description = (String) testCase[2];

            // 通过getAvailableIp方法间接测试checkIpAvailable逻辑
            List<String> filterIps = Lists.newArrayList(testIp);
            AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIps, 5);

            if (expectedAvailable) {
                // 如果期望可用，验证它确实被过滤了（因为在filterIps中）
                assertThat(result.getAvailableIps())
                        .as("可用的IP %s 应该被正确过滤 - %s", testIp, description)
                        .doesNotContain(testIp);
            }
        }
    }

    @Test
    @DisplayName("测试混合IPv4/IPv6环境的兼容性")
    void testMixedIPv4IPv6Compatibility() {
        // 测试在同一个测试中处理IPv4和IPv6
        String ipv4SubnetId = "subnet-ipv4-001";
        String ipv6SubnetId = "subnet-ipv6-001";

        // 配置IPv4 Mock
        when(networkSubnetOrderMapper.selectById(ipv4SubnetId)).thenReturn(ipv4Subnet);
        when(virtualIpDAO.getSubnetId(ipv4SubnetId)).thenReturn("device-ipv4");
        when(virtualIpDAO.getIpListBySubnetId("device-ipv4")).thenReturn(Lists.newArrayList("***********"));
        when(virtualIpDAO.listBySubnetId(ipv4SubnetId)).thenReturn(Lists.newArrayList());

        // 配置IPv6 Mock
        ipv6Subnet.setCidr("2001:db8::/126");
        when(networkSubnetOrderMapper.selectById(ipv6SubnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(ipv6SubnetId)).thenReturn("device-ipv6");
        when(virtualIpDAO.getIpListBySubnetId("device-ipv6")).thenReturn(Lists.newArrayList("2001:db8::2"));
        when(virtualIpDAO.listBySubnetId(ipv6SubnetId)).thenReturn(Lists.newArrayList());

        // 测试IPv4
        AvailableIpDTO ipv4Result = virtualIpManager.getAvailableIp(ipv4SubnetId, Lists.newArrayList(), 2);
        assertThat(ipv4Result.getIpVersion()).isEqualTo("IPv4");
        assertThat(ipv4Result.getAvailableIps()).isNotEmpty();

        // 测试IPv6
        AvailableIpDTO ipv6Result = virtualIpManager.getAvailableIp(ipv6SubnetId, Lists.newArrayList(), 2);
        assertThat(ipv6Result.getIpVersion()).isEqualTo("IPv6");
        assertThat(ipv6Result.getAvailableIps()).isNotEmpty();

        // 验证生成的IP格式正确
        for (String ip : ipv4Result.getAvailableIps()) {
            assertThat(IpUtils.getIpVersion(ip)).isEqualTo(4);
        }

        for (String ip : ipv6Result.getAvailableIps()) {
            assertThat(IpUtils.getIpVersion(ip)).isEqualTo(6);
        }
    }

    @Test
    @DisplayName("测试多种filterIps场景")
    void testMultipleFilterIpsScenarios() {
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/124"); // 16个地址的网段

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(Lists.newArrayList());
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 场景1: 空filterIps列表
        AvailableIpDTO result1 = virtualIpManager.getAvailableIp(subnetId, Lists.newArrayList(), 5);
        assertThat(result1.getAvailableIps()).hasSize(5);

        // 场景2: 单个filterIp
        List<String> singleFilter = Lists.newArrayList("2001:db8::5");
        AvailableIpDTO result2 = virtualIpManager.getAvailableIp(subnetId, singleFilter, 5);
        assertThat(result2.getAvailableIps())
                .as("结果不应包含被过滤的IP")
                .doesNotContainAnyElementsOf(singleFilter);

        // 场景3: 多个filterIps
        List<String> multipleFilters = Lists.newArrayList(
                "2001:db8::2", "2001:db8::3", "2001:db8::4", "2001:db8::5"
        );
        AvailableIpDTO result3 = virtualIpManager.getAvailableIp(subnetId, multipleFilters, 5);
        assertThat(result3.getAvailableIps())
                .as("结果不应包含任何被过滤的IP")
                .doesNotContainAnyElementsOf(multipleFilters);

        // 场景4: filterIps包含网关地址
        List<String> filterWithGateway = Lists.newArrayList(
                ipv6Subnet.getGateway(), "2001:db8::6", "2001:db8::7"
        );
        AvailableIpDTO result4 = virtualIpManager.getAvailableIp(subnetId, filterWithGateway, 5);
        assertThat(result4.getAvailableIps())
                .as("结果不应包含网关和其他过滤IP")
                .doesNotContainAnyElementsOf(filterWithGateway);

        // 场景5: filterIps包含网段外的IP（应该被忽略）
        List<String> filterWithOutOfRange = Lists.newArrayList(
                "2001:db8::8", "2002:db8::1", "***********" // 包含IPv6网段外和IPv4地址
        );
        AvailableIpDTO result5 = virtualIpManager.getAvailableIp(subnetId, filterWithOutOfRange, 5);
        assertThat(result5.getAvailableIps())
                .as("结果不应包含网段内的过滤IP")
                .doesNotContain("2001:db8::8");
        // 网段外的IP不影响结果
        assertThat(result5.getAvailableIps()).hasSize(5);
    }

    @Test
    @DisplayName("测试filterIps与usedIps的交互")
    void testFilterIpsWithUsedIpsInteraction() {
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/125"); // 8个地址的网段

        List<String> usedIps = Lists.newArrayList("2001:db8::2", "2001:db8::3");
        List<String> filterIps = Lists.newArrayList("2001:db8::4", "2001:db8::5");

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(usedIps);
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 执行测试
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIps, 10);

        // 验证结果不包含已使用的IP
        assertThat(result.getAvailableIps())
                .as("结果不应包含已使用的IP")
                .doesNotContainAnyElementsOf(usedIps);

        // 验证结果不包含过滤的IP
        assertThat(result.getAvailableIps())
                .as("结果不应包含过滤的IP")
                .doesNotContainAnyElementsOf(filterIps);

        // 验证结果不包含网关
        assertThat(result.getAvailableIps())
                .as("结果不应包含网关地址")
                .doesNotContain(ipv6Subnet.getGateway());

        // 验证所有返回的IP都在网段内
        for (String ip : result.getAvailableIps()) {
            assertThat(IpUtils.isIpv6InCidr(ip, ipv6Subnet.getCidr()))
                    .as("IP %s 应该在网段 %s 内", ip, ipv6Subnet.getCidr())
                    .isTrue();
        }
    }

    @Test
    @DisplayName("测试filterIps包含重复IP的处理")
    void testFilterIpsWithDuplicates() {
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/126"); // 4个地址的小网段

        // filterIps包含重复的IP
        List<String> filterIpsWithDuplicates = Lists.newArrayList(
                "2001:db8::2", "2001:db8::2", "2001:db8::3", "2001:db8::3", "2001:db8::2"
        );

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(Lists.newArrayList());
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 执行测试
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIpsWithDuplicates, 5);

        // 验证重复的过滤IP被正确处理
        assertThat(result.getAvailableIps())
                .as("结果不应包含任何重复的过滤IP")
                .doesNotContain("2001:db8::2", "2001:db8::3");
    }

    @Test
    @DisplayName("测试filterIps为null或空的边界情况")
    void testFilterIpsNullAndEmpty() {
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/126");

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(Lists.newArrayList());
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 测试null filterIps
        AvailableIpDTO result1 = virtualIpManager.getAvailableIp(subnetId, null, 2);
        assertThat(result1.getAvailableIps()).hasSize(2);

        // 测试空filterIps
        AvailableIpDTO result2 = virtualIpManager.getAvailableIp(subnetId, Lists.newArrayList(), 2);
        assertThat(result2.getAvailableIps()).hasSize(2);

        // 两个结果应该相同（都没有过滤）
        assertThat(result1.getAvailableIps()).containsExactlyInAnyOrderElementsOf(result2.getAvailableIps());
    }

    @Test
    @DisplayName("测试filterIps包含压缩和非压缩IPv6地址")
    void testFilterIpsWithCompressedAndUncompressedIPv6() {
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/120"); // 256个地址的网段

        // filterIps包含压缩和非压缩格式的相同地址
        List<String> filterIps = Lists.newArrayList(
                "2001:db8::1",                              // 压缩格式
                "2001:0db8:0000:0000:0000:0000:0000:0001",  // 非压缩格式（相同地址）
                "2001:db8::2",                              // 另一个压缩格式
                "2001:db8:0:0:0:0:0:3"                      // 部分压缩格式
        );

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(Lists.newArrayList());
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 执行测试
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIps, 10);

        // 验证所有格式的相同地址都被过滤
        assertThat(result.getAvailableIps())
                .as("结果不应包含任何格式的过滤IP")
                .doesNotContain(
                        "2001:db8::1",
                        "2001:0db8:0000:0000:0000:0000:0000:0001",
                        "2001:db8::2",
                        "2001:db8:0:0:0:0:0:3",
                        "2001:db8::3"
                );
    }

    @Test
    @DisplayName("测试filterIps在小网段中的完全过滤场景")
    void testFilterIpsCompleteFiltering() {
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/127"); // 只有2个地址的极小网段

        // 过滤掉除网关外的所有可用IP
        List<String> filterIps = Lists.newArrayList("2001:db8::1"); // 最大地址
        List<String> usedIps = Lists.newArrayList(); // 没有已使用的IP

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(usedIps);
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(Lists.newArrayList());

        // 执行测试
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIps, 5);

        // 验证在极小网段中，过滤后可能没有可用IP
        assertThat(result.getAvailableIps())
                .as("在极小网段中过滤后可能没有可用IP")
                .hasSizeLessThanOrEqualTo(1); // 最多1个IP（排除网络地址、网关、过滤IP）
    }

    @Test
    @DisplayName("测试filterIps与VirtualIpDO列表的交互")
    void testFilterIpsWithVirtualIpDOInteraction() {
        String subnetId = "subnet-ipv6-001";
        ipv6Subnet.setCidr("2001:db8::/124"); // 16个地址的网段

        List<String> filterIps = Lists.newArrayList("2001:db8::5", "2001:db8::6");
        List<String> usedIps = Lists.newArrayList("2001:db8::2");
        List<VirtualIpDO> virtualIpDOS = Lists.newArrayList(
                createVirtualIpDO("2001:db8::3"),
                createVirtualIpDO("2001:db8::4")
        );

        // 配置Mock行为
        when(networkSubnetOrderMapper.selectById(subnetId)).thenReturn(ipv6Subnet);
        when(virtualIpDAO.getSubnetId(subnetId)).thenReturn("device-001");
        when(virtualIpDAO.getIpListBySubnetId("device-001")).thenReturn(usedIps);
        when(virtualIpDAO.listBySubnetId(subnetId)).thenReturn(virtualIpDOS);

        // 执行测试
        AvailableIpDTO result = virtualIpManager.getAvailableIp(subnetId, filterIps, 8);

        // 验证结果不包含filterIps
        assertThat(result.getAvailableIps())
                .as("结果不应包含过滤的IP")
                .doesNotContainAnyElementsOf(filterIps);

        // 验证结果不包含usedIps
        assertThat(result.getAvailableIps())
                .as("结果不应包含已使用的IP")
                .doesNotContainAnyElementsOf(usedIps);

        // 验证结果不包含VirtualIpDO中的IP
        List<String> virtualIpAddresses = virtualIpDOS.stream()
                .map(VirtualIpDO::getIpAddress)
                .collect(java.util.stream.Collectors.toList());
        assertThat(result.getAvailableIps())
                .as("结果不应包含VirtualIpDO中的IP")
                .doesNotContainAnyElementsOf(virtualIpAddresses);

        // 验证结果不包含网关
        assertThat(result.getAvailableIps())
                .as("结果不应包含网关地址")
                .doesNotContain(ipv6Subnet.getGateway());
    }

    @Test
    @DisplayName("测试通用IP方法的自动检测功能")
    void testUniversalIpMethods() {
        System.out.println("\n=== 测试通用IP方法的自动检测功能 ===");

        try {
            // 测试自动版本检测
            int version1 = IpUtils.getIpVersion("***********");
            int version2 = IpUtils.getIpVersion("2001:db8::1");
            int version3 = IpUtils.getIpVersion("invalid");

            System.out.println(String.format("  *********** 版本: %d (期望: 4) %s", version1, version1 == 4 ? "✓" : "✗"));
            System.out.println(String.format("  2001:db8::1 版本: %d (期望: 6) %s", version2, version2 == 6 ? "✓" : "✗"));
            System.out.println(String.format("  invalid 版本: %d (期望: 0) %s", version3, version3 == 0 ? "✓" : "✗"));

            if (version1 != 4) throw new RuntimeException("IPv4版本检测失败");
            if (version2 != 6) throw new RuntimeException("IPv6版本检测失败");
            if (version3 != 0) throw new RuntimeException("无效IP版本检测失败");

            // 测试自动范围检测
            boolean range1 = IpUtils.isIpInCidrAuto("***********00", "***********/24");
            boolean range2 = IpUtils.isIpInCidrAuto("2001:db8::1", "2001:db8::/32");

            System.out.println(String.format("  ***********00 在 ***********/24 内: %s", range1 ? "✓" : "✗"));
            System.out.println(String.format("  2001:db8::1 在 2001:db8::/32 内: %s", range2 ? "✓" : "✗"));

            if (!range1) throw new RuntimeException("IPv4自动范围检测失败");
            if (!range2) throw new RuntimeException("IPv6自动范围检测失败");

            // 测试自动IP生成
            List<String> ipv4Ips = IpUtils.generateAvailableIps("***********/30", null, null, 2);
            List<String> ipv6Ips = IpUtils.generateAvailableIps("2001:db8::/126", null, null, 2);

            if (ipv4Ips.isEmpty()) throw new RuntimeException("IPv4自动生成失败");
            if (ipv6Ips.isEmpty()) throw new RuntimeException("IPv6自动生成失败");

            System.out.println("  IPv4自动生成: " + ipv4Ips);
            System.out.println("  IPv6自动生成: " + ipv6Ips);

        } catch (Exception e) {
            throw new RuntimeException("通用IP方法测试失败: " + e.getMessage(), e);
        }
    }

    public void testVirtualIpManagerIPv6Simulation() {
        System.out.println("\n=== 模拟VirtualIpManagerImpl的IPv6功能 ===");

        // 模拟IPv6子网信息
        NetworkSubnetOrder ipv6Subnet = createMockIPv6Subnet();

        // 模拟已使用的IP列表
        List<String> usedIps = Lists.newArrayList("2001:db8::2", "2001:db8::3", "2001:db8::10");

        // 模拟需要过滤的IP列表（包括网关）
        List<String> filterIps = Lists.newArrayList("2001:db8::4", ipv6Subnet.getGateway());

        try {
            // 模拟getAvailableIp方法的核心逻辑
            String cidr = ipv6Subnet.getCidr();
            String gateway = ipv6Subnet.getGateway();
            String ipVersion = ipv6Subnet.getIpVersion();

            System.out.println(String.format("  子网信息: CIDR=%s, 网关=%s, IP版本=%s",
                    cidr, gateway, ipVersion));
            System.out.println(String.format("  已使用IP: %s", usedIps));
            System.out.println(String.format("  过滤IP: %s", filterIps));

            // 生成可用IP
            List<String> availableIps = IpUtils.generateAvailableIps(cidr, usedIps, filterIps, 5);

            System.out.println(String.format("  生成的可用IP (%d个):", availableIps.size()));
            for (String ip : availableIps) {
                System.out.println("    " + ip);

                // 验证生成的IP不在已使用列表中
                if (usedIps.contains(ip)) {
                    throw new RuntimeException(String.format("生成的IP %s 在已使用列表中", ip));
                }

                // 验证生成的IP不在过滤列表中
                if (filterIps.contains(ip)) {
                    throw new RuntimeException(String.format("生成的IP %s 在过滤列表中", ip));
                }

                // 验证生成的IP在网段范围内
                if (!IpUtils.isIpv6InCidr(ip, cidr)) {
                    throw new RuntimeException(String.format("生成的IP %s 不在网段 %s 内", ip, cidr));
                }
            }

            System.out.println("  VirtualIpManagerImpl IPv6功能模拟测试 ✓");

        } catch (Exception e) {
            throw new RuntimeException("VirtualIpManagerImpl IPv6功能模拟测试失败: " + e.getMessage(), e);
        }
    }

    public void testIPv6AddressAvailabilityCheck() {
        System.out.println("\n=== 测试IPv6地址可用性检查逻辑 ===");

        NetworkSubnetOrder ipv6Subnet = createMockIPv6Subnet();
        List<String> usedIps = Lists.newArrayList("2001:db8::2", "2001:db8::3");

        String cidr = ipv6Subnet.getCidr();

        // 测试用例：[IP地址, 期望结果, 描述]
        Object[][] testCases = {
                {"2001:db8::100", true, "正常可用的IPv6地址"},
                {"2001:db8::1", false, "网关地址（应该不可用）"},
                {"2001:db8::2", false, "已使用的IP地址"},
                {"2001:db8::", false, "网络地址（应该不可用）"},
                {"2002:db8::1", false, "不在网段范围内的地址"},
                {"***********", false, "IPv4地址（版本不匹配）"},
                {"invalid-ip", false, "无效的IP地址格式"}
        };

        System.out.println(String.format("  在子网 %s 中测试IP地址可用性:", cidr));

        for (Object[] testCase : testCases) {
            String testIp = (String) testCase[0];
            boolean expectedAvailable = (Boolean) testCase[1];
            String description = (String) testCase[2];

            boolean actualAvailable = checkIPv6AddressAvailability(testIp, ipv6Subnet, usedIps);

            System.out.println(String.format("    %s: %s (期望: %s) %s - %s",
                    testIp,
                    actualAvailable ? "可用" : "不可用",
                    expectedAvailable ? "可用" : "不可用",
                    actualAvailable == expectedAvailable ? "✓" : "✗",
                    description));

            if (expectedAvailable != actualAvailable) {
                throw new RuntimeException(String.format("IP地址可用性检查失败: %s - %s", testIp, description));
            }
        }
    }

    /**
     * 创建模拟的IPv6子网对象
     */
    private NetworkSubnetOrder createMockIPv6Subnet() {
        NetworkSubnetOrder subnet = new NetworkSubnetOrder();
        subnet.setId("subnet-ipv6-001");
        subnet.setCidr("2001:db8::/64");
        subnet.setIpVersion("IPv6");
        subnet.setGateway("2001:db8::1");
        subnet.setSubnetName("测试IPv6子网");
        return subnet;
    }

    /**
     * 模拟VirtualIpManagerImpl中checkIpAvailable方法的核心逻辑
     */
    private boolean checkIPv6AddressAvailability(String ipAddress, NetworkSubnetOrder subnet, List<String> usedIps) {
        try {
            String cidr = subnet.getCidr();
            String gateway = subnet.getGateway();
            String ipVersion = subnet.getIpVersion();

            // 检查IP地址格式
            int detectedVersion = IpUtils.getIpVersion(ipAddress);
            if (detectedVersion == 0) {
                return false; // 无效格式
            }

            // 检查IP版本是否匹配
            boolean isIpv6 = detectedVersion == 6;
            if (!isIpv6 || !"IPv6".equals(ipVersion)) {
                return false; // 版本不匹配
            }

            // 检查是否是网关地址
            if (ipAddress.equals(gateway)) {
                return false; // 网关地址不可用
            }

            // 检查是否是网络地址
            String networkAddress = IpUtils.getIpv6NetworkAddress(cidr);
            if (ipAddress.equals(networkAddress)) {
                return false; // 网络地址不可用
            }

            // 检查是否在网段范围内
            if (!IpUtils.isIpv6InCidr(ipAddress, cidr)) {
                return false; // 不在范围内
            }

            // 检查是否已被使用
            if (usedIps.contains(ipAddress)) {
                return false; // 已被使用
            }

            return true; // 可用

        } catch (Exception e) {
            return false; // 异常情况视为不可用
        }
    }

    /**
     * 创建VirtualIpDO测试对象
     */
    private VirtualIpDO createVirtualIpDO(String ipAddress) {
        VirtualIpDO virtualIpDO = new VirtualIpDO();
        virtualIpDO.setIpAddress(ipAddress);
        virtualIpDO.setSubnetId("subnet-001");
        return virtualIpDO;
    }
}
