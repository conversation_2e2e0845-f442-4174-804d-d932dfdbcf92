package com.datatech.slgzt.service.device;



import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 物理显卡信息
 */
public interface DeviceGupInfoService {

    /**
     * 获取显卡信息
     * @return
     */
    void syncRemoteDeviceDataInfo();


    List<DeviceGpuInfoDTO> queryLocalDeviceInfos(DeviceInfoQuery query);

    /**
     * 获取所有的指标信息
     * @return
     */
    List<DeviceCardMetricsDTO> queryDeviceCardMetrics();





}
