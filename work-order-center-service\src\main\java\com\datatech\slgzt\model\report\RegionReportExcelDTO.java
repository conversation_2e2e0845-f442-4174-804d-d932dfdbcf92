package com.datatech.slgzt.model.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月26日 09:09:56
 */
@Data
public class RegionReportExcelDTO {

    @ExcelIgnore
    private Long regionId;

    @ExcelIgnore
    private String regionCode;


    @ExcelProperty("数据时间")
    private String dataTime;

    @ExcelProperty("平台")
    private String platform;

    //资源池
    @ExcelProperty("资源池")
    private String regionName;

    //虚拟资源池名称
    @ExcelProperty("虚拟资源池名称")
    private String virtualRegionName;

    //计算服务器节点数
    @ExcelProperty("计算服务器节点数")
    private Integer computeNodeNum;

    //存储服务器节点数
    //@ExcelProperty("存储服务器节点数")
    @ExcelIgnore
    private Integer storageNodeNum;

    //虚拟机数量
    @ExcelProperty({"虚拟机数量"})
    private Integer vmNum;

    //vCPU总量
    @ExcelProperty({"虚拟资源总量","vCPU总量"})
    private BigDecimal vcpuTotal;

    //vCPU可用总量
    @ExcelProperty({"虚拟资源总量","vCPU可用总量"})
    private BigDecimal vcpuAvailable;

    //内存总量
    @ExcelProperty({"虚拟资源总量","内存总量(GB)"})
    private BigDecimal memoryTotal;

    //内存可用总量
    @ExcelProperty({"虚拟资源总量","内存可用总量(GB)"})
    private BigDecimal memoryAvailable;

    //存储总量
    @ExcelProperty({"虚拟资源总量","存储总量(GB)"})
    private BigDecimal storageTotal;


    //vCPU已分配
    @ExcelProperty({"虚拟资源已分配总量","vCPU已分配"})
    private BigDecimal vcpuAllocated;

    //内存已分配
    @ExcelProperty({"虚拟资源已分配总量","内存已分配(GB)"})
    private BigDecimal memoryAllocated;

    //存储已分配
    @ExcelProperty({"虚拟资源已分配总量","存储已分配(GB)"})
    private BigDecimal storageAllocated;

    //vCPU剩余
    @ExcelProperty({"虚拟剩余资源总量","vCPU剩余"})
    private BigDecimal vcpuRemaining;

    //内存剩余
    @ExcelProperty({"虚拟剩余资源总量","内存剩余(GB)"})
    private BigDecimal memoryRemaining;

    //存储剩余
    @ExcelProperty({"虚拟剩余资源总量","存储剩余(GB)"})
    private BigDecimal storageRemaining;

    //vCPU分配率
    @ExcelProperty({"虚拟资源已分配百分比","vCPU分配率"})
    private BigDecimal vcpuAllocationRate;

    //内存分配率
    @ExcelProperty({"虚拟资源已分配百分比","内存分配率"})
    private BigDecimal memoryAllocationRate;

    //存储分配率
    @ExcelProperty({"虚拟资源已分配百分比","存储分配率"})
    private BigDecimal storageAllocationRate;

    //vCPU 使用率均值
    @ExcelProperty({"虚拟资源使用率均值","vCPU使用率均值"})
    private BigDecimal vcpuUsageAvg;

    //内存 使用率均值
    @ExcelProperty({"虚拟资源使用率均值","内存使用率均值"})
    private BigDecimal memoryUsageAvg;

    //存储使用率均值
    @ExcelProperty({"虚拟资源使用率均值","存储使用率均值"})
    private BigDecimal storageUsageAvg;

    //vCPU 使用率峰值
    @ExcelProperty({"虚拟资源使用率峰值","vCPU使用率峰值"})
    private BigDecimal vcpuUsagePeak;

    //内存 使用率峰值
    @ExcelProperty({"虚拟资源使用率峰值","内存使用率峰值"})
    private BigDecimal memoryUsagePeak;

    //存储使用率峰值
    @ExcelProperty({"虚拟资源使用率峰值","存储使用率峰值"})
    private BigDecimal storageUsagePeak;

    //CPU利用率均值
   // @ExcelProperty("计算服务器CPU利用率均值")
    @ExcelIgnore
    private BigDecimal computeServerCpuUsageAvg;

    //内存利用率均值
   // @ExcelProperty("计算服务器内存利用率均值")
    @ExcelIgnore
    private BigDecimal computeServerMemoryUsageAvg;

    //CPU利用率峰值
    //@ExcelProperty("计算服务器CPU利用率峰值")
    @ExcelIgnore
    private BigDecimal computeServerCpuUsagePeak;

    //内存利用率峰值
    //@ExcelProperty("计算服务器内存利用率峰值")
    @ExcelIgnore
    private BigDecimal computeServerMemoryUsagePeak;

    @ExcelIgnore
    private LocalDateTime createTime;


}
