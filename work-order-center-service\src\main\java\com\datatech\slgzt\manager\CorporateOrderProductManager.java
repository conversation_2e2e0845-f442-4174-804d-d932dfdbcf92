package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface CorporateOrderProductManager {

    List<CorporateOrderProductDTO> list(CorporateOrderProductQuery query);

    PageInfo<CorporateOrderProductDTO> page(CorporateOrderProductQuery query);

    void insert(CorporateOrderProductDTO dto);

    void update(CorporateOrderProductDTO dto);

    void delete(Long id);

    void deleteByWorkOrderId(String workOrderId);

    CorporateOrderProductDTO getById(Long id);

    CorporateOrderProductDTO getByGid(String gid);

    CorporateOrderProductDTO getBySubOrderId(Long subOrderId);

    void updateStatusById(Long id, String status);

    void updateStatusByParentId(Long parentId, String status);
} 