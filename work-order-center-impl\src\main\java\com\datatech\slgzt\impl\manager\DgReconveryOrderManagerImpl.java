package com.datatech.slgzt.impl.manager;

import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.convert.DgRecoveryOrderManagerConvert;
import com.datatech.slgzt.dao.DgRecoveryOrderDAO;
import com.datatech.slgzt.dao.model.DgRecoveryOrderDO;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.manager.DgRecoveryOrderManager;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 15:01:22
 */
@Service
public class DgReconveryOrderManagerImpl implements DgRecoveryOrderManager {

    @Resource
    private DgRecoveryOrderDAO dao;

    @Resource
    private DgRecoveryOrderManagerConvert convert;


    @Override
    public String createWorkOrder(DgRecoveryOrderDTO reconveryWorkOrderDTO) {
        if (ObjNullUtils.isNull(reconveryWorkOrderDTO.getId())) {
            reconveryWorkOrderDTO.setId(OrderTypeEnum.CORPORATE_RECOVERY.getPrefix() + "-" + IdUtil.nanoId());
        }
        dao.insert(convert.dto2do(reconveryWorkOrderDTO));
        return reconveryWorkOrderDTO.getId();
    }

    @Override
    public PageResult<DgRecoveryOrderDTO> page(DgRecoveryOrderQuery query) {
        if (query.getPageNum() != null && query.getPageSize() != null) {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
        }
        List<DgRecoveryOrderDO> orderDOS = dao.list(query);
        return PageWarppers.box(new PageInfo<>(orderDOS), convert::do2dto);
    }

    @Override
    public DgRecoveryOrderDTO getById(String id) {
        return convert.do2dto(dao.getById(id));
    }

    @Override
    public void update(DgRecoveryOrderDTO orderDTO) {
        DgRecoveryOrderDO orderDO = convert.dto2do(orderDTO);
        dao.update(orderDO);
    }

    @Override
    public List<DgRecoveryOrderDTO> list(DgRecoveryOrderQuery query) {
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }
}
