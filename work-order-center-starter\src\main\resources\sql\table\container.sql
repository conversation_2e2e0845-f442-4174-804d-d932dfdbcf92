-- 谐云容器-用户表
CREATE TABLE "SLGZT"."XIE_YUN_USER"
(
    "ID" VARCHAR2(90) NOT NULL,
    "XIE_YUN_USER_ID" VARCHAR2(128),
    "USERNAME" VARCHAR2(128),
    "NAME" VARCHAR2(128),
    "MOBILE" VARCHAR2(16),
    "EMAIL" VARCHAR2(64),
    "CREATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "UPDATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "DELETED" NUMBER(1) DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "SLGZT", CLUSTERBTR);
COMMENT ON TABLE "SLGZT"."XIE_YUN_USER" IS '谐云容器用户表';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."ID" IS '用户id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."XIE_YUN_USER_ID" IS '谐云用户id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."USERNAME" IS '创建用户名';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."NAME" IS '用户名';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."MOBILE" IS '用户号码';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."EMAIL" IS '邮箱';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."UPDATED_TIME" IS '更新时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_USER"."DELETED" IS '1：有效,0：失效';

-- 谐云容器-组织表
CREATE TABLE "SLGZT"."XIE_YUN_ORG"
(
    "ID" VARCHAR2(90) NOT NULL,
    "XIE_YUN_ORG_ID" VARCHAR2(128),
    "CODE" VARCHAR2(128),
    "NAME" VARCHAR2(128),
    "XIE_YUN_USER_ID" VARCHAR2(90),
    "XIE_YUN_REPO_ID" VARCHAR2(90),
    "XIE_YUN_REGISTRY_ID" VARCHAR2(90),
    "NODE_POOL_NAME" VARCHAR2(128),
    "CLUSTER_NAME" VARCHAR2(128),
    "CPU_VALUE" VARCHAR2(128),
    "MEMORY_VALUE" VARCHAR2(128),
    "DESCRIPTION" VARCHAR2(512),
    "XIE_YUN_ORG_QUOTA_OPM_LIST" TEXT,
    "CREATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "UPDATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "DELETED" NUMBER(1) DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "SLGZT", CLUSTERBTR);
COMMENT ON TABLE "SLGZT"."XIE_YUN_ORG" IS '谐云容器组织表';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."ID" IS '组织id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."XIE_YUN_ORG_ID" IS '谐云组织id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."CODE" IS '组织编码';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."NAME" IS '组织名称';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."XIE_YUN_USER_ID" IS '谐云用户id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."DESCRIPTION" IS '描述';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."XIE_YUN_ORG_QUOTA_OPM_LIST" IS '组织分配的资源';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."UPDATED_TIME" IS '更新时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_ORG"."DELETED" IS '1：有效,0：失效';


-- 谐云容器-项目表
CREATE TABLE "SLGZT"."XIE_YUN_PROJECT"
(
    "ID" VARCHAR2(90) NOT NULL,
    "XIE_YUN_PROJECT_ID" VARCHAR2(128),
    "XIE_YUN_ORG_ID" VARCHAR2(128),
    "PROJECT_NAME" VARCHAR2(128),
    "DESCRIPTION" VARCHAR2(128),
    "CLUSTER_NAME" VARCHAR2(128),
    "NODE_POOL_NAME" VARCHAR2(128),
    "CPU_VALUE" VARCHAR2(128),
    "MEMORY_VALUE" VARCHAR2(128),
    "XIE_YUN_PROJECT_QUOTA_OPM_LIST" TEXT,
    "CREATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "UPDATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "DELETED" NUMBER(1) DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "SLGZT", CLUSTERBTR);
COMMENT ON TABLE "SLGZT"."XIE_YUN_PROJECT" IS '谐云容器项目表';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."ID" IS '项目id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."XIE_YUN_PROJECT_ID" IS '谐云项目id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."XIE_YUN_ORG_ID" IS '谐云组织id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."CLUSTER_NAME" IS '集群名称';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."NODE_POOL_NAME" IS '节点名';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."XIE_YUN_PROJECT_QUOTA_OPM_LIST" IS '分配的资源';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."UPDATED_TIME" IS '更新时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_PROJECT"."DELETED" IS '1：有效,0：失效';

-- 谐云容器-集群表
CREATE TABLE "SLGZT"."XIE_YUN_CLUSTER"
(
    "ID" VARCHAR2(90) NOT NULL,
    "CLUSTER_NAME" VARCHAR2(128),
    "CREATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "UPDATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "DELETED" NUMBER(1) DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "SLGZT", CLUSTERBTR);
COMMENT ON TABLE "SLGZT"."XIE_YUN_CLUSTER" IS '谐云容器集群表';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_CLUSTER"."ID" IS '主键id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_CLUSTER"."CLUSTER_NAME" IS '谐云项目id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_CLUSTER"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_CLUSTER"."UPDATED_TIME" IS '更新时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_CLUSTER"."DELETED" IS '1：有效,0：失效';


-- 谐云容器-仓库表
CREATE TABLE "SLGZT"."XIE_YUN_REPOSITORY"
(
    "ID" VARCHAR2(90) NOT NULL,
    "XIE_YUN_REPO_ID" VARCHAR2(128),
    "XIE_YUN_REGISTRY_ID" VARCHAR2(128),
    "XIE_YUN_ORG_ID" VARCHAR2(128),
    "REPO_NAME" VARCHAR2(128),
    "CREATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "UPDATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "DELETED" NUMBER(1) DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "SLGZT", CLUSTERBTR);
COMMENT ON TABLE "SLGZT"."XIE_YUN_REPOSITORY" IS '谐云容器仓库表';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_REPOSITORY"."ID" IS 'id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_REPOSITORY"."XIE_YUN_REPO_ID" IS '谐云仓库id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_REPOSITORY"."XIE_YUN_REGISTRY_ID" IS '谐云制品id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_REPOSITORY"."REPO_NAME" IS '仓库名称';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_REPOSITORY"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_REPOSITORY"."UPDATED_TIME" IS '更新时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_REPOSITORY"."DELETED" IS '1：有效,0：失效';

-- 谐云容器-命名空间表
CREATE TABLE "SLGZT"."XIE_YUN_NAMESPACE"
(
    "ID" VARCHAR2(90) NOT NULL,
    "XIE_YUN_NAMESPACE_ID" VARCHAR2(128),
    "XIE_YUN_PROJECT_ID" VARCHAR2(128),
    "XIE_YUN_ORG_ID" VARCHAR2(128),
    "NAMESPACE_NAME" VARCHAR2(128),
    "CLUSTER_NAME" VARCHAR2(128),
    "IP_POOL" VARCHAR2(64),
    "NODE_POOL_NAME" VARCHAR2(128),
    "CPU_VALUE" VARCHAR2(128),
    "MEMORY_VALUE" VARCHAR2(128),
    "NAMESPACE_DESC" VARCHAR2(1024),
    "XIE_YUN_NAMESPACE_CREATE_OPM_LIST" TEXT,
    "CREATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "UPDATED_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "DELETED" NUMBER(1) DEFAULT 0,
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "SLGZT", CLUSTERBTR);
COMMENT ON TABLE "SLGZT"."XIE_YUN_NAMESPACE" IS '谐云容器命名空间表';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."ID" IS 'id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."XIE_YUN_NAMESPACE_ID" IS '谐云命名空间id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."XIE_YUN_PROJECT_ID" IS '谐云项目id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."XIE_YUN_ORG_ID" IS '谐云组织id';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."NAMESPACE_NAME" IS '命名空间名称';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."NAMESPACE_DESC" IS '命名空间描述';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."XIE_YUN_NAMESPACE_CREATE_OPM_LIST" IS '资源数据';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."UPDATED_TIME" IS '更新时间';
COMMENT ON COLUMN "SLGZT"."XIE_YUN_NAMESPACE"."DELETED" IS '1：有效,0：失效';
