package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.SlbListenerManagerConvert;
import com.datatech.slgzt.dao.SlbListenerDAO;
import com.datatech.slgzt.dao.model.SlbListenerDO;
import com.datatech.slgzt.manager.SlbListenerManager;
import com.datatech.slgzt.model.dto.SlbListenerDTO;
import com.datatech.slgzt.model.query.SlbListenerQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
/**
 * SLB监听器Manager实现类
 */
@Service
public class SlbListenerManagerImpl implements SlbListenerManager {

    @Resource
    private SlbListenerDAO slbListenerDAO;
    
    @Resource
    private SlbListenerManagerConvert convert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(SlbListenerDTO dto) {
        SlbListenerDO entity = convert.dto2do(dto);
        entity.setCreateTime(LocalDateTime.now());
        slbListenerDAO.insert(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SlbListenerDTO dto) {
        SlbListenerDO entity = convert.dto2do(dto);
        slbListenerDAO.update(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        slbListenerDAO.delete(id);
    }

    @Override
    public SlbListenerDTO getById(String id) {
        SlbListenerDO entity = slbListenerDAO.getById(id);
        return convert.do2dto(entity);
    }

    @Override
    public List<SlbListenerDTO> listBySlbId(Long slbId) {
        return StreamUtils.mapArray(slbListenerDAO.listBySlbId(slbId), convert::do2dto);
    }

    @Override
    public List<SlbListenerDTO> listBySlbIds(List<Long> slbIds) {
        return StreamUtils.mapArray(slbListenerDAO.listBySlbIds(slbIds), convert::do2dto);
    }

    @Override
    public List<SlbListenerDTO> listByCreateTaskId(String createTaskId) {
        return StreamUtils.mapArray(slbListenerDAO.listByCreateTaskId(createTaskId), convert::do2dto);
    }

    @Override
    public List<SlbListenerDTO> listByUpdateTaskId(String updateTaskId) {
        return StreamUtils.mapArray(slbListenerDAO.listByUpdateTaskId(updateTaskId), convert::do2dto);
    }

    @Override
    public List<SlbListenerDTO> listByDeleteTaskId(String deleteTaskId) {
        return StreamUtils.mapArray(slbListenerDAO.listByDeleteTaskId(deleteTaskId), convert::do2dto);
    }

    @Override
    public PageResult<SlbListenerDTO> page(SlbListenerQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<SlbListenerDO> list = slbListenerDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }
} 