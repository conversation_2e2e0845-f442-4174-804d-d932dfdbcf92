package com.datatech.slgzt.model.req.export;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 导出任务分页查询请求
 */
@Data
public class ExportTaskPageReq {
    
    private Integer pageNum;
    
    private Integer pageSize;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    private String reportName;
} 