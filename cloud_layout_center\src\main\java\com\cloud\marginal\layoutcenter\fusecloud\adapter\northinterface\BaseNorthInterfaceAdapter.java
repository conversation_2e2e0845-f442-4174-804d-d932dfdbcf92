package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EvsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.constant.NorthInterfaceAddress;
import com.cloud.marginal.layoutcenter.fusecloud.utils.NorthInterfaceHttpRequestUtil;
import com.cloud.marginal.mapper.layout.LayoutParamMapper;
import com.cloud.marginal.mapper.layout.LayoutTaskMapper;
import com.cloud.marginal.mapper.layout.LayoutTaskNodeMapper;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;


public class BaseNorthInterfaceAdapter {

    @Autowired
    protected NorthInterfaceHttpRequestUtil northInterfaceHttpRequestUtil;

    @Autowired
    protected NorthInterfaceAddress northInterfaceAddress;

    @Resource
    protected LayoutParamMapper layoutParamMapper;

    @Resource
    protected LayoutTaskMapper layoutTaskMapper;

    @Resource
    protected LayoutTaskNodeMapper layoutTaskNodeMapper;

    /**
     * 北向接口处理成功的响应码
     */
    private final static String SUCCESS_CODE = "200";

    /**
     * 检查北向接口调用返回过来的结果是否正确,如果不正确就抛出异常
     * @param result 调用结果
     * @param functionName 功能名称
     */
    protected void checkResultThrowExceptionIfFail(CecResult result, String functionName){
        if(!SUCCESS_CODE.equals(result.getCode())){
            throw new BusinessException(String.format("interface call fail，function name：%s，result code：%s，fail info：%s",functionName,result.getCode(),result.getMessage()));
        }
    }


    protected ProductOrderParam getProductOrder(List<ProductOrderParam> productOrderParams, String productOrderTypeEnums) {
        for (ProductOrderParam productOrderParam : productOrderParams) {
            ProductOrderTypeEnum productOrderTypeEnum = ProductOrderTypeEnum.valueOf(productOrderParam.getProductOrderType());
            if((productOrderTypeEnum.getCode().equals(productOrderTypeEnums))){
                return productOrderParam;
            }
        }
        throw new BusinessException(productOrderTypeEnums+" order lost.Provide at least one");
    }

    /**
     * 获取编排参数
     * @param taskId 产品任务id或者主任务id
     * @return
     */
    protected LayoutParam getLayoutParam(String taskId){
        if(layoutTaskNodeMapper.selectById(taskId) != null){
            return layoutParamMapper.getLayoutParamByTaskId(taskId);
        }
        if(layoutTaskMapper.selectById(taskId)!=null){
            return layoutParamMapper.getLayoutParamByMainTaskId(taskId);
        }
        throw new BusinessException("task id:"+taskId+" non-existent");
    }

}
