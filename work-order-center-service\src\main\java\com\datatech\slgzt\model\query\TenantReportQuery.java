package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 07月01日 17:04:26
 */
@Data
@Accessors(chain = true)
public class TenantReportQuery {

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Long regionId;

    private List<String> domainCodeList;


}
