package com.datatech.slgzt.model;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Kafka消息包装类
 */
@Data
@Accessors(chain = true)
public class KafkaMessage<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 消息ID（用于幂等）
     */
    private String messageId;
    
    /**
     * 消息主题
     */
    private String topic;
    
    /**
     * 消息内容
     */
    private T payload;
    
    /**
     * 消息时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 消息来源
     */
    private String source;
    
    /**
     * 重试次数
     */
    private Integer retryCount;

    public static <T> String of(T payload) {
        KafkaMessage<T> tKafkaMessage = new KafkaMessage<T>()
                .setMessageId(UUID.randomUUID().toString())
                .setPayload(payload)
                .setTimestamp(LocalDateTime.now())
                .setRetryCount(0);
        return JSON.toJSONString(tKafkaMessage);
    }
} 