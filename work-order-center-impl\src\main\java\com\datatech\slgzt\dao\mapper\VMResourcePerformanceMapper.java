package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.model.VMResourcePerformanceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 虚拟机性能数据表Mapper
 * <AUTHOR>
 */
@Mapper
public interface VMResourcePerformanceMapper extends BaseMapper<VMResourcePerformanceDO> {

    @Select("<script> SELECT *, tmp.CK_CPU_UTIL AS TOP_PERCENT FROM (SELECT  t.*,ROW_NUMBER() OVER (PARTITION BY CUSTOM_ID ORDER BY CK_CPU_UTIL DESC) AS rn FROM SLGZT.WOC_VM_RESOURCE_PERFORMANCE t" +
            " WHERE CK_CPU_UTIL IS NOT NULL AND CUSTOM_ID IN " +
            "<foreach item='item' collection='customIds' open='(' separator=',' close=')'> " +
            "  #{item}" +
            "</foreach> " +
            ") tmp WHERE  rn = 1 ORDER BY  CK_CPU_UTIL DESC LIMIT 5 </script>")
    List<VMResourcePerformanceDO> selectTop5vCPUGroupByCustom(@Param("customIds") List<String> customIds);

    @Select("<script> SELECT *, tmp.CK_MEM_UTIL AS TOP_PERCENT FROM (SELECT  t.*,ROW_NUMBER() OVER (PARTITION BY CUSTOM_ID ORDER BY CK_MEM_UTIL DESC) AS rn FROM SLGZT.WOC_VM_RESOURCE_PERFORMANCE t" +
            " WHERE CK_MEM_UTIL IS NOT NULL AND CUSTOM_ID IN " +
            "<foreach item='item' collection='customIds' open='(' separator=',' close=')'> " +
            "  #{item}" +
            "</foreach> " +
            ") tmp WHERE  rn = 1 ORDER BY  CK_MEM_UTIL DESC LIMIT 5 </script>")
    List<VMResourcePerformanceDO> selectTop5MemGroupByCustom(@Param("customIds") List<String> customIds);

    @Select("<script>SELECT *, GREATEST(NVL(CK_DISK_READ_IOPS,0), NVL(CK_DISK_WRITE_IOPS,0)) AS TOP_PERCENT FROM (SELECT t.*, ROW_NUMBER() OVER (PARTITION BY CUSTOM_ID " +
            "ORDER BY GREATEST(NVL(CK_DISK_READ_IOPS,0), NVL(CK_DISK_WRITE_IOPS,0)) DESC) AS RN FROM SLGZT.WOC_VM_RESOURCE_PERFORMANCE t " +
            "WHERE CUSTOM_ID IN " +
            "<foreach item='item' collection='customIds' open='(' separator=',' close=')'>  " +
            "#{item} " +
            "</foreach>" +
            ") tmp WHERE RN = 1 ORDER BY GREATEST(NVL(CK_DISK_READ_IOPS,0), NVL(CK_DISK_WRITE_IOPS,0)) DESC LIMIT 5</script>")
    List<VMResourcePerformanceDO> selectTop5IORateGroupByCustom(@Param("customIds") List<String> customIds);

    List<VMResourcePerformanceDO> selectTop5Resource(@Param("customId") String customId, @Param("orderType") String orderType);
} 