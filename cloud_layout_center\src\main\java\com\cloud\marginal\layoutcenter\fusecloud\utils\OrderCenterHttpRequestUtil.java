package com.cloud.marginal.layoutcenter.fusecloud.utils;

import com.alibaba.fastjson.TypeReference;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.utils.AbstractHttpRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrderCenterHttpRequestUtil extends AbstractHttpRequestUtils {

    @Value("${http.addresses.ordercenter}")
    private String orderCenterAddress;

    @Value("${http.addresses.workordercenter}")
    private String workOrderCenterAddress;

    @Value("${http.addresses.workordercenterNew}")
    private String workordercenterNew;

    //工单中心标准资源开通回调地址
    @Value("${http.addresses.workordercenter_subscribe}")
    private String workordercenterSubscribe;

    private static Integer TASK_RESOURCE;

    /**
     * post请求，请求参数json数据
     * @param url 请求地址
     * @param jsonData 请求数据
     * @param resultClass 返回结果类型
     * @param <T>
     * @return
     */
    public <T> T postJson(Integer taskResource,
                          String url,
                          Object jsonData,
                          TypeReference<T> resultClass) {
        TASK_RESOURCE = taskResource;
        return this.postJson(url, null, jsonData, resultClass, null,
                requestException -> {
                    this.startRequestException(requestException);
                }, (resultCode, result) -> {
                    this.requestResponseCodeIsNo2xx(resultCode, result);
                });
    }

    @Override
    protected void appendCommonHeader(HttpHeaders httpHeaders) {

    }

    @Override
    protected String getHostAddress() {
        // 任务资源来源 1：订单中心 2：工单中心 3：新工单中心
        String hostAddress = null;
        switch(TASK_RESOURCE) {
            case 1:
                hostAddress = orderCenterAddress;
                break;
            case 2:
                hostAddress = workOrderCenterAddress;
                break;
            case 3:
                hostAddress = workordercenterNew;
                break;
            case 4:
                hostAddress = workordercenterSubscribe;
                break;
            case 5:
                hostAddress = workordercenterSubscribe;
                break;
            default:
                throw new BusinessException("任务资源来源未指定");
        }
        TASK_RESOURCE = null;
        return hostAddress;
    }

    /**
     * 发起http请求响应码不是2xx（xx是其他数字），的处理
     * @param resultCode 响应码
     * @param result 结果字符串
     */
    private void requestResponseCodeIsNo2xx(Integer resultCode, String result) {
        log.error(String.format("接口调用失败。请求响应码：%d。返回结果字符串：%s",resultCode,result));
        throw new BusinessException(String.format("接口调用失败，响应码：%s,返回结果：%s",resultCode,result));
    }

    /**
     * 发起请求出现异常
     * @param requestException
     */
    private void startRequestException(Exception requestException) {
        log.error("接口调用异常", requestException);
        BusinessException businessException = new BusinessException(requestException.getMessage());
        businessException.setStackTrace(requestException.getStackTrace());
        throw businessException;
    }
}
