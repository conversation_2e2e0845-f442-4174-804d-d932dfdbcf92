package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.marginal.model.dto.layout.TempTaskOperationDto;
import com.cloud.marginal.model.dto.layout.TempTaskQueryDto;
import com.cloud.marginal.model.vo.layout.TempTaskDetailVO;

/**
 * <AUTHOR>
 * @Since 2023/5/16 16:07
 */
public interface TempTaskService {

    void create(TempTaskOperationDto detail);

    void remove(String templateTaskId,String taskApiId);

    void update(TempTaskOperationDto detail);

    Page<TempTaskDetailVO> getPage(TempTaskQueryDto query);
}
