package com.datatech.slgzt.enums.ip;

public enum IPVersionEnum {

    IPV4("ipv4", "IPv4", 4L),
    IPV6("ipv6", "IPv6", 6L),
    ;

    private final String code;

    private final String desc;

    private final Long value;

    IPVersionEnum(String code, String desc, Long value) {
        this.code = code;
        this.desc = desc;
        this.value = value;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }

    public Long getValue() {
        return value;
    }

    /**
     * 通过value获取枚举
     *
     * @param value
     * @return
     */
    public static IPVersionEnum getByValue(String value) {
        IPVersionEnum[] values = values();
        for (IPVersionEnum ipVersionEnum : values) {
            if (ipVersionEnum.name().equals(value)) {
                return ipVersionEnum;
            }
        }
        return null;
    }
    public static IPVersionEnum getByDesc(String value) {
        IPVersionEnum[] values = values();
        for (IPVersionEnum ipVersionEnum : values) {
            if (ipVersionEnum.getDesc().equals(value)) {
                return ipVersionEnum;
            }
        }
        return null;
    }
}
