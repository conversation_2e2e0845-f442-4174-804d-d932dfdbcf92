package com.datatech.slgzt.model.req.slb;

import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import lombok.Data;

import java.util.List;

/**
 * SLB监听器服务组更新请求
 */
@Data
public class SlbListenerServerGroupUpdateReq {
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * SLB监听器ID
     */
    private String slbListenerId;

    /**
     * SLB监听器名称
     */
    private String slbListenerName;

    /**
     * 服务器信息列表
     */
    private List<SlbListenerServerGroupDTO.SlbListenerServerInfoModel> serverInfoModelList;
} 