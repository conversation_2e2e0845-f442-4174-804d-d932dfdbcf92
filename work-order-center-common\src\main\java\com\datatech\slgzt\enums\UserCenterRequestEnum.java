package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: suxin
 * @Date: 2025/1/7
 */

@Getter
@AllArgsConstructor
public enum UserCenterRequestEnum {

    GET_USER_DETAIL("/ccmp/usercenter/users/getUserInfo", "通过id或者登录名称获取用户详情"),
    GET_USER_LIST("/ccmp/usercenter/role/getUsers", "通过角色编码获取用户列表"),
    GET_ROLE_LIST("/ccmp/usercenter/role/getRoles", "根据条件获取角色列表"),
    GET_TENANT_DETAIL("/ccmp/usercenter/tenant/detail", "根据租户id获取租户信息"),
    GET_ROLE_LIST_PAGE("/ccmp/usercenter/role/getRoleList", "分页查询角色列表"),
    GET_CMDBID("/ccmp/usercenter/tenant/get/cmdbId", "根据租户获取cmdbId"),
    GET_BILL_ID_BY_SYSTEM_CODE("/ccmp/usercenter/app/custom", "通过业务系统编码获取计费信息"),
    GET_USER_BY_ORG_ID_OR_ROLE_CODE("/ccmp/usercenter/user/getUserByRoleAndOrg", "根据部门id和角色编码查询用户列表"),
    APPEND_USER_ROLE_URL("/ccmp/usercenter/users/role/add", "用户追加角色信息"),
    CREATE_ROLE_URL("/ccmp/usercenter/role/addRole", "给用户中心添加角色"),
    UPDATE_USER_APP("/ccmp/usercenter/app/update", "更新用户中心业务系统"),
    SMS_SEND_MESSAGE("/ccmp/usercenter/messageSend/sendMessage", "用户中心发送短信接口"),
    ;

    private final String url;

    private final String remark;


}
