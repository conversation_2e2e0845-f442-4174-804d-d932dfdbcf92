package com.datatech.slgzt.model.dto;

import com.datatech.slgzt.model.bpmn.BaseWorkOrder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 14:37:04
 */
@Data
public class DgRecoveryOrderDTO {

    private String id;
    // 订单编号
    private String orderCode;

    //租户id
    private Long tenantId;

    //租户名称
    private String tenantName;

    //创建人
    private String creator;

    //创建时间
    private LocalDateTime createTime;

    //创建人id
    private Long creatorId;

    // jobExecutionIdd
    private Long jobExecutionId;

    /**
     * 回收类型。0-默认，1-用户注销
     */
    private Integer recoveryType;

    /**
     * 用户注销时使用
     */
    private String billId;



    ///------------------------




}
