package com.datatech.slgzt.controller;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.SlbListenerWebConvert;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.SlbListenerManager;
import com.datatech.slgzt.manager.SlbListenerServerGroupManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.SlbListenerDTO;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.req.slb.SlbCertificateIdReq;
import com.datatech.slgzt.model.req.slb.SlbListenerCreateReq;
import com.datatech.slgzt.model.req.slb.SlbListenerPageReq;
import com.datatech.slgzt.model.req.slb.SlbListenerUpdateReq;
import com.datatech.slgzt.model.vo.slb.SlbListenerVO;
import com.datatech.slgzt.service.SlbListenerService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * SLB监听器控制器
 */
@RestController
@RequestMapping("/slbListener")
public class SlbListenerController {

    @Resource
    private SlbListenerManager slbListenerManager;

    @Resource
    private SlbListenerService slbListenerService;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private SlbListenerServerGroupManager slbListenerServerGroupManager;


    @Resource
    private SlbListenerWebConvert slbListenerWebConvert;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    public CommonResult<PageResult<SlbListenerVO>> page(@RequestBody SlbListenerPageReq req) {
        PageResult<SlbListenerDTO> page = slbListenerManager.page(slbListenerWebConvert.convert(req));
        PageResult<SlbListenerVO> box = PageWarppers.box(page, slbListenerWebConvert::convert);
        List<SlbListenerVO> records = box.getRecords();
        //设置服务器组查询
        records.forEach(slbListenerVO -> {
            List<SlbListenerServerGroupDTO> slbListenerServerGroupDTOS = slbListenerServerGroupManager.listByListenerId(slbListenerVO.getId());
            slbListenerVO.setSlbListenerServerGroup(StreamUtils.findAny(slbListenerServerGroupDTOS));
        });
        return CommonResult.success(box);
    }

    /**
     * 创建
     */
    @PostMapping("/create")
    @OperationLog(description = "创建SLB监听器", operationType = "CREATE")
    public CommonResult<Void> create(@RequestBody SlbListenerCreateReq req) {
        ResourceDetailDTO slbDetailDTO = resourceDetailManager.getById(req.getSlbResourceDetailId());
        Precondition.checkArgument(slbDetailDTO != null, "资源不存在");
        Precondition.checkArgument(slbDetailDTO.getType().equals("slb"), "资源类型错误");
        //获取服务器组的云主机id
        SlbListenerServerGroupDTO slbListenerServerGroup = req.getSlbListenerServerGroup();
        Precondition.checkArgument(slbListenerServerGroup!= null, "服务器组不能为空");
        List<SlbListenerServerGroupDTO.SlbListenerServerInfoModel> serverInfoModelList = slbListenerServerGroup.getServerInfoModelList();
        Precondition.checkArgument(serverInfoModelList, "服务器组的云主机不能为空");
        //提取所有云主机id
        List<Long> ids = StreamUtils.mapArray(serverInfoModelList, SlbListenerServerGroupDTO.SlbListenerServerInfoModel::getResourceDetailId);
        List<ResourceDetailDTO> vmList = resourceDetailManager.list(new ResourceDetailQuery().setIds(ids));
        //slbDetailDTO 的资源id 一定要和vmList的每个都相等
        vmList.forEach(resourceDetailDTO -> {
            Precondition.checkArgument(resourceDetailDTO.getResourcePoolId().equals(slbDetailDTO.getResourcePoolId()), "服务器组的资源池和slb的资源池不一致");
        });
        slbListenerService.createSlbListener(slbListenerWebConvert.convert(req));
        return CommonResult.success(null);
    }

    /**
     * 更新
     */
    @PostMapping("/update")
    @OperationLog(description = "更新SLB监听器", operationType = "UPDATE")
    public CommonResult<Void> update(@RequestBody SlbListenerUpdateReq req) {
        slbListenerService.updateSlbListener(slbListenerWebConvert.convert(req));
        return CommonResult.success(null);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @OperationLog(description = "删除SLB监听器", operationType = "DELETE")
    public CommonResult<Void> delete(@RequestBody SlbCertificateIdReq req) {
        slbListenerService.deleteSlbListener(req.getId());
        return CommonResult.success(null);
    }


    /**
     * description
     */
    @PostMapping("/getById")
    public CommonResult<SlbListenerVO> getById(@RequestBody SlbCertificateIdReq req) {
        SlbListenerDTO slbListenerDTO = slbListenerManager.getById(req.getId());
        SlbListenerVO slbListenerVO = slbListenerWebConvert.convert(slbListenerDTO);
        //设置服务器组查询
        List<SlbListenerServerGroupDTO> slbListenerServerGroupDTOS = slbListenerServerGroupManager.listByListenerId(slbListenerVO.getId());
        slbListenerVO.setSlbListenerServerGroup(StreamUtils.findAny(slbListenerServerGroupDTOS));
        return CommonResult.success(slbListenerVO);
    }



} 