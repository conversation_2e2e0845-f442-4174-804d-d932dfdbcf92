package com.datatech.slgzt.model.req.dag;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * DAG模板更新请求
 */
@Data
public class DagTemplateUpdateReq {
    
    /**
     * 模板ID
     */
    @NotBlank(message = "模板ID不能为空")
    private String id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String name;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 前端保存的DAG画布数据
     */
    private String dagCanvas;

    /**
     * VPC资源列表
     */
    private List<VpcUnTaskModel> vpcModelList;

    /**
     * 网络资源列表
     */
    private List<NetworkUnTaskModel> networkModelList;

    /**
     * ECS资源列表
     */
    private List<EcsModel> ecsModelList;

    /**
     * MySQL资源列表
     */
    private List<EcsModel> mysqlModelList;

    /**
     * Redis资源列表
     */
    private List<EcsModel> redisModelList;

    /**
     * GCS资源列表
     */
    private List<EcsModel> gcsModelList;

    /**
     * EVS资源列表
     */
    private List<EvsModel> evsModelList;

    /**
     * EIP资源列表
     */
    private List<EipModel> eipModelList;

    /**
     * NAT网关资源列表
     */
    private List<NatGatwayModel> natModelList;

    /**
     * SLB资源列表
     */
    private List<SlbModel> slbModelList;

    /**
     * OBS资源列表
     */
    private List<ObsModel> obsModelList;
} 