package com.datatech.slgzt.service.device;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.DeviceMetricEnum;
import com.datatech.slgzt.model.BaseMetricInfoModel;
import com.datatech.slgzt.model.BaseVdevice;
import com.datatech.slgzt.model.BaseVdeviceInfoModel;
import com.datatech.slgzt.model.BaseVgpuDevicelInfoModel;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import org.apache.commons.compress.utils.Lists;

import java.util.*;
import java.util.stream.Collectors;

public interface DeviceGupMetricsService {

    /**
     * 存储当前显卡指标信息
     */
    List<DeviceCardMetricsDTO>  calculateGpuDeviceMetric();

    void syncRemoteDeviceDataInfo();
    /**
     * 获取所有任务
     * @param baseUrl
     * @return
     */
    default List<BaseVdevice> queryVdeviceList(String baseUrl) {
        List<BaseVdevice> deviceList = Lists.newArrayList();
        Mapper responseMapper = OkHttpsUtils.http().sync(baseUrl)
                .get()
                .getBody()
                .toMapper();
        if (responseMapper != null) {
            List<JSONObject> jsonObjects = Optional.ofNullable(responseMapper.getString("data")).map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(JSONObject.class))
                    .orElse(Collections.emptyList());
            List<BaseVdeviceInfoModel> baseVdeviceInfoModels = new ArrayList<>();
            jsonObjects.forEach(itemMetric -> {
                BaseVdeviceInfoModel deviceMetric = JSONObject.parseObject(itemMetric.getString("status"), BaseVdeviceInfoModel.class);
                baseVdeviceInfoModels.add(deviceMetric);
            });
            deviceList = baseVdeviceInfoModels.stream()
                    .map(BaseVdeviceInfoModel::getVdevices)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }
        return deviceList;

    }

    /**
     * 解析出指标相对应的值
     * @param metricJson
     * @return
     */
    default Map<String, BaseMetricInfoModel> parseMetricInfo(String metricJson){
        List<JSONObject> listObjs = Optional.ofNullable(metricJson)
                .map(JSONObject::parseObject)
                .map(obj -> obj.getString("result"))
                .map(JSONArray::parseArray)
                .map(arr -> arr.toJavaList(JSONObject.class))
                .orElse(Collections.emptyList());

        List<BaseMetricInfoModel> baseMetricInfos = new ArrayList<>();
        listObjs.forEach(itemMetric ->{
            BaseMetricInfoModel deviceMetric = JSONObject.parseObject(itemMetric.getString("metric"), BaseMetricInfoModel.class);
            Optional.ofNullable(itemMetric.getString("value"))
                    .map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(String.class))
                    .filter(list -> list.size() > 1)
                    .map(list -> list.get(1))
                    .ifPresent(deviceMetric::setValue);

            baseMetricInfos.add(deviceMetric);
        });
        //构建指标
//        Map<String, BaseMetricInfoModel> vgpuMemoryUsageIndex = vgpuMemoryUsageList.stream()
//                .collect(Collectors.toMap(BaseMetricInfoModel::getVindex, metricInfo -> metricInfo, (oldValue, newValue) -> oldValue));
        return baseMetricInfos.stream().collect(Collectors.toMap(BaseMetricInfoModel::getUuid, meticInfo ->meticInfo, (oldValue, newValue) -> oldValue));
    }



    /**
     * 根据指标枚举获取具体指标信息
     * @param deviceMetricEnum
     * @return
     */
    default Map<String, BaseMetricInfoModel> queryRemoteDeviceMetric(String url,DeviceMetricEnum deviceMetricEnum) {
        Mapper responseMapper = remoteHttpClientMetric(url,deviceMetricEnum);
        if(responseMapper != null){
            String successStr = responseMapper.getString("status");
            Precondition.checkArgument("success".equals(successStr), deviceMetricEnum.getDesc()+"失败: "
                    + responseMapper.getString("message"));
            return parseMetricInfo(responseMapper.getString("data"));
        }
        return Collections.emptyMap();
    }



    /**
     * 解析出指标相对应的值
     * @param metricJson
     * @return
     */
    default Map<String, List<BaseMetricInfoModel>> parseMetricInfo2Map (String metricJson){
        List<JSONObject> listObjs = Optional.ofNullable(metricJson)
                .map(JSONObject::parseObject)
                .map(obj -> obj.getString("result"))
                .map(JSONArray::parseArray)
                .map(arr -> arr.toJavaList(JSONObject.class))
                .orElse(Collections.emptyList());

        List<BaseMetricInfoModel> baseMetricInfos = new ArrayList<>();
        listObjs.forEach(itemMetric -> {
            BaseMetricInfoModel deviceMetric = JSONObject.parseObject(itemMetric.getString("metric"), BaseMetricInfoModel.class);
            Optional.ofNullable(itemMetric.getString("value"))
                    .map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(String.class))
                    .filter(list -> list.size() > 1)
                    .map(list -> list.get(1))
                    .ifPresent(deviceMetric::setValue);
            baseMetricInfos.add(deviceMetric);
        });
        Map<String, List<BaseMetricInfoModel>> collect = baseMetricInfos.stream().collect(Collectors.groupingBy(BaseMetricInfoModel::getPgpuId));
        return collect;
    }




    default BaseMetricInfoModel parseVgpuMetricInfo (String metricJson){
        List<JSONObject> listObjs = Optional.ofNullable(metricJson)
                .map(JSONObject::parseObject)
                .map(obj -> obj.getString("result"))
                .map(JSONArray::parseArray)
                .map(arr -> arr.toJavaList(JSONObject.class))
                .orElse(Collections.emptyList());

        List<BaseMetricInfoModel> baseMetricInfos = new ArrayList<>();
        listObjs.forEach(itemMetric -> {
            BaseMetricInfoModel deviceMetric = JSONObject.parseObject(itemMetric.getString("metric"), BaseMetricInfoModel.class);
            Optional.ofNullable(itemMetric.getString("value"))
                    .map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(String.class))
                    .filter(list -> list.size() > 1)
                    .map(list -> list.get(1))
                    .ifPresent(deviceMetric::setValue);

            baseMetricInfos.add(deviceMetric);
        });
        return baseMetricInfos.stream().findFirst().orElse(null);
    }




    /**
     * 根据物理显卡id 查询虚拟网卡
     * @param baseUrl
     * @param deviceId
     * @return
     */
    default Mapper remoteHttpClientVgpuDevice(String baseUrl, String deviceId) {
        String url = String.format("%s/%s/vdevices", baseUrl, deviceId);
        Mapper mapper = OkHttpsUtils.http().sync(url)
                .get()
                .getBody()
                .toMapper();
        return mapper;
    }

    /**
     * 调用remote 接口
     * @param baseUrl
     * @param deviceId
     * @param metricEnum
     * @return
     */
    default Mapper remoteHttpClientMetric(String baseUrl, DeviceMetricEnum metricEnum) {
        return OkHttpsUtils.http().sync(baseUrl)
                .addUrlPara("query", metricEnum.getCode())
                .get()
                .getBody()
                .toMapper();
    }


    /**
     * 根据物理设别获取虚拟显卡信息
     * @param deviceId
     * @return
     */
    default List<BaseVgpuDevicelInfoModel> queryVgpuDeviceByDeviceId(String url ,String deviceId) {
        Mapper responseMapper = remoteHttpClientVgpuDevice(url, deviceId);
        Precondition.checkArgument(responseMapper, "根据物理设备ID获取虚拟卡信息失败");
        String successStr = responseMapper.getString("code");
        if ("200".equalsIgnoreCase(successStr)) {
            Precondition.checkArgument(responseMapper, "根据物理显卡ID 获取虚拟显卡信息: " + responseMapper.getString("message"));
            List<BaseVgpuDevicelInfoModel> listObjs = Optional.ofNullable(responseMapper.getString("data"))
                    .map(JSONArray::parseArray)
                    .map(arr -> arr.toJavaList(BaseVgpuDevicelInfoModel.class))
                    .orElse(Collections.emptyList());
            return listObjs;
        }
        return Lists.newArrayList();
    }

    /**
     * 查询虚拟显卡指标接口
     * @param baseUrl
     * @param ip
     * @param deviceId
     * @param metricEnum
     * @param vindex
     * @return
     */
    default BaseMetricInfoModel remoteQueryOneMetric(String baseUrl,String ip, String deviceId, DeviceMetricEnum metricEnum,String vindex) {
//        String baseUrl = gpuDeviceMetricUrl +"/api/v1/query";
        String queryParam = String.format("%s{host_ip=\"%s\",pgpu_id=\"%s\",vindex=\"%s\"}", metricEnum.getCode(), ip, deviceId,vindex);
        Mapper responseMapper = OkHttpsUtils.http().sync(baseUrl)
                .addUrlPara("query", queryParam)
                .get()
                .getBody()
                .toMapper();
        if (responseMapper != null) {
            String successStr = responseMapper.getString("status");
            Precondition.checkArgument("success".equals(successStr), " vGPU的实时显存使用大小: " + responseMapper.getString("message"));
            return parseVgpuMetricInfo(responseMapper.getString("data"));
        }
        return new  BaseMetricInfoModel();
    }

}
