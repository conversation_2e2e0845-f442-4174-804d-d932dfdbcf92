package com.datatech.slgzt.service.corporate;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.model.dto.CorporateOrderTempDTO;

import java.math.BigDecimal;
import java.util.List;

// 1. 定义资源操作接口
public interface CorporateOrderTempSaveService {



    Long handleCreate(String key, String tenantId, JSONObject orderJson);

    void handleDelete(Long id, String type, String tenantId);

    void handleDeleteAll(String tenantId);

    String parseKey(String key);
    /**
     * 获取当前用户的暂存对象
     */
    List<CorporateOrderTempDTO> getTempSave(List<String> tenantIds);

    // 添加用户到租户
    void addTenantToUser(String tenantId, String userId);

    // 删除租户与所有用户的关联，并返回要被删除的用户列表
    List<String> getUsersToBeDeleted(String tenantId);
}