package com.datatech.slgzt.dao.model.container;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/14
 */

@Data
@TableName("XIE_YUN_USER")
public class XieYunUserDO {

    @TableField("ID")
    private String id;

    @TableField("XIE_YUN_USER_ID")
    private String xieYunUserId;

    /**
     * 创建用户名
     */
    @TableField("USERNAME")
    private String username;

    /**
     * 用户名
     */
    @TableField("NAME")
    private String name;

    /**
     * 用户号码
     */
    @TableField("MOBILE")
    private String mobile;

    /**
     * 用户邮箱
     */
    @TableField("EMAIL")
    private String email;


    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;

    /**
     * 1：删除,0：正常
     */
    @TableField("DELETED")
    private Boolean deleted;

}

