package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@Data
public class RdsParam {

    /**
     * 主键id
     */
    private String id;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 云区域编码
     */
    private String regionCode;

    /**
     * 计费号
     */
    private String billId;

    /**
     * 集团客户编码
     */
    private String groupId;

    private String projectId;

    private String azCode;

    private List<String> azCodeSlave;

    /**
     * VPC编号,必填
     */
    private String vpcId;

    private String subnetId;

    private String securityGroupId;

    /**
     * 资源名称
     */
    private String rdsName;

    /**
     * 规格编码
     */
    private String flavorCode;
    /**
     * 数据库版本
     * */
    private String engineVersion;
    /**
     * 部署类型
     * */
    private String deployType;
    /**
     * 端口
     * */
    private String port;
    /**
     * 实例类型
     * */
    private String instanceType;
    /**
     * 存储类型
     * */
    private String storageType;
    /**
     * 存储大小，最小10L 最大2000L
     * */
    private Integer storageSize;

    private String paasId;
    private String rdsLoginName;
    private String RdsPwd;
    private String timeZone;
    private Integer tableIsLower;
    private Integer dbEngine;
    private String masterInstanceId;
    private String gId;



}
