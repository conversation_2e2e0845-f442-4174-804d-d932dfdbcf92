package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.SlbListenerMapper;
import com.datatech.slgzt.dao.model.SlbListenerDO;
import com.datatech.slgzt.model.query.SlbListenerQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * SLB监听器DAO
 */
@Repository
public class SlbListenerDAO {

    @Resource
    private SlbListenerMapper slbListenerMapper;

    /**
     * 插入
     */
    public void insert(SlbListenerDO slbListenerDO) {
        slbListenerMapper.insert(slbListenerDO);
    }

    /**
     * 更新
     */
    public void update(SlbListenerDO slbListenerDO) {
        slbListenerMapper.updateById(slbListenerDO);
    }

    /**
     * 删除
     */
    public void delete(String id) {
        slbListenerMapper.deleteById(id);
    }

    /**
     * 根据ID查询
     */
    public SlbListenerDO getById(String id) {
        return slbListenerMapper.selectById(id);
    }

    /**
     * listBySlbId
     */
    public List<SlbListenerDO> listBySlbId(Long slbId) {
        return slbListenerMapper.selectList(
                Wrappers.<SlbListenerDO>lambdaQuery()
                        .eq(SlbListenerDO::getSlbResourceDetailId, slbId)
                        .orderByDesc(SlbListenerDO::getCreateTime)
        );
    }

    /**
     * listBySlbIds
     */
    public List<SlbListenerDO> listBySlbIds(List<Long> slbIds) {
        return slbListenerMapper.selectList(
                Wrappers.<SlbListenerDO>lambdaQuery()
                        .in(SlbListenerDO::getSlbResourceDetailId, slbIds)
                        .orderByDesc(SlbListenerDO::getCreateTime)
        );
    }

    /**
     * listByCreateTaskId
     */
    public List<SlbListenerDO> listByCreateTaskId(String createTaskId) {
        return slbListenerMapper.selectList(
                Wrappers.<SlbListenerDO>lambdaQuery()
                        .apply("json_value(TASK_STATUS_EXT, '$.createTaskId') = {0}", createTaskId)
        );
    }

    /**
     * listByUpdateTaskId
     */
    public List<SlbListenerDO> listByUpdateTaskId(String updateTaskId) {
        return slbListenerMapper.selectList(
                Wrappers.<SlbListenerDO>lambdaQuery()
                       .apply("json_value(TASK_STATUS_EXT, '$.updateTaskId') = {0}", updateTaskId)
        );
    }

    /**
     * listByDeleteTaskId
     */
    public List<SlbListenerDO> listByDeleteTaskId(String deleteTaskId) {
        return slbListenerMapper.selectList(
                Wrappers.<SlbListenerDO>lambdaQuery()
                        .apply("json_value(TASK_STATUS_EXT, '$.deleteTaskId') = {0}", deleteTaskId)
        );
    }




    /**
     * 列表查询
     */
    public List<SlbListenerDO> list(SlbListenerQuery query) {
        return slbListenerMapper.selectList(
                Wrappers.<SlbListenerDO>lambdaQuery()
                        .like(ObjNullUtils.isNotNull(query.getListenerName()), SlbListenerDO::getListenerName, query.getListenerName())
                        .eq(ObjNullUtils.isNotNull(query.getVpcId()), SlbListenerDO::getVpcId, query.getVpcId())
                        .eq(ObjNullUtils.isNotNull(query.getSlbDeviceId()), SlbListenerDO::getSlbDeviceId, query.getSlbDeviceId())
                        .eq(ObjNullUtils.isNotNull(query.getSlbResourceDetailId()), SlbListenerDO::getSlbResourceDetailId, query.getSlbResourceDetailId())
                        .eq(ObjNullUtils.isNotNull(query.getRunningStatus()), SlbListenerDO::getRunningStatus, query.getRunningStatus())
                        .orderByDesc(SlbListenerDO::getCreateTime)
        );
    }
} 