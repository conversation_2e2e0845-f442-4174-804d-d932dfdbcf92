package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.util.Date;

/**
 * flink导出vo
 * <AUTHOR>
 * @date 2025/6/24
 **/
@Data
public class FlinkExportVO {

    @ExcelExportHeader(value = "flink名称")
    private String deviceName;

    @ExcelExportHeader(value = "实例规格")
    private String spec;

    @ExcelExportHeader(value = "申请时长")
    private String applyTime;

    @ExcelExportHeader(value = "租户名称")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String cloudPlatform;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private Date resourceApplyTime;

    @ExcelExportHeader(value = "到期时间")
    private Date expireTime;

    @ExcelExportHeader(value = "状态")
    private String deviceStatus;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;

    @ExcelExportHeader(value = "计费号")
    private String billId;

}
