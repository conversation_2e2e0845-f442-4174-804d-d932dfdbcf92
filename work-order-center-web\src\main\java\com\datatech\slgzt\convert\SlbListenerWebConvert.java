package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.SlbListenerDTO;
import com.datatech.slgzt.model.query.SlbListenerQuery;
import com.datatech.slgzt.model.req.slb.SlbListenerCreateReq;
import com.datatech.slgzt.model.req.slb.SlbListenerPageReq;
import com.datatech.slgzt.model.req.slb.SlbListenerUpdateReq;
import com.datatech.slgzt.model.vo.slb.SlbListenerVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * SLB监听器Web层转换器
 */
@Mapper(componentModel = "spring")
public interface SlbListenerWebConvert {
    
    /**
     * 分页请求转Query
     */
    SlbListenerQuery convert(SlbListenerPageReq req);
    
    /**
     * DTO转VO
     */
    @Mapping(target = "runningStatus",constant = "运行中")
    SlbListenerVO convert(SlbListenerDTO dto);
    
    /**
     * DTO转VO
     */
    SlbListenerDTO convert(SlbListenerCreateReq vo);
    SlbListenerDTO convert(SlbListenerUpdateReq vo);
}