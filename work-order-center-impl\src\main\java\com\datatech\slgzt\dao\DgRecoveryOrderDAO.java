package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DgRecoveryOrderMapper;
import com.datatech.slgzt.dao.model.DgRecoveryOrderDO;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 07月07日 14:51:14
 */
@Repository
public class DgRecoveryOrderDAO {
    @Resource
    private DgRecoveryOrderMapper mapper;


    public DgRecoveryOrderDO getById(String id) {
        return mapper.selectById(id);
    }


    public String insert(DgRecoveryOrderDO reconveryWorkOrderDO) {
        mapper.insert(reconveryWorkOrderDO);
        return reconveryWorkOrderDO.getId();
    }


    public void update(DgRecoveryOrderDO orderDO) {
        mapper.updateById(orderDO);
    }


    /**
     * 列表查询
     *
     * @param query 查询条件
     * @return 回收工单列表
     */
    public List<DgRecoveryOrderDO> list(DgRecoveryOrderQuery query) {
        LambdaQueryWrapper<DgRecoveryOrderDO> queryWrapper = Wrappers.<DgRecoveryOrderDO>lambdaQuery()
                .isNull(Boolean.TRUE.equals(query.getJobExecutionIdNull()), DgRecoveryOrderDO::getJobExecutionId);
        // 回收类型。null-全部,0-默认，1-用户注销
        queryWrapper.eq(ObjNullUtils.isNotNull(query.getRecoveryType()), DgRecoveryOrderDO::getRecoveryType, query.getRecoveryType());
        queryWrapper.like(ObjNullUtils.isNotNull(query.getOrderCode()), DgRecoveryOrderDO::getOrderCode, query.getOrderCode())
                .like(ObjNullUtils.isNotNull(query.getCreator()), DgRecoveryOrderDO::getCreator, query.getCreator())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), DgRecoveryOrderDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), DgRecoveryOrderDO::getCreateTime, query.getCreateTimeEnd())
                .in(ObjNullUtils.isNotNull(query.getTenantIds()), DgRecoveryOrderDO::getTenantId, query.getTenantIds());
        queryWrapper.orderByDesc(DgRecoveryOrderDO::getCreateTime);
        return mapper.selectList(queryWrapper);
    }

}
