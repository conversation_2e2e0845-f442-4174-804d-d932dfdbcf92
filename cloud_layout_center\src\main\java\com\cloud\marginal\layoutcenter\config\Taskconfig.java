package com.cloud.marginal.layoutcenter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@RefreshScope
@ConfigurationProperties(prefix = "taskconfig")
@Data
public class Taskconfig {

    private Map<String, List<Map<String, String>>> dynamicTask;

    private Map<String, List<Map<String, String>>> dynamicExecuteTask;

    private Map<String, List<String>> customAddRel;

    private Map<String, List<String>> customDelTask;

    private Map<String, List<String>> customAddTask;

}
