package com.cloud.marginal.layoutcenter;

import com.cloud.marginal.layoutcenter.service.MyTestService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;

@SpringBootTest
@RunWith(SpringRunner.class)
public class MyTest {
    @Autowired
    MyTestService myTestService;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Test
    public void test() throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        myTestService.templateCall();
    }

    @Test
    public void test2(){
        kafkaTemplate.send("test02","hello,world");
        System.out.println("已发送了");
    }


}
