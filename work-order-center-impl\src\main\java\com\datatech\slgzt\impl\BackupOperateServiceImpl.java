package com.datatech.slgzt.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.model.dto.BackupOperateOpm;
import com.datatech.slgzt.service.BackupOperateService;
import com.datatech.slgzt.service.McTaskService;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-06-06 10:21
 **/
@Slf4j
@Service
public class BackupOperateServiceImpl implements BackupOperateService {

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;

    @Resource
    private McTaskService mcTaskService;

    @Override
    public void operateBackup(BackupOperateOpm opm) {
        String url = resourceCenterApiUrl + "/v1/cloud/resourcecenter/backup/job/resource/update";
        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("backupJobId", opm.getBackupJobId());
        params.put("backupType", opm.getBackupType());
        if (CollectionUtil.isNotEmpty(opm.getBindResList())) {
            params.put("bindResList", opm.getBindResList());
        } else if (CollectionUtil.isNotEmpty(opm.getUnbindResList())) {
            params.put("unbindResList", opm.getUnbindResList());
        }
        // 发送HTTP请求
        log.info("发送操作云备份策略请求: {}", JSON.toJSONString(params));
        // 使用OkHttps发送请求
        Mapper responseMapper = OkHttpsUtils.http().sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("操作云备份策略响应: {}", responseMapper.toString());
        // 设置响应结果
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equalsIgnoreCase(successStr), "操作云备份策略失败: " + responseMapper.getString("message"));
        Mapper mapper = responseMapper.getMapper("entity");
        String taskId = mapper.getString("id");
        //循环拿任务id去查询，设置最大尝试次数
        mcTaskService.checkStatus(taskId);
    }
}
