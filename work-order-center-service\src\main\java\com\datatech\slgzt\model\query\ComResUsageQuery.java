package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月26日 14:02:48
 */
@Data
@Accessors(chain = true)
public class ComResUsageQuery {

    private Long regionId;

    private List<Long> regionIds;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

}
