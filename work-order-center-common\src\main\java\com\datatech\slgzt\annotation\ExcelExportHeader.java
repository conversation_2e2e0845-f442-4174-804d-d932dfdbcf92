package com.datatech.slgzt.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @ClassName ExcelExportHeader
 * @Description
 * @Date 2025/02/19 15:12
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelExportHeader {

    /**
     * excel导出表头
     */
    String value() default "";
}
