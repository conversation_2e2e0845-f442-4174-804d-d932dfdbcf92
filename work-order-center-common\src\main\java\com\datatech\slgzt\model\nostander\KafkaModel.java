package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: kafka模型
 * @author: LK
 * @create: 2025-06-23 10:06
 **/
@Data
public class KafkaModel extends BaseProductModel {

    /**
     * 名称
     */
    private String name;

    /**
     * 分区
     */
    private Integer partition;

    /**
     * 副本
     */
    private Integer replication;

    /**
     * 保留时间（天）
     */
    private Integer retainTime;

    /**
     * 数据流量
     */
    private String dataFlow;

    /**
     * 数据存储总量
     */
    private String dataStorageTotal;

    /**
     * 申请时长
     */
    private String applyTime;
}
