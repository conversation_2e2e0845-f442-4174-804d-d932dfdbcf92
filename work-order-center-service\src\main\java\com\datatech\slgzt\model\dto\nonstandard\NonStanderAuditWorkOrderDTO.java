package com.datatech.slgzt.model.dto.nonstandard;

import com.datatech.slgzt.model.file.UploadFileModel;
import com.datatech.slgzt.model.nostander.ArchivedIpModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 非标工单审核入参
 *
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/3/14
 */

@Data
public class NonStanderAuditWorkOrderDTO {

    @NotNull(message = "操作订单id不能为空")
    private String orderId;

    /**
     * 审核意见
     */
    private String auditAdvice;

    /**
     * 当前节点标识
     */
    @NotNull(message = "当前节点标识不能为空")
    private String currentNodeCode;

    /**
     * 审核操作状态 1：通过 0：驳回 2：关单
     */
    @NotNull(message = "审核操作状态不能为空")
    private Integer activiteStatus;

    /**
     * 二级业务领导id
     */
    private Long businessDepartLeaderId;

    /**
     * 驳回到指定节点标识
     */
    private String nodeCode;

    /**
     * 产品合计成本费用
     */
    private BigDecimal productTotalCost;
    /**
     * 产品申请方案附件 的json
     */
    private List<UploadFileModel> productApplyFile;
    /**
     * 备注
     */
    private String remark;

    /**
     * 产品归档IP列表
     */
    private List<ArchivedIpModel> modelList;
}

