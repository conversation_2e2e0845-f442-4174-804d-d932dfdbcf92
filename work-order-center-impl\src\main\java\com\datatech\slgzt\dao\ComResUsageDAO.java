package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.ComResUsageMapper;
import com.datatech.slgzt.dao.model.report.ComResUsageDO;
import com.datatech.slgzt.model.query.ComResUsageQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月26日 14:01:06
 */
@Repository
public class ComResUsageDAO {

    @Resource
    private ComResUsageMapper mapper;




    public List<ComResUsageDO> list(ComResUsageQuery query){
        return mapper.selectList(Wrappers.<ComResUsageDO>lambdaQuery()
                                         .eq(ObjNullUtils.isNotNull(query.getRegionId()),ComResUsageDO::getRegionId,query.getRegionId())
                                         .in(ObjNullUtils.isNotNull(query.getRegionIds()),ComResUsageDO::getRegionId,query.getRegionIds())
                                         .ge(ComResUsageDO::getCreatedTime,query.getStartTime())
                                         .le(ComResUsageDO::getCreatedTime,query.getEndTime()));
    }
}
