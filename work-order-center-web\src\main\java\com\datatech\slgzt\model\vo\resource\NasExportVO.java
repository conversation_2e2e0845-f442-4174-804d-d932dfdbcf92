package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: nas导出
 * @author: LK
 * @create: 2025-06-12 09:45
 **/
@Data
public class NasExportVO {

    @ExcelExportHeader(value = "NAS名称")
    private String deviceName;

    @ExcelExportHeader(value = "路径")
    private String path;

    @ExcelExportHeader(value = "存储大小（GB）")
    public String spec;

    @ExcelExportHeader(value = "申请时长")
    private String applyTime;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSysName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String resourcePoolName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "开通时间")
    private String resourceApplyTime;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;

    @ExcelExportHeader(value = "计费号")
    private String billId;

    @ExcelExportHeader(value = "状态")
    private String deviceStatus;

    @ExcelExportHeader(value = "申请人")
    private String applyUserName;
}
