package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EipIpV6Param;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.resource.api.ipv6.dto.CreateIpv6RcDto;
import com.cloud.resource.api.ipv6.dto.DeleteIpv6RcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 弹性公网IPV6接口适配
 */
@Component
@Slf4j
public class EipIpV6MgAdapter extends BaseNorthInterfaceAdapter {

    /**
     * 创建弹性公网IPV6
     */
    public TaskVO createEipIpV6(String taskId, Integer taskSource) {
        log.info("createEipIpV6 start");
        CreateIpv6RcDto createIpv6RcDto = generateCreateIpv6RcDto(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateEipIpV6(),
                null,
                createIpv6RcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        this.checkResultThrowExceptionIfFail(tasksVoResult, "create eipIpV6");
        return tasksVoResult.getEntity();
    }

    /**
     * 删除弹性公网IPV6
     */
    public TaskVO deleteEipIpV6(String taskId, Integer taskSource) {
        log.info("deleteEipIpV6 start");
        DeleteIpv6RcDto deleteIpv6RcDto = generateDeleteEipIpV6(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteEipIpV6(),
                null,
                deleteIpv6RcDto,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "delete eipIpV6");
        return tasksVoResult.getEntity();
    }

    /**
     * 生成北向接口删除弹性公网IPV6的参数
     * @param layoutParam 编排参数
     */
    private DeleteIpv6RcDto generateDeleteEipIpV6(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam productOrderParam = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_IPV6_DELETE.getCode());
        EipIpV6Param eipIpV6Param = JSONObject.parseObject(productOrderParam.getAttrs(), EipIpV6Param.class);
        // 参数设置
        DeleteIpv6RcDto deleteIpv6RcDto = new DeleteIpv6RcDto();
        deleteIpv6RcDto.setRegionCode(eipIpV6Param.getRegionCode());
        deleteIpv6RcDto.setInstanceId(eipIpV6Param.getInstanceId());
        deleteIpv6RcDto.setTenantId(eipIpV6Param.getTenantId());
        deleteIpv6RcDto.setSystemSource(deleteIpv6RcDto.getSystemSource());
        return deleteIpv6RcDto;
    }

    /**
     * 生成北向接口创建弹性公网IPV6的参数
     * @param layoutParam 编排参数
     */
    private CreateIpv6RcDto generateCreateIpv6RcDto(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam productOrderParam = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.EIP_IPV6_CREATE.getCode());
        EipIpV6Param eipIpV6Param = JSONObject.parseObject(productOrderParam.getAttrs(), EipIpV6Param.class);
        // 参数设置
        CreateIpv6RcDto createIpv6RcDto = new CreateIpv6RcDto();
        createIpv6RcDto.setTenantId(eipIpV6Param.getTenantId());
        createIpv6RcDto.setRegionCode(eipIpV6Param.getRegionCode());
        createIpv6RcDto.setOrderId(productOrderParam.getProductOrderId());
        createIpv6RcDto.setIpv6Name(eipIpV6Param.getIpv6Name());
        createIpv6RcDto.setBandwidth(new Long(eipIpV6Param.getBandwidth()));
        createIpv6RcDto.setIpv6Addresses(eipIpV6Param.getIpv6Addresses());
        createIpv6RcDto.setGateWay(eipIpV6Param.getGateWay());
        createIpv6RcDto.setServicesType(eipIpV6Param.getServicesType());
        createIpv6RcDto.setServersId(eipIpV6Param.getServiceId());
        createIpv6RcDto.setgId(eipIpV6Param.getGId());
        return createIpv6RcDto;
    }
}
