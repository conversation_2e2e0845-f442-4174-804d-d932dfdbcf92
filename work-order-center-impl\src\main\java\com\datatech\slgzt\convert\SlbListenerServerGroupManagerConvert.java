package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.SlbListenerServerGroupDO;
import com.datatech.slgzt.model.TaskStatusExt;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * SLB监听器服务组转换器
 */
@Mapper(componentModel = "spring")
public interface SlbListenerServerGroupManagerConvert {

    /**
     * DTO转DO
     */
    @Mapping(target = "serverInfoModelList", source = "serverInfoModelList", qualifiedByName = "modelToString")
    @Mapping(target = "taskStatusExt", source = "taskStatusExt", qualifiedByName = "taskStatusExt")
    SlbListenerServerGroupDO dto2do(SlbListenerServerGroupDTO dto);
    
    /**
     * DO转DTO
     */
    @Mapping(target = "serverInfoModelList", source = "serverInfoModelList", qualifiedByName = "stringToServerInfoModel")
    @Mapping(target = "taskStatusExt", source = "taskStatusExt", qualifiedByName = "taskStatusExt")
    SlbListenerServerGroupDTO do2dto(SlbListenerServerGroupDO entity);

    /**
     * 将Model对象转换为JSON字符串
     */
    @Named("modelToString")
    default String modelToString(List<SlbListenerServerGroupDTO.SlbListenerServerInfoModel> model) {
        if (model == null) {
            return null;
        }
        return JSON.toJSONString(model);
    }

    /**
     * 将JSON字符串转换为ServerInfoModel列表
     */
    @Named("stringToServerInfoModel")
    default List<SlbListenerServerGroupDTO.SlbListenerServerInfoModel> stringToServerInfoModel(String json) {
        if (json == null) {
            return null;
        }
        return JSON.parseArray(json, SlbListenerServerGroupDTO.SlbListenerServerInfoModel.class);
    }

    /**
     * 将TaskStatusExt对象转换为JSON字符串
     */
    @Named("taskStatusExt")
    default String taskStatusExt(TaskStatusExt taskStatusExt) {
        if (taskStatusExt == null) {
            return null;
        }
        return JSON.toJSONString(taskStatusExt);
    }

    /**
     * 将JSON字符串转换为TaskStatusExt对象
     *
     */
    @Named("taskStatusExt")
    default TaskStatusExt taskStatusExt(String json) {
        if (json == null) {
            return null;
        }
        return JSON.parseObject(json, TaskStatusExt.class);
    }

} 