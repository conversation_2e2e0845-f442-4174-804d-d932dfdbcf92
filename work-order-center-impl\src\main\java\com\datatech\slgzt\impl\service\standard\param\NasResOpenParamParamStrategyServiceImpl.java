package com.datatech.slgzt.impl.service.standard.param;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.NasModel;
import com.datatech.slgzt.model.nostander.VpnModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.standard.ResOpenParamStrategyService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: vpn创建参数封装
 * @author: LK
 * @create: 2025-06-10 10:26
 **/
@Service
public class NasResOpenParamParamStrategyServiceImpl implements ResOpenParamStrategyService {

    @Override
    public List<ResOpenReqModel.ProductOrder> assembleParam(ResOpenOpm opm) {
        ArrayList<ResOpenReqModel.ProductOrder> productOrders = Lists.newArrayList();
        List<NasModel> nasModelList = opm.getNasModelList();
        for(NasModel nasModel : nasModelList) {
            ResOpenReqModel.ProductOrder nasProductOrder = new ResOpenReqModel.ProductOrder();
            nasProductOrder.setProductOrderId(nasModel.getId().toString());
            nasProductOrder.setProductOrderType("NAS_CREATE");
            nasProductOrder.setProductType("nas_create");
            nasProductOrder.setSubOrderId(opm.getSubOrderId());
            nasProductOrder.setGId(opm.getGId());
            ResOpenReqModel.Attrs nasAttrs = new ResOpenReqModel.Attrs();
            nasAttrs.setGId(opm.getGId());
            nasAttrs.setRegionCode(nasModel.getRegionCode());
            nasAttrs.setBillId(opm.getAccount());
            nasAttrs.setTenantId(opm.getTenantId());
            nasAttrs.setName(nasModel.getName());
            nasAttrs.setPath(nasModel.getPath());
            nasAttrs.setStorageSize(nasModel.getStorageSize());
            nasProductOrder.setAttrs(nasAttrs);
            productOrders.add(nasProductOrder);
        }
        return productOrders;
    }

    @Override
    public ProductTypeEnum register() {
        return ProductTypeEnum.NAS;
    }
}
