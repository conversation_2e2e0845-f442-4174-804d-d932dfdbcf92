package com.cloud.marginal.layoutcenter.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.edge.TaskStatusEnum;
import com.cloud.marginal.enums.layout.*;
import com.cloud.marginal.layoutcenter.config.Taskconfig;
import com.cloud.marginal.layoutcenter.factory.LaoutServiceFactory;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.EcsParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface.TaskMgAdapter;
import com.cloud.marginal.layoutcenter.scheduler.MonitorTaskHandler;
import com.cloud.marginal.layoutcenter.service.LayoutService;
import com.cloud.marginal.layoutcenter.service.layoutdb.*;
import com.cloud.marginal.mapper.edge.CloudPlatformMapper;
import com.cloud.marginal.mapper.layout.LayoutParamMapper;
import com.cloud.marginal.mapper.layout.LayoutTaskMapper;
import com.cloud.marginal.mapper.layout.LayoutTaskNodeMapper;
import com.cloud.marginal.mapper.layout.TasksRelMapper;
import com.cloud.marginal.model.dto.edge.CreatePortDTO;
import com.cloud.marginal.model.dto.layout.LayoutOrder;
import com.cloud.marginal.model.dto.layout.OperationData;
import com.cloud.marginal.model.dto.layout.Operations;
import com.cloud.marginal.model.dto.layout.ProductOrder;
import com.cloud.marginal.model.entity.edge.CloudPlatform;
import com.cloud.marginal.model.entity.layout.*;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import com.cloud.marginal.model.vo.layout.RelTaskVO;
import com.cloud.marginal.utils.SpringUtil;
import com.cloud.marginal.utils.UuidUtil;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service("layoutServiceImpl")
@Slf4j
public class LayoutServiceImpl implements LayoutService {

    @Resource
    private LayoutTaskDefService layoutTaskDefService;

    @Resource
    private TasksRelDefService tasksRelDefService;

    @Resource
    private TaskApiDefService taskApiDefService;

    @Resource
    private TaskParamDefService taskParamDefService;

    @Resource
    private LayoutTaskNodeService layoutTaskNodeService;

    @Resource
    private TasksRelService tasksRelService;

    @Resource
    private LayoutApiService layoutApiService;

    @Resource
    private TaskNodeApiService taskNodeApiService;

    @Resource
    private LayoutParamService layoutParamService;

    @Resource
    private TaskParamService taskParamService;

    @Resource
    private LayoutTaskService layoutTaskService;

    @Resource
    private LayoutTaskMapper layoutTaskMapper;

    @Resource
    private LayoutTaskNodeMapper layoutTaskNodeMapper;

//    @Resource
//    private MonitorTaskHandler monitorTaskHandler;

    @Autowired
    @Qualifier(value = "asyncServiceExecutor")
    private Executor asyncServiceExecutor;

    @Resource
    private LayoutParamMapper layoutParamMapper;

    @Resource
    private TaskMgAdapter taskMgAdapter;

    @Resource
    private LayoutService layoutServiceImpl;

    @Resource
    private TasksRelMapper tasksRelMapper;

    @Resource
    private LaoutServiceFactory laoutServiceFactory;


    @Resource
    private CloudPlatformMapper cloudPlatformMapper;

    @Resource
    private Taskconfig taskconfig;

    @Override
    public CecResult layoutTaskInit(LayoutOrder layoutOrder) {
        log.info("task layout init start");
        Date curDate = new Date();
        // todo 主任务
        List<LayoutTask> mainTaskList = new ArrayList<>();
        List<LayoutTaskNode> taskNodeList = new ArrayList<>();
        List<TasksRel> tasksRelList = new ArrayList<>();
        List<LayoutApi> layoutApiList = new ArrayList<>();
        List<TaskNodeApi> taskNodeApiList = new ArrayList<>();
        List<LayoutParam> layoutParamList = new ArrayList<>();
        List<TaskParam> taskParamList = new ArrayList<>();

        // 查询云平台
        CloudPlatform platform = cloudPlatformMapper.selectByRegionCode(layoutOrder.getRegionCode());


        // 临时兼容
        if (StringUtils.isEmpty(layoutOrder.getProductOrders().get(0).getSubOrderId()) && !StringUtils.isEmpty((layoutOrder.getSubOrderId()))) {
            layoutOrder.getProductOrders().forEach(productOrder -> productOrder.setSubOrderId(layoutOrder.getSubOrderId()));
        }
        // 按照子订单id进行分组
        Map<String, List<ProductOrder>> collect = layoutOrder.getProductOrders().stream().collect(Collectors.groupingBy(ProductOrder::getSubOrderId));
        //todo subOrderId 原来工单id 现在时非标id
        collect.forEach((subOrderId, productOrderList) -> {
            // 深拷贝
            LayoutOrder layoutOrderCopy = JSONObject.parseObject(JSONObject.toJSONString(layoutOrder), LayoutOrder.class);
            layoutOrderCopy.setSubOrderId(subOrderId);
            layoutOrderCopy.setProductOrders(productOrderList);

            // 主任务
            LayoutTask mainTask = this.generateMainTask(curDate, layoutOrderCopy);
            mainTaskList.add(mainTask);

            // 子任务列表
            List<LayoutTaskNode> taskNodes = new ArrayList<>();
            productOrderList.forEach(productOrder -> {
                // 产品订单生成子任务
                LayoutTaskNode productTaskNode = this.generateProductTaskNode(productOrder, curDate, layoutOrderCopy.getBusinessCode(), mainTask.getId());
                taskNodes.add(productTaskNode);

                // 创建操作类型任务
                List<LayoutTaskNode> productTaskNodes = this.generateProductOperationsTaskNode(productOrder, curDate, layoutOrderCopy.getBusinessCode(), mainTask.getId());
                taskNodes.addAll(productTaskNodes);

                // 添加满足条件指定任务
                for (Map.Entry<String, List<String>> stringListEntry : taskconfig.getCustomAddTask().entrySet()) {
                    if (stringListEntry.getKey().equals(platform.getCode()) && productOrder.getProductOrderType().equals(ProductOrderTypeEnum.ECS_CREATE.getCode())) {
                        for (String taskCode : stringListEntry.getValue()) {
                            LayoutTaskNode attrTaskNode = this.generateProductAttrTaskNode(productOrder,curDate, layoutOrderCopy.getBusinessCode(), mainTask.getId(),taskCode);
                            if(null!=attrTaskNode) {
                                taskNodes.add(attrTaskNode);
                            }
                        }
                    }
                }
            });
            // 创建一个主任务节点加入到任务节点集合里就能统一创建任务依赖关系。
            // 创建完依赖关系和任务与api关系后删除主任务节点，主任务是存主任务表的
            LayoutTaskNode mainTaskNode = this.generateMainTaskNode(mainTask.getId());
            taskNodes.add(mainTaskNode);

            // 创建附件任务，如通知任务(示例：创建A任务依赖B与C任务存在)
            List<LayoutTaskNode> followTaskNodeList = this.generateFollowTaskNode(taskNodes, layoutOrderCopy.getBusinessCode(), mainTask.getId(), curDate);
            // 忽略满足条件指定任务
            for (Map.Entry<String, List<String>> stringListEntry : taskconfig.getCustomDelTask().entrySet()) {
                if (stringListEntry.getKey().equals(platform.getCode())) {
                    for (String taskCode : stringListEntry.getValue()) {
                        followTaskNodeList.removeIf(layoutTaskNode -> layoutTaskNode.getTaskCode().equals(taskCode));
                    }
                }
            }
            taskNodes.addAll(followTaskNodeList);

            // 获取模板下任务与任务的关联(示例：执行A任务依赖B与C任务执行完成)
            List<TaskRel> taskCodeRelList = tasksRelDefService.getTaskRelListByTemplateCode(layoutOrderCopy.getBusinessCode());
            // 只保留满足条件的关联指定任务
            for (Map.Entry<String, List<String>> stringListEntry : taskconfig.getCustomAddRel().entrySet()) {
                if (!stringListEntry.getKey().equals(platform.getCode())) {
                    for (String taskRelDefId : stringListEntry.getValue()) {
                        taskCodeRelList.removeIf(taskRel -> taskRel.getTaskRelDefId().equals(taskRelDefId));
                    }
                }
            }

            List<TasksRel> tasksRels = this.generateTasksRel(curDate, taskNodes, taskCodeRelList, platform, layoutOrder.getBusinessCode());
            tasksRelList.addAll(tasksRels);

            // 生成api和api与任务节点关联关系
            ApiAndTaskNodeApi apiAndTaskNodeApi = this.generateApiAndApiTaskNodeRel(curDate, taskNodes, layoutOrderCopy.getBusinessCode());
            List<LayoutApi> layoutApis = this.layoutApiExtsToLayoutApis(apiAndTaskNodeApi.getLayoutApis());
            List<TaskNodeApi> taskNodeApis = apiAndTaskNodeApi.getTaskNodeApis();
            layoutApiList.addAll(layoutApis);
            taskNodeApiList.addAll(taskNodeApis);

            // 生成编排参数和编排参数与子任务的关联关系
            LayoutParamDef layoutParamDef = taskParamDefService.getLayoutParamDefByTemplateCode(layoutOrderCopy.getBusinessCode());
            LayoutParamAndMainTaskParamRel layoutParamAndMainTaskParamRel = this.generateLayoutParamAndSubTaskRel(curDate, mainTask, layoutParamDef, layoutOrderCopy);
            layoutParamList.add(layoutParamAndMainTaskParamRel.getLayoutParam());
            taskParamList.add(layoutParamAndMainTaskParamRel.getMainTaskParamRel());

            taskNodes.remove(mainTaskNode);
            taskNodeList.addAll(taskNodes);
        });

        // 入库
        layoutServiceImpl.initSave(mainTaskList, taskNodeList, tasksRelList, layoutApiList, taskNodeApiList, layoutParamList, taskParamList);

        // 异步递归执行,只执行1个,超过则通过定时任务分片执行
        asyncServiceExecutor.execute(() -> layoutServiceImpl.layoutTaskExecut(mainTaskList.get(0).getId()));

        log.info("task layout init end");
        return CecResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initSave(List<LayoutTask> mainTaskList, List<LayoutTaskNode> taskNodeList, List<TasksRel> tasksRelList, List<LayoutApi> layoutApiList, List<TaskNodeApi> taskNodeApiList, List<LayoutParam> layoutParamList, List<TaskParam> taskParamList) {
        layoutTaskService.saveBatch(mainTaskList);
        layoutTaskNodeService.saveBatch(taskNodeList);
        tasksRelService.saveBatch(tasksRelList);
        layoutApiService.saveBatch(layoutApiList);
        taskNodeApiService.saveBatch(taskNodeApiList);
        layoutParamService.saveBatch(layoutParamList);
        taskParamService.saveBatch(taskParamList);
    }


    private List<LayoutTaskNode> generateProductOperationsTaskNode(ProductOrder productOrder, Date curDate, String templateCode, String mainTaskId) {
        //操作类型认为  如绑定 挂载
        List<Operations> operations = productOrder.getOperations();

        List<LayoutTaskNode> layoutTaskNodes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(operations)) {
            for (Operations operation : operations) {
                List<OperationData> operationDataList = operation.getOperationDataList();
                for (OperationData operationData : operationDataList) {
                    LayoutTaskNode copylayoutTaskNode = new LayoutTaskNode();
                    LayoutTaskNode layoutTaskNode = layoutTaskDefService.generateTaskNodeByTemplateCodeAndTaskCode(templateCode, operation.getOperation());
                    BeanUtils.copyProperties(layoutTaskNode, copylayoutTaskNode);
                    copylayoutTaskNode.setMasterTaskId(mainTaskId)
                            .setId(UuidUtil.generateId())
                            .setCreatedTime(curDate)
                            .setStatus(StatusEnum.VALID.getCode())
                            .setOrderId(productOrder.getProductOrderId())
                            .setState(StateEnum.UNPROCESSED)
                            // 需要挂载绑定的资源id
                            .setResourceId(operationData.getOperationId())
                            .setRevision(0);
                    layoutTaskNodes.add(copylayoutTaskNode);
                }
            }
        }
        return layoutTaskNodes;
    }

    private LayoutTaskNode generateProductAttrTaskNode(ProductOrder productOrder, Date curDate, String templateCode, String mainTaskId, String taskCode) {
        //云主机创建依赖网卡创建
        LayoutTaskNode copyLayoutTaskNode = new LayoutTaskNode();
        if (taskCode.equals(ProductOrderTypeEnum.PORT_CREATE.getCode())) {
            HashMap<String, Object> attrs = productOrder.getAttrs();
//            String json = JSON.toJSONString(attrs.get("nics"));
//            log.info(JSON.toJSONString(attrs.get("nics")));
//            List<CreatePortDTO.FixedIp> fixedIps = JSONObject.parseArray(json, CreatePortDTO.FixedIp.class);
//            CreatePortDTO.FixedIp fixedIp = fixedIps.get(0);
//            if(null==fixedIp.getSubnetId()||fixedIp.getSubnetId().isEmpty()){
//                return null;
//            }
//            log.info(fixedIp.getSubnetId());
            //网络判断调整
            EcsParam.VpcInfo vpcInfo = JSON.parseObject(JSONObject.toJSONString(attrs.get("vpcInfo")), EcsParam.VpcInfo.class);
            if(Objects.isNull(vpcInfo)||vpcInfo.getNetworkIds().isEmpty()){
                return null;
            }
            log.info(vpcInfo.getNetworkIds().toString());
        }
        LayoutTaskNode layoutTaskNode = layoutTaskDefService.generateTaskNodeByTemplateCodeAndTaskCode(templateCode, taskCode);
        BeanUtils.copyProperties(layoutTaskNode, copyLayoutTaskNode);
        copyLayoutTaskNode.setMasterTaskId(mainTaskId)
                .setId(UuidUtil.generateId())
                .setCreatedTime(curDate)
                .setStatus(StatusEnum.VALID.getCode())
                .setOrderId(productOrder.getSubOrderId())
                .setState(StateEnum.UNPROCESSED)
                .setRevision(0);
        return copyLayoutTaskNode;
    }

    /**
     * 生成主任务节点，只用作增加主任务的依赖关系
     *
     * @param id 主任务id
     */
    private LayoutTaskNode generateMainTaskNode(String id) {
        LayoutTaskNode mainTaskNode = new LayoutTaskNode();
        mainTaskNode.setId(id);
        mainTaskNode.setTaskCode("MASK_TASK");
        return mainTaskNode;
    }

    /**
     * 把List<LayoutApiExt>转成List<LayoutApi>
     */
    private List<LayoutApi> layoutApiExtsToLayoutApis(List<LayoutApiExt> layoutApis) {
        List<LayoutApi> result = new ArrayList<>();
        layoutApis.forEach(layoutApiExt -> {
            boolean add = result.add(layoutApiExt);
        });
        return result;
    }

    /**
     * 生成编排参数和编排参数与子任务的关联关系
     *
     * @param curDate        当前时间
     * @param mainTask       主任务
     * @param layoutParamDef 编排参数定义
     * @param layoutOrder    编排订单
     */
    private LayoutParamAndMainTaskParamRel generateLayoutParamAndSubTaskRel(Date curDate, LayoutTask mainTask, LayoutParamDef layoutParamDef, LayoutOrder layoutOrder) {
        String paramJsonStr = JSONObject.toJSONString(layoutOrder);
        LayoutParamAndMainTaskParamRel layoutParamAndSubTaskRel = new LayoutParamAndMainTaskParamRel();
        LayoutParam layoutParam = new LayoutParam().setId(UuidUtil.generateId())
                .setParamName(layoutParamDef == null ? "DEFAULT" : layoutParamDef.getParamName())
                .setParamCode(layoutParamDef == null ? "COMMON" : layoutParamDef.getParamCode())
                .setBeanName(layoutParamDef == null ? "LayoutOrder" : layoutParamDef.getBeanName())
                .setCreatedTime(curDate)
                .setRevision(0)
                .setStatus(StatusEnum.VALID.getCode())
                .setParamValue(paramJsonStr);
        TaskParam mainTaskParamRels = new TaskParam().setId(UuidUtil.generateId())
                .setParamId(layoutParam.getId())
                .setMasterTaskId(mainTask.getId())
                .setCreatedTime(curDate)
                .setStatus(StatusEnum.VALID.getCode());
        layoutParamAndSubTaskRel.setLayoutParam(layoutParam);
        layoutParamAndSubTaskRel.setMainTaskParamRel(mainTaskParamRels);
        return layoutParamAndSubTaskRel;
    }

    /**
     * 生成api和api与任务节点关联关系
     *
     * @param curDate      当前时间
     * @param taskNodes    任务节点集合
     * @param templateCode 模板编号
     */
    private ApiAndTaskNodeApi generateApiAndApiTaskNodeRel(Date curDate, List<LayoutTaskNode> taskNodes, String templateCode) {
        List<LayoutApiExt> layoutApis = new ArrayList<>();
        List<TaskNodeApi> taskNodeApis = new ArrayList<>();

        taskNodes.forEach(taskNode -> {
            LayoutApiExt copyLayoutApi = new LayoutApiExt();
            LayoutApiExt layoutApi = taskApiDefService.generateApi(templateCode, taskNode.getTaskCode());
            if (layoutApi != null) {
                BeanUtils.copyProperties(layoutApi, copyLayoutApi);
                taskNode.setMonitorCount(copyLayoutApi.getMonitorCount());
                taskNode.setMonitorInterval(copyLayoutApi.getMonitorInterval());
                copyLayoutApi.setId(UuidUtil.generateId()).setCreatedTime(curDate).setStatus(StatusEnum.VALID.getCode());
                layoutApis.add(copyLayoutApi);
                TaskNodeApi taskNodeApi = new TaskNodeApi()
                        .setId(UuidUtil.generateId())
                        .setTaskId(taskNode.getId())
                        .setApiId(copyLayoutApi.getId())
                        .setCreatedTime(curDate)
                        .setStatus(StatusEnum.VALID.getCode());
                taskNodeApis.add(taskNodeApi);
            }
        });
        ApiAndTaskNodeApi apiAndTaskNodeApi = new ApiAndTaskNodeApi();
        apiAndTaskNodeApi.setLayoutApis(layoutApis);
        apiAndTaskNodeApi.setTaskNodeApis(taskNodeApis);
        return apiAndTaskNodeApi;
    }

    /**
     * 生成任务关联关系(执行时依赖)
     *
     * @param curDate         当前时间
     * @param taskNodes       任务列表
     * @param taskCodeRelList 任务编码关联关系
     */
    private List<TasksRel> generateTasksRel(Date curDate, List<LayoutTaskNode> taskNodes, List<TaskRel> taskCodeRelList, CloudPlatform platform, String businessCode) {
        List<String> volumeExist = new ArrayList<String>();

        List<String> noticeExist = new ArrayList<String>();


        List<TasksRel> tasksRels = new ArrayList<>();
        //主任务依赖所有挂载任务
        taskNodes.forEach(mainTask -> {
            if ("MASK_NOTICE".equals(mainTask.getTaskCode())) {
                // 找到所有挂载任务（EVS_MOUNT）并建立依赖
                taskNodes.stream()
                        .filter(task -> ProductOrderTypeEnum.EVS_MOUNT.toString().equals(task.getTaskCode()))
                        .forEach(mountTask -> {
                            TasksRel rel = new TasksRel()
                                    .setId(UuidUtil.generateId())
                                    .setTaskId(mainTask.getId())      // 主任务（MASK_NOTICE）
                                    .setRelTaskId(mountTask.getId())  // 挂载任务（EVS_MOUNT）
                                    .setStatus(StatusEnum.VALID.getCode())
                                    .setCreatedTime(curDate)
                                    .setRelType(TaskRelTypeEnum.EXECUTE)
                                    .setRelState(24)
                                    .setDescription("主任务依赖云硬盘挂载");
                            tasksRels.add(rel);
                        });
            }
        });
        //华三技术栈变更任务特殊处理，云硬盘变更依赖云主机变更完成
        if (businessCode.contains("MODIFY") && "HWS".equals(platform.getType())) {
            taskNodes.forEach(evsModifyTask -> {
                if ("EVS_MODIFY".equals(evsModifyTask.getTaskCode())) {
                    // 找到云主机变更规格
                    taskNodes.stream()
                            .filter(task -> ProductOrderTypeEnum.ECS_MODIFY.toString().equals(task.getTaskCode()))
                            .forEach(ecsModifyTask -> {
                                TasksRel rel = new TasksRel()
                                        .setId(UuidUtil.generateId())
                                        .setTaskId(evsModifyTask.getId())
                                        .setRelTaskId(ecsModifyTask.getId())
                                        .setStatus(StatusEnum.VALID.getCode())
                                        .setCreatedTime(curDate)
                                        .setRelType(TaskRelTypeEnum.EXECUTE)
                                        .setRelState(24)
                                        .setDescription("云硬盘变更任务依赖云主机变更");
                                tasksRels.add(rel);
                            });
                }
            });
        }
        taskNodes.forEach(layoutTaskNode -> {
            Boolean flag = false;
            for (TaskRel taskRel : taskCodeRelList) {
                if (layoutTaskNode.getTaskCode().equals(taskRel.getTaskCode())) {
                    for (LayoutTaskNode taskNode : taskNodes) {
                        if (taskNode.getTaskCode().equals(taskRel.getRelTaskCode()) && taskRel.getRelType().equals(TaskRelTypeEnum.EXECUTE)) {

                            // 针对一个云主机挂载/卸载多块盘的,暂时写死处理
                            if (ProductOrderTypeEnum.EVS_MOUNT.toString().equals(layoutTaskNode.getTaskCode()) ||
                                    ProductOrderTypeEnum.EVS_DELETE.toString().equals(layoutTaskNode.getTaskCode())) {
                                if (ProductOrderTypeEnum.EVS_CREATE.toString().equals(taskNode.getTaskCode()) ||
                                        "EVS_UNMOUNT".equals(taskNode.getTaskCode())) {
                                    if (flag || volumeExist.contains(taskNode.getId())) {
                                        continue;
                                    }
                                    // 1.限制挂盘只能依赖一个盘创建
                                    // 2.限制盘退订只能依赖一个盘卸载
                                    flag = true;
                                    // 1.限制多个挂盘不能重复依赖同一个盘创建
                                    // 2.限制多个盘退订不能重复依赖同一个盘卸载
                                    volumeExist.add(taskNode.getId());
                                }
                            }

                            if ("EVS_CREATE_NOTICE".equals(layoutTaskNode.getTaskCode()) ||
                                    "EVS_DELETE_NOTICE".equals(layoutTaskNode.getTaskCode())) {
                                if (ProductOrderTypeEnum.EVS_CREATE.toString().equals(taskNode.getTaskCode()) ||
                                        ProductOrderTypeEnum.EVS_DELETE.toString().equals(taskNode.getTaskCode())) {
                                    if (flag || noticeExist.contains(taskNode.getId())) {
                                        continue;
                                    }
                                    // 1.限制创建盘通知只能依赖一个盘创建
                                    // 2.限制删除盘通知只能依赖一个盘删除
                                    flag = true;
                                    // 1.限制多个创建盘通知不能重复依赖同一个盘创建
                                    // 1.限制多个删除盘通知不能重复依赖同一个盘删除
                                    noticeExist.add(taskNode.getId());
                                }
                            }

                            TasksRel tasksRel = new TasksRel().setId(UuidUtil.generateId())
                                    .setTaskId(layoutTaskNode.getId()) // 节点任务
                                    .setRelTaskId(taskNode.getId())  //依赖的任务
                                    .setStatus(StatusEnum.VALID.getCode())
                                    .setCreatedTime(curDate)
                                    .setRelType(taskRel.getRelType())
                                    .setDescription(taskRel.getDescription())
                                    .setRelState(taskRel.getRelState());
                            tasksRels.add(tasksRel);
                            // 删除
                            break;
                        }
                    }
                }
            }
        });
        return tasksRels;
    }

    /**
     * 生成产品订单任务
     *
     * @param productOrder 产品订单
     * @param curDate      当前时间
     * @param templateCode 模板编码
     * @param mainTaskId   主任务id
     */
    private LayoutTaskNode generateProductTaskNode(ProductOrder productOrder, Date curDate, String templateCode, String mainTaskId) {
        LayoutTaskNode layoutTaskNode = layoutTaskDefService.generateTaskNodeByTemplateCodeAndTaskCode(templateCode, productOrder.getProductOrderType());
        LayoutTaskNode taskNode = BeanUtil.toBean(layoutTaskNode, LayoutTaskNode.class);
        taskNode.setMasterTaskId(mainTaskId)
                .setId(UuidUtil.generateId())
                .setCreatedTime(curDate)
                .setStatus(StatusEnum.VALID.getCode())
                .setOrderId(productOrder.getProductOrderId())
                .setState(StateEnum.UNPROCESSED)
                .setRevision(0)
                .setgId(productOrder.getgId());

        return taskNode;
    }

    /**
     * 生成附加任务(创建时依赖)
     *
     * @param layoutTaskNodeList 子任务列表
     * @param templateCode       模板编码
     */
    private List<LayoutTaskNode> generateFollowTaskNode(List<LayoutTaskNode> layoutTaskNodeList, String templateCode, String mainTaskId, Date curDate) {
        // 根据模板获取创建类型的依赖任务列表
        List<FollowTaskDef> followTaskDefList = layoutTaskDefService.getFollowTaskDefByTemplateCode(templateCode);
        List<LayoutTaskNode> followTaskNodeList = new ArrayList<>();

        // 按照任务id分组,一个创建任务(自动创建的任务)有一个或多个前置依赖
        Map<String, List<FollowTaskDef>> collect = followTaskDefList.stream()
                .collect(Collectors.groupingBy(FollowTaskDef::getFollowTaskId));
        collect.forEach((followTaskId, followTaskDefs) -> {
            // 前置依赖是否都满足,且如果满足的依赖有多个,则生成多个附加任务
            boolean followTaskIsCreatable = true;
            int count = 1;
            List<String> gids = new ArrayList<>();
            for (FollowTaskDef followTaskDef : followTaskDefs) {
                Map<String, Object> map = this.taskNodeIsContainFollowTaskDependencyTask(followTaskDef.getDependencyTaskCode(), layoutTaskNodeList);
                int i = Integer.parseInt(String.valueOf(map.get("count")));
                if (i == 0) {
                    followTaskIsCreatable = false;
                    break;
                }
                if (i > count) {
                    count = i;
                    gids = (List<String>) map.get("gids");
                }
            }
            if (followTaskIsCreatable) {
                LayoutTaskNode layoutTaskNode = layoutTaskDefService.generateTaskNodeByTemplateCodeAndTaskCode(templateCode, followTaskDefs.get(0).getFollowTaskCode());
                for (int i = 0; i < count; i++) {
                    LayoutTaskNode node = JSONObject.parseObject(JSONObject.toJSONString(layoutTaskNode), LayoutTaskNode.class);
                    node.setRevision(0)
                            .setId(UuidUtil.generateId())
                            .setMasterTaskId(mainTaskId)
                            .setCreatedTime(curDate)
                            .setStatus(StatusEnum.VALID.getCode())
                            .setState(StateEnum.UNPROCESSED);
                    if ("EVS_UNMOUNT".equals(node.getTaskCode()) && gids.size() == count) {
                        node.setgId(gids.get(i));
                    }
                    followTaskNodeList.add(node);
                }
            }
        });
        return followTaskNodeList;
    }


    /**
     * 判断任务节点中是否包含附加任务所依赖的任务
     *
     * @param dependencyTaskCode 附加任务所依赖的任务的编号
     * @param layoutTaskNodeList 任务节点集合
     * @return 包含返回true，否则false
     */
    private Map<String, Object> taskNodeIsContainFollowTaskDependencyTask(String dependencyTaskCode, List<LayoutTaskNode> layoutTaskNodeList) {
        Map<String, Object> map = new HashMap<>();
        List<String> gids = new ArrayList<>();
        int i = 0;
        for (LayoutTaskNode layoutTaskNode : layoutTaskNodeList) {
            if (dependencyTaskCode.equals(layoutTaskNode.getTaskCode())) {
                i++;
                if (!StringUtils.isEmpty(layoutTaskNode.getgId())) {
                    gids.add(layoutTaskNode.getgId());
                }
            }
        }
        map.put("count", i);
        map.put("gids", gids);
        return map;
    }

    /**
     * 生成主任务
     *
     * @param createDate  创建时间
     * @param layoutOrder 编排订单
     */
    private LayoutTask generateMainTask(Date createDate, LayoutOrder layoutOrder) {
        LayoutTask layoutTask = layoutTaskDefService.generateMainTaskByTemplateCode(layoutOrder.getBusinessCode());
        return layoutTask.setId(UuidUtil.generateId())
                .setSubOrderId(layoutOrder.getSubOrderId())
                .setCreatedTime(createDate)
                .setState(StateEnum.UNPROCESSED)
                .setStatus(StatusEnum.VALID.getCode())
                .setRevision(0)
                .setSourceExtType(layoutOrder.getSourceExtType())
                .setTaskSource(layoutOrder.getTaskSource());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CecResult layoutTaskExecut(String masterTaskId) {
        log.info("task layout execut start, masterTaskId is:{}", masterTaskId);
        // 获取主任务下状态未处理,无依赖，或状态失败，重试次数>0的任务列表
        List<LayoutTaskVO> tasks = new ArrayList<>();
        List<LayoutTaskVO> layoutTaskList = layoutTaskMapper.getTreatableTasks(masterTaskId);
        if (CollectionUtils.isEmpty(layoutTaskList)) {
            log.info("masterTaskId:{}No tasks to perform ", masterTaskId);
            return CecResult.success();
        }
        // 依赖关系处理
        for (LayoutTaskVO layoutTaskVO : layoutTaskList) {
            List<RelTaskVO> tasksRel = layoutTaskVO.getTasksRel();
            if (!CollectionUtils.isEmpty(tasksRel)) {
                // 有依赖,校验依赖任务是否满足条件，都满足则为无依赖
                for (Iterator<RelTaskVO> iter = tasksRel.iterator(); iter.hasNext(); ) {
                    RelTaskVO rel = iter.next();
                    Integer relState = rel.getRelState();
                    String state = rel.getState();
                    if (StateEnum.TIMEOUT.getName().equals(state)) {
                        state = StateEnum.ERROR.getName();
                    }
                    Integer retryCount = rel.getRetryCount();
                    List<String> names = StateEnum.getNames(relState);
                    for (String name : names) {
                        if (name.equals(state)) {
                            if (name.equals(StateEnum.ERROR.getName())) {
                                if (retryCount <= 0) {
                                    iter.remove();
                                }
                                continue;
                            }
                            iter.remove();
                        }
                    }
                }
                if (tasksRel.size() == 0) {
                    tasks.add(layoutTaskVO);
                }
            } else {
                // 无依赖
                tasks.add(layoutTaskVO);
            }
        }
        // 修改任务状态为处理中
        for (Iterator<LayoutTaskVO> iter = tasks.iterator(); iter.hasNext(); ) {
            LayoutTaskVO layoutTaskVO = iter.next();
            layoutTaskVO.setStartTime(new Date());
            layoutTaskVO.setState(StateEnum.PROCESSING);
            int update = updateTaskByRevision(layoutTaskVO);
            if (update == 0) {
                iter.remove();
            }
            // 主任务不执行
            if (TaskTypeEnum.MASTER.toString().equals(layoutTaskVO.getTaskType())) {
                iter.remove();
            }
        }
        // 异步并发执行
        for (LayoutTaskVO layoutTaskVO : tasks) {
            asyncServiceExecutor.execute(() -> layoutServiceImpl.taskExecute(layoutTaskVO));
        }
        log.info("task layout execut Issue end, masterTaskId is:{}", masterTaskId);
        return CecResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public void taskExecute(LayoutTaskVO layoutTaskVO) {
        log.info("{} task execut start, taskId is:{}", layoutTaskVO.getTaskCode(), layoutTaskVO.getId());
        // 反射执行
        String className = layoutTaskVO.getClassName();
        if (!StringUtils.isEmpty(className)) {
            try {
                Class<?> cla = Class.forName(layoutTaskVO.getClassName());
                Method method = cla.getDeclaredMethod(layoutTaskVO.getMethodName(), String.class, Integer.class);
                TaskVO resourceTaskVO = (TaskVO) method.invoke(SpringUtil.getBean(cla), layoutTaskVO.getId(), layoutTaskVO.getTaskSource());
                this.updateStateByCloudTask(layoutTaskVO, resourceTaskVO);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if (e.getMessage() != null && e.getMessage().contains("REDIS_LOCK_FAIL")) {
                    // 分布式锁获取失败,状态置为未处理
                    layoutTaskVO.setState(StateEnum.UNPROCESSED);
                    layoutTaskVO.setMessage("REDIS_LOCK_FAIL");
                } else {
                    // 任务状态更新为失败
                    layoutTaskVO.setState(StateEnum.ERROR);
                    layoutTaskVO.setMessage(e.getCause() == null ? e.getMessage() : e.getCause().getMessage());
                }

            }
        } else {
            // 走工厂模式
            try {
                TaskVO handler = laoutServiceFactory.getService(layoutTaskVO.getProductType()).handler(layoutTaskVO);
                this.updateStateByCloudTask(layoutTaskVO, handler);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if (e.getMessage() != null && e.getMessage().contains("REDIS_LOCK_FAIL")) {
                    // 分布式锁获取失败,状态置为未处理
                    layoutTaskVO.setState(StateEnum.UNPROCESSED);
                    layoutTaskVO.setMessage("REDIS_LOCK_FAIL");
                } else {
                    // 任务状态更新为失败
                    layoutTaskVO.setState(StateEnum.ERROR);
                    layoutTaskVO.setMessage(e.getCause() == null ? e.getMessage() : e.getCause().getMessage());
                }
            }
        }
        // 任务更新
        this.updateByCondition(layoutTaskVO);
        // 异步递归执行
        asyncServiceExecutor.execute(() -> layoutServiceImpl.layoutTaskExecut(layoutTaskVO.getMasterTaskId()));
        log.info("{} task execut end, taskId is:{}", layoutTaskVO.getTaskCode(), layoutTaskVO.getId());
    }

    @Override
    public void processingTaskExecute(LayoutTaskVO layoutTaskVO) {
        log.info("{} processing task compensate execut start, taskId is:{}", layoutTaskVO.getTaskCode(), layoutTaskVO.getId());
        String cloudTaskId = layoutTaskVO.getCloudTaskId();
        String orderId = layoutTaskVO.getOrderId();
        String taskType = layoutTaskVO.getTaskType();
        if (TaskTypeEnum.MASTER.toString().equals(taskType)) {
            layoutTaskVO.setState(StateEnum.ERROR);
        } else {
            // 底层任务为空 则需查询有订单id的任务是否有底层任务
            if (StringUtils.isEmpty(cloudTaskId)) {
                if (!StringUtils.isEmpty(orderId)) {
                    try {
                        TaskVO cloudTask = taskMgAdapter.getTaskByOrderId(orderId);
                        if (cloudTask != null && !StringUtils.isEmpty(cloudTask.getId())) {
                            layoutTaskVO.setCloudTaskId(cloudTask.getId());
                            layoutTaskVO.setResourceId(cloudTask.getResourceId());
                            layoutTaskVO.setInstanceId(cloudTask.getInstanceId());
                            layoutTaskVO.setState(StateEnum.EXECUTING);
                        } else {
                            layoutTaskVO.setState(StateEnum.ERROR);
                            layoutTaskVO.setMessage("需人工介入,检查请求是否已到底层");
                        }
                    } catch (Exception e) {
                        layoutTaskVO.setState(StateEnum.ERROR);
                        layoutTaskVO.setMessage(e.getCause() == null ? e.getMessage() : e.getCause().getMessage());
                        layoutTaskVO.setRetryCount(0);
                    }

                } else {
                    layoutTaskVO.setState(StateEnum.UNPROCESSED);
                }
            } else {
                // 底层任务不为空则修改任务状态为执行中
                layoutTaskVO.setState(StateEnum.EXECUTING);
            }
        }
        this.updateByCondition(layoutTaskVO);
        log.info("{} processing task compensate execut end, taskId is:{}", layoutTaskVO.getTaskCode(), layoutTaskVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskMonitor(LayoutTaskVO layoutTaskVO) {
        log.info("{}  task monitor  start, taskId is:{}", layoutTaskVO.getTaskCode(), layoutTaskVO.getId());
        String cloudTaskId = layoutTaskVO.getCloudTaskId();
        layoutTaskVO.setMonitorCount(layoutTaskVO.getMonitorCount() - 1);
        try {
            TaskVO resourceTaskVO = taskMgAdapter.getTaskById(cloudTaskId);
            this.updateStateByCloudTask(layoutTaskVO, resourceTaskVO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 任务状态更新为失败
            layoutTaskVO.setState(StateEnum.ERROR);
            layoutTaskVO.setMessage(e.getCause() == null ? e.getMessage() : e.getCause().getMessage());
        }
        this.updateByCondition(layoutTaskVO);

        // 监控成功,马上执行下一任务
        if(StateEnum.SUCCESS.toString().equals(layoutTaskVO.getState().toString())){
            asyncServiceExecutor.execute(() -> layoutServiceImpl.layoutTaskExecut(layoutTaskVO.getMasterTaskId()));
        }
        log.info("{}  task monitor  end, taskId is:{}", layoutTaskVO.getTaskCode(), layoutTaskVO.getId());
    }

    /**
     * 根据底层任务更新状态
     *
     * @param layoutTaskVO   编排任务
     * @param resourceTaskVO 底层任务
     */
    private void updateStateByCloudTask(LayoutTaskVO layoutTaskVO, TaskVO resourceTaskVO) {
        String status = resourceTaskVO.getStatus();
        layoutTaskVO.setCloudTaskId(resourceTaskVO.getId());
        layoutTaskVO.setResourceId(resourceTaskVO.getResourceId());
        layoutTaskVO.setInstanceId(resourceTaskVO.getInstanceId());
        // 状态更新
        if (TaskStatusEnum.SUCCESS.getCode().equals(status)) {
            // 成功
            layoutTaskVO.setState(StateEnum.SUCCESS);
            layoutTaskVO.setEndTime(new Date());
            if (StrUtil.isNotEmpty(layoutTaskVO.getInstanceId())) {
                layoutTaskVO.setInstanceId(layoutTaskVO.getInstanceId());
            }
            // 全局参数更新
            this.updateParam(layoutTaskVO);

        }
        if (TaskStatusEnum.ERROR.getCode().equals(status) || TaskStatusEnum.EXCEPTION.getCode().equals(status)) {
            // 失败
            layoutTaskVO.setState(StateEnum.ERROR);
            layoutTaskVO.setMessage(resourceTaskVO.getMessage());
        }
        if (TaskStatusEnum.EXECUTING.getCode().equals(status)) {
            if (layoutTaskVO.getMonitorCount() <= 0) {
                // 超时
                layoutTaskVO.setState(StateEnum.TIMEOUT);
                layoutTaskVO.setRetryCount(0);
                layoutTaskVO.setMessage("TASK_TIMEOUT");
            } else {
                // 执行中
                layoutTaskVO.setState(StateEnum.EXECUTING);
                //更新后放入队列
                //monitorTaskHandler.addSchedule(layoutTaskVO);
            }
        }
    }

    /**
     * 全局参数更新
     */
    private void updateParam(LayoutTaskVO layoutTaskVO) {
        // 自旋更新3次
        int j = 0;
        for (int i = 0; i < 3; i++) {
            LayoutParam layoutParam = layoutParamMapper.getLayoutParamByMainTaskId(layoutTaskVO.getMasterTaskId());
            String paramValue = layoutParam.getParamValue();
            LayoutOrder layoutOrder = JSONObject.parseObject(paramValue, LayoutOrder.class);
            for (ProductOrder productOrder : layoutOrder.getProductOrders()) {
                if (productOrder.getProductOrderId().equals(layoutTaskVO.getOrderId())) {
                    if (!StringUtils.isEmpty(layoutTaskVO.getResourceId())) {
                        HashMap<String, Object> attrs = productOrder.getAttrs();
                        attrs.put("id", layoutTaskVO.getResourceId());
                    }
                }
            }
            layoutParam.setParamValue(JSONObject.toJSONString(layoutOrder));
            layoutParam.setUpdatedTime(new Date());
            j = layoutParamMapper.updateByRevision(layoutParam);
            if (j != 0) {
                break;
            }
        }
        if (j == 0) {
            throw new BusinessException("全局参数更新失败");
        }
    }

    /**
     * 更新任务
     */
    private void updateByCondition(LayoutTaskVO layoutTaskVO) {
        String taskType = layoutTaskVO.getTaskType();
        layoutTaskVO.setUpdatedTime(new Date());
        if (!layoutTaskVO.getState().toString().equals(StateEnum.SUCCESS.toString()) && layoutTaskVO.getRetryCount() <= 0) {
            // TODO 发送告警
        }

        if (StateEnum.TIMEOUT.toString().equals(layoutTaskVO.getState().toString())) {
            // 更新主任务
            layoutTaskVO.setState(StateEnum.TIMEOUT);
            layoutTaskVO.setRetryCount(0);
        }

        if (layoutTaskVO.getState().toString().equals(StateEnum.ERROR.toString())) {
            layoutTaskVO.setRetryCount(Math.max(layoutTaskVO.getRetryCount() - 1, 0));
            // 当任务重试次数为0时，被依赖的任务全部置为失败
            if (layoutTaskVO.getRetryCount() <= 0) {
                List<RelTaskVO> relTasksVO = tasksRelMapper.selectTaskByRelTaskId(layoutTaskVO.getId());
                for (RelTaskVO relTaskVO : relTasksVO) {
                    LayoutTaskNode node = new LayoutTaskNode();
                    node.setId(relTaskVO.getId());
                    node.setState(StateEnum.ERROR);
                    node.setUpdatedTime(new Date());
                    node.setRetryCount(0);
                    layoutTaskNodeMapper.updateById(node);
                }
            }
        }


        if (TaskTypeEnum.MASTER.toString().equals(taskType)) {
            layoutTaskMapper.updateByCondition(layoutTaskVO);
        }
        if (TaskTypeEnum.SUB.toString().equals(taskType)) {
            layoutTaskNodeMapper.updateByCondition(layoutTaskVO);
        }

    }

    /**
     * 更新任务(版本控制)
     */
    private int updateTaskByRevision(LayoutTaskVO layoutTaskVO) {
        String taskType = layoutTaskVO.getTaskType();
        layoutTaskVO.setUpdatedTime(new Date());

        if (TaskTypeEnum.MASTER.toString().equals(taskType)) {
            return layoutTaskMapper.updateByRevision(layoutTaskVO);
        }
        if (TaskTypeEnum.SUB.toString().equals(taskType)) {
            return layoutTaskNodeMapper.updateByRevision(layoutTaskVO);
        }
        return 0;
    }

    @Data
    private class LayoutParamAndMainTaskParamRel {
        /**
         * 编排参数
         */
        private LayoutParam layoutParam;

        /**
         * 子任务与编排参数的关联
         */
        private TaskParam mainTaskParamRel;
    }

    @Data
    private class ApiAndTaskNodeApi {
        /**
         * api集合
         */
        private List<LayoutApiExt> layoutApis;

        /**
         * 任务节点和api关联关系集合
         */
        private List<TaskNodeApi> taskNodeApis;
    }

}
