package com.datatech.slgzt.impl.service.corporate;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.standard.StandardEcsCombinationResOpenStrategyServiceProvider;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.EipModel;
import com.datatech.slgzt.model.nostander.NatGatwayModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.datatech.slgzt.model.query.DagProductQuery;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.corporate.CorporateResOpenService;
import com.datatech.slgzt.service.dag.DagResOpenService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 18:50:05
 */
@Slf4j
@Service
public class CorporateResNatOpenServiceImpl implements CorporateResOpenService {

    @Resource
    private CorporateOrderProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private CorporateOrderManager corporateOrderManager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public Object openResource(CorporateOrderProductDTO productDTO) {
        //不是只有ESC
        String productType = productDTO.getProductType();
        //把开通的网络资源挂载到对应的附加字段里去
        CorporateOrderDTO orderDTO = corporateOrderManager.getById(productDTO.getOrderId());
        NatGatwayModel natGatwayModel = JSON.parseObject(productDTO.getPropertySnapshot(), NatGatwayModel.class);
        Long tenantId = platformService.getOrCreateTenantId(natGatwayModel.getBillId(), natGatwayModel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        ResOpenReqModel resOpenReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        resOpenReqModel.setAccount(natGatwayModel.getBillId());
        //设置业务code;
        resOpenReqModel.setSourceExtType(OrderTypeEnum.CORPORATE.getCode());
        //设置业务code
        resOpenReqModel.setBusinessCode("DEFAULT");
        //设置业务系统code
        resOpenReqModel.setBusinessSystemCode(String.valueOf(natGatwayModel.getBusinessSystemId()));
        //设置客户id
        resOpenReqModel.setCustomId(natGatwayModel.getCustomNo());
        //设置区域编码
        resOpenReqModel.setRegionCode(natGatwayModel.getRegionCode());
        //设置的是主产品的gid 这里适配任务中心回调
        resOpenReqModel.setSubOrderId(productDTO.getSubOrderId());
        resOpenReqModel.setGid(productDTO.getGid());
        //设置租户id 可能是需要传入底层租户id 应该要查询下 目前不知道查询的方式
        resOpenReqModel.setTenantId(tenantId);
        //设置userId
        resOpenReqModel.setUserId(orderDTO.getCreateBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resOpenReqModel.setTaskSource(6);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList= Lists.newArrayList();
        //------------设置nat的参数-----------------------------------------------------
        List<ResOpenReqModel.ProductOrder> natProduct = StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.NAT).assembleParam(new ResOpenOpm()
                .setTenantId(tenantId)
                .setCustomId(natGatwayModel.getCustomNo())
                .setAccount(natGatwayModel.getBillId())
                .setBusinessSystemCode(String.valueOf(natGatwayModel.getBusinessSystemId()))
                .setSubOrderId(productDTO.getSubOrderId().toString())
                .setGId(productDTO.getGid())
                .setNatGatwayModelList(Lists.newArrayList(natGatwayModel)));
        reqProductList.addAll(natProduct);
        if(natGatwayModel.getBindPublicIp()){
            //获取父类id时
            List<CorporateOrderProductDTO> productDTOS = productManager.list(new CorporateOrderProductQuery()
                    .setProductType(ProductTypeEnum.EIP.getCode()).setParentId(productDTO.getId()));
            //转成EIP的model
            ArrayList<EipModel> eipModelList = Lists.newArrayList();
            for (CorporateOrderProductDTO dto : productDTOS) {
                EipModel eipModel = JSON.parseObject(dto.getPropertySnapshot(), EipModel.class);
                eipModel.setGId(dto.getGid());
                eipModelList.add(eipModel);
            }
            List<ResOpenReqModel.ProductOrder> eipProduct = StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.EIP).assembleParam(new ResOpenOpm()
                    .setTenantId(tenantId)
                    .setSubOrderId(productDTO.getSubOrderId().toString())
                    .setEipModelList(eipModelList));
            reqProductList.addAll(eipProduct);
        }

        resOpenReqModel.setProductOrders(reqProductList);
        //------------------产品参数设置结束-------------------------------------------------------
        //把对应的产品都改成开通中状态
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl);
        log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化--escOpenReqModel={}", JSON.toJSONString(resOpenReqModel));
        Mapper dataMapper= OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(resOpenReqModel))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败, " + dataMapper.getString("message"));
        log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
        return null;
    }

    /**
     * 注册
     */
    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.NAT;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
}
