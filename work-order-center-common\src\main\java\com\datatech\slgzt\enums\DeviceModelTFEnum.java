package com.datatech.slgzt.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 显卡指标枚举
 * <AUTHOR>
 */
public enum DeviceModelTFEnum {

    /**
     * 1获取GPU的SM时钟频率（MHz）
     */
    DEVICE_MODEL_NAME_910_B("910B", 313.0,"NPU"),
    DEVICE_MODEL_NAME_910_B1("910B1", 280.0,"NPU"),
    DEVICE_MODEL_NAME_910_B2("910B2", 313.0,"NPU"),

    DEVICE_MODEL_NAME_910_B3("910B3", 376.0,"NPU"),
    DEVICE_MODEL_NAME_910_B4("910B4", 414.0,"NPU"),


    DEVICE_MODEL_NAME_300I("300I", 70.0,"NPU"),
    DEVICE_MODEL_NAME_T4("T4", 65.0,"GPU"),
    DEVICE_MODEL_NAME_A10("A10", 31.24,"GPU"),
    DEVICE_MODEL_NAME_A40("A40", 69.6,"GPU"),
    DEVICE_MODEL_NAME_V100("V100", 125.0,"GPU"),

    /**
     * 未知
     */
    UNKNOWN("unknown", 1.0,"unknown");

    @Getter
    private final String modelName;
    @Getter
    private final Double tflops;

    @Getter
    private final String deivceType;
    DeviceModelTFEnum(String modelName, Double detflopssc,String deivceType) {
        this.modelName = modelName;
        this.tflops = detflopssc;
        this.deivceType = deivceType;
    }

    /**
     * 根据modelName 获取detflopssc
     */

    public static DeviceModelTFEnum getByModelName(String modelName) {
        if (!StringUtils.isEmpty(modelName)) {
            for (DeviceModelTFEnum value : values()) {
                if(modelName.contains(value.getModelName())){
                    return value;
                }
            }
        }
        return DeviceModelTFEnum.UNKNOWN;
    }

}
