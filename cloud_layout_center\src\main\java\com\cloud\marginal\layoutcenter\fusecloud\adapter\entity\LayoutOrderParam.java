package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import com.cloud.marginal.model.dto.layout.LayoutOrder;
import lombok.Data;

import java.util.List;

@Data
public class LayoutOrderParam {
    /**
     * 集团客户id
     */
    private String customId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 计费号
     */
    private String account;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 云平区域编码
     */
    private String regionCode;

    /**
     * 订单中心子订单id
     */
    private String subOrderId;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 业务系统编码/资源集编码
     */
    private String businessSystemCode;

    /**
     * 产品订单参数集合
     */
    private List<ProductOrderParam> productOrders;

    /**
     * 操作唯一编号
     */
    private String optUuid;
}
