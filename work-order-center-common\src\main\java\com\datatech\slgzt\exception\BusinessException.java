package com.datatech.slgzt.exception;

/**
 * <AUTHOR>
 * @Date: 2024/11/18 15:40
 * @Description: 自定义异常类
 */
public class BusinessException extends RuntimeException {

    /**
     * 异常状态码
     */
    private Integer code;

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public Integer code() {
        return code;
    }

}
