package com.cloud.marginal.layoutcenter.scheduler;

import com.cloud.marginal.layoutcenter.service.LayoutService;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 监控任务处理器
 */
//@Component
public class MonitorTaskHandler {

    @Resource
    private LayoutService layoutServiceImpl;

    @Autowired
    @Qualifier(value = "scheduledServiceExecutor")
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    /**
     * 添加延迟任务 （延迟时间默认设置）
     *
     * @param layoutTaskVO 任务对象
     * @return 是否添加成功
     */
    public Boolean addSchedule(LayoutTaskVO layoutTaskVO) {
        return addSchedule(layoutTaskVO, 10000, TimeUnit.MILLISECONDS);
    }

    /**
     * 添加延迟任务 （延迟时间自定义设置）
     *
     * @param layoutTaskVO 任务对象
     * @param delay        延迟时间
     * @param unit         延迟单位(示例：TimeUnit.MILLISECONDS)
     * @return 是否添加成功
     */
    public Boolean addSchedule(LayoutTaskVO layoutTaskVO,
                               long delay,
                               TimeUnit unit) {
        // 防止队列中的容量过大,默认maxInt大小
        BlockingQueue<Runnable> queue = scheduledThreadPoolExecutor.getQueue();
        if (queue != null && queue.size() > 100) {
            return false;
        }

        // 加入任务,内部使用的队列为延迟优先队列
        // 延迟指定时间后执行
        scheduledThreadPoolExecutor.schedule(() -> layoutServiceImpl.taskMonitor(layoutTaskVO), delay, unit);

        return true;
    }
}
