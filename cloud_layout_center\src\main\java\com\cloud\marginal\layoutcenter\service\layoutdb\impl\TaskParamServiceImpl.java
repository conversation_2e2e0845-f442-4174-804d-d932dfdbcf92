package com.cloud.marginal.layoutcenter.service.layoutdb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.marginal.layoutcenter.service.layoutdb.TaskParamService;
import com.cloud.marginal.mapper.layout.TaskParamMapper;
import com.cloud.marginal.model.entity.layout.TaskParam;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 主任务与参数关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class TaskParamServiceImpl extends ServiceImpl<TaskParamMapper, TaskParam> implements TaskParamService {

}
