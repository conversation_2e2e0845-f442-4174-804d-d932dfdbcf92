package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 工单流状态枚举
 */
@Getter
@AllArgsConstructor
public enum ActivitiStatusEnum {

    USER_TASK("user_task", "待开通申请"),
    SCHEMA_ADMINISTRATOR("schema_administrator", "待架构负责人审核"),
//    PROFESSIONAL_GROUP("professional_group", "待主机专业组审核"),
    TENANT_TASK("tenant_task", "待租户确认"),
    BUSINESS_DEPART_LEADER2("business_depart_leader2", "待三级业务部门领导审核"),
    BUSINESS2_DEPART_LEADER("business2_depart_leader", "待三级业务部门领导审核"),
    BUSINESS_DEPART_LEADER("business_depart_leader", "待二级业务部门领导审核"),
    cloud_leader("cloud_leader", "待三级云资源部领导审核"),
    cloud_leader_2("cloud_leader_2", "待二级云资源部领导审核"),

    ALARM_SUPPRESSION("alarm_suppression", "待屏蔽告警"),
    SHUTDOWN("shutdown", "待云主机关机"),

    NETWORK_PROVISIONING("network_provisioning", "待网络开通"),
    RESOURCE_CREATION("resource_creation", "待资源开通"),
    ACCESS_TO_4A("access_to_4a", "待入网交维"),
    RETREAT_DIMENSION("retreat_dimension", "待交维清退"),
    RESOURCE_RECOVERY("resource_recovery", "待资源回收"),
    RESOURCE_CHANGE("resource_change", "资源变更"),
    network_recovery("network_recovery", "待网络回收"),
    ORDER_COMPLETED("order_completed", "工单已完结"),
    ORDER_CANCEL("", "工单已撤销"),
    AUTODIT_END("autodit end", "完成审批"),


    //-------------------------------非标流程--------------------------------
    RESPONSE_SCHEME_MANAGER("response_scheme_manager", "待响应方案经理审批"),
    BRANCH_LEADER("branch_leader", "待分公司领导审批"),
    PROVINCE_GOV_ADMIN("province_gov_admin", "待省政企管理员审批"),
    PROVINCE_GOV_LEADER("province_gov_leader", "待省政企领导审批"),
    CLOUD_RESOURCE_LEADER("cloud_resource_leader", "待云资源部领导审批"),
    OFFLINE_OPEN_H("offline_open_h", "待线下开通"),
    INFORMATION_ARCHIVE_H("information_archive_h", "待信息归档"),
    NETWORK_PROVISIONING_H("network_provisioning_h", "待网络开通"),
    RESOURCE_CREATION_H("resource_creation_h", "待资源开通"),
    OFFLINE_OPEN_L("offline_open_l", "待线下开通"),
    INFORMATION_ARCHIVE_L("information_archive_l", "待信息归档"),
    NETWORK_PROVISIONING_L("network_provisioning_l", "待网络开通"),
    RESOURCE_CREATION_L("resource_creation_l", "待资源开通"),
    //-------------------------------非标流程结束--------------------------------
    //默认
    DEFAULT("DEFAULT", "默认");


    private final String node;
    private final String nodeRemark;

    //public static String getNodeRemarkByNode(String node, String orderStatus) {
    //    if (StringUtils.isEmpty(node)) {
    //        if (RecoveryOrderNodeEnum.CLOSE.getCode().equals(orderStatus)) {
    //            return ActivitiStatusEnum.ORDER_CANCEL.nodeRemark;
    //        }
    //        if (RecoveryOrderNodeEnum.END.getCode().equals(orderStatus)) {
    //            return ActivitiStatusEnum.ORDER_CANCEL.nodeRemark;
    //        }
    //    }
    //    List<ActivitiStatusEnum> list = Arrays.asList(values());
    //    for (ActivitiStatusEnum activitiStatusEnum : list) {
    //        if (activitiStatusEnum.node.equals(node)) {
    //            return activitiStatusEnum.nodeRemark;
    //        }
    //    }
    //
    //    return ActivitiStatusEnum.ORDER_CANCEL.nodeRemark;
    //}

    /**
     * 通过code获取enum
     */
    public static ActivitiStatusEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (ActivitiStatusEnum value : values()) {
                if (value.node.equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }

    /**
     * 判断是否为小云节点
     *
     * @param nodeCode 节点代码
     * @return 是否为小云节点
     */
    public static boolean isXiaoYunNode(String nodeCode) {
        return ActivitiStatusEnum.OFFLINE_OPEN_H.getNode().equals(nodeCode)
                || ActivitiStatusEnum.INFORMATION_ARCHIVE_H.getNode().equals(nodeCode)
                || ActivitiStatusEnum.NETWORK_PROVISIONING_H.getNode().equals(nodeCode)
                || ActivitiStatusEnum.RESOURCE_CREATION_H.getNode().equals(nodeCode)
                || ActivitiStatusEnum.OFFLINE_OPEN_L.getNode().equals(nodeCode)
                || ActivitiStatusEnum.INFORMATION_ARCHIVE_L.getNode().equals(nodeCode)
                || ActivitiStatusEnum.NETWORK_PROVISIONING_L.getNode().equals(nodeCode)
                || ActivitiStatusEnum.RESOURCE_CREATION_L.getNode().equals(nodeCode);
    }
}
