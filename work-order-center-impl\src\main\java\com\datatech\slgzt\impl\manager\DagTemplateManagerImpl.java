package com.datatech.slgzt.impl.manager;

import com.datatech.slgzt.convert.DagTemplateManagerConvert;
import com.datatech.slgzt.dao.DagTemplateDAO;
import com.datatech.slgzt.dao.model.DagTemplateDO;
import com.datatech.slgzt.manager.DagTemplateManager;
import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.query.DagTemplateQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DAG模板管理实现类
 */
@Service
public class DagTemplateManagerImpl implements DagTemplateManager {

    @Resource
    private DagTemplateDAO dagTemplateDAO;

    @Resource
    private DagTemplateManagerConvert dagTemplateManagerConvert;

    @Override
    public void create(DagTemplateDTO dto) {
        DagTemplateDO dagTemplateDO = dagTemplateManagerConvert.dto2do(dto);
        dagTemplateDAO.insert(dagTemplateDO);
    }

    @Override
    public void update(DagTemplateDTO dto) {
        DagTemplateDO dagTemplateDO = dagTemplateManagerConvert.dto2do(dto);
        dagTemplateDAO.update(dagTemplateDO);
    }

    @Override
    public void delete(String id) {
        dagTemplateDAO.delete(id);
    }

    @Override
    public DagTemplateDTO getById(String id) {
        DagTemplateDO dagTemplateDO = dagTemplateDAO.getById(id);
        return dagTemplateManagerConvert.do2dto(dagTemplateDO);
    }

    @Override
    public DagTemplateDTO getByName(String name) {
        DagTemplateDO dagTemplateDO = dagTemplateDAO.getByName(name);
        return dagTemplateManagerConvert.do2dto(dagTemplateDO);
    }

    @Override
    public List<DagTemplateDTO> list(DagTemplateQuery query) {
        List<DagTemplateDO> dagTemplateDOList = dagTemplateDAO.list(query);
        return dagTemplateDOList.stream()
                .map(dagTemplateManagerConvert::do2dto)
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<DagTemplateDTO> page(DagTemplateQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<DagTemplateDO> list = dagTemplateDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), dagTemplateManagerConvert::do2dto);
    }
} 