package com.datatech.slgzt.convert;

import com.datatech.slgzt.enums.CertificateTypeEnum;
import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import com.datatech.slgzt.model.query.SlbCertificateQuery;
import com.datatech.slgzt.model.req.slb.SlbCertificateCreateReq;
import com.datatech.slgzt.model.req.slb.SlbCertificatePageReq;
import com.datatech.slgzt.model.req.slb.SlbCertificateUpdateReq;
import com.datatech.slgzt.model.vo.slb.SlbCertificateVO;
import jodd.util.CollectionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Map;
import java.util.Set;

/**
 * SLB证书Web层转换器
 */
@Mapper(componentModel = "spring")
public interface SlbCertificateWebConvert {

    /**
     * 分页请求转Query
     */
    SlbCertificateQuery convert(SlbCertificatePageReq req);

    /**
     * 新增请求转DTO
     */
    SlbCertificateDTO convert(SlbCertificateCreateReq req);

    /**
     * 修改请求转DTO
     */
    SlbCertificateDTO convert(SlbCertificateUpdateReq req);

    /**
     * DTO转VO
     */

    @Mapping(source = "certificateType", target = "certificateTypeName", qualifiedByName = "certificateType")
    @Mapping(source = "slbListenerRel", target = "slbListenerName", qualifiedByName = "slbListenerRel2Listeners")
    SlbCertificateVO convert(SlbCertificateDTO dto);



    @Named("certificateType")
    default String certificateType(String code) {
        return CertificateTypeEnum.getDescByCode(code);
    }

    @Named("slbListenerRel2Listeners")
    default String slbListenerRel2Listeners(Map<String,String> slbListenerRel) {
        if(slbListenerRel != null && !slbListenerRel.isEmpty()){
            Set<String> slbListenerNames = slbListenerRel.keySet();
            return String.join(",", slbListenerRel.keySet());
        }
        return "";
    }
} 