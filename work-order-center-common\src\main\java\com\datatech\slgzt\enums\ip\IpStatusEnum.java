package com.datatech.slgzt.enums.ip;


public enum IpStatusEnum {
    ALL("ALL", "全部"),
    ASSIGNED("Assigned", "已分配"),
    UNDISTRIBUTED("undistributed", "未分配");


    private final String code;

    private final String desc;

    IpStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }

    public static IpStatusEnum getByDesc(String value) {
        IpStatusEnum[] values = values();
        for (IpStatusEnum ipStatusEnum : values) {
            if (ipStatusEnum.getDesc().equals(value)) {
                return ipStatusEnum;
            }
        }
        return null;
    }
}
