package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

import java.util.List;

@Data
public class EcsParam {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 云主机主键ID
     */
    private String id;

    /**
     * 云主机资源中心id
     */
    private String resourceId;

    /**
     * 可用区编码
     */
    private String azCode;

    /**
     * 云主机名称
     */
    private String vmName;

    /**
     * 虚机别名
     */
    private String alias;

    /**
     * 云主机描述
     */
    private String description;

    /**
     * 云区域编码
     */
    private String regionCode;

    /**
     * 镜像
     */
    private String imageId;

    /**
     * 密码策略
     */
    private String passwordPolicy;

    /**
     * 规格编码
     */
    private String flavorCode;

    /**
     * 规格ID
     */
    private String flavorId;


    private String userName;


    private VpcInfo vpcInfo;

    private String adminPass;

    private List<SecurityGroup> securityGroupIds;

    private RootVolume rootVolume;

    private String gId;

    private String outInstanceId;
    /**
     * 云主机创建绑定已有弹性IP
     */
    private String publicIp;

    /**
     * 云主机创建挂载已有硬盘
     */
    private List<EvsId> evsIds;


    /**
     * 网卡集合
     */
    private List<Nic> nics;

    /**
     * 创建子网的信息(网络边缘云)
     */
    private SubnetInfo subnetInfo;


    private String use;


    public String getgId() {
        return gId;
    }

    public void setgId(String gId) {
        this.gId = gId;
    }


    @Data
    public static class Nic {
        // 子网id
        private String subnetId;

        // 待创建云服务器网卡的IP地址（预留）
        private String ipAddress;

    }





    @Data
    public static class VpcInfo{

        private String vpcId;

        private List<Subnet> subnets;

        private List<String> networkIds;
    }

    @Data
    public static class Subnet{

        private String subnetId;

        private String subnetIp;

        private String ipAddress;
    }

    @Data
    public static class SecurityGroup{
        private String securityGroupId;
    }

    @Data
    public static class EvsId{
        private String evsId;
    }

    @Data
    public static class RootVolume{

        private Long sysDiskSize;

        private String sysDiskType;
    }


    /**
     * 存储池ID
     */
    private String storagePoolId;

    /**
     * 宿主机ID
     */
    private String hostId;

    @Data
    public static class SubnetInfo {

        /**
         * 是否开启DHCP
         * TRUE/FALSE
         * 默认：FALSE
         */
        private Boolean dhcpEnable;

        /**
         * 子网掩码信息；不开启DHCP的时候使用
         */
        private String cidr;

        /**
         * 网关IP；dhcpEnable = FALSE的时候使用
         */
        private String gatewayIp;

        private Boolean ipv6Enable;

        private String subnetName;


    }
}
