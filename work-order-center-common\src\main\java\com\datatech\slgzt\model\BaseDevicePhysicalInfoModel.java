package com.datatech.slgzt.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class BaseDevicePhysicalInfoModel {
    /**
     * 显卡UUID
     */
    @JSONField(name="device_id")
    private String deviceId;
    /**
     *  显卡索引
     */
    @JSONField(name="device_index")
    private String deviceIndex;
    /**
     * 显卡名称
     */
    @JSONField(name="device_name")
    private String deviceName;

    /**
     *显卡厂家
     */
    @JSONField(name="device_vendor")
    private String deviceVendor;

    /**
     *显卡类型
     */
    @JSONField(name="device_type")
    private String deviceType;

    /**
     * 显卡节点IP
     */
    @JSONField(name="device_ip")
    private String deviceIp;

    /**
      * 显卡驱动版本
     */
    @JSONField(name="driver_version")
    private String driverVersion;
    /**
     * 显卡监控状态
     */
    @JSONField(name="health_check")
    private String healthCheck;




}
