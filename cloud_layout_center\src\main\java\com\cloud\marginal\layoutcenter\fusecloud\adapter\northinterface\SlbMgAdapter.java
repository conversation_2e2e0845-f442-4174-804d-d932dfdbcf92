package com.cloud.marginal.layoutcenter.fusecloud.adapter.northinterface;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ccmp.exceptioncenter.common.exception.BusinessException;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.enums.layout.ProductOrderTypeEnum;
import com.cloud.marginal.layoutcenter.base.BaseService;
import com.cloud.marginal.layoutcenter.factory.LaoutServiceFactory;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.LayoutOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.ProductOrderParam;
import com.cloud.marginal.layoutcenter.fusecloud.adapter.entity.SlbParam;
import com.cloud.marginal.model.dto.edge.ModifySlbRcDto;
import com.cloud.marginal.model.entity.layout.LayoutParam;
import com.cloud.marginal.model.vo.edge.TaskVO;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import com.cloud.resource.api.slb.dto.CreateSlbRcDto;
import com.cloud.resource.api.slb.dto.DeleteSlbRcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * slb适配管理
 */
@Component
@Slf4j
public class SlbMgAdapter extends BaseNorthInterfaceAdapter implements BaseService {

    @Resource
    private LaoutServiceFactory laoutServiceFactory;

    @Override
    public void afterPropertiesSet() throws Exception {
        laoutServiceFactory.register("SLB", this);
    }

    @Override
    public TaskVO handler(LayoutTaskVO layoutTaskVO) {
        String id = layoutTaskVO.getId();
        Integer taskSource = layoutTaskVO.getTaskSource();
        switch (layoutTaskVO.getTaskCode()) {
            case "SLB_CREATE":
                return this.createSlb(id, taskSource);
            case "SLB_MODIFY_FLAVOR":
                return this.modifySlbFlavor(id, taskSource);
            case "SLB_DELETE":
                return this.deleteSlb(id, taskSource);
            default:
                throw new BusinessException("产品类型错误");
        }
    }

    /**
     * 创建slb
     */
    public TaskVO createSlb(String taskId, Integer taskSource) {
        log.info("createSlb start");
        CreateSlbRcDto slbDTO = generateCreateSlbDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getCreateSlb(),
                null,
                slbDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("createSlb url is:{}", northInterfaceAddress.getCreateSlb());
        log.info("createSlb params is:{}", JSONObject.toJSON(slbDTO));
        checkResultThrowExceptionIfFail(tasksVoResult, "create slb");
        return tasksVoResult.getEntity();
    }

    /**
     * slb规格变更
     */
    public TaskVO modifySlbFlavor(String taskId, Integer taskSource) {
        log.info("modifySlbFlavor start");
        ModifySlbRcDto slbDTO = generateModifySlbFlavorDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getModifySlbFlavor(),
                null,
                slbDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        log.info("modifySlbFlavor url is:{}", northInterfaceAddress.getModifySlbFlavor());
        log.info("modifySlbFlavor params is:{}", JSONObject.toJSON(slbDTO));
        checkResultThrowExceptionIfFail(tasksVoResult, "modify slb flavor");
        return tasksVoResult.getEntity();
    }


    /**
     * slb删除
     */
    public TaskVO deleteSlb(String taskId, Integer taskSource) {
        log.info("deleteSlb start");
        DeleteSlbRcDto slbDTO = generateDeleteRdsDTO(getLayoutParam(taskId));
        CecResult<TaskVO> tasksVoResult = northInterfaceHttpRequestUtil.postJson(northInterfaceAddress.getDeleteSlb(),
                null,
                slbDTO,
                new TypeReference<CecResult<TaskVO>>() {
                },
                null);
        checkResultThrowExceptionIfFail(tasksVoResult, "delete slb");
        return tasksVoResult.getEntity();
    }


    private CreateSlbRcDto generateCreateSlbDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam slbOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.SLB_CREATE.getCode());
        SlbParam slbParam = JSONObject.parseObject(slbOrder.getAttrs(), SlbParam.class);
        CreateSlbRcDto slbDTO = new CreateSlbRcDto();
        BeanUtils.copyProperties(slbParam, slbDTO);
        slbDTO.setOrderId(layoutOrderParam.getSubOrderId());
        slbDTO.setRegionCode(layoutOrderParam.getRegionCode());
        slbDTO.setBillId(layoutOrderParam.getAccount());
        slbDTO.setGroupId(layoutOrderParam.getCustomId());
        slbDTO.setGlobalId(slbParam.getGId());
        slbDTO.setVdcCode(layoutOrderParam.getBusinessSystemCode());
        CreateSlbRcDto.Loadbalancer loadbalancer = new CreateSlbRcDto.Loadbalancer();
        loadbalancer.setAddressType(slbParam.getAddressType());
        loadbalancer.setAzCode(slbParam.getAzCode());
        loadbalancer.setFlavorCode(slbParam.getFlavorCode());
        loadbalancer.setName(slbParam.getName());
        loadbalancer.setSubnetId(slbParam.getSubnetId());
        loadbalancer.setVip(slbParam.getVip());
        loadbalancer.setVpcId(slbParam.getVpcId());

        CreateSlbRcDto.Loadbalancer.PublicInfo publicInfo = new CreateSlbRcDto.Loadbalancer.PublicInfo();
        if(!ObjectUtils.isEmpty(slbParam.getPublicInfo())){
            BeanUtils.copyProperties(slbParam.getPublicInfo(), publicInfo);
            loadbalancer.setPublicInfo(publicInfo);
        }else{
            CreateSlbRcDto.Loadbalancer.PublicInfo publicInfo1 = new CreateSlbRcDto.Loadbalancer.PublicInfo();
            // 无实际意义,只是为了能校验通过
            publicInfo1.setBandwidth(0L);
            loadbalancer.setPublicInfo(publicInfo1);
        }
        slbDTO.setLoadbalancer(loadbalancer);
        return slbDTO;
    }


    private ModifySlbRcDto generateModifySlbFlavorDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam slbOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.SLB_MODIFY_FLAVOR.getCode());
        SlbParam slbParam = JSONObject.parseObject(slbOrder.getAttrs(), SlbParam.class);
        ModifySlbRcDto slbDTO = new ModifySlbRcDto();
        slbDTO.setSlbId(slbParam.getGId());
        // slbDTO.setSlbName(slbParam.getSlbName());
        slbDTO.setFlavorId(slbParam.getFlavorId());
        return slbDTO;
    }


    private DeleteSlbRcDto generateDeleteRdsDTO(LayoutParam layoutParam) {
        LayoutOrderParam layoutOrderParam = JSONObject.parseObject(layoutParam.getParamValue(), LayoutOrderParam.class);
        ProductOrderParam slbOrder = getProductOrder(layoutOrderParam.getProductOrders(), ProductOrderTypeEnum.SLB_DELETE.getCode());
        SlbParam slbParam = JSONObject.parseObject(slbOrder.getAttrs(), SlbParam.class);
        DeleteSlbRcDto slbDTO = new DeleteSlbRcDto();
        slbDTO.setBillId(layoutOrderParam.getAccount());
        slbDTO.setGroupId(layoutOrderParam.getCustomId());
        slbDTO.setRegionCode(layoutOrderParam.getRegionCode());
        slbDTO.setSlbId(slbParam.getGId());
        return slbDTO;
    }
}
