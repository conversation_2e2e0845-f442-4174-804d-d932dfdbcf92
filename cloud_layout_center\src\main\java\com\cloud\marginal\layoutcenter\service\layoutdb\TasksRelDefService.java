package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.model.dto.layout.SaveTasksRelDto;
import com.cloud.marginal.model.dto.layout.TasksRelPageDto;
import com.cloud.marginal.model.entity.layout.TaskRel;
import com.cloud.marginal.model.entity.layout.TasksRelDef;
import com.cloud.marginal.model.vo.layout.TaskRelVO;

import java.util.List;

/**
 * <p>
 * 编排任务关系配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface TasksRelDefService extends IService<TasksRelDef> {

    /**
     * 根据模板编号获取任务编号关联关系
     * @param templateCode 模板编号
     * @return
     */
    List<TaskRel> getTaskRelListByTemplateCode(String templateCode);

    void create(SaveTasksRelDto tasksRelDto);

    void remove(String tasksRelId);

    CecPage<TaskRelVO> getPage(TasksRelPageDto tasksRelPageDto);
}
