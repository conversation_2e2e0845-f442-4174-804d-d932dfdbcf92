package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * 网络(非任务中心)
 */
@Data
public class NetworkUnTaskModel extends BaseProductModel {


    private String networkId;

    /**
     * 网络名称
     */
    private String name;

    /**
     * 网络描述
     */
    private String description;

    /**
     * 工单Id
     */
    private String orderId;

    /**
     * 网络平面
     */
    private String plane;

    /**
     * 租户id
     */
    private Long tenantId;

    private String networkType;


    private String systemSource;

    private String instanceId;

    private String vlanId;

    private String vlan;

    private String functionalModule;

    private String azCode;

    private String catalogueDomainCode;

    private String catalogueDomainName;

    private String domainCode;

    private String domainName;
    private String detail;

    private List<SubnetModel> subnets;


}