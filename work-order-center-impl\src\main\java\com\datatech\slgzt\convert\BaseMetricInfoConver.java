package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.DeviceCardMetricsDO;

import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.math.BigDecimal;

@Mapper(componentModel = "spring")
public interface BaseMetricInfoConver {





    DeviceCardMetricsDO dto2do(DeviceCardMetricsDTO baseMetricInfo);


    @Mapping(target = "memoryUsage", source = "memoryUsage", qualifiedByName = "memoryUsageToBigDecimal")

    DeviceCardMetricsDTO do2dto(DeviceCardMetricsDO deviceCardMetricsDO);

    /**
     * 将memoryUsage转换为BigDecimal
     */
    @Named("modelToString")
    default BigDecimal memoryUsageToBigDecimal(DeviceCardMetricsDO deviceCardMetricsDO) {
        if (deviceCardMetricsDO == null) {
            return null;
        }
        return new BigDecimal(deviceCardMetricsDO.getMemoryUsage());
    }
}
