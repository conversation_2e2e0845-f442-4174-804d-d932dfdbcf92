package com.cloud.marginal.layoutcenter.fusecloud.adapter;

import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class TestAdapter {

    public HashMap m1(){
        HashMap<String,Object> result = new HashMap<>();
        result.put("m1name","张三");
        result.put("age",18);
        result.put("m1birthday","1999-1-1");
        return result;
    }

    public HashMap m2(HashMap<String,Object> param){
        HashMap<String,Object> result = new HashMap<>();
        String name = (String)param.get("name");
        int age = (int)param.get("age");
        String birthday = (String)param.get("birthday");
        result.put("m2result",name+": my name is "+name+",age is "+age+",birthday is "+birthday);
        return result;
    }

    public HashMap m3(HashMap<String,Object> param){
        HashMap<String,Object> result = new HashMap<>();
        result.put("lastMethodResult",param.get("result"));
        return result;
    }
}
