package com.datatech.slgzt.dao.container;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.container.XieYunRepositoryMapper;
import com.datatech.slgzt.dao.model.container.XieYunOrgDO;
import com.datatech.slgzt.dao.model.container.XieYunRepositoryDO;
import com.datatech.slgzt.model.query.container.XieYunRepositoryQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: liupeihan
 * @Date: 2025/4/14
 */

@Repository
public class XieYunRepositoryDAO {

    @Resource
    private XieYunRepositoryMapper mapper;

    public void insert(XieYunRepositoryDO repositoryDO) {
        mapper.insert(repositoryDO);
    }

    public XieYunRepositoryDO getById(String id) {
        return mapper.selectById(id);
    }

    public void update(XieYunRepositoryDO repositoryDO) {
        mapper.updateById(repositoryDO);
    }

    public List<XieYunRepositoryDO> list(XieYunRepositoryQuery query) {
        return mapper.selectList(Wrappers.<XieYunRepositoryDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getRepoName()), XieYunRepositoryDO::getRepoName, query.getRepoName())
                .eq(ObjNullUtils.isNotNull(query.getRegistryId()), XieYunRepositoryDO::getRepoName, query.getRegistryId())
        );
    }

    public void updateByRepoId(XieYunRepositoryDO repositoryDO) {
        UpdateWrapper<XieYunRepositoryDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("XIE_YUN_REPO_ID", repositoryDO.getXieYunRepoId());
        mapper.update(repositoryDO, updateWrapper);
    }
}

