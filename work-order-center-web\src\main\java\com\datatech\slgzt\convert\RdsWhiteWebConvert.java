package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.RdsWhiteDTO;
import com.datatech.slgzt.model.query.RdsWhiteQuery;
import com.datatech.slgzt.model.req.rdswhite.*;
import com.datatech.slgzt.model.vo.RdsWhiteVO;
import org.mapstruct.Mapper;

/**
 * @program: workordercenterproject
 * @description: 数据库白名单Web转换器
 * @author: LK
 * @create: 2025-06-25 16:13
 **/
@Mapper(componentModel = "spring")
public interface RdsWhiteWebConvert {

    /**
     * 分页请求转查询对象
     */
    RdsWhiteQuery convert(RdsWhitePageReq req);

    /**
     * 新增请求转DTO
     */
    RdsWhiteDTO convert(RdsWhiteCreateReq req);

    /**
     * 更新请求转DTO
     */
    RdsWhiteDTO convert(RdsWhiteUpdateReq req);

    /**
     * DTO转VO
     */
    RdsWhiteVO convert(RdsWhiteDTO dto);
} 