package com.datatech.slgzt.impl.service.dag;

import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.JobParameters;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DagCreateJobListener implements JobExecutionListener {

    @Resource
    private DagOrderManager dagOrderManager;
    @Override
    public void beforeJob(JobExecution jobExecution) {
        // 可以在这里添加一些初始化操作，比如设置默认参数等
        System.out.println("Job即将执行: " + jobExecution.getJobInstance().getJobName());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        if (jobExecution.getStatus() == BatchStatus.FAILED) {
            JobParameters jobParameters = jobExecution.getJobParameters();
            String orderId = jobParameters.getString("orderId");
            // 获取失败异常
            List<Throwable> exceptions = jobExecution.getAllFailureExceptions();
            String errorMsg = exceptions.stream()
                .map(Throwable::getMessage)
                .collect(Collectors.joining(", "));
            log.error("DAG创建失败 orderId={}，异常信息: {}",orderId, errorMsg);
            DagOrderDTO dagOrderDTO = dagOrderManager.getById(orderId);
            if (dagOrderDTO != null) {
                dagOrderDTO.setStatus(ResOpenEnum.OPEN_FAIL.getCode());
                dagOrderManager.update(dagOrderDTO);
            }
        }
        //如果完成了
        else if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
            JobParameters jobParameters = jobExecution.getJobParameters();
            String orderId = jobParameters.getString("orderId");
            log.info("DAG创建成功 orderId={}",orderId);
            DagOrderDTO dagOrderDTO = dagOrderManager.getById(orderId);
            if (dagOrderDTO!= null) {
                dagOrderDTO.setStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
                dagOrderManager.update(dagOrderDTO);
            }
        }
    }
}