package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.DgRecoveryOrderProductDO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DgRecoveryOrderProductManagerConvert {

    DgRecoveryOrderProductDTO do2dto(DgRecoveryOrderProductDO recoveryWorkOrderProductDO);

    DgRecoveryOrderProductDO dto2do(DgRecoveryOrderProductDTO orderProductDTO);

    List<DgRecoveryOrderProductDTO> dos2DTOs(List<DgRecoveryOrderProductDO> productDOS);

    List<DgRecoveryOrderProductDO> dtoList2DOs(List<DgRecoveryOrderProductDTO> productDOS);
}
