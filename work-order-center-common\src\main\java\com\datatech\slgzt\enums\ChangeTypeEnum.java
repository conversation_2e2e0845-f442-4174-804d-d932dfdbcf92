package com.datatech.slgzt.enums;


import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.Getter;

/**
 *
 * 云平台枚举
 * <AUTHOR>
 */
@Getter
public enum ChangeTypeEnum {

    /**
     * 更变类型
     */
    //实例规格更变
    INSTANCE_SPEC_CHANGE("instance_spec_change", "实例规格更变"),
    //存储扩容
    STORAGE_EXPAND("storage_expand", "存储扩容"),
    //带宽扩容
    BANDWIDTH_EXPAND("bandwidth_expand", "带宽扩容"),
    //延期
    DELAY("delay", "延期"),
    UNKNOWN("unknown", "-"),
    ;

    private final String code;
    private final String desc;

    ChangeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static ChangeTypeEnum getByCode(String code) {
        if (ObjNullUtils.isNotNull((code))) {
            for (ChangeTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return ChangeTypeEnum.UNKNOWN;
    }
}