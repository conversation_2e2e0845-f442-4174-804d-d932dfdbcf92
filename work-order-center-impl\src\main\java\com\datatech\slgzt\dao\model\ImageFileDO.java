package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 镜像文件类
 */
@Data
@TableName("WOC_IMAGE_FILE")
public class ImageFileDO {
    
    @TableField("ID")
    private Long id;

    /**
     * 镜像文件名称
     * 示例: "Windows Server 2019"
     */
    @TableField("IMAGE_NAME")
    private String imageName;

    /**
     * 操作系统类型
     * 示例: "Windows", "Linux"等
     */
    @TableField("OS_NAME")
    private String osName;

    /**
     * 操作系统版本号
     * 示例: "Server 2019", "Ubuntu 20.04"等
     */
    @TableField("OS_VERSION")
    private String osVersion;

    /**
     * 镜像文件大小
     */
    @TableField("SIZE")
    private String size;

    /**
     * 镜像文件格式
     * 示例: "iso", "img", "qcow2"等
     */
    @TableField("FORMAT")
    private String format;

    /**
     * 上传时间
     * 格式: yyyy-MM-dd HH:mm:ss
     */
    @TableField("UPLOAD_TIME")
    private LocalDateTime uploadTime;

    /**
     * 上传是否完成标志
     * true-已完成 false-未完成
     */
    @TableField("UPLOAD_COMPLETED")
    private Boolean uploadCompleted;

    /**
     * 文件名称
     */
    @TableField("FILE_NAME")
    private String fileName;

    /**
     * 文件MD5校验值
     * 用于文件完整性校验
     */
    @TableField("MD5")
    private String md5;

    /**
     * 文件下载URL
     * 当文件上传完成后生成
     */
    @TableField("DOWNLOAD_URL")
    private String downloadUrl;

    /**
     * 总分片数
     * 用于断点续传场景
     */
    @TableField("TOTAL_PARTS")
    private Integer totalParts;
    
}
