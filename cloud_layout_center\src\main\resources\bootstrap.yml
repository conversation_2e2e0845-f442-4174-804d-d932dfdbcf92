server:
  port: 9095
spring:
  application:
    name: cloud-layout-center
  profiles:
    active: test
  main:
    allow-bean-definition-overriding: true


  cloud:
    nacos:
      # 配置读取：namespace + group + dataId 唯一确定一个配置
      # dataId 的完整格式为:${prefix}-${spring.profiles.active}.${file-extension}
      config:
        # nacos服务地址
        server-addr: ${nacos.server.addr}
        # 空间隔离,默认public
        namespace: ${nacos.namespace}
        # 公共配置文件
        shared-configs:
          - dataId: common-${spring.application.name}-${spring.profiles.active}.yaml
            group: ${nacos.config.group}
        # 常规配置文件
        extension-configs:
          - dataId: ${spring.application.name}-${spring.profiles.active}.yaml
            group: ${nacos.config.group}
            # 开启动态刷新
            refresh: true
      username: ${nacos.username}
      password: ${nacos.password}
      # 服务注册
      discovery:
        # 关闭服务注册,除非需要使用http://微服务名称调用API
        enabled: false
  #        # nacos注册中心地址
  #        server-addr: ${nacos.server.addr}:8848
  #        # 不同命名空间下注册的实例信息做空间隔离
  #        namespace: ${nacos.namespace}

#jasypt加密,工具类 JasyptUtil
jasypt:
  encryptor:
    # 加密盐值
    password: 8a7a27f489fec6760dc849c76f6a9e0c
    # 设置加密算法
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator


# 任务配置
taskconfig:
  # 默认模板名称,默认模板才能使用以下配置
  defaultTemplate: DEFAULT

  # 默认模板的动态任务依赖配置,
  # 格式：Map<String1, List<Map<String2,String3>>>
  # 没有依赖关系的产品任务,则模板使用DEFAULT,以下配置略过

  # String1:任务编码,分为两种
  # 1.产品任务,与ProductOrder.productOrderType保持一致,如ECS_CREATE
  # 2.绑定/解绑任务,自定义,格式:主产品类型_BIND_从产品类型,主产品类型代表接口调用所在的产品类中
  # 3.通知任务不需要配置

  # <String2,String3>: 依赖任务列表
  # 1.code: 依赖任务编码
  # 2.count: 同一任务被依赖次数,默认ONE,可选值:MULTIPLE
  # 3.type: 任务类型,默认为CREATE+EXECUTE,可配置为CREATE,在dynamicExecuteTask中配置执行依赖
    # CREATE: 任务创建
    # EXECUTE：任务执行

  # 创建新任务的依赖,默认也是执行依赖 type=CREATE则表示只能是创建依赖
  dynamicTask:
    # slb绑定eip
    EIP_BIND_SLB:
      - code: SLB_CREATE
      - code: EIP_CREATE

    # nat绑定eip
    NAT_BIND_EIP:
      - code: NAT_CREATE
      - code: EIP_CREATE

    # nat解绑eip
    NAT_UNBIND_EIP:
      - code: NAT_DELETE
        type: CREATE
      - code: EIP_DELETE
        type: CREATE

    EIP_UNBIND_SLB:
      - code: SLB_DELETE
        type: CREATE
      - code: EIP_DELETE
        type: CREATE

    # 云主机绑定云硬盘(示例count=MULTIPLE的情况,此处为多块盘绑定一个云主机)
    ECS_BIND_EVS:
      - code: ECS_CREATE
        count: MULTIPLE
      - code: EVS_CREATE

    EIP_UNBIND_ECS:
      - code: ECS_DELETE
        type: CREATE
      - code: EIP_DELETE
        type: CREATE

  # 任务的执行依赖,如果配置内容与上面的完成一致,且没有定义type=CREATE,则不在此配置
  dynamicExecuteTask:

    # nat删除
    NAT_DELETE:
      - code: NAT_UNBIND_EIP
    # nat删除
    SLB_DELETE:
      - code: EIP_UNBIND_SLB
     # eip删除
    EIP_DELETE:
      - code: NAT_UNBIND_EIP
      - code: EIP_UNBIND_SLB



  # 定制依赖配置,与默认模板无关
  # 格式：Map<String1, List<String2>>
  # String1: 云平台类型
  # String2: 编排任务关系配置(ERC_TASKS_REL_DEF)主键ID
  # 不在如下配置中的依赖,则直接忽略
  customAddRel:
    # VC云平台
    nwc_prov_zj_pf:
      # 创建盘依赖云主机创建执行完成 (该任务为定制的串行执行)
      - 892f4e2684d549cca108ac8ca18e7068
      - 18b905c8e999163032f25af371d4064d
    edge_prov_zj_nfvo:
      # 创建云主机依赖网卡port创建完成（定制串行任务执行）
      - 22963a1bfa44488b917ff679ca9f24e3

  # 定制删除任务配置,,与默认模板无关
  # 指定云平台不需要的任务
  customDelTask:
    # VC云平台
    nwc_prov_zj_pf:
      # 挂盘任务
  #    - EVS_MOUNT

  # 定制创建任务配置,,与默认模板无关
  # 指定云平台需要的任务
  customAddTask:
    # NFVO云平台
    edge_prov_zj_nfvo:
      # 创建网卡任务
#      - PORT_CREATE




