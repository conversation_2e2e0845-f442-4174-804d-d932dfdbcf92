package com.datatech.slgzt.controller;

import com.datatech.slgzt.manager.McExternalNetworkManager;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.McExternalNetworkDTO;
import com.datatech.slgzt.model.dto.RegionDTO;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 外部网络控制器
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月06日 15:47:49
 */
@RestController
@RequestMapping("/mcExternalNetwork")
public class McExternalNetworkController {

    @Resource
    private McExternalNetworkManager mcExternalNetworkManager;

    @Resource
    private RegionManager regionManager;

    @RequestMapping(value = "/listAll",method = RequestMethod.POST)
    public CommonResult<List<McExternalNetworkDTO>> listAll(@RequestBody McExternalReq req) {
        RegionDTO byCode = regionManager.getByCode(req.getRegionCode());
        return CommonResult.success(mcExternalNetworkManager.listAll(byCode.getId()));
    }

    @Data
    public static class McExternalReq {
        private String regionCode; // 区域ID
    }
}
