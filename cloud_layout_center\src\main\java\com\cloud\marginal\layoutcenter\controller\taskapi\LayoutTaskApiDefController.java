package com.cloud.marginal.layoutcenter.controller.taskapi;

import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.common.CecResult;
import com.cloud.marginal.layoutcenter.common.VersionConstant;
import com.cloud.marginal.layoutcenter.service.layoutdb.LayoutApiDefService;
import com.cloud.marginal.layoutcenter.service.layoutdb.TaskApiDefService;
import com.cloud.marginal.model.entity.layout.LayoutApiDef;
import com.cloud.marginal.model.entity.layout.TaskApiDef;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(VersionConstant.V1)
public class LayoutTaskApiDefController {

    @Resource
    TaskApiDefService taskApiDefService;

    /**
     * 创建编排任务和api关联关系
     */
    @PostMapping("createTaskApiDef")
    public CecResult createTaskApiDef(@Validated @RequestBody TaskApiDef taskApiDef){
       return taskApiDefService.createTaskApiDef(taskApiDef);
    }

    /**
     * 修改编排任务和api关联关系
     */
    @PostMapping("updateTaskApiDef")
    public CecResult updateTaskApiDef(@Validated @RequestBody TaskApiDef taskApiDef){
        return taskApiDefService.updateTaskApiDef(taskApiDef);
    }


    /**
     * 删除编排任务和api关联关系
     */
    @DeleteMapping("deleteTaskApiDef")
    public CecResult deleteTaskApiDef(@RequestParam(name = "apiDefId") String apiDefId){
        return taskApiDefService.deleteTaskApiDef(apiDefId);
    }


    /**
     * 查询编排api配置列表，分页
     * @param pageNum 当前页
     * @param pageSize 每页大小
     */
    @GetMapping("/pageTaskApiDef")
    public CecResult<CecPage<TaskApiDef>> pageTaskApiDef(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                               @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        return CecResult.success(taskApiDefService.pageTaskApiDef(pageNum, pageSize));
    }



}
