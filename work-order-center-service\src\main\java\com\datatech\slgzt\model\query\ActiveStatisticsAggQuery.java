package com.datatech.slgzt.model.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 活跃统计聚合查询参数
 */
@Data
public class ActiveStatisticsAggQuery {
    
    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;
    
    /**
     * 聚合类型：DAY-按天, WEEK-按周, MONTH-按月, YEAR-按年
     */
    @NotNull(message = "聚合类型不能为空")
    private String aggType;
} 