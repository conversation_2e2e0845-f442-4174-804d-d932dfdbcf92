package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月05日 19:44:31
 */
@Data
public class CloudPortModel extends BaseProductModel {


    /**
     * 云端口名称
     */
    private String cloudPortName;

    /**
     * 互联的VLAN ID
     */
    private String vlanId;

    /**
     * vpc id
     */
    private String vpcId;

    /**
     * vpc名称
     */
    private String vpcName;

    /**
     * 创建BGP的外部接口地址
     */
    private String srcIp;

    /**
     * 创建BGP的对端邻居地址
     */
    private String peerIp;

    /**
     * peer口令
     */
    private String peerPassword;

}
