package com.datatech.slgzt.model.vo.recovery.export;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: workordercenterproject
 * @description: kafka
 * @author: LK
 * @create: 2025-06-30 15:02
 **/
@Data
public class KafkaExportVO {

    /**
     * 名称
     */
    @ExcelExportHeader("Topic命名")
    private String name;

    /**
     * 副本
     */
    @ExcelExportHeader("副本")
    private String replication;

    /**
     * 保留时间（天）
     */
    @ExcelExportHeader("保留时间")
    private String retainTime;

    /**
     * 数据流量
     */
    @ExcelExportHeader("数据流量")
    private String dataFlow;

    /**
     * 数据存储总量
     */
    @ExcelExportHeader("数据存储总量")
    private String dataStorageTotal;

    /**
     * 申请时长
     */
    @ExcelExportHeader("申请时长")
    private String applyTimeCn;

    @ExcelExportHeader(value = "租户")
    private String tenantName;

    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    @ExcelExportHeader(value = "所属云")
    private String domainName;

    @ExcelExportHeader(value = "资源池")
    private String regionName;

    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;

}
