package com.cloud.marginal.layoutcenter.fusecloud.constant;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 北向接口地址提供类
 */
@Component
@Data
public class NorthInterfaceAddress {

    /**
     * 创建虚拟机
     */
    @Value("${northInterface.createVirtualMachine:v1/cloud/resourcecenter/ecs/create}")
    private String createVirtualMachine;

    /**
     * 变更虚拟机
     */
    @Value("${northInterface.modifyVirtualMachine:v1/cloud/resourcecenter/ecs/change}")
    private String modifyVirtualMachine;

    /**
     * 删除虚拟机
     */
    @Value("${northInterface.deleteVirtualMachine:v1/cloud/resourcecenter/ecs/delete}")
    private String deleteVirtualMachine;

    /**
     * 操作虚拟机（开机、关机、重启、挂起、恢复）
     */
    @Value("${northInterface.operateVirtualMachine:v1/cloud/resourcecenter/ecs/operation}")
    private String operateVirtualMachine;

    /**
     * 修改云主机密码
     */
    @Value("${northInterface.resetPwd:/v1/cloud/resourcecenter/ecs/resetpwd}")
    private String resetPwd;

    /**
     * 创建安全组
     */
    @Value("${northInterface.createSecurityGroup:/v1/cloud/resourcecenter/securitygroup/create}")
    private String createSecurityGroup;

    /**
     * 删除安全组
     */
    @Value("${northInterface.deleteSecurityGroupAndRule:/v1/cloud/resourcecenter/securitygroup/deleteSecurityGroupAndRule/{securityGroupId}/{regionCode}/{tenantId}}")
    private String deleteSecurityGroup;

    /**
     * 创建云硬盘
     */
    @Value("${northInterface.createVolume:v1/cloud/resourcecenter/evs/create}")
    private String createVolume;

    /**
     * 变更云硬盘大小
     */
    @Value("${northInterface.modifyVolume:v1/cloud/resourcecenter/evs/resize}")
    private String modifyVolume;

    /**
     * 删除云硬盘
     */
    @Value("${northInterface.deleteVolume:v1/cloud/resourcecenter/evs/delete}")
    private String deleteVolume;

    /**
     * 挂载硬盘到指定虚拟机  卸载
     */
    @Value("${northInterface.attachVolume:v1/cloud/resourcecenter/evs/operation}")
    private String attachVolume;

    /**
     * 创建弹性ip
     */
    @Value("${northInterface.createEip:v1/cloud/resourcecenter/eip/create}")
    private String createEip;

    /**
     * 云主机绑定弹性ip
     */
//    @Value("${northInterface.bindEip:v1/cloud/resourcecenter/eip/layout/bind}")
    @Value("${northInterface.bindEip:v1/cloud/resourcecenter/eip/bind}")
    private String bindEip;

    /**
     * 解绑弹性ip
     */
    @Value("${northInterface.unBindEip:v1/cloud/resourcecenter/eip/unbind}")
    private String unBindEip;

    /**
     * 删除弹性ip
     */
    @Value("${northInterface.deleteEip:v1/cloud/resourcecenter/eip/delete}")
    private String deleteEip;

    /**
     * 变更弹性ip
     */
    @Value("${northInterface.modifyEip:v1/cloud/resourcecenter/eip/modify}")
    private String modifyEip;

    /**
     * 查询eip
     */
    @Value("${northInterface.bindEip:v1/cloud/resourcecenter/eip/ip}")
    private String queryEip;

    /**
     * 创建网卡
     */
    @Value("${northInterface.createNetworkCard:v1/cloud/resourcecenter/port/create}")
    private String createNetworkCard;

    /**
     * 云主机挂载网卡
     */
    @Value("${northInterface.attachNetworkCard:v1/cloud/resourcecenter/port/attachments}")
    private String attachNetworkCard;

    @Value("${northInterface.createVpc:v1/cloud/resourcecenter/vpc/createVpcAndSub}")
    private String createVpc;

    @Value("${northInterface.deleteVpc:v1/cloud/resourcecenter/vpc/delete}")
    private String deleteVpc;

    @Value("${northInterface.taskDetailById:v1/cloud/resourcecenter/task/task}")
    private String taskDetailById;

    @Value("${northInterface.taskDetailByOrderId:v1/cloud/resourcecenter/task/task/order}")
    private String taskDetailByOrderId;

    @Value("${northInterface.createNetwork:v1/cloud/resourcecenter/network/create}")
    private String createNetwork;

    @Value("${northInterface.createSubnet:v1/cloud/resourcecenter/subnet/layout/create}")
    private String createSubnet;

    @Value("${northInterface.createRoute:v1/cloud/resourcecenter/router/create}")
    private String createRoute;

    @Value("${northInterface.createRouteInterface:v1/cloud/resourcecenter/router/create/interface}")
    private String createRouteInterface;

    @Value("${northInterface.createRdsMysql:v1/cloud/resourcecenter/rds/create}")
    private String createRdsMysql;

    @Value("${northInterface.modifyRdsFlavorMysql:v1/cloud/resourcecenter/rds/flavor/modify}")
    private String modifyRdsFlavorMysql;

    @Value("${northInterface.modifyRdsStorageMysql:v1/cloud/resourcecenter/rds/storage/modify}")
    private String modifyRdsStorageMysql;

    @Value("${northInterface.deleteRdsMysql:v1/cloud/resourcecenter/rds/delete}")
    private String deleteRdsMysql;

    @Value("${northInterface.createObs:v1/cloud/resourcecenter/obs/create}")
    private String createObs;

    @Value("${northInterface.modifyObsQuota:v1/cloud/resourcecenter/obs/quota/modify}")
    private String modifyObsQuota;

    @Value("${northInterface.deleteObs:v1/cloud/resourcecenter/obs/delete}")
    private String deleteObs;

    @Value("${northInterface.deleteBucket:/v1/cloud/resourcecenter/obs/bucket/delete}")
    private String deleteBucket;

    @Value("${northInterface.createVpn:v1/cloud/resourcecenter/vpn/create}")
    private String createVpn;

    @Value("${northInterface.deleteVpn:v1/cloud/resourcecenter/vpn/delete}")
    private String deleteVpn;

    @Value("${northInterface.createSlb:v1/cloud/resourcecenter/slb/create}")
    private String createSlb;

    @Value("${northInterface.modifySlbFlavor:v1/cloud/resourcecenter/slb/modifyflavor}")
    private String modifySlbFlavor;

    @Value("${northInterface.deleteSlb:v1/cloud/resourcecenter/slb/delete}")
    private String deleteSlb;

    @Value("${northInterface.slbBindEip:v1/cloud/resourcecenter/eip/bind}")
    private String slbBindEip;

    @Value("${northInterface.createNat:v1/cloud/resourcecenter/nat/create}")
    private String createNat;

    @Value("${northInterface.bindNatEip:v1/cloud/resourcecenter/nat/bindEip}")
    private String bindNatEip;

    @Value("${northInterface.unbindNatEip:v1/cloud/resourcecenter/nat/unbindEip}")
    private String unbindNatEip;

    @Value("${northInterface.deleteNat:v1/cloud/resourcecenter/nat/delete}")
    private String deleteNat;

    @Value("${northInterface.createBackupEcs:v1/cloud/resourcecenter/backup/service/ecs/create}")
    private String createBackupEcs;

    @Value("${northInterface.createBackupEvs:v1/cloud/resourcecenter/backup/service/evs/create}")
    private String createBackupEvs;

    /**
     * 创建弹性公网IPV6
     */
    @Value("${northInterface.createEipIpV6:v1/cloud/resourcecenter/ipv6/create}")
    private String createEipIpV6;

    /**
     * 删除弹性公网IPV6
     */
    @Value("${northInterface.deleteEipIpV6:v1/cloud/resourcecenter/ipv6/delete}")
    private String deleteEipIpV6;

    /**
     * 创建虚拟IP
     */
    @Value("${northInterface.createVip:v1/cloud/resourcecenter/vip/create}")
    private String createVip;

    /**
     * 删除虚拟IP
     */
    @Value("${northInterface.deleteVip:v1/cloud/resourcecenter/vip/delete}")
    private String deleteVip;

    /**
     * 创建虚拟网卡
     */
    @Value("${northInterface.createNetCard:v1/cloud/resourcecenter/netcard/create}")
    private String createNetcard;

    /**
     * 创建虚拟网卡
     */
    @Value("${northInterface.createNetCard:v1/cloud/resourcecenter/netcard/create/bind}")
    private String createAndBinNetCard;

    /**
     * 云主机绑定网卡
     */
    @Value("${northInterface.bindNetworkCard:v1/cloud/resourcecenter/netcard/bind}")
    private String bindNetworkCard;

    /**
     * 云主机解绑网卡
     */
    @Value("${northInterface.unBindNetworkCard:v1/cloud/resourcecenter/netcard/unbind}")
    private String unbindNetworkCard;

    /**
     * 删除虚拟网卡
     */
    @Value("${northInterface.deleteNetCard:v1/cloud/resourcecenter/netcard/delete}")
    private String deleteNetcard;


}
