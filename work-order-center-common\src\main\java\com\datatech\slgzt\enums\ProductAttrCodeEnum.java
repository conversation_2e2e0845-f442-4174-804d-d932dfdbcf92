package com.datatech.slgzt.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Getter
public enum ProductAttrCodeEnum {

    /**
     * 云主机-云主机属性
     */
    ECS_INSTANCE_NAME("instanceName", "主机名称"),
    ECS_IMAGE_OS("imageOs", "镜像-操作系统"),
    ECS_IMAGE_VERSION("imageVersion", "镜像版本"),
    ECS_PLANE("plane", "网络平面"),

    /**
     * 云主机-云硬盘属性
     */
    ECS_EVS_NAME("evsName", "云硬盘名称"),
    ECS_EVS_RESOURCE_ID("resourceId", "数据盘资源ID"),
    ECS_EVS_INSTANCE_CODE("instanceCode", "数据盘实例编码"),

    /**
     * 云主机-eip属性
     */
    ECS_EIP_BANDWIDTH_NAME("bandwidthName", "弹性公网名称"),
    ECS_EIP_RESOURCE_ID("resourceId", "数据盘资源ID"),
    ECS_EIP_INSTANCE_CODE("instanceCode", "数据盘实例编码"),
    ECS_EIP_IP("ip", "弹性公网ip"),

    /**
     * 云硬盘
     */
    EVS_NAME("evsName", "云硬盘名称"),

    /**
     * eip
     */
    BANDWIDTH_NAME("bandwidthName", "弹性公网名称"),
    EIP_IP("ip", "弹性公网ip"),
    EIP_BIND("EIP_BIND", "绑定EIP"),

    /**
     * mysql
     */
    MYSQL_NAME("mysqlName", "数据库名称"),
    RESOURCE_TYPE("resourceType", "部署"),
    DATABASE_VERSION("databaseVersion", "版本"),

    /**
     * obs
     */
    OBS_NAME("obsName", "对象存储名称"),
    QUOTA("quota", "对象存储桶大小"),

    /**
     * slb
     */
    SLB_NAME("slbName", "负载均衡名称"),

    /**
     * nat
     */
    NAT_NAME("natName", "网关名称"),


    /**
     * 共用
     */
    FUNCTIONAL_MODULE("functionalModule", "功能模块"),
    TIME("time", "申请时长"),
    SECURITY_DOMAIN("securityDomain", "安全域"),
    MOUNT_RESOURCE("mountResource", "挂载资源"),
    EVS_MOUNT("EVS_MOUNT", "挂载云主机"),
    RESOURCE_ID("resourceId", "资源ID");

    private final String code;
    private final String desc;

    ProductAttrCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static ProductAttrCodeEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (ProductAttrCodeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return ECS_INSTANCE_NAME;
    }
}
