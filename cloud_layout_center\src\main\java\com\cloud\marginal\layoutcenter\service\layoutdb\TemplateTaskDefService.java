package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.model.entity.layout.TemplateTaskDef;

/**
 * <p>
 * 编排模板与任务关系配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface TemplateTaskDefService extends IService<TemplateTaskDef> {

    TemplateTaskDef getSingle(String templateId,String taskId);

    void delete(String id);
}
