package com.cloud.marginal.layoutcenter.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cloud.marginal.layoutcenter.common.ReqLog;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import javax.servlet.http.HttpServletRequest;

import java.util.*;

@Aspect
@Component
@Slf4j
public class WebReqLogAOP {

    @Pointcut("execution(public * com.cloud.marginal.layoutcenter.controller.*.*(..))")
    public void webLog() {
    }


    /**
     * 环绕通知，记录接收的参数和返回的结果到日志中
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("webLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        ReqLog reqLog = new ReqLog();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest req = attributes.getRequest();
        reqLog.setUrl(req.getRequestURL().toString());
        reqLog.setMethod(req.getMethod());
        Enumeration<String> nameEnum = req.getParameterNames();
        Map<String,Object> reqParam = new HashMap<>();
        while (nameEnum.hasMoreElements()) {
            String name = nameEnum.nextElement();
            reqParam.put(name,req.getParameter(name));
        }
        reqLog.setReqParam(reqParam);
        reqLog.setReqBody(this.getRequestBody(joinPoint));
        try{
            Object result = joinPoint.proceed();
            reqLog.setReturnResult(result);
            log.info("request info：{}",reqLog);
            return result;
        } catch (Throwable throwable) {
            log.error("request info：{}",reqLog);
            throw  throwable;
        }
    }

    private String getRequestBody(ProceedingJoinPoint joinPoint) {
        List<Object> ret = new ArrayList<>();
        Object[] args = joinPoint.getArgs();
        if(args!=null&&args.length>0){
            for (Object arg : args) {
                try{
                    JSON.toJSONString(arg);
                    ret.add(arg);
                }catch (Exception e){}
            }
        }
        return ret.size()>0?JSON.toJSONString(ret):null;
    }
}