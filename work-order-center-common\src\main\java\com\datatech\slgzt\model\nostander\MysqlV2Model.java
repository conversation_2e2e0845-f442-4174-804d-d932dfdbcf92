package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * 老的myqsl模型 已经被非标站住坑位了，无法修改下重新定义一个真正能通用开通mysql的模型
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月23日 15:44:05
 */
@Data
public class MysqlV2Model extends BaseProductModel {

    /**
     * 请求报文：
     * {
     *     "regionCode": "region.edge_prov_zj_vmware.qz",
     *     "azCode": "az.edge_prov_zj_vmware.qz",
     *     "orderId": "a5fb21a6a40a4af3943c595526f060602",
     *     "billId": "zyfceshi0213",
     *     "vpcId": "4171f301b23543a9b8163cb06e9bb85f",
     *     "subnetId": "6e05fff1120a4990b6ce00b90147c9ee",
     *     "gId": "ccl_test0606_rds_02",
     *     "rdsName": "ccl_test0606_rds_02",
     *     "flavorCode": "mysql.c1.medium.2.1",
     *     "engineVersion": "8.0",
     *     "deployType": "ALONE",
     *     "storageType": "SAS",
     *     "storageSize": 10,
     *     "rdsLoginName": "ccl_test0606_rds_02",
     *     "rdsPwd": "11qq!!QQ",
     *     "timeZone": "+8",
     *     "tableIsLower": 0,
     *     "dbEngine": 1
     * }
     */

    private String mysqlName;

    /**
     * 部署类型
     * ALONE：单机版
     *  COLONY：高可用版
     */
    private String deployType;

    //时区
    private String timeZone;

    //区分大小写
    private Integer tableIsLower;

    /**
     * 系统盘类型
     */
    private String sysDiskType;

    /**
     * 系统盘大小
     */
    private Integer sysDiskSize;

    /**
     * 数据库版本
     * 华三-5.7，
     * Vmware-5.6,5.7,8.0
     */
    private String engineVersion;

    //数据库引擎
    private Integer dbEngine;

    /**
     * 规格编码
     * 通过服务表获取类似ecs.c1.medium.4
     * 底层对应flavorCode
     */
    private String flavorCode;

    /**
     * 规格id
     */
    private String flavorId;
    /**
     * 规格名称
     * 通过服务表获取类似ecs.c1.medium.4
     * 底层对应flavorName
     */
    ;
    private String flavorName;

    /**
     * 规格类型:使用CATEGORY_NAME字段
     */
    private String flavorType;

    /**
     * 云主机用户名
     */
    private String userName;

    /**
     * 云主机密码
     */
    private String password;


    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 开通数量
     */
    private Integer openNum;

    private List<PlaneNetworkModel> planeNetworkModel;

}
