package com.datatech.slgzt.model.req.slb;

import lombok.Data;

/**
 * SLB监听器分页查询请求
 */
@Data
public class SlbListenerPageReq {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    
    /**
     * 监听器名称
     */
    private String listenerName;

    /**
     * slbId
     */
    private Long slbResourceDetailId;

    /**
     * VPC ID
     */
    private String vpcId;
    
    /**
     * SLB设备ID
     */
    private String slbDeviceId;
    
    /**
     * 运行状态
     */
    private String runningStatus;
} 