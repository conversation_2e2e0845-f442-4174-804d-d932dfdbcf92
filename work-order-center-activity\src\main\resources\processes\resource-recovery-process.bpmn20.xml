<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:yaoqiang="http://bpmn.sourceforge.net" exporter="Yaoqiang BPMN Editor" exporterVersion="5.3" expressionLanguage="http://www.w3.org/1999/XPath" id="m1632821341533" name="" targetNamespace="http://www.activiti.org/test" typeLanguage="http://www.w3.org/2001/XMLSchema" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://bpmn.sourceforge.net/schemas/BPMN20.xsd">
  <process id="resource-recovery-process" isClosed="false" isExecutable="true" name="resource-recovery-process" processType="None">
    <extensionElements>
      <yaoqiang:description/>
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
    </extensionElements>
    <exclusiveGateway gatewayDirection="Diverging" id="_3" name="entrance gateway">
      <incoming>_4</incoming>
      <outgoing>_7</outgoing>
      <outgoing>_37</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="_4" sourceRef="_2" targetRef="_3"/>
    <startEvent id="_2" isInterrupting="true" name="Start Event" parallelMultiple="false">
      <outgoing>_4</outgoing>
      <outputSet/>
    </startEvent>
    <userTask activiti:assignee="${tenant}" activiti:exclusive="true" completionQuantity="1" id="_5" implementation="##unspecified" isForCompensation="false" name="user_task" startQuantity="1">
      <incoming>_7</incoming>
      <incoming>_20</incoming>
      <incoming>_38</incoming>
      <outgoing>_10</outgoing>
      <outgoing>_40</outgoing>
    </userTask>
    <sequenceFlow id="_7" name="isTenant" sourceRef="_3" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${isTenant==1}]]>
      </conditionExpression>
    </sequenceFlow>
    <userTask activiti:assignee="${schema}" activiti:exclusive="true" completionQuantity="1" id="_9" implementation="##unspecified" isForCompensation="false" name="schema_administrator" startQuantity="1">
      <incoming>_10</incoming>
      <outgoing>_30</outgoing>
    </userTask>
    <sequenceFlow id="_10" name="pass" sourceRef="_5" targetRef="_9">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==1}]]>
      </conditionExpression>
    </sequenceFlow>
    <userTask activiti:assignee="${retreat_dimension}" activiti:exclusive="true" completionQuantity="1" id="_11" implementation="##unspecified" isForCompensation="false" name="retreat_dimension" startQuantity="1">
      <incoming>_12</incoming>
      <outgoing>_19</outgoing>
    </userTask>
    <sequenceFlow id="_12" name="isRetreatDimensionRecovery=1" sourceRef="_29" targetRef="_11">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==1 && schemaAdministratorChoose==1}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_41" name="isRetreatDimensionRecovery=0" sourceRef="_29" targetRef="_6">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==1 && schemaAdministratorChoose==0}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_47" name="recoveryType=networkOrVpc" sourceRef="_29" targetRef="_13">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==1 && schemaAdministratorChoose==2}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_20" name="no" sourceRef="_29" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==0}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_30" sourceRef="_9" targetRef="_29"/>
    <sequenceFlow id="_39" name="schemaAdministratorClose" sourceRef="_29" targetRef="_18">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==2 || schemaAdministratorChoose==3}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_40" name="userTaskClose" sourceRef="_5" targetRef="_18">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==2}]]>
      </conditionExpression>
    </sequenceFlow>
    <exclusiveGateway gatewayDirection="Diverging" id="_29" name="schema gateway">
      <incoming>_30</incoming>
      <outgoing>_12</outgoing>
      <outgoing>_20</outgoing>
      <outgoing>_39</outgoing>
      <outgoing>_41</outgoing>
      <outgoing>_47</outgoing>
    </exclusiveGateway>
    <userTask activiti:assignee="${system}" activiti:exclusive="true" completionQuantity="1" id="_36" implementation="##unspecified" isForCompensation="false" name="system" startQuantity="1">
      <incoming>_37</incoming>
      <outgoing>_38</outgoing>
    </userTask>
    <sequenceFlow id="_37" name="isSystem" sourceRef="_3" targetRef="_36">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${isTenant==2}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_38" name="pass" sourceRef="_36" targetRef="_5">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${message==1}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_8" sourceRef="_13" targetRef="_18"/>
    <endEvent id="_18" name="End Event">
      <incoming>_8</incoming>
      <incoming>_39</incoming>
      <incoming>_40</incoming>
      <incoming>_16</incoming>
      <incoming>_45</incoming>
      <inputSet/>
    </endEvent>
    <sequenceFlow id="_16" name="noHaveConfirmNetwork=0" sourceRef="_14" targetRef="_18">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${noHaveConfirmNetwork==0}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_17" name="noHaveConfirmNetwork=1" sourceRef="_14" targetRef="_13">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${noHaveConfirmNetwork==1}]]>
      </conditionExpression>
    </sequenceFlow>
    <exclusiveGateway gatewayDirection="Diverging" id="_14" name="Exclusive Gateway">
      <incoming>_21</incoming>
      <outgoing>_16</outgoing>
      <outgoing>_17</outgoing>
    </exclusiveGateway>
    <userTask activiti:assignee="${resource_recovery}" activiti:exclusive="true" completionQuantity="1" id="_6" isForCompensation="false" name="resource_recovery" startQuantity="1">
      <incoming>_19</incoming>
      <incoming>_41</incoming>
      <outgoing>_44</outgoing>
    </userTask>

    <exclusiveGateway gatewayDirection="Diverging" id="_43" name="network recovery tenant Gateway">
      <incoming>_44</incoming>
      <outgoing>_45</outgoing>
      <outgoing>_46</outgoing>
    </exclusiveGateway>

    <userTask activiti:assignee="${network_recovery_tenant}" activiti:exclusive="true" completionQuantity="1" id="_42" isForCompensation="false" name="tenant_task" startQuantity="1">
      <incoming>_46</incoming>
      <outgoing>_21</outgoing>
    </userTask>

    <sequenceFlow id="_45" name="noHaveNetwork=0" sourceRef="_43" targetRef="_18">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${noHaveNetwork==0}]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_46" name="noHaveNetwork=1" sourceRef="_43" targetRef="_42">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[${noHaveNetwork==1}]]>
      </conditionExpression>
    </sequenceFlow>

    <userTask activiti:assignee="${network_recovery}" activiti:exclusive="true" completionQuantity="1" id="_13" implementation="##unspecified" isForCompensation="false" name="network_recovery" startQuantity="1">
      <incoming>_17</incoming>
      <incoming>_47</incoming>
      <outgoing>_8</outgoing>
    </userTask>

    <sequenceFlow id="_19" name="d-pass" sourceRef="_11" targetRef="_6"/>
    <sequenceFlow id="_21" name="r-pass" sourceRef="_42" targetRef="_14"/>
    <sequenceFlow id="_44" name="r-pass" sourceRef="_6" targetRef="_43"/>

  </process>
  <bpmndi:BPMNDiagram id="Yaoqiang_Diagram-resource-recovery-process" name="Untitled Diagram" resolution="96.0">
    <bpmndi:BPMNPlane bpmnElement="resource-recovery-process">
      <bpmndi:BPMNShape bpmnElement="_3" id="Yaoqiang-_3" isMarkerVisible="true">
        <omgdc:Bounds height="42.0" width="42.0" x="474.5853021780276" y="126.56191581705315"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="95.0" x="448.09" y="170.56"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_2" id="Yaoqiang-_2">
        <omgdc:Bounds height="32.0" width="32.0" x="478.5853021780276" y="53.89242781767783"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="59.0" x="465.09" y="93.87"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_5" id="Yaoqiang-_5">
        <omgdc:Bounds height="55.0" width="85.0" x="336.57750672436947" y="176.08752854368703"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="54.0" x="352.08" y="195.67"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_9" id="Yaoqiang-_9">
        <omgdc:Bounds height="55.0" width="85.0" x="333.57750672436947" y="289.2320723608963"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="72.0" x="340.08" y="301.31"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_11" id="Yaoqiang-_11">
        <omgdc:Bounds height="55.0" width="85.0" x="335.72138199711094" y="465.3694892689154"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="72.0" x="342.22" y="477.45"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_13" id="Yaoqiang-_13">
        <omgdc:Bounds height="55.0" width="85.0" x="232.79331963348164" y="586.8017836316077"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="34.84" width="74.0" x="238.29" y="598.88"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_29" id="Yaoqiang-_29" isMarkerVisible="true">
        <omgdc:Bounds height="42.0" width="42.0" x="357.92819746264433" y="393.11704526350434"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="90.0" x="333.93" y="437.12"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_36" id="Yaoqiang-_36">
        <omgdc:Bounds height="55.0" width="85.0" x="598.2607078880407" y="178.22351470763232"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="41.0" x="620.26" y="197.81"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_18" id="Yaoqiang-_18">
        <omgdc:Bounds height="32.0" width="32.0" x="358.76548054118837" y="666.867262903256"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="54.0" x="347.77" y="706.85"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_14" id="Yaoqiang-_14" isMarkerVisible="true">
        <omgdc:Bounds height="42.0" width="42.0" x="355.27272727272725" y="542.3181818181818"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="100.0" x="326.27" y="586.32"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_6" id="Yaoqiang-_6">
        <omgdc:Bounds height="55.0" width="85.0" x="496.4312500583874" y="465.62252709808865"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="100.0" x="488.93" y="485.2"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="_19" id="Yaoqiang-_19">
        <omgdi:waypoint x="421.0" y="492.8694892689154"/>
        <omgdi:waypoint x="496.0" y="493.12252709808865"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="40.0" x="438.58" y="483.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_17" id="Yaoqiang-_17">
        <omgdi:waypoint x="355.31818181818176" y="563.3181818181818"/>
        <omgdi:waypoint x="283.0" y="592.0"/>
        <omgdi:waypoint x="283.0" y="587.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="67.0" x="273.82" y="553.4"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_16" id="Yaoqiang-_16">
        <omgdi:waypoint x="374.76548054118837" y="582.7654805411884"/>
        <omgdi:waypoint x="374.76548054118837" y="667.001718822841"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="67.0" x="341.27" y="614.97"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_8" id="Yaoqiang-_8">
        <omgdi:waypoint x="275.0" y="642.0"/>
        <omgdi:waypoint x="275.0" y="688.0"/>
        <omgdi:waypoint x="359.80131584642936" y="688.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="291.4" y="678.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_7" id="Yaoqiang-_7">
        <omgdi:waypoint x="475.43808418294685" y="147.56191581705315"/>
        <omgdi:waypoint x="379.0" y="201.0"/>
        <omgdi:waypoint x="379.0" y="176.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="47.0" x="389.5" y="137.64"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_12" id="Yaoqiang-_12">
        <omgdi:waypoint x="378.92819746264433" y="434.92819746264433"/>
        <omgdi:waypoint x="378.92819746264433" y="465.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="30.0" x="363.93" y="440.05"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_10" id="Yaoqiang-_10">
        <omgdi:waypoint x="377.57750672436947" y="231.0"/>
        <omgdi:waypoint x="377.57750672436947" y="289.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="30.0" x="362.58" y="250.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_4" id="Yaoqiang-_4">
        <omgdi:waypoint x="494.5853021780276" y="85.99462490077374"/>
        <omgdi:waypoint x="494.5853021780276" y="128.41469782197242"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="491.59" y="97.29"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_21" id="Yaoqiang-_21">
        <omgdi:waypoint x="549.8908713857942" y="521.0"/>
        <omgdi:waypoint x="549.8908713857942" y="563.9231743238687"/>
        <omgdi:waypoint x="396.0768256761313" y="563.9231743238687"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="38.0" x="475.45" y="554.01"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_20" id="Yaoqiang-_20">
        <omgdi:waypoint x="358.11704526350434" y="414.11704526350434"/>
        <omgdi:waypoint x="283.0" y="308.0"/>
        <omgdi:waypoint x="337.0" y="203.58752854368703"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="18.0" x="274.0" y="309.49"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_38" id="Yaoqiang-_38">
        <omgdi:waypoint x="598.0" y="205.72351470763232"/>
        <omgdi:waypoint x="609.0" y="219.0"/>
        <omgdi:waypoint x="422.0" y="205.72351470763232"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="30.0" x="495.0" y="195.81"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_37" id="Yaoqiang-_37">
        <omgdi:waypoint x="512.0" y="143.0"/>
        <omgdi:waypoint x="645.0" y="143.0"/>
        <omgdi:waypoint x="645.0" y="178.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="50.0" x="571.0" y="133.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_30" id="Yaoqiang-_30">
        <omgdi:waypoint x="378.92819746264433" y="344.0"/>
        <omgdi:waypoint x="378.92819746264433" y="393.07180253735567"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="6.0" x="375.93" y="358.62"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_39" id="BPMNEdge__39" sourceElement="_29" targetElement="_18">
        <omgdi:waypoint x="269.0" y="742.0"/>
        <omgdi:waypoint x="368.0" y="746.9046978667511"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_40" id="BPMNEdge__40" sourceElement="_5" targetElement="_18">
        <omgdi:waypoint x="269.0" y="742.0"/>
        <omgdi:waypoint x="368.0" y="746.9046978667511"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_41" id="BPMNEdge__41" sourceElement="_29" targetElement="_6">
        <omgdi:waypoint x="269.0" y="742.0"/>
        <omgdi:waypoint x="368.0" y="746.9046978667511"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_44" id="BPMNEdge__44" sourceElement="_6" targetElement="_43">
        <omgdi:waypoint x="269.0" y="742.0"/>
        <omgdi:waypoint x="368.0" y="746.9046978667511"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="_42" id="Yaoqiang-_42">
        <omgdc:Bounds height="55.0" width="85.0" x="496.4312500583874" y="465.62252709808865"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="100.0" x="488.93" y="485.2"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_43" id="Yaoqiang-_43" isMarkerVisible="true">
        <omgdc:Bounds height="42.0" width="42.0" x="355.27272727272725" y="542.3181818181818"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="100.0" x="326.27" y="586.32"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="_45" id="BPMNEdge__45" sourceElement="_43" targetElement="_18">
        <omgdi:waypoint x="269.0" y="742.0"/>
        <omgdi:waypoint x="368.0" y="746.9046978667511"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_46" id="BPMNEdge__46" sourceElement="_43" targetElement="_42">
        <omgdi:waypoint x="269.0" y="742.0"/>
        <omgdi:waypoint x="368.0" y="746.9046978667511"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_47" id="BPMNEdge__47" sourceElement="_29" targetElement="_13">
        <omgdi:waypoint x="269.0" y="742.0"/>
        <omgdi:waypoint x="368.0" y="746.9046978667511"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="19.84" width="62.0" x="292.42" y="737.08"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
