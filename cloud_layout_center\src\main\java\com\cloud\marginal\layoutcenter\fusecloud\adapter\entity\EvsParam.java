package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

@Data
public class EvsParam {

    /**
     * 该字段为调用资源中心返回的id
     * 此时是创建云硬盘，然后挂载到云主机的时候
     */
    private String id;

    /**
     * 该字段为产品中心调用过来的硬盘id
     * 此时是只挂载硬盘到云主机，而不是创建硬盘然后挂载
     */
    private String evsId;

    /**
     * 云硬盘大小,挂盘时可选择已存在的云硬盘,也可以选择大小,创建+挂盘一个接口完成
     */
    private Long size;

    private String volumeType;

    private String regionCode;

    private String volumeName;

    private Long tenantId;

    private String azCode;

    private Long volumeSize;

    private String vmId;

    //资源中心id
    private String resourceId;

    private String outInstanceId;

    private String gId;

    public String getgId() {
        return gId;
    }

    public void setgId(String gId) {
        this.gId = gId;
    }
}
