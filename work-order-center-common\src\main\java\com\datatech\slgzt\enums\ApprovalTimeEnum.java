package com.datatech.slgzt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 工单流状态枚举
 */
@Getter
@AllArgsConstructor
public enum ApprovalTimeEnum {

    //一个月
    ONE_MONTH("one_month", "一个月"),
    //三个月
    THREE_MONTH("three_months", "三个月"),
    //六个月
    SIX_MONTH("six_months", "六个月"),
    //一年
    ONE_YEAR("one_year", "一年"),
    //两年
    TWO_YEAR("two_years", "两年"),
    DEFAULT("", "暂时未定义该申请时长"),

    ;
    //三年

    private final String code;
    private final String name;


    /**
     * 通过code获取enum
     */
    public static ApprovalTimeEnum getByCode(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (ApprovalTimeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return ApprovalTimeEnum.DEFAULT;
    }

    /**
     * 通过name获取enum
     */
    public static ApprovalTimeEnum getByName(String name) {
        if (StringUtils.isNotEmpty(name)) {
            for (ApprovalTimeEnum value : values()) {
                if (value.getName().equals(name)) {
                    return value;
                }
            }
        }
        return ApprovalTimeEnum.DEFAULT;
    }
}
