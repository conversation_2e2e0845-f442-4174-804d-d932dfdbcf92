<?xml version="1.0" encoding="UTF-8"?>

<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出,可以不设置,当设置成trace时,你会看到log4j2内部各种详细输出-->
<!--monitorInterval: Log4j能够自动检测修改配置 文件和重新配置本身,设置间隔秒数-->
<configuration monitorInterval="5">
    <!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->

    <!-- 自己设置属性,后面通过${}来访问 -->
    <properties>
        <!--日志存放目录-->
        <property name="LOG_HOME">logs</property>
        <!--日志名称-->
        <property name="LOG_NAME">layout_center</property>
        <!--日志格式-文件-->
        <property name="LOG_FORMAT">[%d{yyyy-MM-dd HH:mm:ss.SSS}] %p %t %c[%L] - %m %n</property>
        <!--日志格式-控制台-->
        <property name="LOG_FORMAT_CONSOLE">%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{%-5level} [%t] %highlight{%c{1.}.%M(%L)}: %msg%n%throwable</property>
        <!--备份目录- 根据年月建立文件夹 -->
        <property name="BACKUP_HOME">${LOG_HOME}/$${date:yyyy-MM}</property>
        <!--备份频率-->
        <property name="BACK_HZ">%d{yyyy-MM-dd}</property>

        <Property name="logstashHost">************</Property>
        <Property name="logstashPort">5044</Property>
        <Property name="serviceName">cloud_layout_center</Property>
    </properties>

    <appenders>
        <!--控制台日志-->
        <console name="console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${LOG_FORMAT_CONSOLE}" disableAnsi="false" noConsoleNoAnsi="false"/>
            <!--控制台只输出level及其以上级别的信息（onMatch）,其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
        </console>

        <!--文件会打印出所有信息,这个log每次运行程序会自动清空,由append属性决定,适合临时测试用-->
        <File name="fileLog" fileName="${LOG_HOME}/${LOG_NAME}/${serviceName}.log" append="false">
            <PatternLayout pattern="${LOG_FORMAT}"/>
        </File>

        <!-- 这个会打印出所有的info及以下级别的信息,每次大小超过size,则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩,作为存档-->
        <RollingFile name="infoLog" fileName="${LOG_HOME}/${LOG_NAME}/info.log" filePattern="${LOG_HOME}/bak/${LOG_NAME}-INFO-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch）,其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_FORMAT}"/>
            <Policies>
                <!--interval属性用来指定多久滚动一次,默认是1 hour-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置,则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>

        <!-- 这个会打印出所有的warn及以下级别的信息,每次大小超过size,则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩,作为存档-->
        <RollingFile name="warnLog" fileName="${LOG_HOME}/${LOG_NAME}/warn.log" filePattern="${LOG_HOME}/bak/${LOG_NAME}-WARN-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch）,其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_FORMAT}"/>
            <Policies>
                <!--interval属性用来指定多久滚动一次,默认是1 hour-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置,则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>

        <!-- 这个会打印出所有的error及以下级别的信息,每次大小超过size,则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩,作为存档-->
        <RollingFile name="errorLog" fileName="${LOG_HOME}/${LOG_NAME}/error.log" filePattern="${LOG_HOME}/bak/${LOG_NAME}-ERROR-%d{yyyy-MM-dd}_%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch）,其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_FORMAT}"/>
            <Policies>
                <!--interval属性用来指定多久滚动一次,默认是1 hour-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置,则默认为最多同一文件夹下7个文件开始覆盖-->
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
        <!-- Logstash TCP 输出 -->
        <Socket name="logstash" host="${logstashHost}" port="${logstashPort}" >
            <JsonLayout complete="true" compact="true" eventEol="true" properties="true">
                <KeyValuePair key="service_name" value="${serviceName}" />
            </JsonLayout>
        </Socket>

        <Async name="AsyncLogstash" blocking="false">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <AppenderRef ref="logstash" />
        </Async>

        <Async name="AsyncInfoLog" blocking="false">
            <AppenderRef ref="infoLog" />
        </Async>

        <Async name="AsyncWarnLog" blocking="false">
            <AppenderRef ref="warnLog" />
        </Async>
        <Async name="AsyncErrorLog" blocking="false">
            <AppenderRef ref="errorLog" />
        </Async>
    </appenders>

    <!--Logger节点用来单独指定日志的形式,比如要为指定包下的class指定不同的日志级别等。-->
    <!--然后定义loggers,只有定义了logger并引入的appender,appender才会生效-->
    <loggers>
        <AsyncRoot level="info">
            <!-- 输出到控制台 -->
            <appender-ref ref="console"/>
            <!-- 输出到文件 -->
            <appender-ref ref="fileLog"/>
            <appender-ref ref="AsyncInfoLog"/>
            <appender-ref ref="AsyncWarnLog"/>
            <appender-ref ref="AsyncErrorLog"/>
            <appender-ref ref="AsyncLogstash" level="error"/>
        </AsyncRoot>
    </loggers>

</configuration>
