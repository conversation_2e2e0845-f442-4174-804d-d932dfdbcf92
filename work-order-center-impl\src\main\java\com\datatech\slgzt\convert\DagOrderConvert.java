package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.DagOrderDO;
import com.datatech.slgzt.model.dto.DagOrderDTO;

import org.mapstruct.Mapper;


/**
 * DAG工单对象转换器
 */
@Mapper(componentModel = "spring")
public interface DagOrderConvert {


    /**
     * DTO转DO
     */
    DagOrderDO dto2do(DagOrderDTO dto);

    /**
     * DO转DTO
     */
    DagOrderDTO do2dto(DagOrderDO entity);
} 