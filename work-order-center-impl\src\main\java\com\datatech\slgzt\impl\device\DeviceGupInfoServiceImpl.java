package com.datatech.slgzt.impl.device;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.convert.DeviceVirtualInfoConvert;
import com.datatech.slgzt.enums.DeviceMetricSourceEnum;
import com.datatech.slgzt.enums.DeviceModelTFEnum;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.manager.DeviceVirtualInfoManager;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.service.device.DeviceGupInfoService;
import com.datatech.slgzt.service.device.DeviceGupMetricsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DeviceGupInfoServiceImpl implements DeviceGupInfoService {

    @Resource
    private DeviceGpuInfoManager deviceGpuInfoManager;

    @Resource
    private List<DeviceGupMetricsService> deviceGupMetricsService;

    @Resource
    private DeviceVirtualInfoManager deviceVirtualInfoManager;

    @Resource
    private DeviceVirtualInfoConvert deviceVirtualInfoConvert;


    /**
     * 获取显卡所有的指标统计数据(虚拟+ 物理 + NPU)
     */
    @Override
    public List<DeviceCardMetricsDTO> queryDeviceCardMetrics() {
        List<DeviceCardMetricsDTO> deviceVirtualMetricList = new ArrayList<>();
        deviceGupMetricsService.forEach(item -> {
            deviceVirtualMetricList.addAll(item.calculateGpuDeviceMetric());
        });
        return deviceVirtualMetricList;
    }


    /**
     * 获取远端物理设备显卡信息
     *
     * @return
     */
    @Override
    public void syncRemoteDeviceDataInfo() {
        //获取Gpu 物理显卡 虚拟显卡信息 NPU 显卡基本数据数据
        deviceGupMetricsService.forEach(DeviceGupMetricsService::syncRemoteDeviceDataInfo);
    }

    /**
     * 比较本地数据库与远程显卡数据
     *
     * @param deviceGpuInfoMap
     */

    private void compareLocalDeviceInfo(Map<String, List<DeviceGpuInfoDTO>> deviceGpuInfoMap) {
        log.info("获取远程显卡数据信息是：{}", deviceGpuInfoMap);
        if (CollectionUtil.isNotEmpty(deviceGpuInfoMap)) {
            Optional<String> deviceSourceOption = deviceGpuInfoMap.keySet().stream().findFirst();
            if (deviceSourceOption.isPresent()) {
                String deviceSource = deviceSourceOption.get();
                if (DeviceMetricSourceEnum.QD_PHYSICAL_SOURCE.getCode().equals(deviceSource)) {
                    //本地数据数据与远程数据比较
                    List<DeviceGpuInfoDTO> localDeviceInfoList = queryLocalDeviceInfos(new DeviceInfoQuery().setDeviceType("GPU"));
                    List<String> deviceIdList = localDeviceInfoList.stream()
                                                                   .map(DeviceGpuInfoDTO::getDeviceId)
                                                                   .collect(Collectors.toList());
                    List<DeviceGpuInfoDTO> remoteDeviceInfoList = deviceGpuInfoMap.get(DeviceMetricSourceEnum.QD_PHYSICAL_SOURCE.getCode());
                    diffInsertRemoteDeviceData(remoteDeviceInfoList, deviceIdList);
                }
                //虚拟显卡
                if (DeviceMetricSourceEnum.QD_VIRTUAL_SOURCE.getCode().equals(deviceSource)) {
                    List<DeviceVirtualInfoDTO> deviceVirtualInfoList = deviceVirtualInfoManager.selectDeviceVirtualInfoList(new DeviceInfoQuery());
                    List<String> deviceIdList = deviceVirtualInfoList.stream()
                                                                     .map(DeviceVirtualInfoDTO::getDeviceId)
                                                                     .collect(Collectors.toList());
                    List<DeviceGpuInfoDTO> remoteDeviceInfoList = deviceGpuInfoMap.get(DeviceMetricSourceEnum.QD_VIRTUAL_SOURCE.getCode());
                    if (CollectionUtil.isNotEmpty(remoteDeviceInfoList)) {
                        for (DeviceGpuInfoDTO remoteDevice : remoteDeviceInfoList) {
                            if (!deviceIdList.contains(remoteDevice.getDeviceId())) {
                                DeviceVirtualInfoDTO deviceVirtualInfoDTO = deviceVirtualInfoConvert.info2VirtualDto(remoteDevice);

                                deviceVirtualInfoDTO.setAreaCode("金华");
                                deviceVirtualInfoDTO.setMemory(Objects.isNull(remoteDevice.getMemory()) ? 0 : remoteDevice.getMemory() / 1024);
                                deviceVirtualInfoManager.create(deviceVirtualInfoDTO);

                            }
                        }
                    }
                    //删除设备不能存在的虚拟显卡
                    List<String> remoteDeviceIds = remoteDeviceInfoList.stream()
                                                                       .map(DeviceGpuInfoDTO::getDeviceId)
                                                                       .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(deviceVirtualInfoList)) {
                        List<DeviceVirtualInfoDTO> removeLocalDevice = deviceVirtualInfoList.stream()
                                                                                            .filter(device -> !remoteDeviceIds.contains(device.getDeviceId()))
                                                                                            .collect(Collectors.toList());
                        log.info("删除设备不存在的虚拟显卡数据：{}", removeLocalDevice);
                        //删除设备不存在的虚拟显卡
                        if (CollectionUtil.isNotEmpty(removeLocalDevice)) {

                            deviceVirtualInfoManager.deleteBatch(removeLocalDevice.stream()
                                                                                  .map(DeviceVirtualInfoDTO::getId)
                                                                                  .collect(Collectors.toList()));

                        }
                    }
                }

                if (DeviceMetricSourceEnum.NPU_CHIP_SOURCE.getCode().equals(deviceSource)) {
                    //本地数据数据与远程数据比较
                    List<DeviceGpuInfoDTO> localDeviceInfoList = queryLocalDeviceInfos(new DeviceInfoQuery().setDeviceType("NPU"));
                    List<String> pdeviceIdList = localDeviceInfoList.stream()
                                                                    .map(DeviceGpuInfoDTO::getDeviceId)
                                                                    .collect(Collectors.toList());
                    List<DeviceGpuInfoDTO> remoteDeviceInfoList = deviceGpuInfoMap.get(DeviceMetricSourceEnum.NPU_CHIP_SOURCE.getCode());

                    diffInsertRemoteDeviceData(remoteDeviceInfoList, pdeviceIdList);

                }
            }
        }
    }


    /**
     * 将本地不存在远程存在的数据存入本地库
     *
     * @param remoteDeviceInfoList
     * @param pdeviceIdList
     */

    private void diffInsertRemoteDeviceData(List<DeviceGpuInfoDTO> remoteDeviceInfoList, List<String> pdeviceIdList) {
        log.info("比较远程显卡数据信息是：{}，本地数据：{}", remoteDeviceInfoList, pdeviceIdList);
        AtomicInteger count = new AtomicInteger();
        if (CollectionUtil.isNotEmpty(remoteDeviceInfoList)) {
            remoteDeviceInfoList.forEach(remoteDevice -> {
                if (!pdeviceIdList.contains(remoteDevice.getDeviceId())) {
                    remoteDevice.setAreaCode("金华");
                    remoteDevice.setModelName(DeviceModelTFEnum.getByModelName(remoteDevice.getDeviceName())
                                                               .getModelName());
                    remoteDevice.setBusinessSystemName("业务" + count.getAndIncrement());
                    deviceGpuInfoManager.create(remoteDevice);
                }
            });
        }
    }


    /**
     * 条件查询获取本地数据库物理卡
     *
     * @param query
     * @return
     */
    @Override
    public List<DeviceGpuInfoDTO> queryLocalDeviceInfos(DeviceInfoQuery query) {
        return Optional.ofNullable(deviceGpuInfoManager.selectDeviceGpuInfoList(query)).orElse(Lists.newArrayList());
    }


}
