package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.CatalogueDomainConfigDO;
import com.datatech.slgzt.model.dto.CatalogueDomainConfigDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 目录域配置转换器
 */
@Mapper(componentModel = "spring")
public interface CatalogueDomainConfigManagerConvert {

    /**
     * DTO转DO
     */
    CatalogueDomainConfigDO dto2do(CatalogueDomainConfigDTO dto);

    /**
     * DO转DTO
     */
    CatalogueDomainConfigDTO do2dto(CatalogueDomainConfigDO entity);

    /**
     * DO列表转DTO列表
     */
    List<CatalogueDomainConfigDTO> doList2dtoList(List<CatalogueDomainConfigDO> list);
} 