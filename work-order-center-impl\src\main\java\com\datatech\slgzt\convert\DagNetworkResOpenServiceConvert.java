package com.datatech.slgzt.convert;

import org.mapstruct.Mapper;

import com.datatech.slgzt.model.dto.network.NetworkOrderDTO;
import com.datatech.slgzt.model.dto.network.NetworkSubnetOrderDTO;
import com.datatech.slgzt.model.nostander.NetworkUnTaskModel;
import com.datatech.slgzt.model.nostander.SubnetModel;

/**
 * 编排network资源转换器
 */
@Mapper(componentModel = "spring")
public interface DagNetworkResOpenServiceConvert {

    NetworkUnTaskModel networkDto2Model(NetworkOrderDTO dto);

    NetworkOrderDTO networkModel2dto(NetworkUnTaskModel model);

    SubnetModel subnetDto2Model(NetworkSubnetOrderDTO dto);

    NetworkSubnetOrderDTO subnetModel2dto(SubnetModel model);
}