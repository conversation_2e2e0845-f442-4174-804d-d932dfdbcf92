package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * @program: workordercenterproject
 * @description: nas回收参数
 * @author: LK
 * @create: 2025-06-12 09:21
 **/
@Data
public class RecoveryNasModel extends BaseReconveryProductModel {

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * nas名称
     */
    private String name;

    /**
     * 路径
     */
    private String path;

    /**
     * 存储大小
     */
    public String storageSize;
}
