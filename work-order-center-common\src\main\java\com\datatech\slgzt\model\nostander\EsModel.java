package com.datatech.slgzt.model.nostander;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description: es模型
 * @author: LK
 * @create: 2025-06-23 10:22
 **/
@Data
public class EsModel extends BaseProductModel {

    /**
     * 名称
     */
    private String name;

    /**
     * 日均增量数据
     */
    private String averageDailyIncrementData;

    /**
     * 保留时间（天）
     */
    private Integer retainTime;

    /**
     * 副本数量
     */
    private Integer numberOfReplicas;

    /**
     * 磁盘大小
     */
    private String diskSize;

    /**
     * 申请时长
     */
    private String applyTime;


    /**
     * 索引模板
     */
    private JSONObject indexTemplate;


}
