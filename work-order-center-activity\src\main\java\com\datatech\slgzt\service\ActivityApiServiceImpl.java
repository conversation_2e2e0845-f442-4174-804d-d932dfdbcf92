package com.datatech.slgzt.service;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.constants.ProcessConstants;
import com.datatech.slgzt.enums.GlobalExceptionEnum;
import com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.model.bpmn.*;
import com.datatech.slgzt.service.bpmn.ActivityApiService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.*;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityApiServiceImpl implements ActivityApiService {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    /**
     * 流程部署
     *
     * @param deploymentVo 部署文件信息
     * @return 流程定义信息
     */
    @Override
    public String createDeployment(DeployVo deploymentVo) {
        if (deploymentVo.getDeployId() != null) {
            repositoryService.deleteDeployment(deploymentVo.getDeployId(), true);
        }
        Deployment deployment = repositoryService.createDeployment()
                .addClasspathResource(deploymentVo.getClasspathResource())
                .key(deploymentVo.getKey())
                .name(deploymentVo.getName())
                .deploy();
        log.info("流程部署名称:{},流程部署ID:{}", deployment.getName(), deployment.getId());
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .deploymentId(deployment.getId())
                .singleResult();
        log.info("流程定义文件:{},流程定义ID:{}", processDefinition.getName(), processDefinition.getId());
        return "部署成功";
    }


    /**
     * 流程启动
     *
     * @param runProcessVo 启动流程信息
     * @return 流程实例信息
     */
    @Override
    public String runProcessInstance(RunProcessVo runProcessVo) {
        runProcessVo.getAssignee().put(ProcessConstants.ASSIGNEE_USER_ID, runProcessVo.getUserId());
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(runProcessVo.getProcessKey(), runProcessVo.getOrderId().toString(), runProcessVo.getAssignee());
        log.info("流程实例id:{},流程定义id:{}", pi.getId(), pi.getProcessDefinitionId());
        return pi.getProcessInstanceId();
    }

    /**
     * 查询当前用户任务
     *
     * @param userId assignee信息
     * @return list
     */
    @Override
    public List<TaskVo> queryPersonalTask(Long userId) {
        List<Task> tasks = taskService.createTaskQuery().taskAssignee(userId.toString()).list();
        List<TaskVo> taskVoList = new ArrayList<>();
        for (Task task : tasks) {
            TaskVo taskVo = new TaskVo().setId(task.getId()).setName(task.getName()).setProIntId(task.getProcessInstanceId());
            taskVoList.add(taskVo);
        }
        return taskVoList;
    }


    /**
     * 查询流程定义所有节点及当前节点
     *
     * @param processInstanceId 流程实例id
     */
    @Override
    public ActivityTaskVo queryTasksByProIntId(String processInstanceId) {
        ActivityTaskVo activityTaskVo = new ActivityTaskVo();
        List<HistoricTaskInstance> instances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .list();


        if (CollectionUtil.isEmpty(instances)) {
            log.warn("流程实例已结束或流程实例id参数错误！");
            return null;
        }

        String processDefinitionId = instances.get(0).getProcessDefinitionId();
        BpmnModel model = repositoryService.getBpmnModel(processDefinitionId);
        if (model != null) {
            Collection<FlowElement> flowElements = model.getMainProcess().getFlowElements();
            List<InnerTask> allTasks = new ArrayList<>();
            for (FlowElement e : flowElements) {
                String clazz = e.getClass().toString();
                if (clazz.endsWith(ProcessConstants.USER_TASK)) {
                    InnerTask taskVo = new InnerTask();
                    taskVo.setTask(e.getName());
                    allTasks.add(taskVo);
                }
            }

            InnerTask inner= new InnerTask();
            inner.setTask(ActivitiStatusEnum.AUTODIT_END.getNode());
            inner.setTaskName(ActivitiStatusEnum.AUTODIT_END.getNodeRemark());
            allTasks.add(inner);
            activityTaskVo.setAllTasks(allTasks);
            Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
            if (null == task) {
                InnerTask innerTask = allTasks.get(allTasks.size() - 1);
                activityTaskVo.setCurrentTask(innerTask.getTask());
            } else {
                activityTaskVo.setCurrentTask(task.getName());
            }
        } else {
            throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_DEFINITION_IS_NOT_FOUND);
        }
        return activityTaskVo;
    }
    /**
     * 查询流程定义所有节点(树)及当前节点
     *
     * @param processInstanceId 流程实例id
     */
    @Override
    public ActivityTaskTreeVo queryTasksTreeByProIntId(String processInstanceId) {
        List<HistoricTaskInstance> instances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .list();
        if (CollectionUtil.isEmpty(instances)) {
            log.warn("流程实例已结束或流程实例id参数错误！");
            return null;
        }

        String processDefinitionId = instances.get(0).getProcessDefinitionId();
        TaskTreeNodeDTO taskTree = getTaskTreeByProDefId(processDefinitionId);
        ActivityTaskTreeVo activityTaskVo = new ActivityTaskTreeVo();
        activityTaskVo.setRoot(taskTree);
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
        if (null == task) {
            activityTaskVo.setCurrentTaskBpmnName(ActivitiStatusEnum.AUTODIT_END.getNode());
        } else {
            activityTaskVo.setCurrentTaskBpmnName(task.getName());
        }
        return activityTaskVo;
    }

    /**
     * 查询流程定义所有节点(树)及当前节点
     *
     * @param processKey
     */
    @Override
    public TaskTreeNodeDTO queryTasksTreeByProKey(String processKey) {
        // 查询指定 key 的最新版本流程定义
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(processKey)  // 替换为实际 key
                .latestVersion()
                .singleResult();
        if (processDefinition == null) {
            return null;
        }
        return getTaskTreeByProDefId(processDefinition.getId());
    }

    private TaskTreeNodeDTO getTaskTreeByProDefId(String processDefinitionId) {
        BpmnModel model = repositoryService.getBpmnModel(processDefinitionId);

        if (model != null) {
            Map<String, FlowElement> flowElementMap = model.getMainProcess().getFlowElementMap();
            FlowElement startEvent = flowElementMap.values().stream().filter(e -> e instanceof StartEvent).findFirst().get();
            return buildTree(startEvent, flowElementMap);
        } else {
            throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_DEFINITION_IS_NOT_FOUND);
        }
    }

    /**
     * 获取下一个节点
     *
     * @param processInstanceId 流程实例id
     */
    @Override
    public TaskNodeDTO queryNextTasksByProIntId(String processInstanceId) {
        List<HistoricTaskInstance> instances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .list();
        if (CollectionUtil.isEmpty(instances)) {
            log.warn("流程实例已结束或流程实例id参数错误！");
            return null;
        }
        for (HistoricTaskInstance historicTaskInstance : instances) {
            if (null== historicTaskInstance.getEndTime()){
                TaskNodeDTO ret = new TaskNodeDTO();
                ret.setBpmnName(historicTaskInstance.getName());
                ret.setAssignee(historicTaskInstance.getAssignee());
                return ret;
            }
        }
        return null;
    }

    @Override
    public CurrentBeforeTaskNodeVO queryCurrentAndHistoryTaskNode(String processInstanceId) {
        // allPathNode(processInstanceId);
        // 获取历史活动实例列表
        List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricActivityInstanceStartTime().desc() // 按开始时间降序排序
                .list()
                .stream()
                .filter(item -> ProcessConstants.USER_TASK.equalsIgnoreCase(item.getActivityType()))
                .collect(Collectors.toList());

        // 打印历史活动实例信息
        CurrentBeforeTaskNodeVO nodeVO = new CurrentBeforeTaskNodeVO();
        Set<String> executedActivityIds = new HashSet<>();
        for (int i = 0; i < historicActivityInstances.size(); i++) {
            HistoricActivityInstance instance = historicActivityInstances.get(i);
            if (i == 0) {
                nodeVO.setActivityNodeCode(instance.getActivityName()).setActivityNodeId(instance.getId());
            }
            executedActivityIds.add(instance.getActivityId());
            nodeVO.addNodeElement(instance.getActivityName(), instance.getId());
        }

        // 加载BPMN模型
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(historicActivityInstances.get(0).getProcessDefinitionId())
                .singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());

        // 遍历BPMN模型中的所有节点
        Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
        Set<String> allNodeIds = new HashSet<>();
        Set<String> notExecutedNodes = new HashSet<>();

        for (FlowElement flowElement : flowElements) {
            allNodeIds.add(flowElement.getId());
            if (!executedActivityIds.contains(flowElement.getId())) {
                notExecutedNodes.add(flowElement.getId());
            }
        }


        return nodeVO;
    }

    private static void exploreGateways(FlowElement currentElement, Set<String> visited, BpmnModel bpmnModel) {
        if (!visited.add(currentElement.getId())) {
            return; // 防止循环引用
        }

        System.out.println("Visiting: " + currentElement.getId() + ", Name: " + currentElement.getName());

        if (currentElement instanceof Gateway) {
            System.out.println("Gateway found: " + currentElement.getName());
            // 对于每个网关，探索其所有的流出线
            for (SequenceFlow outgoingFlow : ((Gateway) currentElement).getOutgoingFlows()) {
                FlowElement targetFlowElement = bpmnModel.getFlowElement(outgoingFlow.getTargetRef());
                if (targetFlowElement != null) {
                    exploreGateways(targetFlowElement, visited, bpmnModel); // 递归调用
                }
            }
        } else if (currentElement instanceof FlowNode) { // 对于其他类型的流节点
            for (SequenceFlow outgoingFlow : ((FlowNode) currentElement).getOutgoingFlows()) {
                FlowElement targetFlowElement = bpmnModel.getFlowElement(outgoingFlow.getTargetRef());
                if (targetFlowElement != null) {
                    exploreGateways(targetFlowElement, visited, bpmnModel); // 递归调用
                }
            }
        }
    }

    /**
     * 执行流程实例审核
     */
    @Override
    public void completeTask(TaskSubmitDto taskSubmitDto) {
        //获取当前节点
        Task task = taskService.createTaskQuery().processInstanceId(taskSubmitDto.getProcessInstanceId()).active().singleResult();
        if (null == task) {
            throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_HAS_END);
        }

        // 是否需要判断当前任务是否传入的用户下面，撤销时是否要判断你
        int message = taskSubmitDto.getMessage();
        if (message == 2) {
            // 撤销不进行用户校验
            taskComplete(task, taskSubmitDto);
        } else {
            taskComplete(task, taskSubmitDto);
            /*if (task.getAssignee().equals(taskSubmitDto.getAssignee())) {
            } else {
                throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_AUDIT_ERROR);
            }*/
        }
    }

    private void taskComplete(Task task, TaskSubmitDto taskSubmitDto) {
        //更新task信息
        Map<String, Object> map = new HashMap<String, Object>();
        task.setOwner(taskSubmitDto.getUserName());
        task.setDescription(taskSubmitDto.getAuditOption());
        taskService.saveTask(task);
        map.put(ProcessConstants.ASSIGNEE_MESSAGE, taskSubmitDto.getMessage());
        map.put(ProcessConstants.ASSIGNEE_IS_DELAY, taskSubmitDto.getFlag());
        map.put(ProcessConstants.ASSIGNEE_IS_CLOUD, taskSubmitDto.getIsCloud());
        map.put(ProcessConstants.ASSIGNEE_IS_UPPER_LIMIT, taskSubmitDto.getIsUpperLimit());
        // 执行当前节点审核，根据map中的message字段判断是撤销还是通过操作
        map.put(ProcessConstants.NODE_CODE, taskSubmitDto.getNodeCode());
        taskService.complete(task.getId(), map);
        if (null != taskSubmitDto.getNextAssignee() && !taskSubmitDto.getNextAssignee().isEmpty()) {
            //节点流转后 的节点 即审核前的下一节点
            Task nextTask = taskService.createTaskQuery().processInstanceId(taskSubmitDto.getProcessInstanceId()).active().singleResult();
            if (null != nextTask && null == nextTask.getAssignee()) {
                taskService.setAssignee(nextTask.getId(), taskSubmitDto.getNextAssignee());
            }
        }
    }

    /**
     * 流程已经被取消
     */
    @Override
    public String stopProcessInstance(String processInstanceId, Long userId) {
        //获取当前节点
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
        if (null == task) {
            throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_HAS_END);
        }

        String taskId = task.getId();
        // 获取流程的第一个user task节点，修改为根据指定任务节点进行流程撤销操作
        runtimeService.deleteProcessInstance(processInstanceId, ProcessConstants.CANCEL_PROCESS);
        return taskId;
    }

    @Override
    public String changeVariableByProIntId(VariableVo variableVo) {
        variableVo.getVariables().forEach((key, value) -> runtimeService.setVariable(variableVo.getProcessInstanceId(), key, value));
        return "重新设置成功!";
    }

    @Override
    public Object getVariableByProIntId(VariableVo variableVo) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(variableVo.getProcessInstanceId())
                .singleResult();
        for (Map.Entry<String, Object> entry : variableVo.getVariables().entrySet()) {
            String key = entry.getKey();
            if (processInstance != null) {
                // 查询运行时变量
                // todo 可以去掉直接从 历史表查，找个好时间点去掉试试，这块为了满足之前的完全没问题。
                //  注意：global variable和local variable都要考虑！
                Object variable = runtimeService.getVariable(variableVo.getProcessInstanceId(), key);
                log.info("从运行时获取变量 key:{},value:{}", key, variable);
                return variable;
            } else {
                // 查询历史变量
                List<HistoricVariableInstance> variables = historyService.createHistoricVariableInstanceQuery()
                        .processInstanceId(variableVo.getProcessInstanceId())
                        .variableName(key).list();
                Optional<HistoricVariableInstance> variableOptional = variables.stream().max(Comparator.comparing(HistoricVariableInstance::getLastUpdatedTime));
                if (variableOptional.isPresent()) {
                    Object historicValue = variableOptional.get().getValue();
                    log.info("从历史数据获取变量 key:{},value:{}", key, historicValue);
                    return historicValue;
                }
            }
        }
        return null;
    }

    private void allPathNode(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (null == processInstance) {
            log.warn("流程实例已结束或流程实例id参数错误！");
            return;
        }

        // 获取BpmnModel
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());

        List<FlowElement> startElements = new ArrayList<>();
        for (FlowElement flowElement : bpmnModel.getMainProcess().getFlowElements()) {
            if (flowElement instanceof StartEvent) {
                startElements.add(flowElement);
            }
        }

        // 对于每个开始事件，启动探索
        for (FlowElement startElement : startElements) {
            exploreGateways(startElement, new HashSet<>(), bpmnModel);
        }
    }



    /**
     * 构建task树。
     * <p>1. massage==0表示驳回，message==1表示取消</p>
     * <p>2. 除此之外，流程无环</p>
     *
     * @param currentElement
     * @param elementMap
     * @return
     */
    private TaskTreeNodeDTO buildTree(FlowElement currentElement, Map<String, FlowElement> elementMap) {
        if (!(currentElement instanceof UserTask)
                && !(currentElement instanceof StartEvent)
                && !(currentElement instanceof EndEvent)) {
            return null;
        }

        TaskTreeNodeDTO node = new TaskTreeNodeDTO();
        if (currentElement instanceof EndEvent) {
            node.setTaskBpmnName(ActivitiStatusEnum.AUTODIT_END.getNode());
            node.setChildren(Collections.emptyList());
        } else {
            node.setTaskBpmnName(currentElement.getName());
            node.setChildren(new ArrayList<>());
            // 获取当前节点的出线（outgoingFlows）
            FlowNode flowNode = (FlowNode) currentElement;
            List<SequenceFlow> outgoingFlows = flowNode.getOutgoingFlows();

            for (SequenceFlow flow : outgoingFlows) {
                if (StringUtils.isNotBlank(flow.getConditionExpression())) {
                    if (iscancelOrReject(flow)) continue;
                }
                FlowElement targetElement = elementMap.get(flow.getTargetRef());
                if (targetElement instanceof ExclusiveGateway) {
                    ExclusiveGateway targetElement1 = (ExclusiveGateway) targetElement;
                    for (SequenceFlow outgoingFlow : targetElement1.getOutgoingFlows()) {
                        if (iscancelOrReject(flow)) continue;
                        FlowElement targetElement2 = elementMap.get(outgoingFlow.getTargetRef());
                        TaskTreeNodeDTO taskTreeNode = buildTree(targetElement2, elementMap);
                        if (taskTreeNode != null) {
                            // 起始节点后跟的是默认的userTask，只有一个出口，不会接互斥网关
                            node.getChildren().add(taskTreeNode);
                        }
                    }
                } else {
                    TaskTreeNodeDTO taskTreeNode = buildTree(targetElement, elementMap);
                    if (taskTreeNode != null) {
                        // 起始节点后跟的是默认的userTask，只有一个出口，起始节点直接返回
                        if(currentElement instanceof StartEvent){
                            return taskTreeNode;
                        }
                        node.getChildren().add(taskTreeNode);
                    }
                }
            }
        }
        return node;
    }

    private boolean iscancelOrReject(SequenceFlow flow) {
        boolean isRejectFlow = flow.getConditionExpression().contains("message==0")
                || flow.getConditionExpression().contains("message ==0")
                || flow.getConditionExpression().contains("message == 0")
                || flow.getConditionExpression().contains("message== 0");
        boolean isCancelFlow = flow.getConditionExpression().contains("message==2")
                || flow.getConditionExpression().contains("message ==2")
                || flow.getConditionExpression().contains("message == 2")
                || flow.getConditionExpression().contains("message== 2");
        if (isRejectFlow || isCancelFlow) {
            return true;
        }
        return false;
    }
}
