<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datatech.slgzt.dao.mapper.DeviceCardMetricsMapper">

    <sql id="Base_Column_List">
        ID, DEVICE_ID, GPU_UTIL_PERCENT, MEM_UTIL_PERCENT, MEMORY_USAGE, DEV_POWER_USAGE,DEV_GPU_TEMP,DEVICE_TYPE,GPU_TIME, CREATED_AT
    </sql>



<!--    <insert id="batchInsertDeviceMetrics">-->
<!--        INSERT INTO WOC_GPU_DEVICE_METRICS ( ID, DEVICE_ID, GPU_UTIL_PERCENT, MEM_UTIL_PERCENT, MEMORY_USAGE, DEV_POWER_USAGE,DEV_GPU_TEMP, CREATED_AT )-->
<!--        VALUES-->
<!--        <foreach collection="deviceMetrics" item="item" separator=",">-->
<!--            (#{item.id}, #{item.deviceId}, #{item.gpuUtilPercent},-->
<!--            #{item.memUtilpercent}, #{item.memoryUsage}, #{item.devPowerUsage},-->
<!--            #{item.devGpuTemp},  #{item.deviceType}, #{item.gpuTime}, #{item.createdAt}-->
<!--            )-->
<!--        </foreach>-->
<!--    </insert>-->


</mapper>
