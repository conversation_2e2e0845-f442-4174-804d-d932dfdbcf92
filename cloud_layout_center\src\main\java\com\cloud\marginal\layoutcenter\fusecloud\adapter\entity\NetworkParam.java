package com.cloud.marginal.layoutcenter.fusecloud.adapter.entity;

import lombok.Data;

import java.util.List;

@Data
public class NetworkParam {

    /**
     * 主键id
     */
    private String id;

    /**
     * 租户id
     */
    private String billId;

    /**
     * 云区域编码
     */
    private String regionCode;

    /**
     * 网络名称
     */
    private String name;

    /**
     * 网络类型
     */
    private String networkType;

    /**
     * 业务系统与项目一对一映射，如租户下存在多个项目，需要指定项目则必传此参数
     */
    private String businessCode;

    /**
     * 该网络是否可跨租户共享
     */
    private Boolean shared = false;

    /**
     * 网络是否可被外部访问，默认为false，当创建弹性网络（即外部网络）时，需设置为true
     */
    private Boolean external = false;

    /**
     * 网络状态
     */
    private Boolean adminStateUp = true;

    /**
     * 物理网络的分隔ID
     */
    private Long segmentationId;

    /**
     * 物理网络
     */
    private String physicalNetwork;

    /**
     * 该网络关联的计算可用域列表
     */
    private List<String> aliasZoneIds;

    /**
     * 描述
     */
    private String description;
    /**
     * globalId
     * */
    private String globalId;
}
