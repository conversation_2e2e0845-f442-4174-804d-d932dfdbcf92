package com.cloud.marginal.layoutcenter.queue;

import com.cloud.marginal.layoutcenter.base.BaseService;
import com.cloud.marginal.layoutcenter.factory.LaoutServiceFactory;
import com.cloud.marginal.layoutcenter.service.LayoutService;
import com.cloud.marginal.layoutcenter.utils.QueueUtils;
import com.cloud.marginal.model.vo.layout.LayoutTaskVO;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.RejectedExecutionException;

/**
 * 队列任务处理器
 */
//@Component
public class QueueTaskHandler {

    @Resource
    private LayoutService layoutServiceImpl;

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * spring启动10秒后执行,如果线程意外终止,10秒后再次执行
     */
    @Scheduled(initialDelay = 10000,fixedDelay = 10000)
    public void queueTaskHandler() throws InterruptedException {
        while (true){
            LayoutTaskVO layoutTaskVO = QueueUtils.getHead();
            if(layoutTaskVO != null){
                try {
                    layoutServiceImpl.taskMonitor(layoutTaskVO);
                    // 当前线程休眠,休眠时间由队列中的任务数量动态决定
                    int sleepMillisecond = QueueUtils.getSleepMillisecond();
                    Thread.sleep(sleepMillisecond);
                }catch (RejectedExecutionException rejectedExecutionException){
                    // 线程池拒绝,重新放入队列
                    QueueUtils.addTail(layoutTaskVO);
                    // 线程池无空闲线程,休眠5s
                    Thread.sleep(5000);
                }
            }else {
                // 队列中无数据,休眠10s
                Thread.sleep(10000);
            }
        }
    }
}
