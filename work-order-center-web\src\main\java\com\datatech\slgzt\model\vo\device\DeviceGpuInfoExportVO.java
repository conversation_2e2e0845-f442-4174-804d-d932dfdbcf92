package com.datatech.slgzt.model.vo.device;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.Data;

import java.math.BigDecimal;

/**
 * GPU设备信息导出VO
 * <AUTHOR>
 */
@Data
public class DeviceGpuInfoExportVO {

    @ExcelExportHeader(value = "区域代码")
    private String areaCode;

    @ExcelExportHeader(value = "资源池名称")
    private String regionName;

    @ExcelExportHeader(value = "归属部门")
    private String deptName;

    @ExcelExportHeader(value = "业务系统名称")
    private String businessSystemName;

    @ExcelExportHeader(value = "设备ID")
    private String deviceId;

    @ExcelExportHeader(value = "显卡型号")
    private String modelName;

    @ExcelExportHeader(value = "设备IP")
    private String deviceIp;

    @ExcelExportHeader(value = "算力利用率(%)")
    private BigDecimal gpuUtilPercent;

    @ExcelExportHeader(value = "显存利用率(%)")
    private BigDecimal memUtilpercent;

    @ExcelExportHeader(value = "显存大小(GB)")
    private BigDecimal memory;

    @ExcelExportHeader(value = "任务数")
    private Integer taskNum;

    @ExcelExportHeader(value = "温度(℃)")
    private BigDecimal temperature;

    @ExcelExportHeader(value = "分配状态")
    private String inUsed;

    @ExcelExportHeader(value = "云类型名称")
    private String catalogueDomainName;
} 