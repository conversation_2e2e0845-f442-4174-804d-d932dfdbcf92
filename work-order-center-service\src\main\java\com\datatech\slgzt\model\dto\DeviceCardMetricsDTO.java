package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DeviceCardMetricsDTO implements Serializable {

    private Long id;

    /**
     * GPU/NPU卡序列号/uuid
     */
    private String deviceId;

    /**
     * 算力利用率
     */
    private Double gpuUtilPercent;

    /**
     * 显存利用率
     */
    private Double memUtilpercent;

    /**
     * 显存大小 显存大小（GB）
     */
    private BigDecimal memoryUsage;

    /**
     * 算力能耗top
     */
    private Double devPowerUsage;

    /**
     * gpu温度
     */
    private Double devGpuTemp;


    private String  gpuTime;

    private String  deviceType;

    /**
     * 设备型号
     */
    private String  modelName;

    /**
     * 指标来源
     */
    private String metricSource;

    /**
     * 任务数
     */
    private Integer allocationCount = 0;


    /**
     * 区域编码
     */
    private String areaCode;
    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    private LocalDateTime createdAt;
}
