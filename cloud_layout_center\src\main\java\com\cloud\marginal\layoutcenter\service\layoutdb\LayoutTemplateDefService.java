package com.cloud.marginal.layoutcenter.service.layoutdb;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.marginal.common.CecPage;
import com.cloud.marginal.model.dto.layout.LayoutTemplateDto;
import com.cloud.marginal.model.entity.layout.LayoutTemplateDef;
import com.cloud.marginal.model.vo.layout.LayoutTemplateVo;

import java.util.List;

/**
 * <p>
 * 编排模板配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface LayoutTemplateDefService extends IService<LayoutTemplateDef> {

    void create(LayoutTemplateDef layoutTemplateDef);

    void update(LayoutTemplateDef layoutTemplateDef);

    LayoutTemplateDef getByCode(String code);

    CecPage<LayoutTemplateVo> getPage(LayoutTemplateDto layoutTemplateDto);

    List<LayoutTemplateVo> getList(String name);

    void remove(String templateId);

}
