package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 谐云配置属性类
 * 统一管理谐云相关的配置项
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "xieyun")
public class XieyunProperties {

    /**
     * 管理员账号
     */
    private String adminAccount;

    /**
     * 管理员密码
     */
    private String adminPassword;

    /**
     * 谐云服务URL
     */
    private String xieyunUrl;

    /**
     * RSA公钥
     */
    private String publicKey;

    /**
     * 集群名称映射
     * key: regionCode (谐云中的nodePool)
     * value: 该资源池对应的集群名称
     */
    private Map<String, String> clusterNameMap;

    private String ampOrganId = "1";

    private String ampAppId = "210482";

    private String organManagerRoleId = "3";

    /**
     * 根据regionCode获取对应的集群名称
     *
     * @param regionCode 资源池编码
     * @return 集群名称，如果找不到则返回默认的clusterName
     */
    public String getClusterNameByRegionCode(String regionCode) {
        if (!StringUtils.hasText(regionCode)) {
            return null;
        }

        if (clusterNameMap != null && clusterNameMap.containsKey(regionCode)) {
            return clusterNameMap.get(regionCode);
        }
        return null;
    }
}
