package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.SlbListenerServerGroupDTO;
import com.datatech.slgzt.model.query.SlbListenerServerGroupQuery;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupPageReq;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupCreateReq;
import com.datatech.slgzt.model.req.slb.SlbListenerServerGroupUpdateReq;
import com.datatech.slgzt.model.vo.slb.SlbListenerServerGroupVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

/**
 * SLB监听器服务组Web层转换器
 */
@Mapper(componentModel = "spring")
public interface SlbListenerServerGroupWebConvert {
    
    /**
     * 分页请求转Query
     */
    SlbListenerServerGroupQuery convert(SlbListenerServerGroupPageReq req);
    
    /**
     * DTO转VO
     */
    SlbListenerServerGroupVO convert(SlbListenerServerGroupDTO dto);
    
    /**
     * 创建请求转DTO
     */
    SlbListenerServerGroupDTO convert(SlbListenerServerGroupCreateReq req);
    
    /**
     * 更新请求转DTO
     */
    SlbListenerServerGroupDTO convert(SlbListenerServerGroupUpdateReq req);

    default void fill(SlbListenerServerGroupVO.SlbListenerServerInfoVO serverInfoModel,ResourceDetailDTO detailDTO) {
        if (ObjNullUtils.isNotNull(detailDTO)) {
            serverInfoModel.setEip(detailDTO.getEip());
            serverInfoModel.setResourceDetailId(detailDTO.getId());
            serverInfoModel.setDeviceName(detailDTO.getDeviceName());
            serverInfoModel.setRegionCode(detailDTO.getResourcePoolCode());
            serverInfoModel.setRegionName(detailDTO.getResourcePoolName());
            serverInfoModel.setVpcId(detailDTO.getVpcId());
            serverInfoModel.setIp(detailDTO.getIp());
            serverInfoModel.setDomainName(detailDTO.getDomainName());
            serverInfoModel.setVpcName(detailDTO.getVpcName());
            serverInfoModel.setDeviceStatus(deviceStatus(detailDTO.getDeviceStatus()));
        }
    }

    @Named("deviceStatus")
    default String deviceStatus(String deviceStatus) {
        if (ObjNullUtils.isNull(deviceStatus)) {
            return null;
        }
        if (deviceStatus.equals("RUNING")) {
            return "运行中";
        }
        if (deviceStatus.equals("STARTING")) {
            return "启动中";
        }
        if (deviceStatus.equals("ERROR")) {
            return "异常";
        }
        if (deviceStatus.equals("RESTARTING")) {
            return "重启中";
        }
        if (deviceStatus.equals("STOPED")) {
            return "已关机";
        }
        if (deviceStatus.equals("DELETED")) {
            return "已删除";
        }
        return "运行中";
    }
} 